/*--------------------------------*- C++ -*----------------------------------*\
  =========                 |
  \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox
   \\    /   O peration     | Website:  https://openfoam.org
    \\  /    A nd           | Version:  12
     \\/     M anipulation  |
\*---------------------------------------------------------------------------*/
FoamFile
{
    format      ascii;
    class       volVectorField;
    location    "0.3";
    object      U;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

dimensions      [0 1 -1 0 0 0 0];

internalField   nonuniform List<vector> 
400
(
(0.000253384 -0.000250385 0)
(0.000141196 0.000111435 0)
(-0.00117689 0.000564558 0)
(-0.00348083 0.000884901 0)
(-0.00637694 0.00104561 0)
(-0.00946866 0.00106164 0)
(-0.0124019 0.000957754 0)
(-0.0148837 0.000760364 0)
(-0.0166881 0.000494598 0)
(-0.0176568 0.000184546 0)
(-0.0177007 -0.000145251 0)
(-0.0168037 -0.000468321 0)
(-0.0150289 -0.000755347 0)
(-0.0125242 -0.000974182 0)
(-0.00952569 -0.0010911 0)
(-0.00635454 -0.00107392 0)
(-0.00340043 -0.000898055 0)
(-0.00108639 -0.000556355 0)
(0.000196387 -9.03727e-05 0)
(0.000267833 0.000262354 0)
(-7.74591e-05 -0.000169311 0)
(-0.00161143 0.00164472 0)
(-0.00569245 0.00361702 0)
(-0.0117179 0.00503441 0)
(-0.0188909 0.00574276 0)
(-0.0263988 0.00575974 0)
(-0.0334667 0.00517126 0)
(-0.0394239 0.00409219 0)
(-0.0437408 0.00264744 0)
(-0.0460438 0.00096687 0)
(-0.0461262 -0.000813134 0)
(-0.0439583 -0.00254685 0)
(-0.0396985 -0.00407831 0)
(-0.0337005 -0.00524471 0)
(-0.0265117 -0.00588614 0)
(-0.0188534 -0.00586445 0)
(-0.011566 -0.00509442 0)
(-0.00551298 -0.0035892 0)
(-0.00149301 -0.00155906 0)
(-4.25835e-05 0.000227417 0)
(-0.000528569 0.00109156 0)
(-0.00371113 0.00573588 0)
(-0.0105882 0.0102237 0)
(-0.0199904 0.0134067 0)
(-0.0307628 0.0149451 0)
(-0.0418195 0.0148377 0)
(-0.0521164 0.0132547 0)
(-0.0607403 0.0104554 0)
(-0.0669652 0.00674152 0)
(-0.0702783 0.00243873 0)
(-0.0703963 -0.00210632 0)
(-0.0672802 -0.00652492 0)
(-0.0611487 -0.0104284 0)
(-0.0524854 -0.0134201 0)
(-0.0420346 -0.0151224 0)
(-0.0307721 -0.0152246 0)
(-0.0198279 -0.0135551 0)
(-0.010363 -0.0101783 0)
(-0.00354833 -0.00555075 0)
(-0.000476822 -0.000948798 0)
(-0.000928161 0.0034796 0)
(-0.00569113 0.0122198 0)
(-0.0153009 0.0199203 0)
(-0.0279152 0.025196 0)
(-0.0419829 0.027595 0)
(-0.0561758 0.0271355 0)
(-0.0692481 0.0241072 0)
(-0.0801211 0.0189525 0)
(-0.0879432 0.0121947 0)
(-0.0921136 0.00440484 0)
(-0.0922983 -0.0038076 0)
(-0.0884453 -0.0117956 0)
(-0.0808006 -0.0188807 0)
(-0.0699192 -0.0243745 0)
(-0.0566662 -0.0276257 0)
(-0.0421926 -0.0280998 0)
(-0.0278552 -0.025496 0)
(-0.0150929 -0.0198904 0)
(-0.00550476 -0.011927 0)
(-0.000861556 -0.00322468 0)
(-0.00127885 0.00687731 0)
(-0.00753562 0.0209522 0)
(-0.0198038 0.0325081 0)
(-0.035533 0.0401004 0)
(-0.0527408 0.0432872 0)
(-0.0698551 0.0421898 0)
(-0.0854592 0.0372749 0)
(-0.0983558 0.029211 0)
(-0.107615 0.018776 0)
(-0.112583 0.00681337 0)
(-0.112892 -0.00577982 0)
(-0.108469 -0.0180533 0)
(-0.0995576 -0.0290092 0)
(-0.0867353 -0.03763 0)
(-0.0709325 -0.042942 0)
(-0.0534279 -0.0441259 0)
(-0.035783 -0.0406761 0)
(-0.0197342 -0.0325904 0)
(-0.00737025 -0.0205893 0)
(-0.00120362 -0.00649776 0)
(-0.00160744 0.0112148 0)
(-0.00931758 0.0318724 0)
(-0.0242081 0.0479485 0)
(-0.0430007 0.0580679 0)
(-0.0632542 0.0619327 0)
(-0.0831492 0.0598768 0)
(-0.10112 0.0526245 0)
(-0.115887 0.0411229 0)
(-0.126482 0.0264353 0)
(-0.132231 0.00969238 0)
(-0.132744 -0.00791551 0)
(-0.127917 -0.02513 0)
(-0.117961 -0.0406218 0)
(-0.103429 -0.0530154 0)
(-0.0852653 -0.0609618 0)
(-0.0648328 -0.0632756 0)
(-0.0438836 -0.059138 0)
(-0.0244766 -0.0483412 0)
(-0.0092539 -0.0315556 0)
(-0.00153768 -0.0107279 0)
(-0.00193996 0.0164759 0)
(-0.0111208 0.0449711 0)
(-0.0286492 0.0662287 0)
(-0.0504777 0.0790538 0)
(-0.0736819 0.0834403 0)
(-0.0961997 0.0800683 0)
(-0.116347 0.0700193 0)
(-0.132809 0.0545809 0)
(-0.144625 0.0351293 0)
(-0.151135 0.013084 0)
(-0.151943 -0.0100854 0)
(-0.146909 -0.0328299 0)
(-0.136176 -0.0534955 0)
(-0.120218 -0.0703316 0)
(-0.0999287 -0.0815594 0)
(-0.0766984 -0.0855268 0)
(-0.0524381 -0.0809583 0)
(-0.0295437 -0.0672727 0)
(-0.0112832 -0.0449367 0)
(-0.0019 -0.0159506 0)
(-0.00229995 0.0226934 0)
(-0.0130246 0.0602968 0)
(-0.0332497 0.0873577 0)
(-0.0580777 0.102989 0)
(-0.0840717 0.107658 0)
(-0.10895 0.102554 0)
(-0.130963 0.0892378 0)
(-0.148829 0.069406 0)
(-0.161661 0.0447687 0)
(-0.168862 0.017019 0)
(-0.170057 -0.0121322 0)
(-0.165066 -0.0408898 0)
(-0.153925 -0.0673092 0)
(-0.136965 -0.0892667 0)
(-0.114939 -0.104505 0)
(-0.089173 -0.110789 0)
(-0.0616687 -0.106206 0)
(-0.0351472 -0.089574 0)
(-0.0135854 -0.0609459 0)
(-0.00232626 -0.0222763 0)
(-0.00271158 0.0299451 0)
(-0.0151076 0.0779517 0)
(-0.0381139 0.111349 0)
(-0.0658468 0.129741 0)
(-0.0943245 0.134308 0)
(-0.121096 0.126965 0)
(-0.14444 0.109895 0)
(-0.163209 0.085276 0)
(-0.176683 0.0551636 0)
(-0.184402 0.021486 0)
(-0.186053 -0.0138732 0)
(-0.181417 -0.0489527 0)
(-0.170391 -0.0815874 0)
(-0.153077 -0.109316 0)
(-0.129968 -0.129373 0)
(-0.102183 -0.13882 0)
(-0.0716879 -0.134879 0)
(-0.041466 -0.115463 0)
(-0.0162877 -0.079891 0)
(-0.00285463 -0.029888 0)
(-0.00320763 0.0383574 0)
(-0.0174661 0.0980838 0)
(-0.0433407 0.138181 0)
(-0.073756 0.159036 0)
(-0.104159 0.162891 0)
(-0.132031 0.152663 0)
(-0.155824 0.131333 0)
(-0.174677 0.101634 0)
(-0.188166 0.0659538 0)
(-0.196066 0.0263885 0)
(-0.198184 -0.015111 0)
(-0.194282 -0.056541 0)
(-0.18408 -0.0956354 0)
(-0.167365 -0.129681 0)
(-0.144214 -0.155414 0)
(-0.115337 -0.169088 0)
(-0.082445 -0.166794 0)
(-0.0486356 -0.145117 0)
(-0.0195285 -0.102155 0)
(-0.00353353 -0.0390454 0)
(-0.00384398 0.0481264 0)
(-0.0202463 0.120888 0)
(-0.0490469 0.167744 0)
(-0.081687 0.190356 0)
(-0.113054 0.192547 0)
(-0.140747 0.178605 0)
(-0.163623 0.152504 0)
(-0.18131 0.117586 0)
(-0.193848 0.0765311 0)
(-0.201362 0.0314933 0)
(-0.203858 -0.0156571 0)
(-0.201111 -0.063046 0)
(-0.19265 -0.108484 0)
(-0.177858 -0.149162 0)
(-0.156224 -0.181406 0)
(-0.12778 -0.200597 0)
(-0.0936443 -0.201415 0)
(-0.0567331 -0.178566 0)
(-0.0234798 -0.128168 0)
(-0.00443553 -0.0501025 0)
(-0.00472544 0.059569 0)
(-0.0236951 0.146612 0)
(-0.0553839 0.199748 0)
(-0.0893781 0.222781 0)
(-0.120124 0.221882 0)
(-0.145679 0.203179 0)
(-0.165634 0.171832 0)
(-0.180375 0.131804 0)
(-0.190586 0.0859662 0)
(-0.196868 0.0363754 0)
(-0.199506 -0.0153689 0)
(-0.19834 -0.0677388 0)
(-0.192721 -0.11886 0)
(-0.181577 -0.16607 0)
(-0.163648 -0.205479 0)
(-0.137968 -0.231652 0)
(-0.104597 -0.237616 0)
(-0.0657341 -0.215518 0)
(-0.0283779 -0.158363 0)
(-0.00568211 -0.0635502 0)
(-0.0060517 0.0732265 0)
(-0.0282264 0.175558 0)
(-0.0625102 0.233564 0)
(-0.0962595 0.254738 0)
(-0.12386 0.248722 0)
(-0.144402 0.224018 0)
(-0.158676 0.1871 0)
(-0.168113 0.142456 0)
(-0.174208 0.0929654 0)
(-0.178125 0.0403781 0)
(-0.180492 -0.014197 0)
(-0.181272 -0.0698173 0)
(-0.179701 -0.125205 0)
(-0.174275 -0.178173 0)
(-0.162899 -0.224965 0)
(-0.143319 -0.259579 0)
(-0.113939 -0.273321 0)
(-0.0753941 -0.25506 0)
(-0.0345566 -0.193086 0)
(-0.00748634 -0.0801024 0)
(-0.00819221 0.0900448 0)
(-0.0344834 0.208016 0)
(-0.070414 0.267878 0)
(-0.101026 0.283599 0)
(-0.121579 0.269803 0)
(-0.133113 0.237833 0)
(-0.138169 0.195398 0)
(-0.13947 0.147228 0)
(-0.139375 0.0959061 0)
(-0.139603 0.0426186 0)
(-0.141118 -0.0122318 0)
(-0.144063 -0.0685039 0)
(-0.147676 -0.125789 0)
(-0.150176 -0.182763 0)
(-0.148725 -0.236317 0)
(-0.139647 -0.280439 0)
(-0.119097 -0.305018 0)
(-0.0849373 -0.295153 0)
(-0.0424409 -0.232373 0)
(-0.0102161 -0.100835 0)
(-0.0117676 0.111634 0)
(-0.0432604 0.243958 0)
(-0.078299 0.300029 0)
(-0.100631 0.305087 0)
(-0.108355 0.280454 0)
(-0.105736 0.240376 0)
(-0.0975357 0.193276 0)
(-0.087778 0.143545 0)
(-0.0795087 0.0930332 0)
(-0.0747934 0.0420941 0)
(-0.0748109 -0.0097279 0)
(-0.0798984 -0.0632086 0)
(-0.0894698 -0.118986 0)
(-0.101794 -0.176952 0)
(-0.113691 -0.235279 0)
(-0.120309 -0.288876 0)
(-0.115266 -0.32719 0)
(-0.0922678 -0.331789 0)
(-0.0523162 -0.2754 0)
(-0.0144304 -0.127346 0)
(-0.0175494 0.140658 0)
(-0.0547651 0.282143 0)
(-0.0827877 0.324715 0)
(-0.088083 0.312469 0)
(-0.0751123 0.274451 0)
(-0.0525959 0.226766 0)
(-0.0274645 0.177284 0)
(-0.00452149 0.129132 0)
(0.0130912 0.0829107 0)
(0.0234321 0.0379462 0)
(0.0253907 -0.0070729 0)
(0.0185 -0.0537567 0)
(0.00297301 -0.103749 0)
(-0.0200028 -0.158316 0)
(-0.0476906 -0.217498 0)
(-0.0749883 -0.278356 0)
(-0.0935491 -0.331787 0)
(-0.0920651 -0.357679 0)
(-0.063281 -0.319292 0)
(-0.0206337 -0.161973 0)
(-0.0254498 0.181711 0)
(-0.065775 0.31777 0)
(-0.0733342 0.33136 0)
(-0.0480791 0.295398 0)
(-0.00558144 0.244209 0)
(0.0412142 0.192377 0)
(0.0846477 0.145044 0)
(0.12053 0.102969 0)
(0.146695 0.0651364 0)
(0.16204 0.0298909 0)
(0.165934 -0.00467121 0)
(0.157929 -0.0406273 0)
(0.137726 -0.0802447 0)
(0.105435 -0.125915 0)
(0.0622576 -0.179724 0)
(0.0118023 -0.242018 0)
(-0.0378323 -0.307875 0)
(-0.0716807 -0.360141 0)
(-0.070056 -0.356607 0)
(-0.0279123 -0.208374 0)
(-0.029649 0.239176 0)
(-0.0608415 0.335767 0)
(-0.0219431 0.300502 0)
(0.0505404 0.240126 0)
(0.12757 0.182947 0)
(0.197004 0.135388 0)
(0.254332 0.0973974 0)
(0.298245 0.0668498 0)
(0.328819 0.0413545 0)
(0.346461 0.0187783 0)
(0.35137 -0.00279396 0)
(0.343296 -0.0252645 0)
(0.321463 -0.0507867 0)
(0.284638 -0.0820468 0)
(0.231568 -0.122444 0)
(0.162178 -0.175668 0)
(0.0798985 -0.24343 0)
(-0.00319102 -0.319135 0)
(-0.05467 -0.368611 0)
(-0.0284594 -0.267618 0)
(0.00808312 0.281861 0)
(0.019783 0.294972 0)
(0.139066 0.210972 0)
(0.264459 0.143476 0)
(0.364366 0.0971853 0)
(0.440362 0.0657768 0)
(0.49674 0.0442369 0)
(0.53687 0.0288855 0)
(0.563442 0.0171986 0)
(0.578301 0.00748468 0)
(0.582376 -0.00149402 0)
(0.575691 -0.010845 0)
(0.557304 -0.0218466 0)
(0.525131 -0.0363344 0)
(0.475774 -0.057259 0)
(0.404535 -0.0893835 0)
(0.305175 -0.139741 0)
(0.173416 -0.216386 0)
(0.0411718 -0.311655 0)
(0.0184302 -0.299995 0)
(0.299604 0.146091 0)
(0.396272 0.127503 0)
(0.55742 0.0760895 0)
(0.674487 0.0437232 0)
(0.743888 0.025572 0)
(0.787152 0.0152421 0)
(0.815484 0.00921222 0)
(0.833954 0.00550932 0)
(0.845383 0.00302713 0)
(0.851369 0.00114933 0)
(0.852664 -0.000499356 0)
(0.84938 -0.002215 0)
(0.840998 -0.00433847 0)
(0.826139 -0.00742398 0)
(0.802077 -0.0125321 0)
(0.76362 -0.0217727 0)
(0.699108 -0.0392828 0)
(0.583952 -0.0726601 0)
(0.415827 -0.127868 0)
(0.308819 -0.149468 0)
)
;

boundaryField
{
    movingWall
    {
        type            fixedValue;
        value           uniform (1 0 0);
    }
    fixedWalls
    {
        type            noSlip;
    }
    frontAndBack
    {
        type            empty;
    }
}


// ************************************************************************* //
