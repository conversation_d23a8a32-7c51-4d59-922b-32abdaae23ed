/*--------------------------------*- C++ -*----------------------------------*\
  =========                 |
  \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox
   \\    /   O peration     | Website:  https://openfoam.org
    \\  /    A nd           | Version:  12
     \\/     M anipulation  |
\*---------------------------------------------------------------------------*/
FoamFile
{
    format      ascii;
    class       surfaceScalarField;
    location    "0.5";
    object      phi;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

dimensions      [0 3 -1 0 0 0 0];

internalField   nonuniform List<scalar> 
760
(
1.42325e-08
-1.42685e-08
-2.22117e-08
3.64442e-08
-1.14276e-07
9.20639e-08
-2.45507e-07
1.31232e-07
-3.96119e-07
1.50613e-07
-5.47395e-07
1.51276e-07
-6.83227e-07
1.35832e-07
-7.90703e-07
1.07477e-07
-8.6025e-07
6.95468e-08
-8.85685e-07
2.5434e-08
-8.64361e-07
-2.13236e-08
-7.97426e-07
-6.69353e-08
-6.90117e-07
-1.07309e-07
-5.51997e-07
-1.38119e-07
-3.9696e-07
-1.55037e-07
-2.42759e-07
-1.54202e-07
-1.09737e-07
-1.33022e-07
-1.84167e-08
-9.13211e-08
1.56903e-08
-3.41072e-08
1.56904e-08
-3.42176e-08
1.99491e-08
-1.80386e-07
1.82612e-07
-4.34017e-07
3.45695e-07
-7.64483e-07
4.61698e-07
-1.13229e-06
5.18415e-07
-1.49734e-06
5.16332e-07
-1.82346e-06
4.61947e-07
-2.08067e-06
3.64691e-07
-2.24642e-06
2.35295e-07
-2.30618e-06
8.52007e-08
-2.25404e-06
-7.34686e-08
-2.09318e-06
-2.27799e-07
-1.83637e-06
-3.64115e-07
-1.50613e-06
-4.68361e-07
-1.13415e-06
-5.2702e-07
-7.59562e-07
-5.28784e-07
-4.25422e-07
-4.67162e-07
-1.72701e-07
-3.44043e-07
-3.08035e-08
-1.76004e-07
-1.51131e-08
-9.22344e-08
1.12183e-07
-3.57019e-07
4.47398e-07
-7.64606e-07
7.53282e-07
-1.26857e-06
9.65657e-07
-1.81481e-06
1.06466e-06
-2.34919e-06
1.05071e-06
-2.82261e-06
9.35362e-07
-3.19413e-06
7.36221e-07
-3.43281e-06
4.73975e-07
-3.51872e-06
1.71106e-07
-3.44377e-06
-1.48416e-07
-3.21244e-06
-4.59132e-07
-2.84226e-06
-7.34292e-07
-2.36393e-06
-9.46693e-07
-1.82041e-06
-1.07054e-06
-1.26455e-06
-1.08464e-06
-7.5456e-07
-9.77154e-07
-3.46988e-07
-7.51615e-07
-8.73402e-08
-4.35652e-07
-1.02453e-07
-1.4522e-07
2.57403e-07
-5.25491e-07
8.27669e-07
-1.08169e-06
1.30948e-06
-1.7476e-06
1.63157e-06
-2.45446e-06
1.77152e-06
-3.13654e-06
1.73279e-06
-3.73549e-06
1.53431e-06
-4.20313e-06
1.20386e-06
-4.50315e-06
7.74e-07
-4.61216e-06
2.80118e-07
-4.52048e-06
-2.40093e-07
-4.23294e-06
-7.46674e-07
-3.76955e-06
-1.19769e-06
-3.16579e-06
-1.55046e-06
-2.47199e-06
-1.76434e-06
-1.75115e-06
-1.80548e-06
-1.07456e-06
-1.65374e-06
-5.15161e-07
-1.31101e-06
-1.39322e-07
-8.11491e-07
-2.41775e-07
-1.9362e-07
4.51022e-07
-6.85033e-07
1.31908e-06
-1.38568e-06
2.01013e-06
-2.20734e-06
2.45323e-06
-3.0656e-06
2.62978e-06
-3.88395e-06
2.55114e-06
-4.5967e-06
2.24706e-06
-5.1508e-06
1.75796e-06
-5.50668e-06
1.12988e-06
-5.63876e-06
4.12196e-07
-5.53598e-06
-3.4287e-07
-5.20258e-06
-1.08007e-06
-4.65906e-06
-1.7412e-06
-3.94311e-06
-2.26641e-06
-3.10982e-06
-2.59762e-06
-2.23058e-06
-2.68472e-06
-1.38969e-06
-2.49464e-06
-6.78523e-07
-2.02218e-06
-1.87775e-07
-1.30224e-06
-4.2955e-07
-2.3995e-07
6.90972e-07
-8.40381e-07
1.91951e-06
-1.68335e-06
2.8531e-06
-2.65715e-06
3.42703e-06
-3.66097e-06
3.63359e-06
-4.60793e-06
3.4981e-06
-5.42655e-06
3.06568e-06
-6.06078e-06
2.3922e-06
-6.46958e-06
1.53868e-06
-6.62628e-06
5.68895e-07
-6.51854e-06
-4.50609e-07
-6.14901e-06
-1.44961e-06
-5.53672e-06
-2.35349e-06
-4.71907e-06
-3.08406e-06
-3.75358e-06
-3.56312e-06
-2.71856e-06
-3.71974e-06
-1.71159e-06
-3.50161e-06
-8.44688e-07
-2.88908e-06
-2.36187e-07
-1.91074e-06
-6.65736e-07
-2.86754e-07
9.77726e-07
-9.97116e-07
2.62987e-06
-1.98214e-06
3.83813e-06
-3.10504e-06
4.54992e-06
-4.24808e-06
4.77664e-06
-5.31497e-06
4.565e-06
-6.23029e-06
3.981e-06
-6.93737e-06
3.09928e-06
-7.3957e-06
1.997e-06
-7.57884e-06
7.52033e-07
-7.47337e-06
-5.56077e-07
-7.07931e-06
-1.84367e-06
-6.41205e-06
-3.02074e-06
-5.50573e-06
-3.99039e-06
-4.41717e-06
-4.65168e-06
-3.22945e-06
-4.90746e-06
-2.053e-06
-4.67805e-06
-1.02256e-06
-3.91952e-06
-2.88289e-07
-2.64501e-06
-9.54024e-07
-3.36338e-07
1.31406e-06
-1.1604e-06
3.45393e-06
-2.28806e-06
4.9658e-06
-3.55507e-06
5.81693e-06
-4.82672e-06
6.04829e-06
-5.99919e-06
5.73747e-06
-6.99618e-06
4.97799e-06
-7.76368e-06
3.86678e-06
-8.26465e-06
2.49798e-06
-8.47479e-06
9.62164e-07
-8.38013e-06
-6.50733e-07
-7.97705e-06
-2.24675e-06
-7.27472e-06
-3.72307e-06
-6.30007e-06
-4.96503e-06
-5.10477e-06
-5.84698e-06
-3.7726e-06
-6.23963e-06
-2.42493e-06
-6.02572e-06
-1.22077e-06
-5.12368e-06
-3.47714e-07
-3.51807e-06
-1.30174e-06
-3.90962e-07
1.70503e-06
-1.3349e-06
4.39787e-06
-2.60501e-06
6.2359e-06
-4.00599e-06
7.21791e-06
-5.38688e-06
7.42918e-06
-6.63983e-06
6.99042e-06
-7.69253e-06
6.03069e-06
-8.49857e-06
4.67282e-06
-9.02853e-06
3.02793e-06
-9.26304e-06
1.19667e-06
-9.18876e-06
-7.25008e-07
-8.79759e-06
-2.63792e-06
-8.08946e-06
-4.4312e-06
-7.0791e-06
-5.97539e-06
-5.80639e-06
-7.11969e-06
-4.3491e-06
-7.69693e-06
-2.83485e-06
-7.53998e-06
-1.44711e-06
-6.51142e-06
-4.18104e-07
-4.54708e-06
-1.71984e-06
-4.53455e-07
2.15848e-06
-1.52569e-06
5.47011e-06
-2.93489e-06
7.64511e-06
-4.45013e-06
8.73315e-06
-5.90636e-06
8.8854e-06
-7.19785e-06
8.28192e-06
-8.26371e-06
7.09654e-06
-9.07212e-06
5.48124e-06
-9.60694e-06
3.56274e-06
-9.85766e-06
1.4474e-06
-9.81349e-06
-7.69179e-07
-9.46144e-06
-2.98997e-06
-8.78917e-06
-5.10348e-06
-7.79305e-06
-6.9715e-06
-6.49225e-06
-8.42049e-06
-4.948e-06
-9.24118e-06
-3.28513e-06
-9.20284e-06
-1.70869e-06
-8.08786e-06
-5.0359e-07
-5.75217e-06
-2.22343e-06
-5.28333e-07
2.68681e-06
-1.73965e-06
6.68142e-06
-3.27796e-06
9.18341e-06
-4.8716e-06
1.03268e-05
-6.34698e-06
1.03608e-05
-7.61079e-06
9.54572e-06
-8.62432e-06
8.11007e-06
-9.37965e-06
6.23657e-06
-9.88096e-06
4.06405e-06
-1.01315e-05
1.69794e-06
-1.01258e-05
-7.74925e-07
-9.84631e-06
-3.26941e-06
-9.26597e-06
-5.68382e-06
-8.35632e-06
-7.88115e-06
-7.10469e-06
-9.67213e-06
-5.5407e-06
-1.08052e-05
-3.7707e-06
-1.09728e-05
-2.01217e-06
-9.8464e-06
-6.09688e-07
-7.15465e-06
-2.83312e-06
-6.23659e-07
3.31047e-06
-1.98736e-06
8.04513e-06
-3.63187e-06
1.08279e-05
-5.24187e-06
1.19368e-05
-6.6475e-06
1.17664e-05
-7.78439e-06
1.06826e-05
-8.65092e-06
8.9766e-06
-9.2742e-06
6.85985e-06
-9.68643e-06
4.47628e-06
-9.90971e-06
1.92123e-06
-9.94713e-06
-7.37501e-07
-9.7785e-06
-3.43805e-06
-9.36081e-06
-6.1015e-06
-8.63555e-06
-8.60641e-06
-7.54634e-06
-1.07613e-05
-6.07155e-06
-1.228e-05
-4.27425e-06
-1.27701e-05
-2.36364e-06
-1.1757e-05
-7.44774e-07
-8.77351e-06
-3.57789e-06
-7.54066e-07
4.06454e-06
-2.28432e-06
9.57538e-06
-3.98695e-06
1.25306e-05
-5.50901e-06
1.34589e-05
-6.70949e-06
1.29669e-05
-7.57843e-06
1.15515e-05
-8.1699e-06
9.56808e-06
-8.55747e-06
7.24742e-06
-8.80757e-06
4.72637e-06
-8.96489e-06
2.07855e-06
-9.04422e-06
-6.58174e-07
-9.02561e-06
-3.45665e-06
-8.85248e-06
-6.27463e-06
-8.43473e-06
-9.02416e-06
-7.66283e-06
-1.15332e-05
-6.44195e-06
-1.35008e-05
-4.75622e-06
-1.44559e-05
-2.76673e-06
-1.37465e-05
-9.2243e-07
-1.06178e-05
-4.50032e-06
-9.45193e-07
5.00973e-06
-2.64845e-06
1.12786e-05
-4.31092e-06
1.4193e-05
-5.57318e-06
1.47211e-05
-6.37053e-06
1.37642e-05
-6.78309e-06
1.19641e-05
-6.94028e-06
9.72527e-06
-6.96948e-06
7.27662e-06
-6.97252e-06
4.7294e-06
-7.01623e-06
2.12227e-06
-7.12834e-06
-5.46064e-07
-7.29352e-06
-3.29148e-06
-7.44843e-06
-6.11973e-06
-7.47771e-06
-8.99488e-06
-7.2178e-06
-1.17931e-05
-6.48266e-06
-1.4236e-05
-5.13338e-06
-1.58051e-05
-3.21495e-06
-1.56649e-05
-1.16438e-06
-1.26684e-05
-5.6647e-06
-1.2373e-06
6.24703e-06
-3.08325e-06
1.31246e-05
-4.50762e-06
1.56174e-05
-5.23435e-06
1.54479e-05
-5.35504e-06
1.38849e-05
-5.08161e-06
1.16907e-05
-4.63063e-06
9.27429e-06
-4.17897e-06
6.82496e-06
-3.85405e-06
4.40448e-06
-3.7367e-06
2.00493e-06
-3.86482e-06
-4.1795e-07
-4.23235e-06
-2.92394e-06
-4.78183e-06
-5.57025e-06
-5.39086e-06
-8.38585e-06
-5.85833e-06
-1.13257e-05
-5.90646e-06
-1.41879e-05
-5.23275e-06
-1.64789e-05
-3.66624e-06
-1.72314e-05
-1.50007e-06
-1.48346e-05
-7.16477e-06
-1.67379e-06
7.92083e-06
-3.51333e-06
1.49641e-05
-4.31522e-06
1.64193e-05
-4.08875e-06
1.52214e-05
-3.19287e-06
1.29891e-05
-1.99852e-06
1.04963e-05
-7.94882e-07
8.07065e-06
2.19629e-07
5.81045e-06
9.18545e-07
3.70557e-06
1.22592e-06
1.69755e-06
1.10237e-06
-2.94401e-07
5.4135e-07
-2.36292e-06
-4.22673e-07
-4.60623e-06
-1.69235e-06
-7.11617e-06
-3.07224e-06
-9.94578e-06
-4.2307e-06
-1.30294e-05
-4.69557e-06
-1.6014e-05
-3.96714e-06
-1.79599e-05
-1.94491e-06
-1.68568e-05
-9.10969e-06
-2.23068e-06
1.01515e-05
-3.59026e-06
1.63237e-05
-3.07772e-06
1.59067e-05
-1.34377e-06
1.34874e-05
8.97183e-07
1.07481e-05
3.15516e-06
8.23834e-06
5.13835e-06
6.08747e-06
6.68895e-06
4.25984e-06
7.72611e-06
2.66841e-06
8.20684e-06
1.21682e-06
8.10417e-06
-1.9173e-07
7.3993e-06
-1.65805e-06
6.08709e-06
-3.29402e-06
4.19938e-06
-5.22845e-06
1.85431e-06
-7.6007e-06
-6.61606e-07
-1.05135e-05
-2.79211e-06
-1.38835e-05
-3.66206e-06
-1.70899e-05
-2.41153e-06
-1.81073e-05
-1.15212e-05
-2.57381e-06
1.27253e-05
-2.18344e-06
1.59333e-05
7.18112e-07
1.30052e-05
4.46964e-06
9.73591e-06
8.1334e-06
7.08434e-06
1.13017e-05
5.07002e-06
1.38293e-05
3.55992e-06
1.56883e-05
2.40081e-06
1.68919e-05
1.46482e-06
1.74551e-05
6.53608e-07
1.73766e-05
-1.13207e-07
1.66307e-05
-9.12169e-07
1.51674e-05
-1.83068e-06
1.29237e-05
-2.98476e-06
9.86187e-06
-4.53891e-06
6.06205e-06
-6.71366e-06
1.91239e-06
-9.73382e-06
-1.5596e-06
-1.36179e-05
-2.45833e-06
-1.72086e-05
-1.39795e-05
-7.85014e-07
1.35103e-05
4.0808e-06
1.10675e-05
1.02976e-05
6.78843e-06
1.57937e-05
4.2398e-06
2.01581e-05
2.71992e-06
2.34582e-05
1.76996e-06
2.58609e-05
1.15715e-06
2.75217e-05
7.40061e-07
2.85542e-05
4.32298e-07
2.90266e-05
1.81223e-07
2.89624e-05
-4.89835e-08
2.83389e-05
-2.88704e-07
2.7082e-05
-5.73748e-07
2.50552e-05
-9.57964e-07
2.20518e-05
-1.53553e-06
1.78155e-05
-2.47742e-06
1.21724e-05
-4.09069e-06
5.47774e-06
-6.92325e-06
-1.29166e-07
-1.16017e-05
-1.41087e-05
1.35103e-05
2.45778e-05
3.13663e-05
3.56061e-05
3.8326e-05
4.0096e-05
4.12531e-05
4.19932e-05
4.24255e-05
4.26067e-05
4.25577e-05
4.2269e-05
4.16952e-05
4.07373e-05
3.92018e-05
3.67243e-05
3.26336e-05
2.57104e-05
1.41087e-05
)
;

boundaryField
{
    movingWall
    {
        type            calculated;
        value           uniform 0;
    }
    fixedWalls
    {
        type            calculated;
        value           uniform 0;
    }
    frontAndBack
    {
        type            empty;
    }
}


// ************************************************************************* //
