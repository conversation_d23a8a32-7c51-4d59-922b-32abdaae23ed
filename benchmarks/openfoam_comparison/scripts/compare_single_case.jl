#!/usr/bin/env julia

"""
Compare Single OpenFOAM Case

Run a comprehensive comparison between OpenFOAM and JuliaFOAM for a single test case.
Focus on accuracy first, then performance.

Usage:
    julia compare_single_case.jl <case_name>
    julia compare_single_case.jl cavity
    julia compare_single_case.jl /path/to/case
"""

include("../src/SolverComparison.jl")
include("../src/ReportGeneration.jl")

using Printf

# Available test cases
const AVAILABLE_CASES = Dict(
    "cavity" => "/opt/openfoam12/tutorials/legacy/incompressible/icoFoam/cavity/cavity",
    "elbow" => "/opt/openfoam12/tutorials/legacy/incompressible/icoFoam/elbow/elbow",
    "pitzDaily" => "/opt/openfoam12/tutorials/incompressibleFluid/pitzDaily",
    "airFoil2D" => "/opt/openfoam12/tutorials/incompressibleFluid/airFoil2D"
)

function print_usage()
    println("Usage: julia compare_single_case.jl <case_name>")
    println()
    println("Available cases:")
    for (name, path) in AVAILABLE_CASES
        println("  $name")
    end
    println()
    println("Or provide full path to OpenFOAM case directory")
end

function main()
    if length(ARGS) == 0
        print_usage()
        return 1
    end
    
    case_input = ARGS[1]
    
    # Determine case path
    case_path = ""
    case_name = ""
    
    if haskey(AVAILABLE_CASES, case_input)
        case_path = AVAILABLE_CASES[case_input]
        case_name = case_input
        println("📋 Selected case: $case_name")
        println("📁 Path: $case_path")
    elseif isdir(case_input)
        case_path = case_input
        case_name = basename(case_path)
        println("📋 Custom case: $case_name")
        println("📁 Path: $case_path")
    else
        println("❌ Error: Case '$case_input' not found")
        println()
        print_usage()
        return 1
    end
    
    # Verify OpenFOAM is available
    try
        run(`bash -c "source /opt/openfoam12/etc/bashrc && which blockMesh"`)
        println("✅ OpenFOAM environment detected")
    catch
        println("❌ Error: OpenFOAM not found. Please ensure:")
        println("   source /opt/openfoam12/etc/bashrc")
        return 1
    end
    
    # Create working directory for this comparison
    work_dir = "test_cases"
    mkpath(work_dir)
    
    # Copy case to working directory
    local_case_path = joinpath(work_dir, case_name)
    
    try
        println("📁 Copying case to working directory...")
        copy_openfoam_case(case_path, local_case_path)
        
        # Run comparison
        println("🚀 Starting solver comparison...")
        comparison_result = compare_solvers(local_case_path)
        
        # Generate reports
        println("📄 Generating detailed reports...")
        report_file = generate_comparison_report(comparison_result, "results")
        
        # Print final summary
        print_final_summary(comparison_result)
        
        println("\\n🎉 Comparison completed successfully!")
        println("📊 Main report: $report_file")
        
        return 0
        
    catch e
        println("❌ Comparison failed: $e")
        return 1
    finally
        # Cleanup working directory
        try
            if isdir(local_case_path)
                rm(local_case_path, recursive=true, force=true)
            end
        catch
            # Ignore cleanup errors
        end
    end
end

function print_final_summary(comparison::ComparisonResult)
    println("\\n" * "="^80)
    println("🏆 FINAL COMPARISON SUMMARY")
    println("="^80)
    
    of_result = comparison.openfoam_result
    jf_result = comparison.juliafoam_result
    
    # Overall success
    if of_result.success && jf_result.success
        println("✅ Both solvers completed successfully")
        
        # Performance
        speedup = of_result.solve_time / max(jf_result.solve_time, 1e-10)
        if speedup > 1.0
            println("🚀 JuliaFOAM is $(round(speedup, digits=1))x faster")
        else
            println("⚠️ OpenFOAM is $(round(1/speedup, digits=1))x faster")
        end
        
        # Accuracy
        if !isempty(comparison.accuracy_metrics)
            accuracy_summary = generate_accuracy_summary(comparison.accuracy_metrics, comparison.field_errors)
            overall_grade = accuracy_summary["overall_grade"]
            assessment = accuracy_summary["assessment"]
            
            println("🎯 Accuracy: Grade $overall_grade ($assessment)")
            
            avg_l2 = get(comparison.accuracy_metrics, "average_l2_error", 0.0)
            println("📊 Average L2 Error: $(@sprintf("%.2e", avg_l2))")
        else
            println("⚠️ Accuracy comparison not available")
        end
        
    else
        println("❌ One or both solvers failed")
        if !of_result.success
            println("   OpenFOAM: $(of_result.error_message)")
        end
        if !jf_result.success
            println("   JuliaFOAM: $(jf_result.error_message)")
        end
    end
    
    println("="^80)
end

# Run if executed directly
if abspath(PROGRAM_FILE) == @__FILE__
    exit_code = main()
    exit(exit_code)
end