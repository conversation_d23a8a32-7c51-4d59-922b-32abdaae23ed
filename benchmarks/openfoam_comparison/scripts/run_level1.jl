#!/usr/bin/env julia

"""
Run Level 1 Benchmarks

Execute basic laminar flow comparisons between OpenFOAM and JuliaFOAM.
These are the foundational test cases for solver validation.

Level 1 Cases:
- cavity: 2D lid-driven cavity (icoFoam)
- elbow: 90-degree elbow (icoFoam)
"""

include("../src/SolverComparison.jl")
include("../src/ReportGeneration.jl")

using Printf
using Dates

# Level 1 test cases (basic laminar flows)
const LEVEL1_CASES = [
    ("cavity", "/opt/openfoam12/tutorials/legacy/incompressible/icoFoam/cavity/cavity"),
    ("elbow", "/opt/openfoam12/tutorials/legacy/incompressible/icoFoam/elbow/elbow")
]

function main()
    println("🚀 OpenFOAM vs JuliaFOAM Level 1 Benchmark Suite")
    println("=" ^ 80)
    println("Focus: Basic laminar incompressible flows")
    println("Emphasis: Accuracy validation before performance optimization")
    println("=" ^ 80)
    println()
    
    # Verify OpenFOAM environment
    try
        run(`bash -c "source /opt/openfoam12/etc/bashrc && which icoFoam"`)
        println("✅ OpenFOAM environment verified")
    catch
        println("❌ Error: OpenFOAM not found")
        println("Please run: source /opt/openfoam12/etc/bashrc")
        return 1
    end
    
    # Setup directories
    work_dir = "test_cases"
    results_dir = "results"
    mkpath(work_dir)
    mkpath(results_dir)
    
    println("📁 Working directory: $work_dir")
    println("📊 Results directory: $results_dir")
    println()
    
    # Run all Level 1 cases
    comparison_results = ComparisonResult[]
    successful_cases = String[]
    failed_cases = String[]
    
    for (case_name, case_path) in LEVEL1_CASES
        println("\\n" * "="^60)
        println("🏃 Running case: $case_name")
        println("="^60)
        
        local_case_path = joinpath(work_dir, case_name)
        
        try
            # Copy case to working directory
            copy_openfoam_case(case_path, local_case_path)
            
            # Run comparison
            comparison_result = compare_solvers(local_case_path)
            push!(comparison_results, comparison_result)
            
            # Generate individual report
            generate_comparison_report(comparison_result, results_dir)
            
            if comparison_result.openfoam_result.success && comparison_result.juliafoam_result.success
                push!(successful_cases, case_name)
                println("✅ $case_name completed successfully")
            else
                push!(failed_cases, case_name)
                println("❌ $case_name had failures")
            end
            
        catch e
            println("❌ Error in $case_name: $e")
            push!(failed_cases, case_name)
        finally
            # Cleanup
            try
                if isdir(local_case_path)
                    rm(local_case_path, recursive=true, force=true)
                end
            catch
                # Ignore cleanup errors
            end
        end
    end
    
    # Generate batch report
    if !isempty(comparison_results)
        batch_report_file = joinpath(results_dir, "level1_batch_report_$(Dates.format(now(), "yyyy-mm-dd_HHMMSS")).md")
        generate_batch_report(comparison_results, batch_report_file)
        
        println("\\n📊 Batch report generated: $batch_report_file")
    end
    
    # Print final summary
    print_level1_summary(comparison_results, successful_cases, failed_cases)
    
    return isempty(failed_cases) ? 0 : 1
end

function print_level1_summary(comparison_results::Vector, successful_cases::Vector{String}, failed_cases::Vector{String})
    println("\\n" * "="^80)
    println("🏆 LEVEL 1 BENCHMARK SUMMARY")
    println("="^80)
    
    total_cases = length(comparison_results)
    success_count = length(successful_cases)
    fail_count = length(failed_cases)
    
    println("📊 Overall Results:")
    println("   Total cases: $total_cases")
    println("   Successful: $success_count")
    println("   Failed: $fail_count")
    println("   Success rate: $(round(100 * success_count / max(total_cases, 1), digits=1))%")
    println()
    
    if !isempty(successful_cases)
        println("✅ Successful cases:")
        for case_name in successful_cases
            println("   - $case_name")
        end
        println()
        
        # Performance analysis
        successful_comparisons = filter(c -> c.openfoam_result.success && c.juliafoam_result.success, comparison_results)
        
        if !isempty(successful_comparisons)
            println("🚀 Performance Analysis:")
            
            speedups = Float64[]
            accuracy_grades = String[]
            
            for comparison in successful_comparisons
                speedup = comparison.openfoam_result.solve_time / max(comparison.juliafoam_result.solve_time, 1e-10)
                push!(speedups, speedup)
                
                if !isempty(comparison.accuracy_metrics)
                    accuracy_summary = generate_accuracy_summary(comparison.accuracy_metrics, comparison.field_errors)
                    push!(accuracy_grades, accuracy_summary["overall_grade"])
                end
            end
            
            println("   Average speedup: $(round(mean(speedups), digits=2))x")
            println("   Best speedup: $(round(maximum(speedups), digits=2))x")
            println("   Worst speedup: $(round(minimum(speedups), digits=2))x")
            println()
            
            if !isempty(accuracy_grades)
                println("🎯 Accuracy Analysis:")
                grade_counts = Dict{String, Int}()
                for grade in accuracy_grades
                    grade_counts[grade] = get(grade_counts, grade, 0) + 1
                end
                
                for (grade, count) in sort(collect(grade_counts), by=x->x[1])
                    println("   Grade $grade: $count cases")
                end
                println()
            end
        end
    end
    
    if !isempty(failed_cases)
        println("❌ Failed cases:")
        for case_name in failed_cases
            println("   - $case_name")
        end
        println()
    end
    
    # Recommendations
    println("📋 Recommendations:")
    if success_count == total_cases
        println("   ✅ Excellent! All Level 1 cases passed")
        println("   🚀 Ready to proceed to Level 2 (turbulent flows)")
    elseif success_count >= total_cases / 2
        println("   🟡 Partial success - investigate failed cases")
        println("   🔧 Fix issues before proceeding to Level 2")
    else
        println("   ❌ Major issues detected")
        println("   🛠️ Focus on fundamental solver implementation")
        println("   📚 Review basic Navier-Stokes discretization")
    end
    
    println("\\n📄 Detailed reports available in results/ directory")
    println("="^80)
end

# Run if executed directly
if abspath(PROGRAM_FILE) == @__FILE__
    exit_code = main()
    exit(exit_code)
end