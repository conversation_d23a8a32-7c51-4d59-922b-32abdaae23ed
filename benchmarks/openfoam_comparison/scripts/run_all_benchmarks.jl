#!/usr/bin/env julia

"""
Run Complete OpenFOAM vs JuliaFOAM Benchmark Suite

Execute the full progression of test cases from basic to complex.
Honest, comprehensive comparison with detailed reporting.

Benchmark Levels:
1. Basic Laminar (cavity, elbow)
2. Steady Turbulent (pitzDaily, airFoil2D) 
3. Unsteady Flows (mixerVessel2D)
4. Complex Physics (T3A, channel395)
"""

include("../src/SolverComparison.jl")
include("../src/ReportGeneration.jl")

using Printf
using Dates

# Complete test case hierarchy
const BENCHMARK_LEVELS = [
    (
        1, 
        "Basic Laminar Flows",
        [
            ("cavity", "/opt/openfoam12/tutorials/legacy/incompressible/icoFoam/cavity"),
            ("elbow", "/opt/openfoam12/tutorials/legacy/incompressible/icoFoam/elbow")
        ]
    ),
    (
        2,
        "Steady Turbulent Flows", 
        [
            ("pitzDaily", "/opt/openfoam12/tutorials/incompressibleFluid/pitzDaily"),
            # ("airFoil2D", "/opt/openfoam12/tutorials/incompressibleFluid/airFoil2D")  # Commented out - more complex setup
        ]
    ),
    (
        3,
        "Unsteady Flows",
        [
            # ("mixerVessel2D", "/opt/openfoam12/tutorials/incompressibleFluid/mixerVessel2D")  # Future work
        ]
    ),
    (
        4,
        "Complex Physics",
        [
            # ("T3A", "/opt/openfoam12/tutorials/incompressibleFluid/T3A"),  # Future work
            # ("channel395", "/opt/openfoam12/tutorials/incompressibleFluid/channel395")  # Future work
        ]
    )
]

function main()
    println("🚀 OpenFOAM vs JuliaFOAM Complete Benchmark Suite")
    println("=" ^ 100)
    println("Philosophy: Honest comparison with accuracy as priority")
    println("Approach: Incremental complexity validation")
    println("=" ^ 100)
    println()
    
    # Environment verification
    if !verify_environment()
        return 1
    end
    
    # Setup directories
    setup_directories()
    
    # Run benchmark levels
    all_results = ComparisonResult[]
    level_summaries = Dict{Int, Dict{String, Any}}()
    
    for (level, description, cases) in BENCHMARK_LEVELS
        if isempty(cases)
            println("⏭️  Level $level ($description): No cases defined yet")
            continue
        end
        
        println("\\n" * "="^80)
        println("🎯 LEVEL $level: $description")
        println("="^80)
        
        level_results, level_summary = run_benchmark_level(level, cases)
        append!(all_results, level_results)
        level_summaries[level] = level_summary
        
        # Check if we should continue to next level
        if level_summary["success_rate"] < 0.5
            println("⚠️  Level $level success rate too low ($(round(level_summary["success_rate"] * 100, digits=1))%)")
            println("🛑 Stopping benchmark progression")
            break
        end
    end
    
    # Generate comprehensive reports
    generate_comprehensive_reports(all_results, level_summaries)
    
    # Print final analysis
    print_final_analysis(all_results, level_summaries)
    
    return 0
end

function verify_environment()
    println("🔍 Verifying environment...")
    
    # Check OpenFOAM
    try
        run(`bash -c "source /opt/openfoam12/etc/bashrc && which blockMesh"`)
        run(`bash -c "source /opt/openfoam12/etc/bashrc && which icoFoam"`)
        println("   ✅ OpenFOAM found and functional")
    catch
        println("   ❌ OpenFOAM not found or not functional")
        println("   Please run: source /opt/openfoam12/etc/bashrc")
        return false
    end
    
    # Check Julia packages
    try
        # Test basic functionality without full JuliaFOAM import
        using LinearAlgebra, SparseArrays, Printf, Dates
        println("   ✅ Julia packages available")
    catch e
        println("   ❌ Required Julia packages missing: $e")
        return false
    end
    
    # Check tutorial paths
    missing_paths = String[]
    for (level, description, cases) in BENCHMARK_LEVELS
        for (case_name, case_path) in cases
            if !isdir(case_path)
                push!(missing_paths, case_path)
            end
        end
    end
    
    if !isempty(missing_paths)
        println("   ⚠️  Some tutorial paths not found:")
        for path in missing_paths
            println("      $path")
        end
        println("   Continuing with available cases...")
    end
    
    println("   ✅ Environment verification complete")
    return true
end

function setup_directories()
    println("📁 Setting up directories...")
    
    dirs = ["test_cases", "results", "results/accuracy_reports", "results/performance_data", "results/field_comparisons"]
    
    for dir in dirs
        mkpath(dir)
    end
    
    println("   ✅ Directories ready")
end

function run_benchmark_level(level::Int, cases::Vector{Tuple{String, String}})
    println("📋 Level $level cases: $(join([case[1] for case in cases], ", "))")
    println()
    
    results = ComparisonResult[]
    successful_cases = String[]
    failed_cases = String[]
    timing_data = Float64[]
    
    for (case_name, case_path) in cases
        if !isdir(case_path)
            println("⏭️  Skipping $case_name (path not found)")
            push!(failed_cases, case_name)
            continue
        end
        
        println("🏃 Running case: $case_name")
        println("   Path: $case_path")
        
        local_case_path = joinpath("test_cases", case_name)
        
        try
            start_time = time()
            
            # Copy and run case
            copy_openfoam_case(case_path, local_case_path)
            comparison_result = compare_solvers(local_case_path)
            push!(results, comparison_result)
            
            # Generate individual report
            generate_comparison_report(comparison_result, "results")
            
            elapsed_time = time() - start_time
            push!(timing_data, elapsed_time)
            
            # Assess success
            if comparison_result.openfoam_result.success && comparison_result.juliafoam_result.success
                push!(successful_cases, case_name)
                println("   ✅ Success ($(round(elapsed_time, digits=1))s)")
                
                # Quick accuracy check
                if !isempty(comparison_result.accuracy_metrics)
                    accuracy_summary = generate_accuracy_summary(comparison_result.accuracy_metrics, comparison_result.field_errors)
                    grade = accuracy_summary["overall_grade"]
                    println("   🎯 Accuracy: Grade $grade")
                end
            else
                push!(failed_cases, case_name)
                println("   ❌ Failed")
                
                if !comparison_result.openfoam_result.success
                    println("      OF Error: $(comparison_result.openfoam_result.error_message)")
                end
                if !comparison_result.juliafoam_result.success
                    println("      JF Error: $(comparison_result.juliafoam_result.error_message)")
                end
            end
            
        catch e
            println("   ❌ Exception: $e")
            push!(failed_cases, case_name)
        finally
            # Cleanup
            try
                if isdir(local_case_path)
                    rm(local_case_path, recursive=true, force=true)
                end
            catch
            end
        end
        
        println()
    end
    
    # Level summary
    total_cases = length(cases)
    success_count = length(successful_cases)
    success_rate = success_count / max(total_cases, 1)
    
    level_summary = Dict{String, Any}(
        "total_cases" => total_cases,
        "successful_cases" => successful_cases,
        "failed_cases" => failed_cases,
        "success_count" => success_count,
        "success_rate" => success_rate,
        "timing_data" => timing_data
    )
    
    print_level_summary(level, level_summary, results)
    
    return results, level_summary
end

function print_level_summary(level::Int, summary::Dict{String, Any}, results::Vector)
    println("📊 Level $level Summary:")
    println("   Cases: $(summary["success_count"])/$(summary["total_cases"]) successful ($(round(summary["success_rate"] * 100, digits=1))%)")
    
    if !isempty(summary["successful_cases"])
        println("   ✅ Passed: $(join(summary["successful_cases"], ", "))")
    end
    
    if !isempty(summary["failed_cases"])
        println("   ❌ Failed: $(join(summary["failed_cases"], ", "))")
    end
    
    # Performance summary for successful cases
    successful_results = filter(r -> r.openfoam_result.success && r.juliafoam_result.success, results)
    
    if !isempty(successful_results)
        speedups = [r.openfoam_result.solve_time / max(r.juliafoam_result.solve_time, 1e-10) for r in successful_results]
        println("   ⚡ Speedup: avg $(round(mean(speedups), digits=1))x, best $(round(maximum(speedups), digits=1))x")
        
        # Accuracy summary
        grades = String[]
        for result in successful_results
            if !isempty(result.accuracy_metrics)
                accuracy_summary = generate_accuracy_summary(result.accuracy_metrics, result.field_errors)
                push!(grades, accuracy_summary["overall_grade"])
            end
        end
        
        if !isempty(grades)
            grade_counts = Dict{String, Int}()
            for grade in grades
                grade_counts[grade] = get(grade_counts, grade, 0) + 1
            end
            
            grade_summary = join(["$grade:$count" for (grade, count) in sort(collect(grade_counts))], " ")
            println("   🎯 Accuracy: $grade_summary")
        end
    end
    
    # Recommendation for next level
    if summary["success_rate"] >= 0.8
        println("   ✅ Excellent - ready for next level")
    elseif summary["success_rate"] >= 0.5
        println("   🟡 Good - investigate failures before proceeding")
    else
        println("   ❌ Poor - fix major issues before advancing")
    end
end

function generate_comprehensive_reports(all_results::Vector, level_summaries::Dict{Int, Dict{String, Any}})
    println("\\n📄 Generating comprehensive reports...")
    
    timestamp = Dates.format(now(), "yyyy-mm-dd_HHMMSS")
    
    # Master batch report
    master_report = joinpath("results", "master_benchmark_report_$timestamp.md")
    generate_master_report(all_results, level_summaries, master_report)
    
    # Level-specific batch reports
    for (level, summary) in level_summaries
        if summary["success_count"] > 0
            level_results = filter(r -> any(s -> r.case_name in s["successful_cases"], [summary]), all_results)
            level_report = joinpath("results", "level_$(level)_batch_report_$timestamp.md")
            generate_batch_report(level_results, level_report)
        end
    end
    
    println("   ✅ Reports generated in results/ directory")
end

function generate_master_report(all_results::Vector, level_summaries::Dict{Int, Dict{String, Any}}, filename::String)
    open(filename, "w") do io
        write(io, """
# OpenFOAM vs JuliaFOAM Master Benchmark Report

**Generated:** $(now())  
**Total Cases:** $(length(all_results))  
**Benchmark Philosophy:** Honest comparison, accuracy first

---

## Executive Summary

""")
        
        # Overall statistics
        total_cases = length(all_results)
        successful_results = filter(r -> r.openfoam_result.success && r.juliafoam_result.success, all_results)
        success_count = length(successful_results)
        
        write(io, """
- **Cases Executed:** $total_cases
- **Successful Comparisons:** $success_count
- **Overall Success Rate:** $(round(100 * success_count / max(total_cases, 1), digits=1))%

""")
        
        if !isempty(successful_results)
            speedups = [r.openfoam_result.solve_time / max(r.juliafoam_result.solve_time, 1e-10) for r in successful_results]
            write(io, """
### Performance Overview
- **Average Speedup:** $(round(mean(speedups), digits=2))x
- **Best Case:** $(round(maximum(speedups), digits=2))x speedup
- **Worst Case:** $(round(minimum(speedups), digits=2))x speedup

""")
        end
        
        # Level-by-level summary
        write(io, """
## Level-by-Level Results

""")
        
        for level in sort(collect(keys(level_summaries)))
            summary = level_summaries[level]
            level_name = level == 1 ? "Basic Laminar" : 
                        level == 2 ? "Steady Turbulent" :
                        level == 3 ? "Unsteady Flows" : "Complex Physics"
            
            write(io, """
### Level $level: $level_name

- **Success Rate:** $(round(summary["success_rate"] * 100, digits=1))%
- **Passed:** $(join(summary["successful_cases"], ", "))
""")
            
            if !isempty(summary["failed_cases"])
                write(io, "- **Failed:** $(join(summary["failed_cases"], ", "))\\n")
            end
            
            write(io, "\\n")
        end
        
        # Detailed case results table
        write(io, """
## Detailed Results Table

| Case | Level | OF Success | JF Success | Speedup | Accuracy Grade |
|------|-------|------------|------------|---------|----------------|
""")
        
        for result in all_results
            level = result.case_name == "cavity" || result.case_name == "elbow" ? 1 : 2
            of_success = result.openfoam_result.success ? "✅" : "❌"
            jf_success = result.juliafoam_result.success ? "✅" : "❌"
            
            speedup = "N/A"
            if result.openfoam_result.success && result.juliafoam_result.success
                speedup_val = result.openfoam_result.solve_time / max(result.juliafoam_result.solve_time, 1e-10)
                speedup = @sprintf("%.2fx", speedup_val)
            end
            
            accuracy_grade = "N/A"
            if !isempty(result.accuracy_metrics)
                accuracy_summary = generate_accuracy_summary(result.accuracy_metrics, result.field_errors)
                accuracy_grade = accuracy_summary["overall_grade"]
            end
            
            write(io, "| $(result.case_name) | $level | $of_success | $jf_success | $speedup | $accuracy_grade |\\n")
        end
        
        write(io, """

---

## Key Findings

### Strengths
""")
        
        if success_count >= total_cases * 0.8
            write(io, "- ✅ High success rate demonstrates robust implementation\\n")
        end
        
        if !isempty(successful_results)
            avg_speedup = mean([r.openfoam_result.solve_time / max(r.juliafoam_result.solve_time, 1e-10) for r in successful_results])
            if avg_speedup > 1.0
                write(io, "- 🚀 JuliaFOAM shows performance advantages ($(round(avg_speedup, digits=1))x average speedup)\\n")
            end
        end
        
        write(io, """

### Areas for Improvement
""")
        
        if success_count < total_cases
            write(io, "- 🔧 $(total_cases - success_count) cases require investigation\\n")
        end
        
        write(io, """
- 📊 Expand test case coverage for more complex physics
- 🎯 Enhance accuracy validation methods
- ⚡ Continue performance optimization efforts

### Recommendations

1. **Immediate Actions:**
   - Fix failed test cases
   - Implement missing solver features
   - Improve error handling and diagnostics

2. **Medium Term:**
   - Add turbulent flow capabilities
   - Implement unsteady solvers
   - Expand boundary condition support

3. **Long Term:**
   - Add complex physics models
   - Implement parallel solvers
   - Develop mesh adaptation capabilities

---

*Generated by JuliaFOAM Benchmark Suite v1.0*
""")
    end
end

function print_final_analysis(all_results::Vector, level_summaries::Dict{Int, Dict{String, Any}})
    println("\\n" * "="^100)
    println("🎯 COMPREHENSIVE BENCHMARK ANALYSIS")
    println("="^100)
    
    total_cases = length(all_results)
    successful_results = filter(r -> r.openfoam_result.success && r.juliafoam_result.success, all_results)
    success_count = length(successful_results)
    
    println("📊 Overall Statistics:")
    println("   Total cases attempted: $total_cases")
    println("   Successful comparisons: $success_count")
    println("   Success rate: $(round(100 * success_count / max(total_cases, 1), digits=1))%")
    println()
    
    if !isempty(successful_results)
        speedups = [r.openfoam_result.solve_time / max(r.juliafoam_result.solve_time, 1e-10) for r in successful_results]
        
        println("🚀 Performance Analysis:")
        println("   Average speedup: $(round(mean(speedups), digits=2))x")
        println("   Best speedup: $(round(maximum(speedups), digits=2))x")
        println("   Cases where JuliaFOAM faster: $(count(s -> s > 1.0, speedups))/$success_count")
        println()
        
        # Accuracy analysis
        accuracy_grades = String[]
        for result in successful_results
            if !isempty(result.accuracy_metrics)
                accuracy_summary = generate_accuracy_summary(result.accuracy_metrics, result.field_errors)
                push!(accuracy_grades, accuracy_summary["overall_grade"])
            end
        end
        
        if !isempty(accuracy_grades)
            println("🎯 Accuracy Analysis:")
            grade_counts = Dict{String, Int}()
            for grade in accuracy_grades
                grade_counts[grade] = get(grade_counts, grade, 0) + 1
            end
            
            for grade in ["A+", "A", "B", "C", "F"]
                count = get(grade_counts, grade, 0)
                if count > 0
                    println("   Grade $grade: $count cases")
                end
            end
            println()
        end
    end
    
    # Level progression analysis
    println("📈 Level Progression:")
    for level in sort(collect(keys(level_summaries)))
        summary = level_summaries[level]
        rate = round(summary["success_rate"] * 100, digits=1)
        status = rate >= 80 ? "✅" : rate >= 50 ? "🟡" : "❌"
        println("   Level $level: $rate% success $status")
    end
    println()
    
    # Final recommendation
    println("🎯 Final Assessment:")
    if success_count >= total_cases * 0.8
        println("   ✅ EXCELLENT: JuliaFOAM shows strong potential")
        println("   🚀 Ready for advanced development and optimization")
    elseif success_count >= total_cases * 0.5
        println("   🟡 GOOD: Solid foundation with room for improvement")
        println("   🔧 Focus on fixing remaining issues")
    else
        println("   ❌ NEEDS WORK: Fundamental issues require attention")
        println("   🛠️ Recommend revisiting core implementation")
    end
    
    println("\\n📄 Detailed reports available in results/ directory")
    println("="^100)
end

# Run if executed directly
if abspath(PROGRAM_FILE) == @__FILE__
    exit_code = main()
    exit(exit_code)
end