#!/usr/bin/env julia

"""
Run Level 2 Benchmarks - Turbulent Flows

Execute turbulent flow comparisons between OpenFOAM and JuliaFOAM.
These cases test RANS turbulence model capabilities.

Level 2 Cases:
- pitzDaily: Backward-facing step with k-epsilon turbulence
- airFoil2D: NACA airfoil with boundary layer modeling (future)
"""

include("../src/SolverComparison.jl")
include("../src/ReportGeneration.jl")

using Printf
using Dates
using Statistics

# Level 2 test cases (turbulent flows)
const LEVEL2_CASES = [
    ("pitzDaily", "/opt/openfoam12/tutorials/incompressibleFluid/pitzDaily"),
    # ("airFoil2D", "/opt/openfoam12/tutorials/incompressibleFluid/airFoil2D")  # Future work
]

function main()
    println("🌪️  OpenFOAM vs JuliaFOAM Level 2 Benchmark Suite")
    println("=" ^ 80)
    println("Focus: Turbulent flows with RANS modeling")
    println("Emphasis: Validation of turbulence model implementation")
    println("=" ^ 80)
    println()
    
    # Verify OpenFOAM environment
    try
        run(`bash -c "source /opt/openfoam12/etc/bashrc && which foamRun"`)
        println("✅ OpenFOAM environment verified")
    catch
        println("❌ Error: OpenFOAM not found")
        println("Please run: source /opt/openfoam12/etc/bashrc")
        return 1
    end
    
    # Setup directories
    work_dir = "test_cases"
    results_dir = "results"
    mkpath(work_dir)
    mkpath(results_dir)
    
    println("📁 Working directory: $work_dir")
    println("📊 Results directory: $results_dir")
    println()
    
    # Run all Level 2 cases
    comparison_results = ComparisonResult[]
    successful_cases = String[]
    failed_cases = String[]
    
    for (case_name, case_path) in LEVEL2_CASES
        println("\\n" * "="^60)
        println("🌪️  Running turbulent case: $case_name")
        println("="^60)
        
        local_case_path = joinpath(work_dir, case_name)
        
        try
            # Copy case to working directory
            copy_openfoam_case(case_path, local_case_path)
            
            # Run comparison
            comparison_result = compare_solvers(local_case_path)
            push!(comparison_results, comparison_result)
            
            # Generate individual report
            generate_comparison_report(comparison_result, results_dir)
            
            if comparison_result.openfoam_result.success
                if comparison_result.juliafoam_result.success
                    push!(successful_cases, case_name)
                    println("✅ $case_name: Both solvers completed")
                else
                    println("🟡 $case_name: OpenFOAM success, JuliaFOAM failed (expected for now)")
                    println("   JF Error: $(comparison_result.juliafoam_result.error_message)")
                end
            else
                push!(failed_cases, case_name)
                println("❌ $case_name: OpenFOAM failed")
                println("   OF Error: $(comparison_result.openfoam_result.error_message)")
            end
            
        catch e
            println("❌ Error in $case_name: $e")
            push!(failed_cases, case_name)
        finally
            # Cleanup
            try
                if isdir(local_case_path)
                    rm(local_case_path, recursive=true, force=true)
                end
            catch
                # Ignore cleanup errors
            end
        end
    end
    
    # Generate batch report
    if !isempty(comparison_results)
        batch_report_file = joinpath(results_dir, "level2_batch_report_$(Dates.format(now(), "yyyy-mm-dd_HHMMSS")).md")
        generate_batch_report(comparison_results, batch_report_file)
        
        println("\\n📊 Batch report generated: $batch_report_file")
    end
    
    # Print final summary
    print_level2_summary(comparison_results, successful_cases, failed_cases)
    
    return isempty(failed_cases) ? 0 : 1
end

function print_level2_summary(comparison_results::Vector, successful_cases::Vector{String}, failed_cases::Vector{String})
    println("\\n" * "="^80)
    println("🏆 LEVEL 2 TURBULENT BENCHMARK SUMMARY")
    println("="^80)
    
    total_cases = length(comparison_results)
    success_count = length(successful_cases)
    fail_count = length(failed_cases)
    
    println("📊 Overall Results:")
    println("   Total cases: $total_cases")
    println("   OpenFOAM successful: $(count(r -> r.openfoam_result.success, comparison_results))")
    println("   JuliaFOAM successful: $(count(r -> r.juliafoam_result.success, comparison_results))")
    println("   Both successful: $success_count")
    println("   Failed: $fail_count")
    println()
    
    # OpenFOAM analysis
    openfoam_successful = filter(r -> r.openfoam_result.success, comparison_results)
    if !isempty(openfoam_successful)
        println("🔧 OpenFOAM Analysis:")
        avg_time = mean([r.openfoam_result.solve_time for r in openfoam_successful])
        avg_iterations = mean([r.openfoam_result.iterations for r in openfoam_successful])
        println("   Average solve time: $(round(avg_time, digits=2))s")
        println("   Average iterations: $(round(avg_iterations, digits=0))")
        
        # Check turbulence model detection
        for result in openfoam_successful
            case_name = result.case_name
            if case_name == "pitzDaily"
                println("   pitzDaily turbulence: k-epsilon RANS detected")
            end
        end
        println()
    end
    
    # JuliaFOAM analysis  
    println("🚀 JuliaFOAM Analysis:")
    if success_count > 0
        juliafoam_successful = filter(r -> r.juliafoam_result.success, comparison_results)
        avg_time = mean([r.juliafoam_result.solve_time for r in juliafoam_successful])
        println("   Successfully completed: $success_count cases")
        println("   Average solve time: $(round(avg_time, digits=3))s")
        
        # Calculate speedups
        both_successful = filter(r -> r.openfoam_result.success && r.juliafoam_result.success, comparison_results)
        if !isempty(both_successful)
            speedups = [r.openfoam_result.solve_time / max(r.juliafoam_result.solve_time, 1e-10) for r in both_successful]
            println("   Speedup range: $(round(minimum(speedups), digits=1))x - $(round(maximum(speedups), digits=1))x")
        end
    else
        println("   No cases completed successfully (expected for initial turbulent implementation)")
        
        # Analyze failure modes
        julia_failures = filter(r -> !r.juliafoam_result.success, comparison_results)
        if !isempty(julia_failures)
            println("   Common failure reasons:")
            for result in julia_failures
                error_msg = result.juliafoam_result.error_message
                if contains(error_msg, "Turbulent")
                    println("     - Turbulent solver not implemented: $(result.case_name)")
                elseif contains(error_msg, "not yet implemented")
                    println("     - Feature not implemented: $(result.case_name)")
                else
                    println("     - Other error: $(result.case_name)")
                end
            end
        end
    end
    println()
    
    # Next steps recommendations
    println("📋 Recommendations:")
    if success_count == total_cases
        println("   ✅ Excellent! All Level 2 cases passed")
        println("   🚀 Ready to proceed to Level 3 (complex flows)")
    elseif success_count > 0
        println("   🟡 Partial success - continue turbulence model development")
        println("   🔧 Fix remaining turbulent solver issues")
    else
        openfoam_success_count = count(r -> r.openfoam_result.success, comparison_results)
        if openfoam_success_count == total_cases
            println("   🟡 OpenFOAM cases work - implement JuliaFOAM turbulent solver")
            println("   📚 Follow enhancement plan for k-epsilon model")
            println("   🎯 Start with modular turbulence architecture")
        else
            println("   ❌ OpenFOAM issues detected")
            println("   🛠️ Check case paths and OpenFOAM installation")
        end
    end
    
    # Specific next actions
    println("\\n🎯 Next Actions:")
    if count(r -> r.openfoam_result.success, comparison_results) > 0
        println("   1. ✅ OpenFOAM integration working")
        if success_count == 0
            println("   2. 🚧 Implement k-epsilon turbulence model in JuliaFOAM")
            println("   3. 🎯 Start with basic RANS equations")
            println("   4. 📊 Validate against pitzDaily experimental data")
        end
    else
        println("   1. 🔧 Fix OpenFOAM case import issues")
        println("   2. ✅ Verify foamRun solver compatibility")
    end
    
    println("\\n📄 Detailed reports available in results/ directory")
    println("="^80)
end

# Run if executed directly
if abspath(PROGRAM_FILE) == @__FILE__
    exit_code = main()
    exit(exit_code)
end