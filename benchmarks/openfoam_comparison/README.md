# OpenFOAM vs JuliaFOAM Comprehensive Benchmark Suite

## Overview

This benchmark suite provides honest, side-by-side comparisons between OpenFOAM and JuliaFOAM solvers with a focus on **accuracy first, performance second**. 

## Philosophy

1. **Accuracy is Priority**: Field solutions must match within numerical tolerance
2. **Honest Results**: Report failures and limitations transparently
3. **Incremental Complexity**: Start simple, gradually increase case complexity
4. **Direct Import**: Use actual OpenFOAM cases, not approximations
5. **Solver Focus**: Compare solvers, not mesh generation

## Test Case Progression

### Level 1: Basic Laminar Flows
- `cavity` - 2D lid-driven cavity (icoFoam)
- `elbow` - 90-degree elbow (icoFoam)

### Level 2: Steady Turbulent Flows  
- `pitzDaily` - Backward-facing step (simpleFoam)
- `airFoil2D` - NACA airfoil (simpleFoam)

### Level 3: Unsteady Flows
- `mixerVessel2D` - Mixing vessel (pimpleFoam)
- `cylinder` - Flow around cylinder (pimpleFoam)

### Level 4: Complex Physics
- `T3A` - Transition test case
- `channel395` - Turbulent channel flow

## Benchmark Structure

```
benchmarks/openfoam_comparison/
├── README.md                          # This file
├── src/
│   ├── OpenFOAMCaseImporter.jl       # Import OpenFOAM cases
│   ├── SolverComparison.jl           # Run comparative tests
│   ├── AccuracyAnalysis.jl           # Field comparison tools
│   └── ReportGeneration.jl           # Generate comparison reports
├── test_cases/                       # Imported OpenFOAM cases
│   ├── cavity/
│   ├── elbow/
│   └── ...
├── results/                          # Benchmark results
│   ├── accuracy_reports/
│   ├── performance_data/
│   └── field_comparisons/
└── scripts/
    ├── run_all_benchmarks.jl         # Run complete suite
    ├── run_level1.jl                 # Run basic cases
    └── compare_single_case.jl        # Compare one case
```

## Usage

### Quick Start
```bash
cd benchmarks/openfoam_comparison
julia scripts/run_level1.jl
```

### Single Case Comparison
```bash
julia scripts/compare_single_case.jl cavity
```

### Complete Benchmark Suite
```bash
julia scripts/run_all_benchmarks.jl
```

## Results Interpretation

### Accuracy Metrics
- **L2 Error**: ||φ_JF - φ_OF||₂ / ||φ_OF||₂
- **Max Error**: max|φ_JF - φ_OF| / max|φ_OF|
- **Field Correlation**: Pearson correlation coefficient

### Performance Metrics
- **Solve Time**: Wall clock time for solver execution
- **Memory Usage**: Peak memory consumption
- **Iterations**: Number of solver iterations to convergence

### Quality Rankings
- ✅ **Excellent**: Error < 1e-6, correlation > 0.999
- 🟡 **Good**: Error < 1e-4, correlation > 0.99
- 🟠 **Acceptable**: Error < 1e-2, correlation > 0.95
- ❌ **Poor**: Error > 1e-2 or correlation < 0.95

## OpenFOAM Setup

Ensure OpenFOAM is properly sourced:
```bash
source /opt/openfoam12/etc/bashrc
```

## Dependencies

- Julia 1.9+
- OpenFOAM 12+
- JuliaFOAM (local development version)

## Contributing

When adding new test cases:
1. Add to appropriate complexity level
2. Include expected accuracy targets
3. Document any known limitations
4. Update this README

## Known Limitations

- Mesh generation differences may affect comparison
- Boundary condition mapping needs refinement  
- Some advanced OpenFOAM features not yet supported
- Parallel performance comparison not included

---

*Last updated: $(now())*