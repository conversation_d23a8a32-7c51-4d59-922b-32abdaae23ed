#!/usr/bin/env julia

"""
Run Real OpenFOAM Cases
=======================

This script runs actual OpenFOAM tutorial cases and saves the results
for comparison with JuliaFOAM implementations.

Cases to test:
1. icoFoam cavity - Laminar flow
2. simpleFoam pitzDaily - Turbulent flow with backward-facing step
3. Various mesh sizes for scalability testing
"""

using Printf
using Dates
using DelimitedFiles

println("🚀 Running Real OpenFOAM Cases")
println("=" ^ 60)
println("📅 Date: $(Dates.now())")
println("🎯 OpenFOAM Version: 12")
println()

# Test case configurations
const TEST_CASES = [
    (
        name = "cavity_laminar",
        tutorial_path = "/opt/openfoam12/tutorials/legacy/incompressible/icoFoam/cavity/cavity",
        solver = "icoFoam",
        mesh_sizes = [(20,20), (40,40), (80,80)],
        description = "Lid-driven cavity - laminar flow"
    ),
    (
        name = "pitzDaily_turbulent", 
        tutorial_path = "/opt/openfoam12/tutorials/incompressibleFluid/pitzDaily",
        solver = "foamRun",
        mesh_sizes = [(1,1)], # Use default mesh
        description = "Backward-facing step - turbulent flow"
    )
]

# Results storage
mutable struct OpenFOAMResult
    case_name::String
    mesh_size::String
    setup_time::Float64
    mesh_time::Float64
    solve_time::Float64
    total_time::Float64
    iterations::Int
    converged::Bool
    final_residuals::Dict{String, Float64}
    mesh_cells::Int
    success::Bool
    error_msg::String
    timestamp::DateTime
end

function setup_test_directory()
    """Create clean test directory"""
    test_dir = "openfoam_test_runs"
    if isdir(test_dir)
        println("🗑️ Cleaning previous test directory...")
        run(`rm -rf $test_dir`)
    end
    mkpath(test_dir)
    return test_dir
end

function modify_cavity_mesh(case_dir::String, nx::Int, ny::Int)
    """Modify cavity mesh resolution"""
    blockMesh_file = joinpath(case_dir, "system", "blockMeshDict")
    
    if isfile(blockMesh_file)
        content = read(blockMesh_file, String)
        # Replace mesh resolution
        new_content = replace(content, 
            r"hex \(0 1 2 3 4 5 6 7\) \(\d+ \d+ \d+\)" => 
            "hex (0 1 2 3 4 5 6 7) ($nx $ny 1)")
        write(blockMesh_file, new_content)
        println("   📏 Modified mesh to $(nx)×$(ny)")
        return true
    end
    return false
end

function extract_residuals(log_content::String)
    """Extract final residuals from OpenFOAM log"""
    residuals = Dict{String, Float64}()
    
    lines = split(log_content, '\n')
    
    # Track the latest residuals
    for line in reverse(lines)
        # icoFoam style
        if occursin("Solving for Ux", line) && !haskey(residuals, "Ux")
            m = match(r"Initial residual = ([\d.e+-]+)", line)
            if m !== nothing
                residuals["Ux"] = parse(Float64, m.captures[1])
            end
        elseif occursin("Solving for Uy", line) && !haskey(residuals, "Uy")
            m = match(r"Initial residual = ([\d.e+-]+)", line)
            if m !== nothing
                residuals["Uy"] = parse(Float64, m.captures[1])
            end
        elseif occursin("Solving for p", line) && !haskey(residuals, "p")
            m = match(r"Initial residual = ([\d.e+-]+)", line)
            if m !== nothing
                residuals["p"] = parse(Float64, m.captures[1])
            end
        end
        
        # simpleFoam/foamRun style
        if occursin("Solving for k", line) && !haskey(residuals, "k")
            m = match(r"Initial residual = ([\d.e+-]+)", line)
            if m !== nothing
                residuals["k"] = parse(Float64, m.captures[1])
            end
        elseif occursin("Solving for epsilon", line) && !haskey(residuals, "epsilon")
            m = match(r"Initial residual = ([\d.e+-]+)", line)
            if m !== nothing
                residuals["epsilon"] = parse(Float64, m.captures[1])
            end
        end
        
        # Stop if we have enough residuals
        if length(residuals) >= 3
            break
        end
    end
    
    return residuals
end

function count_iterations(log_content::String)
    """Count number of time steps/iterations"""
    count = 0
    for line in split(log_content, '\n')
        if occursin(r"^Time = \d", line)
            count += 1
        end
    end
    return count
end

function get_mesh_cells(case_dir::String)
    """Extract mesh cell count from checkMesh or log"""
    try
        checkMesh_output = readchomp(`bash -c "cd $case_dir && source /opt/openfoam12/etc/bashrc && checkMesh 2>&1"`)
        
        # Extract cell count
        m = match(r"cells:\s+(\d+)", checkMesh_output)
        if m !== nothing
            return parse(Int, m.captures[1])
        end
    catch
        # Fallback to estimate
    end
    
    return 0  # Unknown
end

function run_openfoam_case(case_config, mesh_size::Tuple{Int,Int}, test_dir::String)
    """Run a single OpenFOAM case"""
    
    nx, ny = mesh_size
    mesh_str = (nx == 1) ? "default" : "$(nx)x$(ny)"
    
    println("\n🔧 Running OpenFOAM: $(case_config.name) ($mesh_str mesh)")
    
    # Setup case directory
    case_name = "$(case_config.name)_$(mesh_str)"
    case_dir = joinpath(test_dir, case_name)
    
    result = OpenFOAMResult(
        case_config.name, mesh_str,
        0.0, 0.0, 0.0, 0.0, 0, false,
        Dict{String, Float64}(), 0,
        false, "", now()
    )
    
    try
        total_start = time()
        setup_start = time()
        
        # Copy case
        println("   📁 Copying case files...")
        run(`cp -r $(case_config.tutorial_path) $case_dir`)
        
        cd(case_dir) do
            # Clean previous runs
            run(`bash -c "source /opt/openfoam12/etc/bashrc && rm -rf [0-9]* processor* postProcessing log.*"`)
            
            # Modify mesh if needed
            if nx > 1 && case_config.name == "cavity_laminar"
                modify_cavity_mesh(case_dir, nx, ny)
            end
            
            setup_time = time() - setup_start
            
            # Generate mesh
            println("   🔨 Generating mesh...")
            mesh_start = time()
            mesh_log = readchomp(`bash -c "source /opt/openfoam12/etc/bashrc && blockMesh 2>&1"`)
            mesh_time = time() - mesh_start
            
            if !occursin("End", mesh_log)
                throw(ErrorException("Mesh generation failed"))
            end
            
            # Get mesh size
            mesh_cells = get_mesh_cells(case_dir)
            
            # Run solver
            println("   ⚡ Running $(case_config.solver)...")
            solve_start = time()
            
            # Run with time tracking
            solver_log = readchomp(`bash -c "source /opt/openfoam12/etc/bashrc && time $(case_config.solver) 2>&1"`)
            
            solve_time = time() - solve_start
            total_time = time() - total_start
            
            # Parse results
            iterations = count_iterations(solver_log)
            residuals = extract_residuals(solver_log)
            converged = occursin("End", solver_log)
            
            # Save log
            write("solver.log", solver_log)
            
            result = OpenFOAMResult(
                case_config.name, mesh_str,
                setup_time, mesh_time, solve_time, total_time,
                iterations, converged, residuals, mesh_cells,
                true, "", now()
            )
            
            println("   ✅ Success!")
            println("      Setup: $(round(setup_time, digits=3))s")
            println("      Mesh: $(round(mesh_time, digits=3))s")
            println("      Solve: $(round(solve_time, digits=3))s")
            println("      Total: $(round(total_time, digits=3))s")
            println("      Iterations: $iterations")
            println("      Cells: $mesh_cells")
        end
        
    catch e
        result.error_msg = string(e)
        result.success = false
        println("   ❌ Failed: $(result.error_msg)")
    end
    
    return result
end

function save_results(results::Vector{OpenFOAMResult}, test_dir::String)
    """Save results to CSV and report"""
    
    # CSV file
    csv_file = joinpath(test_dir, "openfoam_results.csv")
    
    open(csv_file, "w") do f
        # Header
        write(f, "case_name,mesh_size,setup_time,mesh_time,solve_time,total_time,")
        write(f, "iterations,converged,mesh_cells,success,timestamp\n")
        
        # Data
        for r in results
            write(f, "$(r.case_name),$(r.mesh_size),")
            write(f, "$(r.setup_time),$(r.mesh_time),$(r.solve_time),$(r.total_time),")
            write(f, "$(r.iterations),$(r.converged),$(r.mesh_cells),$(r.success),")
            write(f, "$(r.timestamp)\n")
        end
    end
    
    println("\n📊 Results saved to: $csv_file")
    
    # Markdown report
    report_file = joinpath(test_dir, "openfoam_benchmark_report.md")
    
    open(report_file, "w") do f
        write(f, """
# OpenFOAM Benchmark Results

**Date**: $(Dates.now())  
**OpenFOAM Version**: 12  
**Total Cases**: $(length(results))

## Performance Summary

| Case | Mesh | Cells | Solve Time (s) | Iterations | Converged |
|------|------|-------|----------------|------------|-----------|
""")
        
        for r in results
            if r.success
                write(f, "| $(r.case_name) | $(r.mesh_size) | $(r.mesh_cells) | ")
                write(f, "$(round(r.solve_time, digits=3)) | $(r.iterations) | ")
                write(f, "$(r.converged ? "✅" : "❌") |\n")
            end
        end
        
        write(f, """

## Detailed Timing

| Case | Mesh | Setup (s) | Mesh (s) | Solve (s) | Total (s) |
|------|------|-----------|----------|-----------|-----------|
""")
        
        for r in results
            if r.success
                write(f, "| $(r.case_name) | $(r.mesh_size) | ")
                write(f, "$(round(r.setup_time, digits=3)) | ")
                write(f, "$(round(r.mesh_time, digits=3)) | ")
                write(f, "$(round(r.solve_time, digits=3)) | ")
                write(f, "$(round(r.total_time, digits=3)) |\n")
            end
        end
        
        write(f, """

## Final Residuals

""")
        
        for r in results
            if r.success && !isempty(r.final_residuals)
                write(f, "### $(r.case_name) ($(r.mesh_size)):\n")
                for (field, value) in r.final_residuals
                    write(f, "- $field: $(value)\n")
                end
                write(f, "\n")
            end
        end
        
        # Summary statistics
        successful = count(r -> r.success, results)
        failed = length(results) - successful
        
        write(f, """
## Summary

- **Total runs**: $(length(results))
- **Successful**: $successful
- **Failed**: $failed
- **Success rate**: $(round(successful/length(results)*100, digits=1))%

---
*All timings are wall-clock measurements*
""")
    end
    
    println("📄 Report saved to: $report_file")
    
    return csv_file, report_file
end

function main()
    """Run all OpenFOAM benchmark cases"""
    
    # Setup
    test_dir = setup_test_directory()
    all_results = OpenFOAMResult[]
    
    println("\n🏁 Starting OpenFOAM Benchmark Suite")
    println("📁 Test directory: $test_dir")
    
    # Run all test cases
    for case_config in TEST_CASES
        println("\n" * "=" * "^" * "60")
        println("📋 Case: $(case_config.description)")
        println("=" * "^" * "60")
        
        for mesh_size in case_config.mesh_sizes
            result = run_openfoam_case(case_config, mesh_size, test_dir)
            push!(all_results, result)
        end
    end
    
    # Save results
    println("\n" * "=" * "^" * "60")
    println("💾 Saving Results")
    println("=" * "^" * "60")
    
    csv_file, report_file = save_results(all_results, test_dir)
    
    # Summary
    println("\n🎉 OpenFOAM Benchmark Complete!")
    println("📊 Total cases run: $(length(all_results))")
    println("✅ Successful: $(count(r -> r.success, all_results))")
    println("❌ Failed: $(count(r -> !r.success, all_results))")
    
    # Performance summary
    if any(r -> r.success, all_results)
        println("\n⚡ Performance Summary:")
        for case_name in unique([r.case_name for r in all_results])
            case_results = filter(r -> r.case_name == case_name && r.success, all_results)
            if !isempty(case_results)
                println("   $(case_name):")
                for r in case_results
                    println("      $(r.mesh_size): $(round(r.solve_time, digits=3))s ($(r.iterations) iter)")
                end
            end
        end
    end
    
    return all_results, test_dir
end

# Execute benchmark
if abspath(PROGRAM_FILE) == @__FILE__
    results, test_dir = main()
end