/*--------------------------------*- C++ -*----------------------------------*\
  =========                 |
  \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox
   \\    /   O peration     | Website:  https://openfoam.org
    \\  /    A nd           | Version:  12
     \\/     M anipulation  |
\*---------------------------------------------------------------------------*/
FoamFile
{
    format      ascii;
    class       labelList;
    note        "nPoints: 882 nCells: 400 nFaces: 1640 nInternalFaces: 760";
    location    "constant/polyMesh";
    object      owner;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //


1640
(
0
0
1
1
2
2
3
3
4
4
5
5
6
6
7
7
8
8
9
9
10
10
11
11
12
12
13
13
14
14
15
15
16
16
17
17
18
18
19
20
20
21
21
22
22
23
23
24
24
25
25
26
26
27
27
28
28
29
29
30
30
31
31
32
32
33
33
34
34
35
35
36
36
37
37
38
38
39
40
40
41
41
42
42
43
43
44
44
45
45
46
46
47
47
48
48
49
49
50
50
51
51
52
52
53
53
54
54
55
55
56
56
57
57
58
58
59
60
60
61
61
62
62
63
63
64
64
65
65
66
66
67
67
68
68
69
69
70
70
71
71
72
72
73
73
74
74
75
75
76
76
77
77
78
78
79
80
80
81
81
82
82
83
83
84
84
85
85
86
86
87
87
88
88
89
89
90
90
91
91
92
92
93
93
94
94
95
95
96
96
97
97
98
98
99
100
100
101
101
102
102
103
103
104
104
105
105
106
106
107
107
108
108
109
109
110
110
111
111
112
112
113
113
114
114
115
115
116
116
117
117
118
118
119
120
120
121
121
122
122
123
123
124
124
125
125
126
126
127
127
128
128
129
129
130
130
131
131
132
132
133
133
134
134
135
135
136
136
137
137
138
138
139
140
140
141
141
142
142
143
143
144
144
145
145
146
146
147
147
148
148
149
149
150
150
151
151
152
152
153
153
154
154
155
155
156
156
157
157
158
158
159
160
160
161
161
162
162
163
163
164
164
165
165
166
166
167
167
168
168
169
169
170
170
171
171
172
172
173
173
174
174
175
175
176
176
177
177
178
178
179
180
180
181
181
182
182
183
183
184
184
185
185
186
186
187
187
188
188
189
189
190
190
191
191
192
192
193
193
194
194
195
195
196
196
197
197
198
198
199
200
200
201
201
202
202
203
203
204
204
205
205
206
206
207
207
208
208
209
209
210
210
211
211
212
212
213
213
214
214
215
215
216
216
217
217
218
218
219
220
220
221
221
222
222
223
223
224
224
225
225
226
226
227
227
228
228
229
229
230
230
231
231
232
232
233
233
234
234
235
235
236
236
237
237
238
238
239
240
240
241
241
242
242
243
243
244
244
245
245
246
246
247
247
248
248
249
249
250
250
251
251
252
252
253
253
254
254
255
255
256
256
257
257
258
258
259
260
260
261
261
262
262
263
263
264
264
265
265
266
266
267
267
268
268
269
269
270
270
271
271
272
272
273
273
274
274
275
275
276
276
277
277
278
278
279
280
280
281
281
282
282
283
283
284
284
285
285
286
286
287
287
288
288
289
289
290
290
291
291
292
292
293
293
294
294
295
295
296
296
297
297
298
298
299
300
300
301
301
302
302
303
303
304
304
305
305
306
306
307
307
308
308
309
309
310
310
311
311
312
312
313
313
314
314
315
315
316
316
317
317
318
318
319
320
320
321
321
322
322
323
323
324
324
325
325
326
326
327
327
328
328
329
329
330
330
331
331
332
332
333
333
334
334
335
335
336
336
337
337
338
338
339
340
340
341
341
342
342
343
343
344
344
345
345
346
346
347
347
348
348
349
349
350
350
351
351
352
352
353
353
354
354
355
355
356
356
357
357
358
358
359
360
360
361
361
362
362
363
363
364
364
365
365
366
366
367
367
368
368
369
369
370
370
371
371
372
372
373
373
374
374
375
375
376
376
377
377
378
378
379
380
381
382
383
384
385
386
387
388
389
390
391
392
393
394
395
396
397
398
380
381
382
383
384
385
386
387
388
389
390
391
392
393
394
395
396
397
398
399
0
20
40
60
80
100
120
140
160
180
200
220
240
260
280
300
320
340
360
380
19
39
59
79
99
119
139
159
179
199
219
239
259
279
299
319
339
359
379
399
0
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
0
20
40
60
80
100
120
140
160
180
200
220
240
260
280
300
320
340
360
380
1
21
41
61
81
101
121
141
161
181
201
221
241
261
281
301
321
341
361
381
2
22
42
62
82
102
122
142
162
182
202
222
242
262
282
302
322
342
362
382
3
23
43
63
83
103
123
143
163
183
203
223
243
263
283
303
323
343
363
383
4
24
44
64
84
104
124
144
164
184
204
224
244
264
284
304
324
344
364
384
5
25
45
65
85
105
125
145
165
185
205
225
245
265
285
305
325
345
365
385
6
26
46
66
86
106
126
146
166
186
206
226
246
266
286
306
326
346
366
386
7
27
47
67
87
107
127
147
167
187
207
227
247
267
287
307
327
347
367
387
8
28
48
68
88
108
128
148
168
188
208
228
248
268
288
308
328
348
368
388
9
29
49
69
89
109
129
149
169
189
209
229
249
269
289
309
329
349
369
389
10
30
50
70
90
110
130
150
170
190
210
230
250
270
290
310
330
350
370
390
11
31
51
71
91
111
131
151
171
191
211
231
251
271
291
311
331
351
371
391
12
32
52
72
92
112
132
152
172
192
212
232
252
272
292
312
332
352
372
392
13
33
53
73
93
113
133
153
173
193
213
233
253
273
293
313
333
353
373
393
14
34
54
74
94
114
134
154
174
194
214
234
254
274
294
314
334
354
374
394
15
35
55
75
95
115
135
155
175
195
215
235
255
275
295
315
335
355
375
395
16
36
56
76
96
116
136
156
176
196
216
236
256
276
296
316
336
356
376
396
17
37
57
77
97
117
137
157
177
197
217
237
257
277
297
317
337
357
377
397
18
38
58
78
98
118
138
158
178
198
218
238
258
278
298
318
338
358
378
398
19
39
59
79
99
119
139
159
179
199
219
239
259
279
299
319
339
359
379
399
0
20
40
60
80
100
120
140
160
180
200
220
240
260
280
300
320
340
360
380
1
21
41
61
81
101
121
141
161
181
201
221
241
261
281
301
321
341
361
381
2
22
42
62
82
102
122
142
162
182
202
222
242
262
282
302
322
342
362
382
3
23
43
63
83
103
123
143
163
183
203
223
243
263
283
303
323
343
363
383
4
24
44
64
84
104
124
144
164
184
204
224
244
264
284
304
324
344
364
384
5
25
45
65
85
105
125
145
165
185
205
225
245
265
285
305
325
345
365
385
6
26
46
66
86
106
126
146
166
186
206
226
246
266
286
306
326
346
366
386
7
27
47
67
87
107
127
147
167
187
207
227
247
267
287
307
327
347
367
387
8
28
48
68
88
108
128
148
168
188
208
228
248
268
288
308
328
348
368
388
9
29
49
69
89
109
129
149
169
189
209
229
249
269
289
309
329
349
369
389
10
30
50
70
90
110
130
150
170
190
210
230
250
270
290
310
330
350
370
390
11
31
51
71
91
111
131
151
171
191
211
231
251
271
291
311
331
351
371
391
12
32
52
72
92
112
132
152
172
192
212
232
252
272
292
312
332
352
372
392
13
33
53
73
93
113
133
153
173
193
213
233
253
273
293
313
333
353
373
393
14
34
54
74
94
114
134
154
174
194
214
234
254
274
294
314
334
354
374
394
15
35
55
75
95
115
135
155
175
195
215
235
255
275
295
315
335
355
375
395
16
36
56
76
96
116
136
156
176
196
216
236
256
276
296
316
336
356
376
396
17
37
57
77
97
117
137
157
177
197
217
237
257
277
297
317
337
357
377
397
18
38
58
78
98
118
138
158
178
198
218
238
258
278
298
318
338
358
378
398
19
39
59
79
99
119
139
159
179
199
219
239
259
279
299
319
339
359
379
399
)


// ************************************************************************* //
