/*--------------------------------*- C++ -*----------------------------------*\
  =========                 |
  \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox
   \\    /   O peration     | Website:  https://openfoam.org
    \\  /    A nd           | Version:  12
     \\/     M anipulation  |
\*---------------------------------------------------------------------------*/
FoamFile
{
    format      ascii;
    class       labelList;
    note        "nPoints: 882 nCells: 400 nFaces: 1640 nInternalFaces: 760";
    location    "constant/polyMesh";
    object      neighbour;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //


760
(
1
20
2
21
3
22
4
23
5
24
6
25
7
26
8
27
9
28
10
29
11
30
12
31
13
32
14
33
15
34
16
35
17
36
18
37
19
38
39
21
40
22
41
23
42
24
43
25
44
26
45
27
46
28
47
29
48
30
49
31
50
32
51
33
52
34
53
35
54
36
55
37
56
38
57
39
58
59
41
60
42
61
43
62
44
63
45
64
46
65
47
66
48
67
49
68
50
69
51
70
52
71
53
72
54
73
55
74
56
75
57
76
58
77
59
78
79
61
80
62
81
63
82
64
83
65
84
66
85
67
86
68
87
69
88
70
89
71
90
72
91
73
92
74
93
75
94
76
95
77
96
78
97
79
98
99
81
100
82
101
83
102
84
103
85
104
86
105
87
106
88
107
89
108
90
109
91
110
92
111
93
112
94
113
95
114
96
115
97
116
98
117
99
118
119
101
120
102
121
103
122
104
123
105
124
106
125
107
126
108
127
109
128
110
129
111
130
112
131
113
132
114
133
115
134
116
135
117
136
118
137
119
138
139
121
140
122
141
123
142
124
143
125
144
126
145
127
146
128
147
129
148
130
149
131
150
132
151
133
152
134
153
135
154
136
155
137
156
138
157
139
158
159
141
160
142
161
143
162
144
163
145
164
146
165
147
166
148
167
149
168
150
169
151
170
152
171
153
172
154
173
155
174
156
175
157
176
158
177
159
178
179
161
180
162
181
163
182
164
183
165
184
166
185
167
186
168
187
169
188
170
189
171
190
172
191
173
192
174
193
175
194
176
195
177
196
178
197
179
198
199
181
200
182
201
183
202
184
203
185
204
186
205
187
206
188
207
189
208
190
209
191
210
192
211
193
212
194
213
195
214
196
215
197
216
198
217
199
218
219
201
220
202
221
203
222
204
223
205
224
206
225
207
226
208
227
209
228
210
229
211
230
212
231
213
232
214
233
215
234
216
235
217
236
218
237
219
238
239
221
240
222
241
223
242
224
243
225
244
226
245
227
246
228
247
229
248
230
249
231
250
232
251
233
252
234
253
235
254
236
255
237
256
238
257
239
258
259
241
260
242
261
243
262
244
263
245
264
246
265
247
266
248
267
249
268
250
269
251
270
252
271
253
272
254
273
255
274
256
275
257
276
258
277
259
278
279
261
280
262
281
263
282
264
283
265
284
266
285
267
286
268
287
269
288
270
289
271
290
272
291
273
292
274
293
275
294
276
295
277
296
278
297
279
298
299
281
300
282
301
283
302
284
303
285
304
286
305
287
306
288
307
289
308
290
309
291
310
292
311
293
312
294
313
295
314
296
315
297
316
298
317
299
318
319
301
320
302
321
303
322
304
323
305
324
306
325
307
326
308
327
309
328
310
329
311
330
312
331
313
332
314
333
315
334
316
335
317
336
318
337
319
338
339
321
340
322
341
323
342
324
343
325
344
326
345
327
346
328
347
329
348
330
349
331
350
332
351
333
352
334
353
335
354
336
355
337
356
338
357
339
358
359
341
360
342
361
343
362
344
363
345
364
346
365
347
366
348
367
349
368
350
369
351
370
352
371
353
372
354
373
355
374
356
375
357
376
358
377
359
378
379
361
380
362
381
363
382
364
383
365
384
366
385
367
386
368
387
369
388
370
389
371
390
372
391
373
392
374
393
375
394
376
395
377
396
378
397
379
398
399
381
382
383
384
385
386
387
388
389
390
391
392
393
394
395
396
397
398
399
)


// ************************************************************************* //
