"""
ReportGeneration.jl

Generate comprehensive comparison reports between OpenFOAM and JuliaFOAM.
Honest reporting with focus on accuracy and limitations.
"""

include("AccuracyAnalysis.jl")

using Printf
using Dates

"""
Generate comprehensive comparison report
"""
function generate_comparison_report(comparison_result, output_dir::String="results")
    println("📄 Generating comparison report...")
    
    # Create output directory
    mkpath(output_dir)
    mkpath(joinpath(output_dir, "accuracy_reports"))
    mkpath(joinpath(output_dir, "performance_data"))
    
    # Generate timestamp for unique filenames
    timestamp = Dates.format(now(), "yyyy-mm-dd_HHMMSS")
    case_name = comparison_result.case_name
    
    # Main report
    main_report_file = joinpath(output_dir, "accuracy_reports", "$(case_name)_comparison_$(timestamp).md")
    generate_main_report(comparison_result, main_report_file)
    
    # Performance data (CSV)
    perf_data_file = joinpath(output_dir, "performance_data", "$(case_name)_performance_$(timestamp).csv")
    generate_performance_csv(comparison_result, perf_data_file)
    
    # Accuracy summary (JSON-like)
    accuracy_file = joinpath(output_dir, "accuracy_reports", "$(case_name)_accuracy_$(timestamp).txt")
    generate_accuracy_summary_file(comparison_result, accuracy_file)
    
    println("   ✅ Reports generated:")
    println("      📊 Main report: $main_report_file")
    println("      ⚡ Performance: $perf_data_file")
    println("      🎯 Accuracy: $accuracy_file")
    
    return main_report_file
end

"""
Generate main comparison report in Markdown format
"""
function generate_main_report(comparison::ComparisonResult, filename::String)
    of_result = comparison.openfoam_result
    jf_result = comparison.juliafoam_result
    
    # Generate accuracy summary
    accuracy_summary = generate_accuracy_summary(comparison.accuracy_metrics, comparison.field_errors)
    
    open(filename, "w") do io
        write(io, """
# OpenFOAM vs JuliaFOAM Comparison Report

**Case:** $(comparison.case_name)  
**Generated:** $(comparison.timestamp)  
**Comparison Type:** Solver Performance and Accuracy

---

## Executive Summary

$(generate_executive_summary(comparison, accuracy_summary))

---

## Test Configuration

### Problem Setup
- **Case Name:** $(comparison.case_name)
- **Solver Type:** $(get(of_result.final_fields, "solver_type", "Unknown"))
- **Test Date:** $(Dates.format(comparison.timestamp, "yyyy-mm-dd HH:MM:SS"))

### OpenFOAM Configuration
- **Success:** $(of_result.success ? "✅ Yes" : "❌ No")
- **Solve Time:** $(round(of_result.solve_time, digits=3))s
- **Iterations:** $(of_result.iterations)
- **Memory Usage:** $(of_result.memory_usage) MB

### JuliaFOAM Configuration  
- **Success:** $(jf_result.success ? "✅ Yes" : "❌ No")
- **Solve Time:** $(round(jf_result.solve_time, digits=3))s
- **Iterations:** $(jf_result.iterations)
- **Memory Usage:** $(jf_result.memory_usage) MB

---

## Performance Results

### Execution Metrics

| Metric | OpenFOAM | JuliaFOAM | Ratio |
|--------|----------|-----------|-------|
| **Solve Time** | $(round(of_result.solve_time, digits=3))s | $(round(jf_result.solve_time, digits=3))s | $(of_result.success && jf_result.success ? @sprintf("%.2fx", of_result.solve_time / max(jf_result.solve_time, 1e-10)) : "N/A") |
| **Iterations** | $(of_result.iterations) | $(jf_result.iterations) | $(of_result.success && jf_result.success ? @sprintf("%.2fx", of_result.iterations / max(jf_result.iterations, 1)) : "N/A") |
| **Memory Usage** | $(of_result.memory_usage) MB | $(jf_result.memory_usage) MB | $(of_result.memory_usage > 0 && jf_result.memory_usage > 0 ? @sprintf("%.2fx", of_result.memory_usage / jf_result.memory_usage) : "N/A") |

### Convergence Analysis

""")
        
        # Convergence details
        if of_result.success && jf_result.success
            write(io, """
#### Final Residuals

| Field | OpenFOAM | JuliaFOAM | Difference |
|-------|----------|-----------|------------|
""")
            
            for field in union(keys(of_result.final_residuals), keys(jf_result.final_residuals))
                of_res = get(of_result.final_residuals, field, 0.0)
                jf_res = get(jf_result.final_residuals, field, 0.0)
                diff = abs(jf_res - of_res)
                
                write(io, "| $field | $(@sprintf("%.2e", of_res)) | $(@sprintf("%.2e", jf_res)) | $(@sprintf("%.2e", diff)) |\n")
            end
        else
            write(io, """
#### Convergence Issues
""")
            if !of_result.success
                write(io, "- **OpenFOAM Failed:** $(of_result.error_message)\n")
            end
            if !jf_result.success
                write(io, "- **JuliaFOAM Failed:** $(jf_result.error_message)\n")
            end
        end
        
        write(io, """

---

## Accuracy Assessment

""")
        
        if of_result.success && jf_result.success && !isempty(comparison.accuracy_metrics)
            write(io, """
### Overall Accuracy

| Metric | Value | Grade |
|--------|-------|-------|
| **Average L2 Error** | $(@sprintf("%.2e", get(comparison.accuracy_metrics, "average_l2_error", 0.0))) | $(accuracy_summary["overall_grade"]) |
| **Max Error (Any Field)** | $(@sprintf("%.2e", get(comparison.accuracy_metrics, "max_error_any_field", 0.0))) | - |
| **Fields Compared** | $(Int(get(comparison.accuracy_metrics, "fields_compared", 0))) | - |
| **Assessment** | $(accuracy_summary["assessment"]) | - |

### Field-by-Field Analysis

""")
            
            for (field_name, field_metrics) in comparison.field_errors
                grade = get(accuracy_summary["field_grades"], field_name, "N/A")
                write(io, """
#### Field: $field_name (Grade: $grade)

| Metric | Value |
|--------|-------|
| L2 Relative Error | $(@sprintf("%.2e", field_metrics["l2_error"])) |
| Max Relative Error | $(@sprintf("%.2e", field_metrics["max_error"])) |
| RMS Error | $(@sprintf("%.2e", field_metrics["rms_error"])) |
| Correlation Coefficient | $(@sprintf("%.6f", field_metrics["correlation"])) |
| R² | $(@sprintf("%.6f", field_metrics["r_squared"])) |

**Quality Assessment:**
""")
                
                l2_error = field_metrics["l2_error"]
                correlation = field_metrics["correlation"]
                
                if l2_error < 1e-6 && correlation > 0.999
                    write(io, "✅ **Excellent**: Solutions match within numerical precision\n\n")
                elseif l2_error < 1e-4 && correlation > 0.99
                    write(io, "🟡 **Good**: Solutions show strong agreement with minor differences\n\n")
                elseif l2_error < 1e-2 && correlation > 0.95
                    write(io, "🟠 **Acceptable**: Solutions agree reasonably well\n\n")
                else
                    write(io, "❌ **Poor**: Significant differences between solutions\n\n")
                end
            end
        else
            write(io, """
### Accuracy Analysis Not Available

Accuracy comparison requires both solvers to complete successfully.

""")
            if !of_result.success
                write(io, "- OpenFOAM solver failed\n")
            end
            if !jf_result.success
                write(io, "- JuliaFOAM solver failed\n")
            end
            if isempty(comparison.accuracy_metrics)
                write(io, "- No comparable field data found\n")
            end
        end
        
        write(io, """

---

## Key Findings

### Performance Summary
""")
        
        if of_result.success && jf_result.success
            speedup = of_result.solve_time / max(jf_result.solve_time, 1e-10)
            if speedup > 1.0
                write(io, "- ✅ **JuliaFOAM is $(round(speedup, digits=1))x faster** than OpenFOAM\n")
            else
                write(io, "- ⚠️ **OpenFOAM is $(round(1/speedup, digits=1))x faster** than JuliaFOAM\n")
            end
            
            write(io, "- Both solvers converged successfully\n")
            write(io, "- Iteration counts: OpenFOAM $(of_result.iterations), JuliaFOAM $(jf_result.iterations)\n")
        else
            write(io, "- Performance comparison not meaningful due to solver failures\n")
        end
        
        write(io, """

### Accuracy Summary
""")
        
        if !isempty(comparison.accuracy_metrics)
            overall_grade = accuracy_summary["overall_grade"]
            if overall_grade in ["A+", "A"]
                write(io, "- ✅ **Excellent accuracy**: Solutions match within numerical precision\n")
            elseif overall_grade == "B"
                write(io, "- 🟡 **Good accuracy**: Solutions show strong agreement\n")
            elseif overall_grade == "C"
                write(io, "- 🟠 **Fair accuracy**: Solutions agree reasonably well\n")
            else
                write(io, "- ❌ **Poor accuracy**: Significant differences between solutions\n")
            end
            
            avg_l2 = get(comparison.accuracy_metrics, "average_l2_error", 0.0)
            write(io, "- Average L2 error: $(@sprintf("%.2e", avg_l2))\n")
            
            field_count = Int(get(comparison.accuracy_metrics, "fields_compared", 0))
            write(io, "- Successfully compared $field_count field(s)\n")
        else
            write(io, "- Accuracy assessment not available\n")
        end
        
        write(io, """

### Limitations and Notes

""")
        
        write(io, generate_limitations_section(comparison))
        
        write(io, """

---

## Recommendations

""")
        
        write(io, generate_recommendations(comparison, accuracy_summary))
        
        write(io, """

---

## Technical Details

### OpenFOAM Output
$(of_result.success ? "Solver completed successfully" : "Error: $(of_result.error_message)")

### JuliaFOAM Output  
$(jf_result.success ? "Solver completed successfully" : "Error: $(jf_result.error_message)")

### Data Processing
- Field extraction method: Automatic detection
- Error metrics: L2 norm, maximum error, correlation
- Comparison tolerance: Machine precision

---

**Report Generated:** $(now())  
**JuliaFOAM Benchmark Suite v1.0**
""")
    end
end

"""
Generate executive summary
"""
function generate_executive_summary(comparison::ComparisonResult, accuracy_summary::Dict{String, Any})
    of_result = comparison.openfoam_result
    jf_result = comparison.juliafoam_result
    
    summary_parts = String[]
    
    # Success status
    if of_result.success && jf_result.success
        push!(summary_parts, "Both OpenFOAM and JuliaFOAM solvers completed successfully.")
        
        # Performance
        speedup = of_result.solve_time / max(jf_result.solve_time, 1e-10)
        if speedup > 1.0
            push!(summary_parts, "JuliaFOAM demonstrated $(round(speedup, digits=1))x faster execution.")
        else
            push!(summary_parts, "OpenFOAM was $(round(1/speedup, digits=1))x faster than JuliaFOAM.")
        end
        
        # Accuracy
        if !isempty(comparison.accuracy_metrics)
            overall_grade = accuracy_summary["overall_grade"]
            assessment = accuracy_summary["assessment"]
            push!(summary_parts, "Accuracy grade: $overall_grade ($assessment).")
        end
    else
        if !of_result.success && !jf_result.success
            push!(summary_parts, "Both solvers failed to complete successfully.")
        elseif !of_result.success
            push!(summary_parts, "OpenFOAM solver failed while JuliaFOAM completed successfully.")
        else
            push!(summary_parts, "JuliaFOAM solver failed while OpenFOAM completed successfully.")
        end
    end
    
    return join(summary_parts, " ")
end

"""
Generate limitations section
"""
function generate_limitations_section(comparison::ComparisonResult)
    limitations = String[]
    
    push!(limitations, "- This comparison uses simplified equivalent implementations")
    push!(limitations, "- Mesh generation differences may affect results")
    push!(limitations, "- Boundary condition mapping is approximated")
    
    if !comparison.openfoam_result.success
        push!(limitations, "- OpenFOAM solver encountered errors")
    end
    
    if !comparison.juliafoam_result.success
        push!(limitations, "- JuliaFOAM solver encountered errors")
    end
    
    if isempty(comparison.accuracy_metrics)
        push!(limitations, "- No field data available for accuracy comparison")
    end
    
    push!(limitations, "- Performance comparison includes setup overhead")
    
    return join(["  " * limitation for limitation in limitations], "\\n") * "\\n"
end

"""
Generate recommendations section
"""
function generate_recommendations(comparison::ComparisonResult, accuracy_summary::Dict{String, Any})
    recommendations = String[]
    
    if comparison.openfoam_result.success && comparison.juliafoam_result.success
        # Both succeeded
        if !isempty(comparison.accuracy_metrics)
            overall_grade = accuracy_summary["overall_grade"]
            
            if overall_grade in ["A+", "A"]
                push!(recommendations, "✅ **Continue development**: Excellent accuracy achieved")
                push!(recommendations, "🚀 **Focus on performance**: Explore further optimizations")
            elseif overall_grade == "B"
                push!(recommendations, "🔧 **Refine algorithms**: Good accuracy, minor improvements needed")
                push!(recommendations, "📊 **Validate with more cases**: Test robustness across different problems")
            else
                push!(recommendations, "⚠️ **Address accuracy issues**: Significant improvements needed")
                push!(recommendations, "🔍 **Debug implementation**: Check numerical methods and boundary conditions")
            end
        end
        
        # Performance recommendations
        of_time = comparison.openfoam_result.solve_time
        jf_time = comparison.juliafoam_result.solve_time
        
        if jf_time > of_time
            push!(recommendations, "⚡ **Optimize performance**: JuliaFOAM slower than OpenFOAM")
            push!(recommendations, "🔧 **Profile bottlenecks**: Identify and optimize slow components")
        end
    else
        # One or both failed
        if !comparison.openfoam_result.success
            push!(recommendations, "🔧 **Fix OpenFOAM setup**: Resolve configuration or case issues")
        end
        
        if !comparison.juliafoam_result.success
            push!(recommendations, "🚀 **Debug JuliaFOAM**: Address solver implementation issues")
            push!(recommendations, "📝 **Improve error handling**: Better diagnostics needed")
        end
    end
    
    # General recommendations
    push!(recommendations, "📊 **Expand test suite**: Add more complex validation cases")
    push!(recommendations, "🔄 **Automate testing**: Regular regression testing recommended")
    
    return join(["- " * recommendation for recommendation in recommendations], "\\n") * "\\n"
end

"""
Generate performance data CSV
"""
function generate_performance_csv(comparison::ComparisonResult, filename::String)
    open(filename, "w") do io
        # Header
        write(io, "solver,case,success,solve_time,iterations,memory_usage,u_residual,p_residual\\n")
        
        # OpenFOAM data
        of_result = comparison.openfoam_result
        of_u_res = get(of_result.final_residuals, "U", 0.0)
        of_p_res = get(of_result.final_residuals, "p", 0.0)
        
        write(io, "OpenFOAM,$(comparison.case_name),$(of_result.success),$(of_result.solve_time),$(of_result.iterations),$(of_result.memory_usage),$of_u_res,$of_p_res\\n")
        
        # JuliaFOAM data
        jf_result = comparison.juliafoam_result
        jf_u_res = get(jf_result.final_residuals, "U", 0.0)
        jf_p_res = get(jf_result.final_residuals, "p", 0.0)
        
        write(io, "JuliaFOAM,$(comparison.case_name),$(jf_result.success),$(jf_result.solve_time),$(jf_result.iterations),$(jf_result.memory_usage),$jf_u_res,$jf_p_res\\n")
    end
end

"""
Generate accuracy summary file
"""
function generate_accuracy_summary_file(comparison::ComparisonResult, filename::String)
    open(filename, "w") do io
        write(io, "ACCURACY SUMMARY REPORT\\n")
        write(io, "=" ^ 50 * "\\n\\n")
        
        write(io, "Case: $(comparison.case_name)\\n")
        write(io, "Timestamp: $(comparison.timestamp)\\n\\n")
        
        if !isempty(comparison.accuracy_metrics)
            write(io, "OVERALL METRICS:\\n")
            write(io, "-" ^ 20 * "\\n")
            
            for (metric, value) in comparison.accuracy_metrics
                write(io, "$(rpad(metric, 25)): $(@sprintf("%.6e", value))\\n")
            end
            
            write(io, "\\nFIELD ERRORS:\\n")
            write(io, "-" ^ 20 * "\\n")
            
            for (field_name, field_metrics) in comparison.field_errors
                write(io, "\\n$field_name:\\n")
                for (metric, value) in field_metrics
                    write(io, "  $(rpad(metric, 20)): $(@sprintf("%.6e", value))\\n")
                end
            end
        else
            write(io, "No accuracy data available\\n")
            if !comparison.openfoam_result.success
                write(io, "OpenFOAM failed: $(comparison.openfoam_result.error_message)\\n")
            end
            if !comparison.juliafoam_result.success
                write(io, "JuliaFOAM failed: $(comparison.juliafoam_result.error_message)\\n")
            end
        end
    end
end

"""
Generate batch report for multiple cases
"""
function generate_batch_report(comparison_results::Vector, output_file::String)
    println("📊 Generating batch comparison report...")
    
    open(output_file, "w") do io
        write(io, """
# OpenFOAM vs JuliaFOAM Batch Comparison Report

**Generated:** $(now())  
**Cases Tested:** $(length(comparison_results))

---

## Summary Table

| Case | OF Success | JF Success | Speedup | Accuracy Grade | Notes |
|------|------------|------------|---------|----------------|-------|
""")
        
        for comparison in comparison_results
            of_success = comparison.openfoam_result.success ? "✅" : "❌"
            jf_success = comparison.juliafoam_result.success ? "✅" : "❌"
            
            speedup = "N/A"
            if comparison.openfoam_result.success && comparison.juliafoam_result.success
                speedup_val = comparison.openfoam_result.solve_time / max(comparison.juliafoam_result.solve_time, 1e-10)
                speedup = @sprintf("%.2fx", speedup_val)
            end
            
            accuracy_grade = "N/A"
            if !isempty(comparison.accuracy_metrics)
                accuracy_summary = generate_accuracy_summary(comparison.accuracy_metrics, comparison.field_errors)
                accuracy_grade = accuracy_summary["overall_grade"]
            end
            
            notes = ""
            if !comparison.openfoam_result.success || !comparison.juliafoam_result.success
                notes = "Solver failed"
            end
            
            write(io, "| $(comparison.case_name) | $of_success | $jf_success | $speedup | $accuracy_grade | $notes |\\n")
        end
        
        write(io, """

---

## Detailed Analysis

""")
        
        # Performance statistics
        successful_comparisons = filter(c -> c.openfoam_result.success && c.juliafoam_result.success, comparison_results)
        
        if !isempty(successful_comparisons)
            speedups = [c.openfoam_result.solve_time / c.juliafoam_result.solve_time for c in successful_comparisons]
            
            write(io, """
### Performance Summary

- **Cases Completed**: $(length(successful_comparisons))/$(length(comparison_results))
- **Average Speedup**: $(@sprintf("%.2fx", mean(speedups)))
- **Best Speedup**: $(@sprintf("%.2fx", maximum(speedups)))
- **Worst Speedup**: $(@sprintf("%.2fx", minimum(speedups)))

""")
        end
        
        # Accuracy statistics
        accuracy_grades = String[]
        for comparison in comparison_results
            if !isempty(comparison.accuracy_metrics)
                accuracy_summary = generate_accuracy_summary(comparison.accuracy_metrics, comparison.field_errors)
                push!(accuracy_grades, accuracy_summary["overall_grade"])
            end
        end
        
        if !isempty(accuracy_grades)
            grade_counts = Dict{String, Int}()
            for grade in accuracy_grades
                grade_counts[grade] = get(grade_counts, grade, 0) + 1
            end
            
            write(io, """
### Accuracy Summary

""")
            for (grade, count) in sort(collect(grade_counts), by=x->x[1])
                write(io, "- **Grade $grade**: $count cases\\n")
            end
        end
        
        write(io, """

---

*Generated by JuliaFOAM Benchmark Suite*
""")
    end
    
    println("   ✅ Batch report saved: $output_file")
end