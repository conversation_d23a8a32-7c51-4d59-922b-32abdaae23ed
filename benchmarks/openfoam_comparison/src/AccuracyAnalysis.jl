"""
AccuracyAnalysis.jl

Analyze accuracy between OpenFOAM and JuliaFOAM solver results.
Focus on field-by-field comparison and error metrics.
"""

using LinearAlgebra
using Statistics
using Printf

"""
Analyze solver accuracy between OpenFOAM and JuliaFOAM results
"""
function analyze_solver_accuracy(openfoam_result, juliafoam_result)
    if !openfoam_result.success || !juliafoam_result.success
        println("   ⚠️  Cannot analyze accuracy - one or both solvers failed")
        return Dict{String, Float64}(), Dict{String, Dict{String, Float64}}()
    end
    
    accuracy_metrics = Dict{String, Float64}()
    field_errors = Dict{String, Dict{String, Float64}}()
    
    # Compare common fields
    of_fields = openfoam_result.final_fields
    jf_fields = juliafoam_result.final_fields
    
    common_fields = intersect(keys(of_fields), keys(jf_fields))
    
    if isempty(common_fields)
        println("   ⚠️  No common fields found for comparison")
        return accuracy_metrics, field_errors
    end
    
    println("   📊 Comparing fields: $(join(common_fields, ", "))")
    
    total_l2_error = 0.0
    total_max_error = 0.0
    field_count = 0
    
    for field_name in common_fields
        try
            of_data = of_fields[field_name]
            jf_data = jf_fields[field_name]
            
            # Convert to comparable format
            of_values = extract_field_values(of_data)
            jf_values = extract_field_values(jf_data)
            
            if isempty(of_values) || isempty(jf_values)
                println("      ⚠️  Empty field data for $field_name")
                continue
            end
            
            # Analyze field accuracy
            field_metrics = compute_field_accuracy(of_values, jf_values, field_name)
            field_errors[field_name] = field_metrics
            
            # Accumulate overall metrics
            total_l2_error += field_metrics["l2_error"]
            total_max_error = max(total_max_error, field_metrics["max_error"])
            field_count += 1
            
            # Print field results
            print_field_accuracy(field_name, field_metrics)
            
        catch e
            println("      ❌ Error comparing field $field_name: $e")
        end
    end
    
    # Overall accuracy metrics
    if field_count > 0
        accuracy_metrics["average_l2_error"] = total_l2_error / field_count
        accuracy_metrics["max_error_any_field"] = total_max_error
        accuracy_metrics["fields_compared"] = Float64(field_count)
        
        # Overall quality assessment
        avg_l2 = accuracy_metrics["average_l2_error"]
        if avg_l2 < 1e-6
            accuracy_metrics["quality_score"] = 5.0  # Excellent
        elseif avg_l2 < 1e-4
            accuracy_metrics["quality_score"] = 4.0  # Good
        elseif avg_l2 < 1e-2
            accuracy_metrics["quality_score"] = 3.0  # Acceptable
        else
            accuracy_metrics["quality_score"] = 1.0  # Poor
        end
    end
    
    return accuracy_metrics, field_errors
end

"""
Extract numerical values from field data
"""
function extract_field_values(field_data)
    if isa(field_data, AbstractArray)
        # Already in array format
        if eltype(field_data) <: Number
            return vec(field_data)
        elseif eltype(field_data) <: AbstractArray
            # Vector field - flatten to components
            return vcat([vec(component) for component in field_data]...)
        else
            return Float64[]
        end
    elseif isa(field_data, Dict)
        # Dictionary format - extract values
        if haskey(field_data, "internal_field")
            return extract_field_values(field_data["internal_field"])
        elseif haskey(field_data, "data")
            return extract_field_values(field_data["data"])
        else
            # Try to extract any numeric arrays
            numeric_values = Float64[]
            for (key, value) in field_data
                if isa(value, AbstractArray) && eltype(value) <: Number
                    append!(numeric_values, vec(value))
                end
            end
            return numeric_values
        end
    elseif isa(field_data, Number)
        # Single value
        return [Float64(field_data)]
    else
        # Try to convert to array
        try
            return vec(Float64.(field_data))
        catch
            return Float64[]
        end
    end
end

"""
Compute accuracy metrics for a single field
"""
function compute_field_accuracy(of_values::Vector{Float64}, jf_values::Vector{Float64}, field_name::String)
    metrics = Dict{String, Float64}()
    
    # Handle size differences
    min_length = min(length(of_values), length(jf_values))
    if min_length == 0
        return Dict("l2_error" => Inf, "max_error" => Inf, "correlation" => 0.0)
    end
    
    # Truncate to common length
    of_vals = of_values[1:min_length]
    jf_vals = jf_values[1:min_length]
    
    # Compute error metrics
    diff = jf_vals - of_vals
    
    # L2 relative error
    of_norm = norm(of_vals)
    if of_norm > 1e-15
        metrics["l2_error"] = norm(diff) / of_norm
    else
        metrics["l2_error"] = norm(diff)
    end
    
    # Maximum relative error
    of_max = maximum(abs.(of_vals))
    if of_max > 1e-15
        metrics["max_error"] = maximum(abs.(diff)) / of_max
    else
        metrics["max_error"] = maximum(abs.(diff))
    end
    
    # Mean absolute error
    metrics["mean_abs_error"] = mean(abs.(diff))
    
    # Root mean square error
    metrics["rms_error"] = sqrt(mean(diff.^2))
    
    # Correlation coefficient
    if std(of_vals) > 1e-15 && std(jf_vals) > 1e-15
        metrics["correlation"] = cor(of_vals, jf_vals)
    else
        metrics["correlation"] = 1.0  # Constant fields are perfectly correlated
    end
    
    # R-squared
    if var(of_vals) > 1e-15
        ss_res = sum(diff.^2)
        ss_tot = sum((of_vals .- mean(of_vals)).^2)
        metrics["r_squared"] = 1.0 - ss_res / ss_tot
    else
        metrics["r_squared"] = 1.0
    end
    
    # Data range comparison
    metrics["of_min"] = minimum(of_vals)
    metrics["of_max"] = maximum(of_vals)
    metrics["jf_min"] = minimum(jf_vals)
    metrics["jf_max"] = maximum(jf_vals)
    metrics["range_error"] = abs((maximum(jf_vals) - minimum(jf_vals)) - 
                                 (maximum(of_vals) - minimum(of_vals))) / 
                             max(maximum(of_vals) - minimum(of_vals), 1e-15)
    
    return metrics
end

"""
Print field accuracy results
"""
function print_field_accuracy(field_name::String, metrics::Dict{String, Float64})
    println("      📊 Field: $field_name")
    
    # Error metrics
    @printf "         L2 Error:     %8.2e\n" metrics["l2_error"]
    @printf "         Max Error:    %8.2e\n" metrics["max_error"]
    @printf "         RMS Error:    %8.2e\n" metrics["rms_error"]
    @printf "         Correlation:  %8.6f\n" metrics["correlation"]
    @printf "         R²:           %8.6f\n" metrics["r_squared"]
    
    # Quality assessment
    l2_error = metrics["l2_error"]
    correlation = metrics["correlation"]
    
    if l2_error < 1e-6 && correlation > 0.999
        quality = "✅ Excellent"
    elseif l2_error < 1e-4 && correlation > 0.99
        quality = "🟡 Good"
    elseif l2_error < 1e-2 && correlation > 0.95
        quality = "🟠 Acceptable"
    else
        quality = "❌ Poor"
    end
    
    println("         Quality:      $quality")
    
    # Range comparison
    @printf "         OF Range:     [%8.3e, %8.3e]\n" metrics["of_min"] metrics["of_max"]
    @printf "         JF Range:     [%8.3e, %8.3e]\n" metrics["jf_min"] metrics["jf_max"]
end

"""
Generate accuracy assessment summary
"""
function generate_accuracy_summary(accuracy_metrics::Dict{String, Float64}, field_errors::Dict{String, Dict{String, Float64}})
    summary = Dict{String, Any}()
    
    if isempty(accuracy_metrics)
        summary["overall_grade"] = "N/A"
        summary["assessment"] = "No accuracy analysis possible"
        return summary
    end
    
    # Overall assessment
    avg_l2 = get(accuracy_metrics, "average_l2_error", Inf)
    max_error = get(accuracy_metrics, "max_error_any_field", Inf)
    
    if avg_l2 < 1e-6
        summary["overall_grade"] = "A+"
        summary["assessment"] = "Excellent agreement"
    elseif avg_l2 < 1e-4
        summary["overall_grade"] = "A"
        summary["assessment"] = "Very good agreement"
    elseif avg_l2 < 1e-2
        summary["overall_grade"] = "B"
        summary["assessment"] = "Good agreement"
    elseif avg_l2 < 1e-1
        summary["overall_grade"] = "C"
        summary["assessment"] = "Fair agreement"
    else
        summary["overall_grade"] = "F"
        summary["assessment"] = "Poor agreement"
    end
    
    # Field-by-field summary
    summary["field_grades"] = Dict{String, String}()
    for (field_name, field_metrics) in field_errors
        l2_error = get(field_metrics, "l2_error", Inf)
        
        if l2_error < 1e-6
            summary["field_grades"][field_name] = "A+"
        elseif l2_error < 1e-4
            summary["field_grades"][field_name] = "A"
        elseif l2_error < 1e-2
            summary["field_grades"][field_name] = "B"
        elseif l2_error < 1e-1
            summary["field_grades"][field_name] = "C"
        else
            summary["field_grades"][field_name] = "F"
        end
    end
    
    return summary
end

"""
Compare convergence history between solvers
"""
function compare_convergence_histories(of_history::Dict{String, Vector{Float64}}, 
                                     jf_history::Dict{String, Vector{Float64}})
    comparison = Dict{String, Any}()
    
    common_fields = intersect(keys(of_history), keys(jf_history))
    
    for field in common_fields
        of_conv = of_history[field]
        jf_conv = jf_history[field]
        
        if isempty(of_conv) || isempty(jf_conv)
            continue
        end
        
        field_comparison = Dict{String, Any}()
        
        # Convergence rate comparison
        if length(of_conv) > 1 && length(jf_conv) > 1
            # Estimate convergence rate (slope in log space)
            of_rate = estimate_convergence_rate(of_conv)
            jf_rate = estimate_convergence_rate(jf_conv)
            
            field_comparison["of_convergence_rate"] = of_rate
            field_comparison["jf_convergence_rate"] = jf_rate
            field_comparison["rate_ratio"] = jf_rate / max(of_rate, 1e-15)
        end
        
        # Final residual comparison
        field_comparison["of_final_residual"] = of_conv[end]
        field_comparison["jf_final_residual"] = jf_conv[end]
        field_comparison["residual_ratio"] = jf_conv[end] / max(of_conv[end], 1e-15)
        
        # Iterations to convergence
        of_conv_iter = count_iterations_to_convergence(of_conv, 1e-6)
        jf_conv_iter = count_iterations_to_convergence(jf_conv, 1e-6)
        
        field_comparison["of_iterations_to_convergence"] = of_conv_iter
        field_comparison["jf_iterations_to_convergence"] = jf_conv_iter
        
        comparison[field] = field_comparison
    end
    
    return comparison
end

"""
Estimate convergence rate from residual history
"""
function estimate_convergence_rate(residuals::Vector{Float64})
    if length(residuals) < 3
        return 0.0
    end
    
    # Take last half of iterations to avoid initial transients
    start_idx = max(1, length(residuals) ÷ 2)
    recent_residuals = residuals[start_idx:end]
    
    if any(r <= 0 for r in recent_residuals)
        return 0.0
    end
    
    # Linear fit in log space
    log_residuals = log10.(recent_residuals)
    iterations = collect(1:length(log_residuals))
    
    # Simple linear regression
    n = length(iterations)
    sum_x = sum(iterations)
    sum_y = sum(log_residuals)
    sum_xy = sum(iterations .* log_residuals)
    sum_x2 = sum(iterations.^2)
    
    slope = (n * sum_xy - sum_x * sum_y) / (n * sum_x2 - sum_x^2)
    
    return -slope  # Negative slope indicates convergence
end

"""
Count iterations needed to reach convergence tolerance
"""
function count_iterations_to_convergence(residuals::Vector{Float64}, tolerance::Float64)
    for (i, residual) in enumerate(residuals)
        if residual < tolerance
            return i
        end
    end
    return length(residuals)  # Never converged
end

"""
Create detailed accuracy report
"""
function create_accuracy_report(field_errors::Dict{String, Dict{String, Float64}}, 
                               accuracy_metrics::Dict{String, Float64})
    report_lines = String[]
    
    push!(report_lines, "# Accuracy Analysis Report")
    push!(report_lines, "")
    push!(report_lines, "## Overall Assessment")
    
    if !isempty(accuracy_metrics)
        avg_l2 = get(accuracy_metrics, "average_l2_error", 0.0)
        max_error = get(accuracy_metrics, "max_error_any_field", 0.0)
        fields_compared = Int(get(accuracy_metrics, "fields_compared", 0))
        
        push!(report_lines, "- **Average L2 Error**: $(@sprintf("%.2e", avg_l2))")
        push!(report_lines, "- **Maximum Error**: $(@sprintf("%.2e", max_error))")
        push!(report_lines, "- **Fields Compared**: $fields_compared")
        
        summary = generate_accuracy_summary(accuracy_metrics, field_errors)
        push!(report_lines, "- **Overall Grade**: $(summary["overall_grade"])")
        push!(report_lines, "- **Assessment**: $(summary["assessment"])")
    end
    
    push!(report_lines, "")
    push!(report_lines, "## Field-by-Field Analysis")
    
    for (field_name, metrics) in field_errors
        push!(report_lines, "")
        push!(report_lines, "### $field_name")
        push!(report_lines, "")
        push!(report_lines, "| Metric | Value |")
        push!(report_lines, "|--------|-------|")
        push!(report_lines, "| L2 Error | $(@sprintf("%.2e", metrics["l2_error"])) |")
        push!(report_lines, "| Max Error | $(@sprintf("%.2e", metrics["max_error"])) |")
        push!(report_lines, "| RMS Error | $(@sprintf("%.2e", metrics["rms_error"])) |")
        push!(report_lines, "| Correlation | $(@sprintf("%.6f", metrics["correlation"])) |")
        push!(report_lines, "| R² | $(@sprintf("%.6f", metrics["r_squared"])) |")
        
        # Quality assessment
        l2_error = metrics["l2_error"]
        if l2_error < 1e-6
            quality = "✅ Excellent"
        elseif l2_error < 1e-4
            quality = "🟡 Good"
        elseif l2_error < 1e-2
            quality = "🟠 Acceptable"
        else
            quality = "❌ Poor"
        end
        
        push!(report_lines, "| Quality | $quality |")
    end
    
    return join(report_lines, "\\n")
end

# Helper function for string formatting removed - using @sprintf directly