"""
TurbulentFields.jl

Real turbulent flow field management with proper memory layout,
boundary condition handling, and field operations.
"""

include("MeshUtilities.jl")
using LinearAlgebra

# ============================================================================
# TURBULENT FIELD STRUCTURE
# ============================================================================

"""
Complete turbulent flow field structure with proper memory management
"""
mutable struct TurbulentFlowFields
    # Mesh information
    mesh::StructuredMesh
    
    # Primary flow variables (cell-centered)
    u::Array{Float64, 3}      # x-velocity
    v::Array{Float64, 3}      # y-velocity
    w::Array{Float64, 3}      # z-velocity
    p::Array{Float64, 3}      # pressure
    
    # RANS turbulence variables
    k::Array{Float64, 3}      # turbulent kinetic energy
    epsilon::Array{Float64, 3} # dissipation rate
    omega::Array{Float64, 3}   # specific dissipation rate
    nut::Array{Float64, 3}    # turbulent viscosity
    
    # Additional RANS fields
    nuTilda::Array{Float64, 3} # modified viscosity (Spalart-Allmaras)
    
    # Wall quantities
    wall_distance::Array{Float64, 3}
    y_plus::Array{Float64, 3}
    u_tau::Array{Float64, 3}
    
    # Material properties
    nu::Float64               # molecular viscosity
    rho::Float64              # density
    
    # Time information
    time::Float64
    dt::Float64
    
    # Gradient fields (computed on demand)
    velocity_gradients::Union{Nothing, Tuple}
    strain_rate_tensor::Union{Nothing, Tuple}
    vorticity_tensor::Union{Nothing, Tuple}
    
    function TurbulentFlowFields(mesh::StructuredMesh, nu::Float64=1e-5, rho::Float64=1.0)
        nx, ny, nz = mesh.nx, mesh.ny, mesh.nz
        
        # Initialize all fields
        u = zeros(nx, ny, nz)
        v = zeros(nx, ny, nz)
        w = zeros(nx, ny, nz)
        p = zeros(nx, ny, nz)
        
        k = zeros(nx, ny, nz)
        epsilon = zeros(nx, ny, nz)
        omega = zeros(nx, ny, nz)
        nut = zeros(nx, ny, nz)
        nuTilda = zeros(nx, ny, nz)
        
        wall_distance = calculate_wall_distances(mesh)
        y_plus = zeros(nx, ny, nz)
        u_tau = zeros(nx, ny, nz)
        
        new(mesh, u, v, w, p, k, epsilon, omega, nut, nuTilda,
            wall_distance, y_plus, u_tau, nu, rho, 0.0, 0.001,
            nothing, nothing, nothing)
    end
end

# ============================================================================
# FIELD INITIALIZATION
# ============================================================================

"""
Initialize velocity field with specified profile
"""
function initialize_velocity!(fields::TurbulentFlowFields, 
                            u_profile::Function, v_profile::Function, w_profile::Function)
    mesh = fields.mesh
    
    for i in 1:mesh.nx, j in 1:mesh.ny, k in 1:mesh.nz
        x = mesh.xc[i,j,k]
        y = mesh.yc[i,j,k]
        z = mesh.zc[i,j,k]
        
        fields.u[i,j,k] = u_profile(x, y, z)
        fields.v[i,j,k] = v_profile(x, y, z)
        fields.w[i,j,k] = w_profile(x, y, z)
    end
end

"""
Initialize turbulent kinetic energy based on turbulence intensity
"""
function initialize_turbulent_kinetic_energy!(fields::TurbulentFlowFields, 
                                            turbulence_intensity::Float64=0.05)
    for i in 1:fields.mesh.nx, j in 1:fields.mesh.ny, k in 1:fields.mesh.nz
        u_mag = sqrt(fields.u[i,j,k]^2 + fields.v[i,j,k]^2 + fields.w[i,j,k]^2)
        fields.k[i,j,k] = 1.5 * (turbulence_intensity * u_mag)^2
    end
end

"""
Initialize dissipation rate based on turbulent length scale
"""
function initialize_dissipation_rate!(fields::TurbulentFlowFields, 
                                     length_scale_ratio::Float64=0.07)
    Cmu = 0.09
    
    for i in 1:fields.mesh.nx, j in 1:fields.mesh.ny, k in 1:fields.mesh.nz
        # Estimate turbulent length scale as fraction of domain size
        L = length_scale_ratio * min(fields.mesh.dx * fields.mesh.nx, 
                                   fields.mesh.dy * fields.mesh.ny)
        
        k_local = fields.k[i,j,k]
        fields.epsilon[i,j,k] = Cmu^0.75 * k_local^1.5 / L
    end
end

"""
Initialize specific dissipation rate (omega) from k and epsilon
"""
function initialize_specific_dissipation_rate!(fields::TurbulentFlowFields)
    Cmu = 0.09
    
    for i in 1:fields.mesh.nx, j in 1:fields.mesh.ny, k in 1:fields.mesh.nz
        k_local = max(fields.k[i,j,k], 1e-12)
        eps_local = max(fields.epsilon[i,j,k], 1e-12)
        
        fields.omega[i,j,k] = eps_local / (Cmu * k_local)
    end
end

# ============================================================================
# GRADIENT COMPUTATIONS
# ============================================================================

"""
Compute and cache velocity gradients
"""
function compute_velocity_gradients!(fields::TurbulentFlowFields)
    gradients = calculate_velocity_gradients(fields.u, fields.v, fields.w, fields.mesh)
    fields.velocity_gradients = gradients
    return gradients
end

"""
Get velocity gradients (compute if not cached)
"""
function get_velocity_gradients(fields::TurbulentFlowFields)
    if fields.velocity_gradients === nothing
        compute_velocity_gradients!(fields)
    end
    return fields.velocity_gradients
end

"""
Compute and cache strain rate tensor
"""
function compute_strain_rate_tensor!(fields::TurbulentFlowFields)
    gradients = get_velocity_gradients(fields)
    dudx, dudy, dudz, dvdx, dvdy, dvdz, dwdx, dwdy, dwdz = gradients
    
    strain_tensor = calculate_strain_rate_tensor(dudx, dudy, dudz, dvdx, dvdy, dvdz, dwdx, dwdy, dwdz)
    fields.strain_rate_tensor = strain_tensor
    return strain_tensor
end

"""
Get strain rate tensor (compute if not cached)
"""
function get_strain_rate_tensor(fields::TurbulentFlowFields)
    if fields.strain_rate_tensor === nothing
        compute_strain_rate_tensor!(fields)
    end
    return fields.strain_rate_tensor
end

"""
Compute strain rate magnitude
"""
function compute_strain_rate_magnitude(fields::TurbulentFlowFields)
    S11, S22, S33, S12, S13, S23 = get_strain_rate_tensor(fields)
    return calculate_strain_rate_magnitude(S11, S22, S33, S12, S13, S23)
end

"""
Compute vorticity tensor and magnitude
"""
function compute_vorticity_tensor!(fields::TurbulentFlowFields)
    gradients = get_velocity_gradients(fields)
    dudx, dudy, dudz, dvdx, dvdy, dvdz, dwdx, dwdy, dwdz = gradients
    
    vorticity_tensor = calculate_vorticity_tensor(dudx, dudy, dudz, dvdx, dvdy, dvdz, dwdx, dwdy, dwdz)
    fields.vorticity_tensor = vorticity_tensor
    return vorticity_tensor
end

"""
Get vorticity magnitude
"""
function compute_vorticity_magnitude(fields::TurbulentFlowFields)
    if fields.vorticity_tensor === nothing
        compute_vorticity_tensor!(fields)
    end
    Omega12, Omega13, Omega23 = fields.vorticity_tensor
    return calculate_vorticity_magnitude(Omega12, Omega13, Omega23)
end

# ============================================================================
# WALL FUNCTION UTILITIES
# ============================================================================

"""
Update y+ values based on current flow state
"""
function update_y_plus!(fields::TurbulentFlowFields)
    mesh = fields.mesh
    
    # Calculate friction velocity at wall cells
    calculate_friction_velocity!(fields)
    
    for i in 1:mesh.nx, j in 1:mesh.ny, k in 1:mesh.nz
        y_wall = fields.wall_distance[i,j,k]
        u_tau_local = fields.u_tau[i,j,k]
        
        fields.y_plus[i,j,k] = y_wall * u_tau_local / fields.nu
    end
end

"""
Calculate friction velocity at near-wall cells
"""
function calculate_friction_velocity!(fields::TurbulentFlowFields)
    mesh = fields.mesh
    
    # For each wall boundary
    for (name, patch) in mesh.boundaries
        if patch.type == :wall
            wall_cells = get_wall_adjacent_cells(mesh, name)
            
            for cell_idx in wall_cells
                i, j, k = cell_idx.I
                
                # Calculate velocity magnitude
                u_mag = sqrt(fields.u[i,j,k]^2 + fields.v[i,j,k]^2 + fields.w[i,j,k]^2)
                
                # Estimate friction velocity using wall law
                y_wall = fields.wall_distance[i,j,k]
                
                # Newton iteration for u_tau
                u_tau = estimate_friction_velocity_newton(u_mag, y_wall, fields.nu)
                fields.u_tau[i,j,k] = u_tau
            end
        end
    end
end

"""
Newton iteration to find friction velocity
"""
function estimate_friction_velocity_newton(U_p::Float64, y::Float64, nu::Float64; 
                                         kappa::Float64=0.41, E::Float64=9.8,
                                         max_iter::Int=20, tol::Float64=1e-6)
    # Initial guess
    u_tau = sqrt(nu * U_p / y)
    
    for iter in 1:max_iter
        y_plus = y * u_tau / nu
        
        # Log law or linear law
        if y_plus > 11.0
            u_plus = (1.0 / kappa) * log(E * y_plus)
        else
            u_plus = y_plus
        end
        
        # Newton update
        u_tau_new = U_p / u_plus
        
        if abs(u_tau_new - u_tau) / (u_tau + 1e-12) < tol
            return u_tau_new
        end
        
        u_tau = 0.7 * u_tau + 0.3 * u_tau_new  # Under-relaxation
    end
    
    return u_tau
end

# ============================================================================
# BOUNDARY CONDITIONS
# ============================================================================

"""
Apply no-slip boundary conditions at walls
"""
function apply_no_slip_walls!(fields::TurbulentFlowFields)
    mesh = fields.mesh
    
    for (name, patch) in mesh.boundaries
        if patch.type == :wall
            apply_wall_boundary_condition!(fields, name, patch)
        end
    end
end

"""
Apply wall boundary condition for a specific patch
"""
function apply_wall_boundary_condition!(fields::TurbulentFlowFields, name::String, patch::BoundaryPatch)
    mesh = fields.mesh
    
    for face_idx in patch.face_indices
        i, j, k = face_idx.I
        
        # Apply no-slip condition
        if name == "bottom"
            # Bottom wall - may have moving lid
            fields.u[i,1,k] = 0.0
            fields.v[i,1,k] = 0.0
            fields.w[i,1,k] = 0.0
        elseif name == "top"
            # Top wall - cavity lid
            fields.u[i,mesh.ny,k] = 1.0  # Moving lid
            fields.v[i,mesh.ny,k] = 0.0
            fields.w[i,mesh.ny,k] = 0.0
        elseif name == "left" || name == "right"
            # Side walls
            fields.u[i,j,k] = 0.0
            fields.v[i,j,k] = 0.0
            fields.w[i,j,k] = 0.0
        end
    end
end

"""
Apply inlet boundary conditions
"""
function apply_inlet_boundary_condition!(fields::TurbulentFlowFields, 
                                        u_inlet::Float64, k_inlet::Float64, epsilon_inlet::Float64)
    mesh = fields.mesh
    
    # Apply to left boundary (inlet)
    for j in 1:mesh.ny, k in 1:mesh.nz
        fields.u[1,j,k] = u_inlet
        fields.v[1,j,k] = 0.0
        fields.w[1,j,k] = 0.0
        fields.k[1,j,k] = k_inlet
        fields.epsilon[1,j,k] = epsilon_inlet
    end
end

"""
Apply outlet boundary conditions (zero gradient)
"""
function apply_outlet_boundary_condition!(fields::TurbulentFlowFields)
    mesh = fields.mesh
    nx = mesh.nx
    
    # Zero gradient at outlet
    for j in 1:mesh.ny, k in 1:mesh.nz
        fields.u[nx,j,k] = fields.u[nx-1,j,k]
        fields.v[nx,j,k] = fields.v[nx-1,j,k]
        fields.w[nx,j,k] = fields.w[nx-1,j,k]
        fields.p[nx,j,k] = fields.p[nx-1,j,k]
        fields.k[nx,j,k] = fields.k[nx-1,j,k]
        fields.epsilon[nx,j,k] = fields.epsilon[nx-1,j,k]
        fields.nut[nx,j,k] = fields.nut[nx-1,j,k]
    end
end

# ============================================================================
# FIELD UTILITIES
# ============================================================================

"""
Calculate turbulent production term Pk = nut * |S|^2
"""
function calculate_turbulent_production(fields::TurbulentFlowFields)
    S_magnitude = compute_strain_rate_magnitude(fields)
    return fields.nut .* S_magnitude.^2
end

"""
Apply realizability constraints to turbulent quantities
"""
function apply_realizability_constraints!(fields::TurbulentFlowFields)
    # Bound k and epsilon to positive values
    @. fields.k = max(fields.k, 1e-12)
    @. fields.epsilon = max(fields.epsilon, 1e-12)
    @. fields.omega = max(fields.omega, 1e-12)
    
    # Bound turbulent viscosity
    @. fields.nut = max(fields.nut, 0.0)
    @. fields.nut = min(fields.nut, 1000.0 * fields.nu)  # Upper limit
end

"""
Calculate turbulent time scale
"""
function calculate_turbulent_time_scale(fields::TurbulentFlowFields)
    return fields.k ./ (fields.epsilon .+ 1e-12)
end

"""
Calculate turbulent length scale
"""
function calculate_turbulent_length_scale(fields::TurbulentFlowFields)
    Cmu = 0.09
    return Cmu^0.75 * fields.k.^1.5 ./ (fields.epsilon .+ 1e-12)
end

"""
Calculate Reynolds number based on turbulent quantities
"""
function calculate_turbulent_reynolds_number(fields::TurbulentFlowFields)
    return fields.k.^2 ./ (fields.nu * (fields.epsilon .+ 1e-12))
end

# ============================================================================
# FIELD I/O AND DIAGNOSTICS
# ============================================================================

"""
Print field statistics
"""
function print_field_statistics(fields::TurbulentFlowFields)
    println("Field Statistics:")
    println("  u: min=$(minimum(fields.u)), max=$(maximum(fields.u)), mean=$(mean(fields.u))")
    println("  v: min=$(minimum(fields.v)), max=$(maximum(fields.v)), mean=$(mean(fields.v))")
    println("  p: min=$(minimum(fields.p)), max=$(maximum(fields.p)), mean=$(mean(fields.p))")
    println("  k: min=$(minimum(fields.k)), max=$(maximum(fields.k)), mean=$(mean(fields.k))")
    println("  ε: min=$(minimum(fields.epsilon)), max=$(maximum(fields.epsilon)), mean=$(mean(fields.epsilon))")
    println("  νt: min=$(minimum(fields.nut)), max=$(maximum(fields.nut)), mean=$(mean(fields.nut))")
end

"""
Calculate field norms for convergence checking
"""
function calculate_field_norms(fields::TurbulentFlowFields)
    return Dict(
        "u" => norm(fields.u),
        "v" => norm(fields.v),
        "w" => norm(fields.w),
        "p" => norm(fields.p),
        "k" => norm(fields.k),
        "epsilon" => norm(fields.epsilon),
        "nut" => norm(fields.nut)
    )
end

"""
Clear cached gradients (call after field updates)
"""
function clear_cached_gradients!(fields::TurbulentFlowFields)
    fields.velocity_gradients = nothing
    fields.strain_rate_tensor = nothing
    fields.vorticity_tensor = nothing
end

# ============================================================================
# EXPORTS
# ============================================================================

export TurbulentFlowFields
export initialize_velocity!, initialize_turbulent_kinetic_energy!, initialize_dissipation_rate!
export initialize_specific_dissipation_rate!
export compute_velocity_gradients!, compute_strain_rate_tensor!, compute_strain_rate_magnitude
export compute_vorticity_tensor!, compute_vorticity_magnitude
export update_y_plus!, calculate_friction_velocity!
export apply_no_slip_walls!, apply_inlet_boundary_condition!, apply_outlet_boundary_condition!
export calculate_turbulent_production, apply_realizability_constraints!
export calculate_turbulent_time_scale, calculate_turbulent_length_scale, calculate_turbulent_reynolds_number
export print_field_statistics, calculate_field_norms, clear_cached_gradients!