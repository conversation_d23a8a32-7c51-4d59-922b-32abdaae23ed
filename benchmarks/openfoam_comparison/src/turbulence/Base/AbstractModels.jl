"""
AbstractModels.jl

Base types and interfaces for turbulence models following OpenFOAM's architecture.
Provides abstract types for RANS, LES, and hybrid models with common interfaces.
"""

using LinearAlgebra

# ============================================================================
# ABSTRACT BASE TYPES
# ============================================================================

"""
Abstract base type for all turbulence models

Similar to OpenFOAM's turbulenceModel base class. All turbulence models
must implement:
- `calculate_turbulent_viscosity!(fields, model)`
- `solve_turbulence_equations!(fields, model, dt)`
- `apply_boundary_conditions!(fields, model)`
- `get_model_coefficients(model)`
"""
abstract type AbstractTurbulenceModel end

"""
Abstract base for Reynolds-Averaged Navier-Stokes (RANS) models
"""
abstract type AbstractRANSModel <: AbstractTurbulenceModel end

"""
Abstract base for Large Eddy Simulation (LES) models
"""
abstract type AbstractLESModel <: AbstractTurbulenceModel end

"""
Abstract base for Detached Eddy Simulation (DES) models
"""
abstract type AbstractDESModel <: AbstractTurbulenceModel end

"""
Abstract base for turbulence model coefficients
"""
abstract type AbstractModelCoefficients end

# ============================================================================
# MODEL TYPE TRAITS
# ============================================================================

"""
Model complexity trait
"""
abstract type ModelComplexity end
struct ZeroEquation <: ModelComplexity end
struct OneEquation <: ModelComplexity end
struct TwoEquation <: ModelComplexity end
struct AlgebraicStress <: ModelComplexity end

"""
Get model complexity for dispatch
"""
model_complexity(::AbstractTurbulenceModel) = TwoEquation()

"""
Check if model is steady or unsteady
"""
is_steady(::AbstractTurbulenceModel) = false
is_steady(::AbstractRANSModel) = false  # RANS can be unsteady (URANS)
is_steady(::AbstractLESModel) = false   # LES is always unsteady

# ============================================================================
# REQUIRED INTERFACES
# ============================================================================

"""
Calculate turbulent viscosity (must be implemented by all models)
"""
function calculate_turbulent_viscosity!(fields, model::AbstractTurbulenceModel)
    error("calculate_turbulent_viscosity! not implemented for $(typeof(model))")
end

"""
Solve turbulence transport equations (must be implemented by all models)
"""
function solve_turbulence_equations!(fields, model::AbstractTurbulenceModel, dt)
    error("solve_turbulence_equations! not implemented for $(typeof(model))")
end

"""
Apply turbulence boundary conditions (must be implemented by all models)
"""
function apply_boundary_conditions!(fields, model::AbstractTurbulenceModel)
    error("apply_boundary_conditions! not implemented for $(typeof(model))")
end

"""
Get model coefficients (must be implemented by all models)
"""
function get_model_coefficients(model::AbstractTurbulenceModel)
    error("get_model_coefficients not implemented for $(typeof(model))")
end

# ============================================================================
# COMMON INTERFACES (WITH DEFAULTS)
# ============================================================================

"""
Initialize turbulence fields (can be overridden)
"""
function initialize_fields!(fields, model::AbstractTurbulenceModel)
    # Default: do nothing (for algebraic models)
    return nothing
end

"""
Get required field names for this model
"""
function required_fields(model::AbstractTurbulenceModel)
    # Default: just velocity and pressure
    return ["U", "p"]
end

"""
Get turbulence field names managed by this model
"""
function turbulence_fields(model::AbstractTurbulenceModel)
    # Default: just turbulent viscosity
    return ["nut"]
end

"""
Validate model setup
"""
function validate_model(model::AbstractTurbulenceModel)
    # Check coefficients are physical
    coeffs = get_model_coefficients(model)
    return validate_coefficients(coeffs)
end

"""
Model name for reporting
"""
model_name(model::AbstractTurbulenceModel) = string(typeof(model))

"""
Short model description
"""
model_info(model::AbstractTurbulenceModel) = "$(model_name(model)) turbulence model"

# ============================================================================
# COEFFICIENT VALIDATION
# ============================================================================

"""
Validate coefficient values are physical
"""
function validate_coefficients(coeffs::AbstractModelCoefficients)
    # Default: check all fields are positive
    for field in fieldnames(typeof(coeffs))
        value = getfield(coeffs, field)
        if isa(value, Number) && value <= 0
            return false
        end
    end
    return true
end

# ============================================================================
# SHARED CALCULATIONS
# ============================================================================

"""
Calculate strain rate magnitude squared: 2*Sij*Sij
"""
function strain_rate_magnitude_squared(u, v, w, dx, dy, dz)
    # Velocity gradients
    dudx = diff(u, dims=1) / dx
    dudy = diff(u, dims=2) / dy
    dvdx = diff(v, dims=1) / dx
    dvdy = diff(v, dims=2) / dy
    
    # For 2D flows (w=0)
    if all(w .== 0)
        # Sij = 0.5*(dui/dxj + duj/dxi)
        S2 = 2.0 * (dudx.^2 + dvdy.^2) + (dudy + dvdx).^2
    else
        # 3D case
        dudz = diff(u, dims=3) / dz
        dvdz = diff(v, dims=3) / dz
        dwdx = diff(w, dims=1) / dx
        dwdy = diff(w, dims=2) / dy
        dwdz = diff(w, dims=3) / dz
        
        S2 = 2.0 * (dudx.^2 + dvdy.^2 + dwdz.^2) +
             (dudy + dvdx).^2 + (dudz + dwdx).^2 + (dvdz + dwdy).^2
    end
    
    return S2
end

"""
Calculate vorticity magnitude squared: Ωij*Ωij
"""
function vorticity_magnitude_squared(u, v, w, dx, dy, dz)
    # Velocity gradients
    dudy = diff(u, dims=2) / dy
    dvdx = diff(v, dims=1) / dx
    
    # For 2D flows
    if all(w .== 0)
        # Only z-component of vorticity
        omega_z = dvdx - dudy
        return omega_z.^2
    else
        # 3D case
        dudz = diff(u, dims=3) / dz
        dvdz = diff(v, dims=3) / dz
        dwdx = diff(w, dims=1) / dx
        dwdy = diff(w, dims=2) / dy
        
        omega_x = dwdy - dvdz
        omega_y = dudz - dwdx
        omega_z = dvdx - dudy
        
        return omega_x.^2 + omega_y.^2 + omega_z.^2
    end
end

# ============================================================================
# LIMITERS AND BOUNDS
# ============================================================================

"""
Apply realizability constraints to turbulence quantities
"""
function apply_realizability!(k, epsilon, omega=nothing)
    # Ensure k is non-negative
    @. k = max(k, 1e-12)
    
    # Ensure epsilon is positive
    @. epsilon = max(epsilon, 1e-12)
    
    # Ensure omega is positive if provided
    if omega !== nothing
        @. omega = max(omega, 1e-12)
    end
end

"""
Limit turbulent viscosity to physical values
"""
function limit_turbulent_viscosity!(nut, nu_molecular, max_ratio=1e5)
    # Ensure non-negative
    @. nut = max(nut, 0.0)
    
    # Limit maximum ratio
    @. nut = min(nut, max_ratio * nu_molecular)
end

# ============================================================================
# MODEL SELECTION
# ============================================================================

"""
Runtime turbulence model selection based on string identifier
"""
function select_turbulence_model(model_type::String, coeffs_dict::Dict=Dict())
    model_map = Dict(
        "kEpsilon" => :k_epsilon,
        "RNGkEpsilon" => :rng_k_epsilon,
        "realizableKE" => :realizable_ke,
        "kOmega" => :k_omega,
        "kOmegaSST" => :k_omega_sst,
        "SpalartAllmaras" => :spalart_allmaras,
        "Smagorinsky" => :smagorinsky,
        "WALE" => :wale,
        "laminar" => :laminar
    )
    
    if haskey(model_map, model_type)
        return model_map[model_type]
    else
        @warn "Unknown turbulence model: $model_type, defaulting to laminar"
        return :laminar
    end
end