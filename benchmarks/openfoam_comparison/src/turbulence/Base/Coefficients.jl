"""
Coefficients.jl

Runtime coefficient management system for turbulence models.
Allows loading coefficients from dictionaries and modifying at runtime.
"""

# ============================================================================
# BASE COEFFICIENT TYPES
# ============================================================================

"""
Base k-epsilon model coefficients
"""
Base.@kwdef mutable struct KEpsilonCoefficients <: AbstractModelCoefficients
    Cmu::Float64 = 0.09       # Turbulent viscosity constant
    C1::Float64 = 1.44        # Production coefficient
    C2::Float64 = 1.92        # Dissipation coefficient  
    C3::Float64 = 0.0         # Buoyancy coefficient (usually 0 or 1)
    sigmak::Float64 = 1.0     # Prandtl number for k
    sigmaEps::Float64 = 1.3   # Prandtl number for epsilon
end

"""
RNG k-epsilon model coefficients
"""
Base.@kwdef mutable struct RNGKEpsilonCoefficients <: AbstractModelCoefficients
    Cmu::Float64 = 0.0845
    C1::Float64 = 1.42
    C2::Float64 = 1.68
    C3::Float64 = 0.0
    sigmak::Float64 = 0.7194
    sigmaEps::Float64 = 0.7194
    eta0::Float64 = 4.38      # RNG specific
    beta::Float64 = 0.012     # RNG specific
end

"""
Realizable k-epsilon model coefficients
"""
Base.@kwdef mutable struct RealizableKECoefficients <: AbstractModelCoefficients
    A0::Float64 = 4.04        # Realizable specific
    C2::Float64 = 1.9         # Modified in realizable
    sigmak::Float64 = 1.0
    sigmaEps::Float64 = 1.2
    # C1 and Cmu are calculated dynamically in realizable model
end

"""
Standard k-omega model coefficients
"""
Base.@kwdef mutable struct KOmegaCoefficients <: AbstractModelCoefficients
    betaStar::Float64 = 0.09   # β* 
    beta::Float64 = 0.072      # β
    gamma::Float64 = 0.52      # γ (production)
    sigmak::Float64 = 2.0      # σ_k
    sigmaOmega::Float64 = 2.0  # σ_ω
    alphaOmega::Float64 = 0.5  # α_ω (for compressible)
end

"""
k-omega SST model coefficients (uses blending)
"""
Base.@kwdef mutable struct KOmegaSSTCoefficients <: AbstractModelCoefficients
    # Inner coefficients (near wall)
    betaStar::Float64 = 0.09
    beta1::Float64 = 0.075
    gamma1::Float64 = 0.553
    sigmak1::Float64 = 1.176
    sigmaOmega1::Float64 = 2.0
    # Outer coefficients (free stream)
    beta2::Float64 = 0.0828
    gamma2::Float64 = 0.44
    sigmak2::Float64 = 1.0
    sigmaOmega2::Float64 = 1.168
    # SST specific
    a1::Float64 = 0.31
    c1::Float64 = 10.0
end

"""
Spalart-Allmaras model coefficients
"""
Base.@kwdef mutable struct SpalartAllmarasCoefficients <: AbstractModelCoefficients
    Cb1::Float64 = 0.1355
    Cb2::Float64 = 0.622
    Cw1::Float64 = 3.24       # Computed from Cb1/kappa^2 + (1+Cb2)/sigma
    Cw2::Float64 = 0.3
    Cw3::Float64 = 2.0
    Cv1::Float64 = 7.1
    Cs::Float64 = 0.3
    sigma::Float64 = 2.0/3.0
    kappa::Float64 = 0.41
end

"""
Smagorinsky LES model coefficients
"""
Base.@kwdef mutable struct SmagorinskyCoefficients <: AbstractModelCoefficients
    Cs::Float64 = 0.16        # Smagorinsky constant (0.1-0.2)
    Ce::Float64 = 1.048       # Model constant for k equation
    Ck::Float64 = 0.07        # Model constant
end

"""
WALE LES model coefficients
"""
Base.@kwdef mutable struct WALECoefficients <: AbstractModelCoefficients
    Cw::Float64 = 0.325       # WALE constant
    Ck::Float64 = 0.07        # Model constant
end

# ============================================================================
# COEFFICIENT LOADING AND MANAGEMENT
# ============================================================================

"""
Load coefficients from dictionary (OpenFOAM style)
"""
function load_coefficients(::Type{T}, dict::Dict) where T <: AbstractModelCoefficients
    # Start with defaults
    coeffs = T()
    
    # Override with dictionary values
    for (key, value) in dict
        field_sym = Symbol(key)
        if hasproperty(coeffs, field_sym)
            setproperty!(coeffs, field_sym, Float64(value))
        else
            @warn "Unknown coefficient $key for $(T), ignoring"
        end
    end
    
    return coeffs
end

"""
Convert coefficients to dictionary
"""
function coefficients_to_dict(coeffs::AbstractModelCoefficients)
    dict = Dict{String, Float64}()
    for field in fieldnames(typeof(coeffs))
        dict[string(field)] = getfield(coeffs, field)
    end
    return dict
end

"""
Pretty print coefficients
"""
function Base.show(io::IO, coeffs::AbstractModelCoefficients)
    println(io, "$(typeof(coeffs)):")
    for field in fieldnames(typeof(coeffs))
        value = getfield(coeffs, field)
        println(io, "  $field = $value")
    end
end

# ============================================================================
# COEFFICIENT BLENDING (FOR HYBRID MODELS)
# ============================================================================

"""
Blend two coefficient sets (for SST and similar models)
"""
function blend_coefficients(coeffs1::T, coeffs2::T, blend_factor::Float64) where T
    blended = T()
    
    # Linear blending: coeff = F1*coeff1 + (1-F1)*coeff2
    for field in fieldnames(T)
        val1 = getfield(coeffs1, field)
        val2 = getfield(coeffs2, field)
        blended_val = blend_factor * val1 + (1 - blend_factor) * val2
        setfield!(blended, field, blended_val)
    end
    
    return blended
end

"""
SST blending function F1
"""
function sst_blending_function(k, omega, nut, wall_dist, nu)
    # Simplified SST F1 function
    # In practice, this involves gradients and is more complex
    
    CDkOmega = 1e-20  # Cross-diffusion term (simplified)
    
    arg1_1 = sqrt(k) / (0.09 * omega * wall_dist)
    arg1_2 = 500 * nu / (wall_dist^2 * omega)
    arg1_3 = 4 * k / (CDkOmega * wall_dist^2)
    
    arg1 = min(max(arg1_1, arg1_2), arg1_3)
    F1 = tanh(arg1^4)
    
    return F1
end

# ============================================================================
# DYNAMIC COEFFICIENT ADJUSTMENT
# ============================================================================

"""
Adjust coefficients based on flow conditions (for advanced models)
"""
function adjust_coefficients!(coeffs::AbstractModelCoefficients, flow_state::Dict)
    # Default: no adjustment
    # Can be overridden for specific models
    return coeffs
end

"""
Realizable k-epsilon dynamic Cmu calculation
"""
function calculate_realizable_cmu(strain_rate, rotation_rate, k, epsilon)
    # Realizable constraint on Cmu
    U_star = sqrt(strain_rate^2 + rotation_rate^2)
    
    # Model constants
    A0 = 4.04
    As = sqrt(6) * cos(1/3 * acos(sqrt(6) * strain_rate / U_star))
    
    # Dynamic Cmu
    Cmu = 1.0 / (A0 + As * U_star * k / epsilon)
    
    return clamp(Cmu, 0.0, 0.09)  # Bounded for stability
end

# ============================================================================
# MODEL-SPECIFIC COEFFICIENT HELPERS
# ============================================================================

"""
Get default coefficients for a model type
"""
function default_coefficients(model_type::Symbol)
    coeff_map = Dict(
        :k_epsilon => KEpsilonCoefficients(),
        :rng_k_epsilon => RNGKEpsilonCoefficients(),
        :realizable_ke => RealizableKECoefficients(),
        :k_omega => KOmegaCoefficients(),
        :k_omega_sst => KOmegaSSTCoefficients(),
        :spalart_allmaras => SpalartAllmarasCoefficients(),
        :smagorinsky => SmagorinskyCoefficients(),
        :wale => WALECoefficients()
    )
    
    return get(coeff_map, model_type, nothing)
end

"""
Validate coefficient ranges
"""
function validate_coefficient_ranges(coeffs::KEpsilonCoefficients)
    valid = true
    
    # Check typical ranges
    if !(0.08 <= coeffs.Cmu <= 0.11)
        @warn "Cmu = $(coeffs.Cmu) outside typical range [0.08, 0.11]"
        valid = false
    end
    
    if !(1.3 <= coeffs.C1 <= 1.5)
        @warn "C1 = $(coeffs.C1) outside typical range [1.3, 1.5]"
        valid = false
    end
    
    if !(1.8 <= coeffs.C2 <= 2.0)
        @warn "C2 = $(coeffs.C2) outside typical range [1.8, 2.0]"
        valid = false
    end
    
    return valid
end