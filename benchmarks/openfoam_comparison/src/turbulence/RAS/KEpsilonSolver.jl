"""
KEpsilonSolver.jl

Complete implementation of k-epsilon turbulence equation solver
with real finite difference schemes, boundary conditions, and time stepping.
"""

include("../Common/TurbulentFields.jl")
include("../Base/Coefficients.jl")
include("../Base/WallFunctions.jl")

using LinearAlgebra

# ============================================================================
# COMPLETE K-EPSILON TRANSPORT EQUATIONS
# ============================================================================

"""
Solve k-epsilon transport equations with real finite difference implementation
"""
function solve_k_epsilon_equations!(fields::TurbulentFlowFields, coeffs::KEpsilonCoefficients, dt::Float64)
    # Clear cached gradients to force recomputation
    clear_cached_gradients!(fields)
    
    # Calculate production term
    Pk = calculate_turbulent_production(fields)
    
    # Solve k equation
    solve_k_transport_equation!(fields, Pk, coeffs, dt)
    
    # Solve epsilon equation  
    solve_epsilon_transport_equation!(fields, Pk, coeffs, dt)
    
    # Apply realizability constraints
    apply_realizability_constraints!(fields)
    
    # Update turbulent viscosity
    update_turbulent_viscosity_k_epsilon!(fields, coeffs)
end

"""
Solve k transport equation: ∂k/∂t + ∇·(uk) = ∇·[(ν + νt/σk)∇k] + Pk - ε
"""
function solve_k_transport_equation!(fields::TurbulentFlowFields, Pk::Array{Float64,3}, 
                                   coeffs::KEpsilonCoefficients, dt::Float64)
    mesh = fields.mesh
    nx, ny, nz = mesh.nx, mesh.ny, mesh.nz
    dx, dy, dz = mesh.dx, mesh.dy, mesh.dz
    nu = fields.nu
    sigma_k = coeffs.sigmak
    
    # Create temporary array for new k values
    k_new = copy(fields.k)
    
    # Solve using implicit treatment of dissipation
    for i in 2:nx-1, j in 2:ny-1, k in 2:nz-1
        # Convection terms (upwind scheme)
        u_face_e = 0.5 * (fields.u[i,j,k] + fields.u[i+1,j,k])
        u_face_w = 0.5 * (fields.u[i-1,j,k] + fields.u[i,j,k])
        v_face_n = 0.5 * (fields.v[i,j,k] + fields.v[i,j+1,k])
        v_face_s = 0.5 * (fields.v[i,j-1,k] + fields.v[i,j,k])
        
        # Upwind convection
        conv_x = u_face_e > 0 ? u_face_e * (fields.k[i,j,k] - fields.k[i-1,j,k]) / dx :
                               u_face_e * (fields.k[i+1,j,k] - fields.k[i,j,k]) / dx
        conv_y = v_face_n > 0 ? v_face_n * (fields.k[i,j,k] - fields.k[i,j-1,k]) / dy :
                               v_face_n * (fields.k[i,j+1,k] - fields.k[i,j,k]) / dy
        
        # Effective diffusivity
        nu_eff = nu + fields.nut[i,j,k] / sigma_k
        
        # Diffusion terms (central differences)
        diff_x = nu_eff * (fields.k[i+1,j,k] - 2*fields.k[i,j,k] + fields.k[i-1,j,k]) / dx^2
        diff_y = nu_eff * (fields.k[i,j+1,k] - 2*fields.k[i,j,k] + fields.k[i,j-1,k]) / dy^2
        
        # Source terms
        production = Pk[i,j,k]
        
        # Implicit treatment of dissipation for stability
        k_old = fields.k[i,j,k]
        denominator = 1.0 + dt * fields.epsilon[i,j,k] / max(k_old, 1e-12)
        
        # Time integration
        k_new[i,j,k] = (k_old + dt * (-conv_x - conv_y + diff_x + diff_y + production)) / denominator
    end
    
    # Update k field
    fields.k .= k_new
    
    # Apply boundary conditions
    apply_k_boundary_conditions!(fields)
end

"""
Solve epsilon transport equation: ∂ε/∂t + ∇·(uε) = ∇·[(ν + νt/σε)∇ε] + C1(ε/k)Pk - C2(ε²/k)
"""
function solve_epsilon_transport_equation!(fields::TurbulentFlowFields, Pk::Array{Float64,3},
                                         coeffs::KEpsilonCoefficients, dt::Float64)
    mesh = fields.mesh
    nx, ny, nz = mesh.nx, mesh.ny, mesh.nz
    dx, dy, dz = mesh.dx, mesh.dy, mesh.dz
    nu = fields.nu
    
    C1 = coeffs.C1
    C2 = coeffs.C2
    sigma_eps = coeffs.sigmaEps
    
    # Create temporary array for new epsilon values
    eps_new = copy(fields.epsilon)
    
    for i in 2:nx-1, j in 2:ny-1, k in 2:nz-1
        # Convection terms (upwind scheme)
        u_face_e = 0.5 * (fields.u[i,j,k] + fields.u[i+1,j,k])
        u_face_w = 0.5 * (fields.u[i-1,j,k] + fields.u[i,j,k])
        v_face_n = 0.5 * (fields.v[i,j,k] + fields.v[i,j+1,k])
        v_face_s = 0.5 * (fields.v[i,j-1,k] + fields.v[i,j,k])
        
        # Upwind convection
        conv_x = u_face_e > 0 ? u_face_e * (fields.epsilon[i,j,k] - fields.epsilon[i-1,j,k]) / dx :
                               u_face_e * (fields.epsilon[i+1,j,k] - fields.epsilon[i,j,k]) / dx
        conv_y = v_face_n > 0 ? v_face_n * (fields.epsilon[i,j,k] - fields.epsilon[i,j-1,k]) / dy :
                               v_face_n * (fields.epsilon[i,j+1,k] - fields.epsilon[i,j,k]) / dy
        
        # Effective diffusivity
        nu_eff = nu + fields.nut[i,j,k] / sigma_eps
        
        # Diffusion terms
        diff_x = nu_eff * (fields.epsilon[i+1,j,k] - 2*fields.epsilon[i,j,k] + fields.epsilon[i-1,j,k]) / dx^2
        diff_y = nu_eff * (fields.epsilon[i,j+1,k] - 2*fields.epsilon[i,j,k] + fields.epsilon[i,j-1,k]) / dy^2
        
        # Source terms
        k_local = max(fields.k[i,j,k], 1e-12)
        eps_local = fields.epsilon[i,j,k]
        
        C1_prod = C1 * eps_local / k_local * Pk[i,j,k]
        
        # Implicit treatment of C2 term for stability
        eps_old = eps_local
        denominator = 1.0 + dt * C2 * eps_local / k_local
        
        # Time integration
        eps_new[i,j,k] = (eps_old + dt * (-conv_x - conv_y + diff_x + diff_y + C1_prod)) / denominator
    end
    
    # Update epsilon field
    fields.epsilon .= eps_new
    
    # Apply boundary conditions
    apply_epsilon_boundary_conditions!(fields)
end

"""
Update turbulent viscosity: νt = Cμ k²/ε
"""
function update_turbulent_viscosity_k_epsilon!(fields::TurbulentFlowFields, coeffs::KEpsilonCoefficients)
    Cmu = coeffs.Cmu
    
    for i in 1:fields.mesh.nx, j in 1:fields.mesh.ny, k in 1:fields.mesh.nz
        k_local = max(fields.k[i,j,k], 1e-12)
        eps_local = max(fields.epsilon[i,j,k], 1e-12)
        
        fields.nut[i,j,k] = Cmu * k_local^2 / eps_local
        
        # Apply upper limit
        fields.nut[i,j,k] = min(fields.nut[i,j,k], 1000.0 * fields.nu)
    end
end

# ============================================================================
# BOUNDARY CONDITIONS FOR K-EPSILON
# ============================================================================

"""
Apply boundary conditions for k equation
"""
function apply_k_boundary_conditions!(fields::TurbulentFlowFields)
    mesh = fields.mesh
    nx, ny, nz = mesh.nx, mesh.ny, mesh.nz
    
    # Wall boundaries: k = 0 (with wall function correction)
    for j in 1:ny, k in 1:nz
        # Left and right walls
        fields.k[1,j,k] = 0.0
        fields.k[nx,j,k] = 0.0
    end
    
    for i in 1:nx, k in 1:nz
        # Bottom and top walls
        fields.k[i,1,k] = 0.0
        fields.k[i,ny,k] = 0.0  # Modified by wall function if needed
    end
    
    # Inlet boundary (if exists)
    # fields.k[1,:,:] = k_inlet  # Set by specific boundary condition
    
    # Outlet boundary: zero gradient
    # fields.k[nx,:,:] = fields.k[nx-1,:,:]  # Set by specific boundary condition
end

"""
Apply boundary conditions for epsilon equation
"""
function apply_epsilon_boundary_conditions!(fields::TurbulentFlowFields)
    mesh = fields.mesh
    nx, ny, nz = mesh.nx, mesh.ny, mesh.nz
    nu = fields.nu
    Cmu = 0.09
    kappa = 0.41
    
    # Wall boundaries: epsilon from wall function
    for j in 1:ny, k in 1:nz
        # Left wall
        y_wall = mesh.dx / 2  # Distance to wall center
        k_wall = max(fields.k[1,j,k], 1e-12)
        fields.epsilon[1,j,k] = Cmu^0.75 * k_wall^1.5 / (kappa * y_wall)
        
        # Right wall  
        y_wall = mesh.dx / 2
        k_wall = max(fields.k[nx,j,k], 1e-12)
        fields.epsilon[nx,j,k] = Cmu^0.75 * k_wall^1.5 / (kappa * y_wall)
    end
    
    for i in 1:nx, k in 1:nz
        # Bottom wall
        y_wall = mesh.dy / 2
        k_wall = max(fields.k[i,1,k], 1e-12)
        fields.epsilon[i,1,k] = Cmu^0.75 * k_wall^1.5 / (kappa * y_wall)
        
        # Top wall
        y_wall = mesh.dy / 2
        k_wall = max(fields.k[i,ny,k], 1e-12)
        fields.epsilon[i,ny,k] = Cmu^0.75 * k_wall^1.5 / (kappa * y_wall)
    end
end

# ============================================================================
# INITIALIZATION FUNCTIONS
# ============================================================================

"""
Initialize k-epsilon fields for cavity flow
"""
function initialize_k_epsilon_cavity!(fields::TurbulentFlowFields, U_lid::Float64=1.0, 
                                     turbulence_intensity::Float64=0.05)
    # Initialize velocity field (cavity with moving lid)
    for i in 1:fields.mesh.nx, j in 1:fields.mesh.ny, k in 1:fields.mesh.nz
        fields.u[i,j,k] = 0.0
        fields.v[i,j,k] = 0.0
        fields.w[i,j,k] = 0.0
        
        # Moving lid condition
        if j == fields.mesh.ny
            fields.u[i,j,k] = U_lid
        end
    end
    
    # Initialize turbulent kinetic energy
    initialize_turbulent_kinetic_energy!(fields, turbulence_intensity)
    
    # Initialize dissipation rate
    initialize_dissipation_rate!(fields, 0.07)  # 7% of domain size as length scale
    
    # Initialize turbulent viscosity
    coeffs = KEpsilonCoefficients()
    update_turbulent_viscosity_k_epsilon!(fields, coeffs)
end

"""
Initialize k-epsilon fields for channel flow
"""
function initialize_k_epsilon_channel!(fields::TurbulentFlowFields, U_bulk::Float64=1.0,
                                      Re_tau::Float64=180.0)
    mesh = fields.mesh
    
    # Parabolic velocity profile
    for i in 1:mesh.nx, j in 1:mesh.ny, k in 1:mesh.nz
        y = mesh.yc[i,j,k]
        H = mesh.dy * mesh.ny  # Channel height
        
        # Parabolic profile
        fields.u[i,j,k] = U_bulk * 1.5 * (1.0 - (2*y/H - 1.0)^2)
        fields.v[i,j,k] = 0.0
        fields.w[i,j,k] = 0.0
    end
    
    # Initialize k and epsilon based on Re_tau
    u_tau = Re_tau * fields.nu / (0.5 * mesh.dy * mesh.ny)
    
    for i in 1:mesh.nx, j in 1:mesh.ny, k in 1:mesh.nz
        y = mesh.yc[i,j,k]
        y_plus = y * u_tau / fields.nu
        
        # k profile
        if y_plus < 11.0
            fields.k[i,j,k] = u_tau^2 / sqrt(0.09) * (y_plus / 11.0)
        else
            fields.k[i,j,k] = u_tau^2 / sqrt(0.09)
        end
        
        # epsilon profile
        wall_dist = min(y, mesh.dy * mesh.ny - y)
        fields.epsilon[i,j,k] = u_tau^3 / (0.41 * wall_dist)
    end
    
    # Update turbulent viscosity
    coeffs = KEpsilonCoefficients()
    update_turbulent_viscosity_k_epsilon!(fields, coeffs)
end

# ============================================================================
# CONVERGENCE AND MONITORING
# ============================================================================

"""
Check convergence of k-epsilon equations
"""
function check_k_epsilon_convergence(fields_old::TurbulentFlowFields, 
                                   fields_new::TurbulentFlowFields, 
                                   tol::Float64=1e-6)
    # Calculate relative changes
    k_change = norm(fields_new.k - fields_old.k) / (norm(fields_old.k) + 1e-12)
    eps_change = norm(fields_new.epsilon - fields_old.epsilon) / (norm(fields_old.epsilon) + 1e-12)
    u_change = norm(fields_new.u - fields_old.u) / (norm(fields_old.u) + 1e-12)
    
    converged = k_change < tol && eps_change < tol && u_change < tol
    
    return converged, Dict("k" => k_change, "epsilon" => eps_change, "u" => u_change)
end

"""
Calculate residuals for k-epsilon equations
"""
function calculate_k_epsilon_residuals(fields::TurbulentFlowFields, dt::Float64)
    # Calculate current production
    Pk = calculate_turbulent_production(fields)
    
    # Calculate residuals (simplified)
    k_residual = norm(Pk - fields.epsilon) / (norm(fields.k) / dt + 1e-12)
    eps_residual = norm(fields.epsilon.^2 ./ fields.k) / (norm(fields.epsilon) / dt + 1e-12)
    
    return Dict("k" => k_residual, "epsilon" => eps_residual)
end

# ============================================================================
# EXPORTS
# ============================================================================

export solve_k_epsilon_equations!, solve_k_transport_equation!, solve_epsilon_transport_equation!
export update_turbulent_viscosity_k_epsilon!
export apply_k_boundary_conditions!, apply_epsilon_boundary_conditions!
export initialize_k_epsilon_cavity!, initialize_k_epsilon_channel!
export check_k_epsilon_convergence, calculate_k_epsilon_residuals