"""
KOmega.jl

Implementation of k-omega turbulence models:
- Standard k-omega (Wilcox 1998)
- k-omega SST (Menter 1994)
"""

include("../Base/AbstractModels.jl")
include("../Base/Coefficients.jl")
include("../Base/WallFunctions.jl")
include("../Common/TurbulentFields.jl")

using LinearAlgebra

# ============================================================================
# STANDARD K-OMEGA MODEL
# ============================================================================

"""
Standard k-omega turbulence model (Wilcox 1998)
"""
struct StandardKOmega <: AbstractRANSModel
    coefficients::KOmegaCoefficients
    wall_function::AbstractWallFunction
    boundary_conditions::Dict{String, Any}
    
    function StandardKOmega(; 
        coefficients = KOmegaCoefficients(),
        wall_function = LowReynoldsWallTreatment(),  # k-omega typically low-Re
        boundary_conditions = Dict{String, Any}()
    )
        new(coefficients, wall_function, boundary_conditions)
    end
end

model_name(::StandardKOmega) = "Standard k-omega"
model_info(m::StandardKOmega) = "Standard k-omega (β*=$(m.coefficients.betaStar))"
required_fields(::StandardKOmega) = ["U", "p", "k", "omega", "nut"]
turbulence_fields(::StandardKOmega) = ["k", "omega", "nut"]
get_model_coefficients(m::StandardKOmega) = m.coefficients

"""
Calculate turbulent viscosity for k-omega: νt = k/ω
"""
function calculate_turbulent_viscosity!(fields::TurbulentFlowFields, model::StandardKOmega)
    for i in 1:fields.mesh.nx, j in 1:fields.mesh.ny, k in 1:fields.mesh.nz
        k_local = max(fields.k[i,j,k], 1e-12)
        omega_local = max(fields.omega[i,j,k], 1e-12)
        
        fields.nut[i,j,k] = k_local / omega_local
        
        # Apply upper limit
        fields.nut[i,j,k] = min(fields.nut[i,j,k], 1000.0 * fields.nu)
    end
end

"""
Solve k-omega transport equations with real mesh implementation
"""
function solve_turbulence_equations!(fields::TurbulentFlowFields, model::StandardKOmega, dt::Float64)
    clear_cached_gradients!(fields)
    
    # Calculate production term
    Pk = calculate_turbulent_production(fields)
    
    # Solve k equation
    solve_k_equation_komega!(fields, Pk, model.coefficients, dt)
    
    # Solve omega equation
    solve_omega_equation_komega!(fields, Pk, model.coefficients, dt)
    
    # Apply realizability constraints
    apply_realizability_constraints!(fields)
    
    # Update turbulent viscosity
    calculate_turbulent_viscosity!(fields, model)
end

"""
Solve k transport equation for k-omega model: ∂k/∂t + ∇·(uk) = ∇·[(ν + νt/σk)∇k] + Pk - β*k*ω
"""
function solve_k_equation_komega!(fields::TurbulentFlowFields, Pk::Array{Float64,3}, 
                                 coeffs::KOmegaCoefficients, dt::Float64)
    mesh = fields.mesh
    nx, ny, nz = mesh.nx, mesh.ny, mesh.nz
    dx, dy, dz = mesh.dx, mesh.dy, mesh.dz
    nu = fields.nu
    sigma_k = coeffs.sigmak
    beta_star = coeffs.betaStar
    
    # Create temporary array for new k values
    k_new = copy(fields.k)
    
    for i in 2:nx-1, j in 2:ny-1, k in 2:nz-1
        # Convection terms (upwind scheme)
        u_face_e = 0.5 * (fields.u[i,j,k] + fields.u[i+1,j,k])
        u_face_w = 0.5 * (fields.u[i-1,j,k] + fields.u[i,j,k])
        v_face_n = 0.5 * (fields.v[i,j,k] + fields.v[i,j+1,k])
        v_face_s = 0.5 * (fields.v[i,j-1,k] + fields.v[i,j,k])
        
        # Upwind convection
        conv_x = u_face_e > 0 ? u_face_e * (fields.k[i,j,k] - fields.k[i-1,j,k]) / dx :
                               u_face_e * (fields.k[i+1,j,k] - fields.k[i,j,k]) / dx
        conv_y = v_face_n > 0 ? v_face_n * (fields.k[i,j,k] - fields.k[i,j-1,k]) / dy :
                               v_face_n * (fields.k[i,j+1,k] - fields.k[i,j,k]) / dy
        
        # Effective diffusivity
        nu_eff = nu + fields.nut[i,j,k] / sigma_k
        
        # Diffusion terms
        diff_x = nu_eff * (fields.k[i+1,j,k] - 2*fields.k[i,j,k] + fields.k[i-1,j,k]) / dx^2
        diff_y = nu_eff * (fields.k[i,j+1,k] - 2*fields.k[i,j,k] + fields.k[i,j-1,k]) / dy^2
        
        # Source terms
        production = Pk[i,j,k]
        
        # Implicit treatment of dissipation
        k_old = fields.k[i,j,k]
        omega_local = fields.omega[i,j,k]
        denominator = 1.0 + dt * beta_star * omega_local
        
        # Time integration
        k_new[i,j,k] = (k_old + dt * (-conv_x - conv_y + diff_x + diff_y + production)) / denominator
    end
    
    # Update k field
    fields.k .= k_new
    
    # Apply boundary conditions
    apply_k_boundary_conditions_komega!(fields)
end

"""
Solve omega transport equation for k-omega model: ∂ω/∂t + ∇·(uω) = ∇·[(ν + νt/σω)∇ω] + γ(ω/k)Pk - βω²
"""
function solve_omega_equation_komega!(fields::TurbulentFlowFields, Pk::Array{Float64,3},
                                     coeffs::KOmegaCoefficients, dt::Float64)
    mesh = fields.mesh
    nx, ny, nz = mesh.nx, mesh.ny, mesh.nz
    dx, dy, dz = mesh.dx, mesh.dy, mesh.dz
    nu = fields.nu
    
    gamma = coeffs.gamma
    beta = coeffs.beta
    sigma_omega = coeffs.sigmaOmega
    
    # Create temporary array for new omega values
    omega_new = copy(fields.omega)
    
    for i in 2:nx-1, j in 2:ny-1, k in 2:nz-1
        # Convection terms (upwind scheme)
        u_face_e = 0.5 * (fields.u[i,j,k] + fields.u[i+1,j,k])
        u_face_w = 0.5 * (fields.u[i-1,j,k] + fields.u[i,j,k])
        v_face_n = 0.5 * (fields.v[i,j,k] + fields.v[i,j+1,k])
        v_face_s = 0.5 * (fields.v[i,j-1,k] + fields.v[i,j,k])
        
        # Upwind convection
        conv_x = u_face_e > 0 ? u_face_e * (fields.omega[i,j,k] - fields.omega[i-1,j,k]) / dx :
                               u_face_e * (fields.omega[i+1,j,k] - fields.omega[i,j,k]) / dx
        conv_y = v_face_n > 0 ? v_face_n * (fields.omega[i,j,k] - fields.omega[i,j-1,k]) / dy :
                               v_face_n * (fields.omega[i,j+1,k] - fields.omega[i,j,k]) / dy
        
        # Effective diffusivity
        nu_eff = nu + fields.nut[i,j,k] / sigma_omega
        
        # Diffusion terms
        diff_x = nu_eff * (fields.omega[i+1,j,k] - 2*fields.omega[i,j,k] + fields.omega[i-1,j,k]) / dx^2
        diff_y = nu_eff * (fields.omega[i,j+1,k] - 2*fields.omega[i,j,k] + fields.omega[i,j-1,k]) / dy^2
        
        # Source terms
        k_local = max(fields.k[i,j,k], 1e-12)
        omega_local = fields.omega[i,j,k]
        
        gamma_prod = gamma * omega_local / k_local * Pk[i,j,k]
        
        # Implicit treatment of beta term for stability
        omega_old = omega_local
        denominator = 1.0 + dt * beta * omega_local
        
        # Time integration
        omega_new[i,j,k] = (omega_old + dt * (-conv_x - conv_y + diff_x + diff_y + gamma_prod)) / denominator
    end
    
    # Update omega field
    fields.omega .= omega_new
    
    # Apply boundary conditions
    apply_omega_boundary_conditions_komega!(fields)
end

"""
Apply boundary conditions for k in k-omega model
"""
function apply_k_boundary_conditions_komega!(fields::TurbulentFlowFields)
    mesh = fields.mesh
    nx, ny, nz = mesh.nx, mesh.ny, mesh.nz
    
    # Wall boundaries: k = 0 (low-Re treatment)
    for j in 1:ny, k in 1:nz
        fields.k[1,j,k] = 0.0    # Left wall
        fields.k[nx,j,k] = 0.0   # Right wall
    end
    
    for i in 1:nx, k in 1:nz
        fields.k[i,1,k] = 0.0    # Bottom wall
        fields.k[i,ny,k] = 0.0   # Top wall
    end
end

"""
Apply boundary conditions for omega in k-omega model
"""
function apply_omega_boundary_conditions_komega!(fields::TurbulentFlowFields)
    mesh = fields.mesh
    nx, ny, nz = mesh.nx, mesh.ny, mesh.nz
    nu = fields.nu
    beta = 0.075  # Standard k-omega coefficient
    
    # Wall boundaries: omega from wall function (ω_wall = 60ν/(β*y²))
    for j in 1:ny, k in 1:nz
        # Left wall
        y_wall = mesh.dx / 2
        fields.omega[1,j,k] = 60.0 * nu / (beta * y_wall^2)
        
        # Right wall
        y_wall = mesh.dx / 2
        fields.omega[nx,j,k] = 60.0 * nu / (beta * y_wall^2)
    end
    
    for i in 1:nx, k in 1:nz
        # Bottom wall
        y_wall = mesh.dy / 2
        fields.omega[i,1,k] = 60.0 * nu / (beta * y_wall^2)
        
        # Top wall
        y_wall = mesh.dy / 2
        fields.omega[i,ny,k] = 60.0 * nu / (beta * y_wall^2)
    end
end

# ============================================================================
# K-OMEGA SST MODEL
# ============================================================================

"""
k-omega SST turbulence model (Menter 1994)

Combines k-omega formulation in near-wall region with k-epsilon in free stream
"""
struct KOmegaSST <: AbstractRANSModel
    coefficients::KOmegaSSTCoefficients
    wall_function::AbstractWallFunction
    boundary_conditions::Dict{String, Any}
    
    function KOmegaSST(; 
        coefficients = KOmegaSSTCoefficients(),
        wall_function = LowReynoldsWallTreatment(),
        boundary_conditions = Dict{String, Any}()
    )
        new(coefficients, wall_function, boundary_conditions)
    end
end

model_name(::KOmegaSST) = "k-omega SST"
model_info(m::KOmegaSST) = "k-omega SST (a1=$(m.coefficients.a1))"
required_fields(::KOmegaSST) = ["U", "p", "k", "omega", "nut"]
turbulence_fields(::KOmegaSST) = ["k", "omega", "nut"]
get_model_coefficients(m::KOmegaSST) = m.coefficients

"""
Calculate turbulent viscosity for SST model with vorticity correction
"""
function calculate_turbulent_viscosity!(fields, model::KOmegaSST)
    k = fields.k
    omega = fields.omega
    nut = fields.nut
    u = fields.u
    v = fields.v
    dx, dy = fields.dx, fields.dy
    
    a1 = model.coefficients.a1
    
    # Calculate strain rate
    S = calculate_mean_strain_rate_sst(u, v, dx, dy)
    
    # SST limiter: nut = a1*k / max(a1*omega, S*F2)
    for i in 1:size(k,1), j in 1:size(k,2)
        if k[i,j] > 1e-12 && omega[i,j] > 1e-12
            # Calculate F2 blending function (simplified)
            F2 = calculate_f2_blending(k[i,j], omega[i,j], fields.nu, 1e-3)  # Assume wall distance
            
            denominator = max(a1 * omega[i,j], S[i,j] * F2)
            nut[i,j] = a1 * k[i,j] / denominator
        else
            nut[i,j] = 0.0
        end
    end
    
    limit_turbulent_viscosity!(nut, fields.nu)
end

"""
Calculate strain rate for SST model
"""
function calculate_mean_strain_rate_sst(u, v, dx, dy)
    nx, ny = size(u)
    S = zeros(nx, ny)
    
    for i in 2:nx-1, j in 2:ny-1
        dudx = (u[i+1,j] - u[i-1,j]) / (2*dx)
        dudy = (u[i,j+1] - u[i,j-1]) / (2*dy)
        dvdx = (v[i+1,j] - v[i-1,j]) / (2*dx)
        dvdy = (v[i,j+1] - v[i,j-1]) / (2*dy)
        
        S[i,j] = sqrt(2*(dudx^2 + dvdy^2) + (dudy + dvdx)^2)
    end
    
    return S
end

"""
Calculate F2 blending function for SST
"""
function calculate_f2_blending(k, omega, nu, wall_dist)
    # Simplified F2 calculation
    arg2_1 = 2*sqrt(k) / (0.09*omega*wall_dist)
    arg2_2 = 500*nu / (wall_dist^2 * omega)
    arg2 = max(arg2_1, arg2_2)
    
    return tanh(arg2^2)
end

"""
Solve SST k-omega equations with blending
"""
function solve_turbulence_equations!(fields, model::KOmegaSST, dt)
    k = fields.k
    omega = fields.omega
    nut = fields.nut
    u = fields.u
    v = fields.v
    
    dx, dy = fields.dx, fields.dy
    nu = fields.nu
    coeffs = model.coefficients
    
    # Calculate production
    Pk = calculate_production_komega(u, v, zeros(size(u)), nut, dx, dy, 0.0)
    
    # Calculate blending function F1
    F1 = calculate_f1_blending_sst(k, omega, nu, nut, dx, dy)
    
    # Solve k equation (same as standard k-omega)
    solve_k_equation_sst!(k, omega, nut, u, v, Pk, nu, coeffs, F1, dt, dx, dy)
    
    # Solve omega equation with SST modifications
    solve_omega_equation_sst!(omega, k, nut, u, v, Pk, nu, coeffs, F1, dt, dx, dy)
    
    apply_realizability!(k, nothing, omega)
end

"""
Calculate F1 blending function for SST
"""
function calculate_f1_blending_sst(k, omega, nu, nut, dx, dy)
    nx, ny = size(k)
    F1 = zeros(nx, ny)
    
    for i in 2:nx-1, j in 2:ny-1
        wall_dist = 1e-3  # Simplified wall distance
        
        # Calculate cross-diffusion term
        dkdx = (k[i+1,j] - k[i-1,j]) / (2*dx)
        dkdy = (k[i,j+1] - k[i,j-1]) / (2*dy)
        domegadx = (omega[i+1,j] - omega[i-1,j]) / (2*dx)
        domegady = (omega[i,j+1] - omega[i,j-1]) / (2*dy)
        
        CDkOmega = max(2*1.168*(dkdx*domegadx + dkdy*domegady)/omega[i,j], 1e-20)
        
        # F1 arguments
        arg1_1 = sqrt(k[i,j]) / (0.09*omega[i,j]*wall_dist)
        arg1_2 = 500*nu / (wall_dist^2 * omega[i,j])
        arg1_3 = 4*1.168*k[i,j] / (CDkOmega*wall_dist^2)
        
        arg1 = min(max(arg1_1, arg1_2), arg1_3)
        F1[i,j] = tanh(arg1^4)
    end
    
    return F1
end

"""
Solve k equation for SST
"""
function solve_k_equation_sst!(k, omega, nut, u, v, Pk, nu, coeffs, F1, dt, dx, dy)
    nx, ny = size(k)
    k_new = copy(k)
    
    for i in 2:nx-1, j in 2:ny-1
        # Blended coefficient
        sigmak = F1[i,j]*coeffs.sigmak1 + (1-F1[i,j])*coeffs.sigmak2
        
        # Advection
        u_face = 0.5*(u[i,j] + u[i-1,j])
        v_face = 0.5*(v[i,j] + v[i,j-1])
        
        adv_x = u_face >= 0 ? u_face*(k[i,j] - k[i-1,j])/dx : u_face*(k[i+1,j] - k[i,j])/dx
        adv_y = v_face >= 0 ? v_face*(k[i,j] - k[i,j-1])/dy : v_face*(k[i,j+1] - k[i,j])/dy
        
        # Diffusion
        nu_eff = nu + nut[i,j]/sigmak
        diff_x = nu_eff * (k[i+1,j] - 2*k[i,j] + k[i-1,j]) / dx^2
        diff_y = nu_eff * (k[i,j+1] - 2*k[i,j] + k[i,j-1]) / dy^2
        
        # Source terms
        production = Pk[i,j]
        dissipation = coeffs.betaStar * k[i,j] * omega[i,j]
        
        k_new[i,j] = (k[i,j] + dt*(-adv_x - adv_y + diff_x + diff_y + production)) / 
                     (1.0 + dt*coeffs.betaStar*omega[i,j])
    end
    
    k .= k_new
end

"""
Solve omega equation for SST with cross-diffusion
"""
function solve_omega_equation_sst!(omega, k, nut, u, v, Pk, nu, coeffs, F1, dt, dx, dy)
    nx, ny = size(omega)
    omega_new = copy(omega)
    
    for i in 2:nx-1, j in 2:ny-1
        # Blended coefficients
        gamma = F1[i,j]*coeffs.gamma1 + (1-F1[i,j])*coeffs.gamma2
        beta = F1[i,j]*coeffs.beta1 + (1-F1[i,j])*coeffs.beta2
        sigma_omega = F1[i,j]*coeffs.sigmaOmega1 + (1-F1[i,j])*coeffs.sigmaOmega2
        
        # Advection
        u_face = 0.5*(u[i,j] + u[i-1,j])
        v_face = 0.5*(v[i,j] + v[i,j-1])
        
        adv_x = u_face >= 0 ? u_face*(omega[i,j] - omega[i-1,j])/dx : 
                             u_face*(omega[i+1,j] - omega[i,j])/dx
        adv_y = v_face >= 0 ? v_face*(omega[i,j] - omega[i,j-1])/dy : 
                             v_face*(omega[i,j+1] - omega[i,j])/dy
        
        # Diffusion
        nu_eff = nu + nut[i,j]/sigma_omega
        diff_x = nu_eff * (omega[i+1,j] - 2*omega[i,j] + omega[i-1,j]) / dx^2
        diff_y = nu_eff * (omega[i,j+1] - 2*omega[i,j] + omega[i,j-1]) / dy^2
        
        # Cross-diffusion term (only in outer region)
        cross_diff = 0.0
        if F1[i,j] < 1.0
            dkdx = (k[i+1,j] - k[i-1,j]) / (2*dx)
            dkdy = (k[i,j+1] - k[i,j-1]) / (2*dy)
            domegadx = (omega[i+1,j] - omega[i-1,j]) / (2*dx)
            domegady = (omega[i,j+1] - omega[i,j-1]) / (2*dy)
            
            cross_diff = 2*(1-F1[i,j])*coeffs.sigmaOmega2*(dkdx*domegadx + dkdy*domegady)/omega[i,j]
        end
        
        # Source terms
        k_local = max(k[i,j], 1e-12)
        gamma_prod = gamma * omega[i,j] / k_local * Pk[i,j]
        beta_diss = beta * omega[i,j]^2
        
        # Time integration
        omega_new[i,j] = (omega[i,j] + dt*(-adv_x - adv_y + diff_x + diff_y + 
                         gamma_prod + cross_diff)) / (1.0 + dt*beta_diss/omega[i,j])
    end
    
    omega .= omega_new
end

# ============================================================================
# BOUNDARY CONDITIONS
# ============================================================================

"""
Apply boundary conditions for k-omega models
"""
function apply_boundary_conditions!(fields, model::Union{StandardKOmega, KOmegaSST})
    # k-omega typically uses low-Re treatment at walls
    apply_komega_wall_bc!(fields, model)
    
    # Apply other BCs
    for (name, bc) in model.boundary_conditions
        if bc.type == :inlet
            apply_inlet_bc_komega!(fields, bc)
        elseif bc.type == :outlet
            apply_outlet_bc_komega!(fields, bc)
        end
    end
end

"""
Apply wall BC for k-omega (low-Re treatment)
"""
function apply_komega_wall_bc!(fields, model)
    # k = 0 at wall
    fields.k[:, 1] .= 0.0
    fields.k[:, end] .= 0.0
    fields.k[1, :] .= 0.0
    fields.k[end, :] .= 0.0
    
    # omega at wall
    beta = model.coefficients.beta
    nu = fields.nu
    
    # Calculate omega at wall cells
    for j in 1:size(fields.omega, 2)
        y_wall = fields.dy  # First cell height
        fields.omega[1, j] = 60*nu/(beta*y_wall^2)
        fields.omega[end, j] = 60*nu/(beta*y_wall^2)
    end
    
    for i in 1:size(fields.omega, 1)
        y_wall = fields.dx
        fields.omega[i, 1] = 60*nu/(beta*y_wall^2)
        fields.omega[i, end] = 60*nu/(beta*y_wall^2)
    end
    
    # nut = 0 at wall
    fields.nut[:, 1] .= 0.0
    fields.nut[:, end] .= 0.0
    fields.nut[1, :] .= 0.0
    fields.nut[end, :] .= 0.0
end

"""
Apply inlet BC for k-omega
"""
function apply_inlet_bc_komega!(fields, bc)
    k_inlet = bc.k_value
    omega_inlet = bc.omega_value
    
    fields.k[1, :] .= k_inlet
    fields.omega[1, :] .= omega_inlet
end

"""
Apply outlet BC for k-omega
"""
function apply_outlet_bc_komega!(fields, bc)
    nx = size(fields.k, 1)
    fields.k[nx, :] .= fields.k[nx-1, :]
    fields.omega[nx, :] .= fields.omega[nx-1, :]
    fields.nut[nx, :] .= fields.nut[nx-1, :]
end