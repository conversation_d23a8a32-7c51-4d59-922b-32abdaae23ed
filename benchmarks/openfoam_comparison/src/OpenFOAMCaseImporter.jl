"""
OpenFOAMCaseImporter.jl

Comprehensive OpenFOAM case importer for accurate JuliaFOAM comparison.
Reads actual OpenFOAM case files and converts them to JuliaFOAM format.
"""

using LinearAlgebra
using SparseArrays
using DelimitedFiles

# Core data structures for OpenFOAM case data
struct OpenFOAMField
    name::String
    dimensions::Vector{Int}
    internal_field::Any
    boundary_field::Dict{String, Any}
end

struct OpenFOAMBoundary
    name::String
    type::String
    n_faces::Int
    start_face::Int
    physical_type::Union{String, Nothing}
end

struct OpenFOAMCase
    name::String
    path::String
    
    # Mesh data
    points::Vector{Vector{Float64}}
    faces::Vector{Vector{Int}}
    owner::Vector{Int}
    neighbour::Vector{Int}
    boundaries::Vector{OpenFOAMBoundary}
    
    # Fields
    fields::Dict{String, OpenFOAMField}
    
    # Control and solver settings
    control_dict::Dict{String, Any}
    fv_schemes::Dict{String, Any}
    fv_solution::Dict{String, Any}
    transport_properties::Dict{String, Any}
end

"""
Import a complete OpenFOAM case
"""
function import_openfoam_case(case_path::String)
    println("📥 Importing OpenFOAM case: $case_path")
    
    if !isdir(case_path)
        error("Case directory not found: $case_path")
    end
    
    case_name = basename(case_path)
    
    # Read mesh
    println("   📐 Reading mesh...")
    points, faces, owner, neighbour, boundaries = read_openfoam_mesh(case_path)
    
    # Read fields
    println("   📊 Reading fields...")
    fields = read_openfoam_fields(case_path)
    
    # Read control files
    println("   ⚙️  Reading control files...")
    control_dict = read_control_dict(case_path)
    fv_schemes = read_fv_schemes(case_path)
    fv_solution = read_fv_solution(case_path)
    transport_props = read_transport_properties(case_path)
    
    case = OpenFOAMCase(
        case_name, case_path,
        points, faces, owner, neighbour, boundaries,
        fields,
        control_dict, fv_schemes, fv_solution, transport_props
    )
    
    println("   ✅ Case imported successfully")
    println("      Points: $(length(points)), Faces: $(length(faces))")
    println("      Fields: $(join(keys(fields), ", "))")
    
    return case
end

"""
Read OpenFOAM mesh files
"""
function read_openfoam_mesh(case_path::String)
    polymesh_path = joinpath(case_path, "constant", "polyMesh")
    
    if !isdir(polymesh_path)
        error("polyMesh directory not found: $polymesh_path")
    end
    
    # Read points
    points_file = joinpath(polymesh_path, "points")
    points = read_points_file(points_file)
    
    # Read faces
    faces_file = joinpath(polymesh_path, "faces")
    faces = read_faces_file(faces_file)
    
    # Read owner/neighbour
    owner_file = joinpath(polymesh_path, "owner")
    owner = read_label_list(owner_file)
    
    neighbour_file = joinpath(polymesh_path, "neighbour")
    neighbour = read_label_list(neighbour_file)
    
    # Read boundary
    boundary_file = joinpath(polymesh_path, "boundary")
    boundaries = read_boundary_file(boundary_file)
    
    return points, faces, owner, neighbour, boundaries
end

"""
Read OpenFOAM points file
"""
function read_points_file(file_path::String)
    content = read(file_path, String)
    
    # Find the data section
    data_start = findfirst('(', content)
    data_end = findlast(')', content)
    
    if data_start === nothing || data_end === nothing
        error("Invalid points file format")
    end
    
    data_section = content[data_start+1:data_end-1]
    
    points = Vector{Vector{Float64}}()
    
    # Parse point coordinates
    for line in split(data_section, '\n')
        line = strip(line)
        if isempty(line) || !startswith(line, '(')
            continue
        end
        
        # Extract coordinates from (x y z) format
        coords_match = match(r"\\(([^)]+)\\)", line)
        if coords_match !== nothing
            coords_str = coords_match.captures[1]
            coords = parse.(Float64, split(coords_str))
            if length(coords) == 3
                push!(points, coords)
            end
        end
    end
    
    return points
end

"""
Read OpenFOAM faces file
"""
function read_faces_file(file_path::String)
    content = read(file_path, String)
    
    # Find the data section
    data_start = findfirst('(', content)
    data_end = findlast(')', content)
    
    if data_start === nothing || data_end === nothing
        error("Invalid faces file format")
    end
    
    data_section = content[data_start+1:data_end-1]
    
    faces = Vector{Vector{Int}}()
    
    # Parse face definitions
    for line in split(data_section, '\n')
        line = strip(line)
        if isempty(line)
            continue
        end
        
        # Match pattern like "4(0 1 2 3)"
        face_match = match(r"(\\d+)\\(([^)]+)\\)", line)
        if face_match !== nothing
            n_vertices = parse(Int, face_match.captures[1])
            vertices_str = face_match.captures[2]
            vertices = parse.(Int, split(vertices_str)) .+ 1  # Convert to 1-based
            
            if length(vertices) == n_vertices
                push!(faces, vertices)
            end
        end
    end
    
    return faces
end

"""
Read OpenFOAM label list (owner/neighbour)
"""
function read_label_list(file_path::String)
    content = read(file_path, String)
    
    # Find the data section
    data_start = findfirst('(', content)
    data_end = findlast(')', content)
    
    if data_start === nothing || data_end === nothing
        error("Invalid label list file format")
    end
    
    data_section = content[data_start+1:data_end-1]
    
    labels = Int[]
    
    # Parse labels
    for line in split(data_section, '\n')
        line = strip(line)
        if isempty(line)
            continue
        end
        
        # Try to parse as integer
        try
            label = parse(Int, line)
            push!(labels, label + 1)  # Convert to 1-based
        catch
            # Skip non-integer lines
        end
    end
    
    return labels
end

"""
Read OpenFOAM boundary file
"""
function read_boundary_file(file_path::String)
    content = read(file_path, String)
    
    boundaries = OpenFOAMBoundary[]
    
    # Simple parser for boundary definitions
    lines = split(content, '\n')
    i = 1
    while i <= length(lines)
        line = strip(lines[i])
        
        # Look for boundary patch name
        if !isempty(line) && !startswith(line, "//") && !contains(line, "FoamFile") && 
           !contains(line, '{') && !contains(line, '}') && !isdigit(line[1])
            
            patch_name = line
            
            # Find the opening brace
            while i <= length(lines) && !contains(lines[i], '{')
                i += 1
            end
            
            if i > length(lines)
                break
            end
            
            # Parse patch properties
            patch_type = ""
            n_faces = 0
            start_face = 0
            physical_type = nothing
            
            i += 1
            brace_count = 1
            
            while i <= length(lines) && brace_count > 0
                line = strip(lines[i])
                
                if contains(line, '{')
                    brace_count += count('{', line)
                end
                if contains(line, '}')
                    brace_count -= count('}', line)
                end
                
                # Parse properties
                type_match = match(r"type\\s+(\\w+);", line)
                if type_match !== nothing
                    patch_type = type_match.captures[1]
                end
                
                nfaces_match = match(r"nFaces\\s+(\\d+);", line)
                if nfaces_match !== nothing
                    n_faces = parse(Int, nfaces_match.captures[1])
                end
                
                startface_match = match(r"startFace\\s+(\\d+);", line)
                if startface_match !== nothing
                    start_face = parse(Int, startface_match.captures[1]) + 1  # Convert to 1-based
                end
                
                phystype_match = match(r"physicalType\\s+(\\w+);", line)
                if phystype_match !== nothing
                    physical_type = phystype_match.captures[1]
                end
                
                i += 1
            end
            
            if !isempty(patch_type)
                boundary = OpenFOAMBoundary(patch_name, patch_type, n_faces, start_face, physical_type)
                push!(boundaries, boundary)
            end
        else
            i += 1
        end
    end
    
    return boundaries
end

"""
Read OpenFOAM fields from time directories
"""
function read_openfoam_fields(case_path::String)
    fields = Dict{String, OpenFOAMField}()
    
    # Find time directories
    time_dirs = String[]
    for item in readdir(case_path)
        item_path = joinpath(case_path, item)
        if isdir(item_path)
            # Check if it's a time directory (numeric name)
            if occursin(r"^\\d+(\\.\\d+)?$", item)
                push!(time_dirs, item)
            elseif item == "0"
                push!(time_dirs, item)
            end
        end
    end
    
    if isempty(time_dirs)
        println("   ⚠️  No time directories found")
        return fields
    end
    
    # Use the initial time directory (usually "0")
    time_dir = "0" in time_dirs ? "0" : sort(time_dirs)[1]
    field_dir = joinpath(case_path, time_dir)
    
    # Read field files
    for field_file in readdir(field_dir)
        field_path = joinpath(field_dir, field_file)
        
        if isfile(field_path) && !startswith(field_file, ".")
            try
                field = read_field_file(field_path, field_file)
                fields[field_file] = field
            catch e
                println("   ⚠️  Failed to read field $field_file: $e")
            end
        end
    end
    
    return fields
end

"""
Read individual OpenFOAM field file
"""
function read_field_file(file_path::String, field_name::String)
    content = read(file_path, String)
    
    # Parse dimensions
    dimensions = Int[0, 0, 0, 0, 0, 0, 0]  # Default
    dim_match = match(r"dimensions\\s*\\[(.*?)\\];", content)
    if dim_match !== nothing
        dim_str = dim_match.captures[1]
        dimensions = parse.(Int, split(dim_str))
    end
    
    # Parse internal field
    internal_field = nothing
    
    # Try uniform value first
    uniform_match = match(r"internalField\\s+uniform\\s+([^;]+);", content)
    if uniform_match !== nothing
        value_str = strip(uniform_match.captures[1])
        
        if startswith(value_str, '(')
            # Vector value
            vec_match = match(r"\\(([^)]+)\\)", value_str)
            if vec_match !== nothing
                coords = parse.(Float64, split(vec_match.captures[1]))
                internal_field = coords
            end
        else
            # Scalar value
            internal_field = parse(Float64, value_str)
        end
    end
    
    # Parse boundary field
    boundary_field = Dict{String, Any}()
    
    # Find boundaryField section
    boundary_start = findfirst("boundaryField", content)
    if boundary_start !== nothing
        # Extract boundary field section
        remaining = content[boundary_start[end]+1:end]
        
        # Simple parsing of boundary conditions
        patch_pattern = r"(\\w+)\\s*\\{([^}]*)\\}"
        
        for match in eachmatch(patch_pattern, remaining)
            patch_name = match.captures[1]
            patch_content = match.captures[2]
            
            patch_bc = Dict{String, Any}()
            
            # Parse type
            type_match = match(r"type\\s+(\\w+);", patch_content)
            if type_match !== nothing
                patch_bc["type"] = type_match.captures[1]
            end
            
            # Parse value if present
            value_match = match(r"value\\s+uniform\\s+([^;]+);", patch_content)
            if value_match !== nothing
                value_str = strip(value_match.captures[1])
                
                if startswith(value_str, '(')
                    # Vector value
                    vec_match = match(r"\\(([^)]+)\\)", value_str)
                    if vec_match !== nothing
                        coords = parse.(Float64, split(vec_match.captures[1]))
                        patch_bc["value"] = coords
                    end
                else
                    # Scalar value
                    try
                        patch_bc["value"] = parse(Float64, value_str)
                    catch
                        patch_bc["value"] = value_str
                    end
                end
            end
            
            boundary_field[patch_name] = patch_bc
        end
    end
    
    return OpenFOAMField(field_name, dimensions, internal_field, boundary_field)
end

"""
Read controlDict file
"""
function read_control_dict(case_path::String)
    control_file = joinpath(case_path, "system", "controlDict")
    
    if !isfile(control_file)
        println("   ⚠️  No controlDict found")
        return Dict{String, Any}()
    end
    
    content = read(control_file, String)
    control_dict = parse_openfoam_dict(content)
    
    return control_dict
end

"""
Read fvSchemes file
"""
function read_fv_schemes(case_path::String)
    schemes_file = joinpath(case_path, "system", "fvSchemes")
    
    if !isfile(schemes_file)
        println("   ⚠️  No fvSchemes found")
        return Dict{String, Any}()
    end
    
    content = read(schemes_file, String)
    fv_schemes = parse_openfoam_dict(content)
    
    return fv_schemes
end

"""
Read fvSolution file
"""
function read_fv_solution(case_path::String)
    solution_file = joinpath(case_path, "system", "fvSolution")
    
    if !isfile(solution_file)
        println("   ⚠️  No fvSolution found")
        return Dict{String, Any}()
    end
    
    content = read(solution_file, String)
    fv_solution = parse_openfoam_dict(content)
    
    return fv_solution
end

"""
Read transportProperties file
"""
function read_transport_properties(case_path::String)
    props_file = joinpath(case_path, "constant", "transportProperties")
    
    if !isfile(props_file)
        println("   ⚠️  No transportProperties found")
        return Dict{String, Any}()
    end
    
    content = read(props_file, String)
    transport_props = parse_openfoam_dict(content)
    
    return transport_props
end

"""
Parse OpenFOAM dictionary format (simplified)
"""
function parse_openfoam_dict(content::String)
    dict = Dict{String, Any}()
    
    # Remove comments
    lines = split(content, '\n')
    clean_lines = String[]
    
    for line in lines
        # Remove single-line comments
        comment_pos = findfirst("//", line)
        if comment_pos !== nothing
            line = line[1:comment_pos[1]-1]
        end
        
        line = strip(line)
        if !isempty(line)
            push!(clean_lines, line)
        end
    end
    
    # Simple key-value parsing
    for line in clean_lines
        # Skip structural elements
        if line in ["{", "}", "FoamFile", "/*", "*/"] || startswith(line, "*")
            continue
        end
        
        # Look for key-value pairs
        if contains(line, ';') && !contains(line, '{')
            parts = split(line, r"\s+", limit=2)
            if length(parts) >= 2
                key = parts[1]
                value = strip(parts[2], ';')
                
                # Try to parse as number
                if occursin(r"^[-+]?\d*\.?\d+([eE][-+]?\d+)?$", value)
                    try
                        dict[key] = parse(Float64, value)
                    catch
                        dict[key] = value
                    end
                else
                    dict[key] = value
                end
            end
        end
    end
    
    return dict
end

"""
Copy OpenFOAM case to working directory
"""
function copy_openfoam_case(source_path::String, dest_path::String)
    println("📁 Copying OpenFOAM case...")
    println("   From: $source_path")
    println("   To: $dest_path")
    
    if !isdir(source_path)
        error("Source case not found: $source_path")
    end
    
    # Create destination directory
    mkpath(dest_path)
    
    # Copy case files
    run(`cp -r $source_path/. $dest_path/`)
    
    println("   ✅ Case copied successfully")
end

"""
Print case summary
"""
function print_case_summary(case::OpenFOAMCase)
    println("📊 OpenFOAM Case Summary")
    println("=" ^ 50)
    println("Case: $(case.name)")
    println("Path: $(case.path)")
    println()
    
    println("📐 Mesh:")
    println("   Points: $(length(case.points))")
    println("   Faces: $(length(case.faces))")
    println("   Boundaries: $(length(case.boundaries))")
    for boundary in case.boundaries
        println("      $(boundary.name): $(boundary.type) ($(boundary.n_faces) faces)")
    end
    println()
    
    println("📊 Fields:")
    for (name, field) in case.fields
        println("   $name: dims=$(field.dimensions)")
    end
    println()
    
    if haskey(case.control_dict, "application")
        println("⚙️  Solver: $(case.control_dict["application"])")
    end
    
    if haskey(case.control_dict, "endTime")
        println("⏱️  End time: $(case.control_dict["endTime"])")
    end
    
    if haskey(case.transport_properties, "nu")
        println("🌊 Viscosity: $(case.transport_properties["nu"])")
    end
end