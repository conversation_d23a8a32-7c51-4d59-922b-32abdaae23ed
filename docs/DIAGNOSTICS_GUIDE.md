# JuliaFOAM Diagnostic & Doctor Utilities Guide

## Overview

JuliaFOAM provides comprehensive diagnostic and "doctor" utilities for debugging, validation, and health assessment of CFD simulations. These tools are essential for ensuring mathematical correctness, mesh quality, and overall system reliability.

## 🩺 Quick Start

### Basic Health Check
```bash
# Quick health assessment
julia tools/juliafoam_doctor.jl --quick

# Detailed health check with report
julia tools/juliafoam_doctor.jl --output health_report.txt

# Get just the health score
julia tools/juliafoam_doctor.jl --score-only
```

### Mathematical Validation
```bash
# Quick mathematical validation
julia validation/quick_validation_test.jl

# Comprehensive validation suite
julia validation/run_all_validation.jl

# Unified validation + diagnostics
julia validation/diagnostic_validation.jl
```

## 🔧 Diagnostic Tools

### 1. Mesh Diagnostics (`MeshDiagnostics.jl`)

**Purpose**: Comprehensive mesh quality analysis and geometric validation

**Features**:
- Orthogonality, skewness, aspect ratio analysis
- Connectivity validation
- Geometric consistency checks
- Boundary condition validation
- Performance analysis

**Usage**:
```julia
using JuliaFOAM
include("src/tools/MeshDiagnostics.jl")
using .MeshDiagnostics

# Create mesh doctor
doctor = MeshDoctor(mesh, verbose=true, tolerance=1e-12)

# Run comprehensive diagnosis
results = diagnose_mesh(doctor)

# Generate report
mesh_report(results, output_file="mesh_diagnosis.txt")
```

**Key Metrics**:
- **Orthogonality**: Face normal alignment with cell-to-cell vector
- **Skewness**: Cell center deviation from geometric center
- **Aspect Ratio**: Cell dimension ratios
- **Face Area Ratios**: Neighboring face size variations

### 2. FVC Diagnostics (`FVCDiagnostics.jl`)

**Purpose**: Finite Volume Calculus operator validation and accuracy analysis

**Features**:
- Gradient operator accuracy testing
- Divergence operator conservation verification
- Laplacian operator convergence analysis
- Face interpolation scheme validation
- Boundary condition consistency

**Usage**:
```julia
include("src/tools/FVCDiagnostics.jl")
using .FVCDiagnostics

# Create FVC doctor
doctor = FVCDoctor(mesh, verbose=true, tolerance=1e-10)

# Analyze operators
results = diagnose_fvc_operators(doctor)

# Generate report
fvc_report(results, output_file="fvc_diagnosis.txt")
```

**Key Tests**:
- **Gradient**: Tests with analytical functions (linear, quadratic, cubic)
- **Divergence**: Conservation law verification
- **Laplacian**: Matrix symmetry and convergence order
- **Interpolation**: Boundedness and monotonicity

### 3. FVM Diagnostics (`FVMDiagnostics.jl`)

**Purpose**: Finite Volume Method system analysis and solver validation

**Features**:
- Matrix assembly validation
- Linear solver convergence analysis
- Boundary condition implementation verification
- Conservation law checking
- Solution quality assessment
- Stability analysis

**Usage**:
```julia
include("src/tools/FVMDiagnostics.jl")
using .FVMDiagnostics

# Create FVM doctor
doctor = FVMDoctor(mesh, verbose=true, tolerance=1e-10)

# Analyze system
results = diagnose_fvm_system(doctor)

# Generate report
fvm_report(results, output_file="fvm_diagnosis.txt")
```

**Key Analyses**:
- **Matrix Properties**: Symmetry, conditioning, sparsity
- **Solver Behavior**: Convergence, robustness, preconditioner effectiveness
- **Conservation**: Mass, momentum, energy conservation
- **Stability**: CFL limits, stability margins

### 4. Master Doctor (`JuliaFOAMDoctor.jl`)

**Purpose**: Unified health assessment combining all diagnostic tools

**Features**:
- Comprehensive health scoring (0-100)
- Letter grade assessment (A+ to F)
- System readiness evaluation
- Prioritized issue identification
- Automated recommendations

**Usage**:
```julia
include("src/tools/JuliaFOAMDoctor.jl")
using .JuliaFOAMDoctor

# Create health check
health_check = JuliaFOAMHealthCheck(mesh, verbose=true)

# Run full diagnostics
results = run_full_diagnostics(health_check)

# Generate comprehensive report
generate_health_report(results, output_file="health_report.txt")
```

### 5. Mesh Quality Checks (`MeshQualityChecks.jl`)

**Purpose**: Industry-standard CFD mesh quality assessment

**Features**:
- Advanced geometric metrics (warpage, taper, Jacobian)
- Boundary layer analysis
- Resolution adequacy assessment
- CFD application-specific validation
- Solver and discretization recommendations

**Usage**:
```julia
include("src/tools/MeshQualityChecks.jl")
using .MeshQualityChecks

# Create quality analyzer
analyzer = MeshQualityAnalyzer(
    mesh, 
    cfd_application="turbulent",
    reynolds_number=1e5,
    target_y_plus=1.0
)

# Analyze quality
results = analyze_mesh_quality(analyzer)

# Generate report
generate_quality_report(results, output_file="quality_report.txt")
```

## 🔍 Validation Integration

### Unified Validation & Diagnostics

The `diagnostic_validation.jl` script provides integrated validation and diagnostic analysis:

```bash
# Run unified validation and diagnostics
julia validation/diagnostic_validation.jl --output unified_report.txt

# Quick mode (faster, less comprehensive)
julia validation/diagnostic_validation.jl --quick

# Skip validation tests (diagnostics only)
julia validation/diagnostic_validation.jl --no-validation
```

**Features**:
- Cross-validation between diagnostic and validation results
- Conflict detection and issue correlation
- Confidence scoring
- Deployment readiness assessment

## 📊 Understanding Results

### Health Scores

| Score Range | Grade | Interpretation |
|-------------|-------|----------------|
| 95-100 | A+ | Excellent - Production ready |
| 90-94 | A | Very Good - Production ready |
| 85-89 | A- | Good - Production ready with monitoring |
| 80-84 | B+ | Good - Development ready |
| 75-79 | B | Acceptable - Development ready |
| 70-74 | B- | Acceptable - Needs attention |
| 65-69 | C+ | Below average - Needs work |
| 60-64 | C | Poor - Significant issues |
| 55-59 | C- | Poor - Major improvements needed |
| 50-54 | D+ | Very poor - Extensive work required |
| 45-49 | D | Very poor - Not suitable for CFD |
| 40-44 | D- | Critical - Do not use |
| <40 | F | Critical - Dangerous to use |

### System Readiness Levels

- **PRODUCTION_READY**: Safe for production CFD simulations
- **DEVELOPMENT_READY**: Suitable for development and testing
- **TESTING_ONLY**: Use only for algorithm testing
- **CRITICAL_ISSUES**: Do not use - fix issues first

### Quality Metrics Guidelines

#### Orthogonality
- **Excellent**: min > 0.8
- **Good**: min > 0.5
- **Acceptable**: min > 0.2
- **Poor**: min < 0.2

#### Skewness
- **Excellent**: max < 0.2
- **Good**: max < 0.5
- **Acceptable**: max < 0.8
- **Poor**: max > 0.8

#### Aspect Ratio
- **Excellent**: max < 10
- **Good**: max < 100
- **Acceptable**: max < 1000
- **Poor**: max > 1000

## 🚀 Best Practices

### 1. Pre-Simulation Workflow

```bash
# 1. Quick health check
julia tools/juliafoam_doctor.jl --quick

# 2. If issues found, run detailed diagnostics
julia tools/juliafoam_doctor.jl --output detailed_health.txt

# 3. Run mathematical validation
julia validation/quick_validation_test.jl

# 4. If developing, run full validation suite
julia validation/run_all_validation.jl
```

### 2. Continuous Integration

```yaml
# GitHub Actions example
- name: JuliaFOAM Health Check
  run: |
    julia tools/juliafoam_doctor.jl --score-only > health_score.txt
    score=$(cat health_score.txt)
    if (( $(echo "$score < 70" | bc -l) )); then
      echo "Health score too low: $score"
      exit 1
    fi
```

### 3. Development Workflow

1. **Before committing**: Run quick validation
2. **Before merging**: Run comprehensive diagnostics
3. **Before releasing**: Run unified validation & diagnostics
4. **Performance regression**: Use diagnostic tools to identify issues

### 4. Debugging Workflow

```bash
# 1. Run unified diagnostics to identify problem areas
julia validation/diagnostic_validation.jl

# 2. Focus on specific area (e.g., mesh quality)
julia -e "
include(\"src/tools/MeshQualityChecks.jl\")
# ... detailed mesh analysis
"

# 3. Test fixes with validation
julia validation/fixes/test_fixes_verification.jl
```

## 📈 Interpreting Common Issues

### Mathematical Validation Failures

**Symptoms**: Quick validation fails, poor convergence orders
**Diagnosis**: Check FVC operator implementations
**Solutions**: 
- Fix gradient computation
- Correct boundary condition treatment
- Verify matrix assembly

### Mesh Quality Issues

**Symptoms**: Poor orthogonality, high skewness
**Diagnosis**: Mesh geometry problems
**Solutions**:
- Remesh with better quality
- Use robust solvers
- Adjust discretization schemes

### Solver Convergence Problems

**Symptoms**: Slow or failed convergence
**Diagnosis**: Matrix conditioning, preconditioner effectiveness
**Solutions**:
- Use better preconditioners
- Adjust solver tolerances
- Check matrix assembly

### Integration Issues

**Symptoms**: Component tests pass individually but integration fails
**Diagnosis**: Interface problems, inconsistent implementations
**Solutions**:
- Check component interfaces
- Verify data consistency
- Test component interactions

## 🔧 Advanced Usage

### Custom Diagnostic Development

```julia
# Extend the diagnostic framework
include("src/tools/MeshDiagnostics.jl")
using .MeshDiagnostics

function my_custom_check(mesh::Mesh)
    # Custom analysis
    return custom_results
end

# Integrate with existing diagnostics
```

### Performance Profiling

```julia
# Use diagnostic tools for performance analysis
results = run_full_diagnostics(health_check)
println("Assembly time: $(results.fvm_results.assembly_performance_ms) ms")
println("Solve time: $(results.fvm_results.solve_performance_ms) ms")
```

### Automated Quality Gates

```julia
# Set up automated quality gates
function check_quality_gate(mesh::Mesh)
    health_score = get_health_score(mesh)
    recommendations = doctor_recommendations(mesh, max_recommendations=3)
    
    if health_score < 70
        error("Quality gate failed: score $health_score < 70")
    end
    
    return health_score, recommendations
end
```

## 📚 Reference

### Command Line Tools

- `tools/juliafoam_doctor.jl`: Main diagnostic CLI
- `validation/quick_validation_test.jl`: Fast mathematical validation
- `validation/run_all_validation.jl`: Comprehensive validation
- `validation/diagnostic_validation.jl`: Unified validation & diagnostics

### Julia Modules

- `MeshDiagnostics`: Mesh quality and geometry analysis
- `FVCDiagnostics`: Finite volume calculus operator validation
- `FVMDiagnostics`: Finite volume method system analysis
- `JuliaFOAMDoctor`: Master health assessment
- `MeshQualityChecks`: Industry-standard mesh quality metrics

### Configuration Files

- `validation/validation_config.toml`: Validation test configuration
- Individual diagnostic tolerances can be set per tool

## 🆘 Troubleshooting

### Common Error Messages

**"Matrix assembly failed"**
- Check mesh connectivity
- Verify boundary conditions
- Inspect cell volumes and face areas

**"Poor convergence order detected"**
- Review operator implementations
- Check boundary condition treatment
- Verify mesh quality

**"Conservation laws violated"**
- Check face flux calculations
- Verify boundary condition consistency
- Inspect matrix assembly

### Getting Help

1. **Run diagnostics**: Always start with health check
2. **Check reports**: Read detailed diagnostic reports
3. **Follow recommendations**: Implement suggested fixes
4. **Re-validate**: Run diagnostics again after fixes
5. **Community support**: Share diagnostic reports when seeking help

---

## Conclusion

The JuliaFOAM diagnostic and doctor utilities provide comprehensive tools for ensuring the mathematical correctness, mesh quality, and overall health of CFD simulations. Use these tools regularly during development, before production runs, and when debugging issues.

**Remember**: A healthy CFD system starts with proper diagnostics! 🩺✨