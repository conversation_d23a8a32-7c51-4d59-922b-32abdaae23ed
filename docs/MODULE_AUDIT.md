# Module Dependency Audit

This document maps out the current module hierarchy, their includes, imports/exports, and key type definitions in JuliaFOAM.

## 1. Top-level loader (`JuliaFOAM.jl`)

**Layer 1: Core**
- include(`core/Types.jl`)
  - Defines: `Mesh`, `Cell`, `Face`, `Field`, `BoundaryCondition`, `FluidProperties`, `CaseConfiguration`, `SolverSettings`.
  - Exported: as public types in main module.
- include(`core/BoundaryConditions.jl`)
  - Imports: `..JuliaFOAM: Field, Mesh`
  - Defines: `apply_boundary_conditions!`, `get_boundary_face_value`, `get_boundary_face_vector_value`.

**Layer 2: Mesh & Numerics**
- include(`meshes/Mesh.jl`)
  - Imports: `..JuliaFOAM: Cell, Face, BoundaryCondition`
  - Exports: `create_box_mesh`, `read_basic_mesh`, etc.
- include(`numerics/VectorizedOperations.jl`)
  - Imports: core types.
  - Exports: SIMD operations.

**Layer 3: Discretization**
- include(`finiteVolume/FiniteVolume.jl`)
  - Imports: `..JuliaFOAM: Mesh, Field, FluidProperties, Face, get_boundary_face_value`
  - Exports: `grad_gauss_linear!`, `div_gauss_linear!`, `laplacian_gauss_linear!`, `build_momentum_matrix`, `build_pressure_equation`.
- include(`finiteVolume/FluxLimiters.jl`)

**Layer 4: Solvers**
- include(`solvers/AdvancedPreconditioners.jl`)
- include(`solvers/OptimizedSolvers.jl`)
- include(`solvers/EnhancedSolvers.jl`)
- include(`solvers/PisoSolver.jl`)
- include(`solvers/CoupledSolver.jl`)
- include(`solvers/SimpleSolver.jl`)
  - Imports: `..JuliaFOAM: Mesh, FluidProperties, Field, apply_boundary_conditions!, correct_velocity!`,
             `..JuliaFOAM: build_momentum_matrix, build_pressure_equation, solve_linear_system`
  - Defines: `SimpleSolverConfig`, `solve_simple`.

**Layer 5: I/O & Interoperability**
- include(`io/OpenFOAMImportExport.jl`)
- include(`core/OpenFOFMDictionary.jl`)

## 2. Import/Export Summary

| Module                            | Imports                                   | Exports                                           |
|-----------------------------------|-------------------------------------------|---------------------------------------------------|
| core/Types.jl                     | none                                      | `Mesh, Cell, Face, Field, BoundaryCondition, FluidProperties, CaseConfiguration, SolverSettings` |
| core/BoundaryConditions.jl        | `..JuliaFOAM: Field, Mesh`               | `apply_boundary_conditions!, get_boundary_face_value, get_boundary_face_vector_value`  |
| meshes/Mesh.jl                    | `..JuliaFOAM: Cell, Face, BoundaryCondition` | `create_box_mesh, read_basic_mesh, ...`           |
| finiteVolume/FiniteVolume.jl      | `..JuliaFOAM: Mesh, Field, FluidProperties, Face, get_boundary_face_value` | `grad_gauss_linear!, div_gauss_linear!, laplacian_gauss_linear!, build_momentum_matrix, build_pressure_equation` |
| solvers/SimpleSolver.jl           | see above                                 | `SimpleSolverConfig, solve_simple`                |

## 3. Key Type Definitions

- **Mesh**: in `core/Types.jl`, central mesh container imported everywhere.
- **Field**: in `core/Types.jl`, used across discretization and solvers.
- **FluidProperties**: in `core/Types.jl`, imported by solvers and discretization.
- **CaseConfiguration** & **SimpleSolverConfig**: configuration types for case setup and SIMPLE solver.

> This audit identifies potential circular imports (e.g., solver modules importing each other). Next steps: resolve circular references by consolidating interfaces and using abstract interfaces where needed.
