# OpenFOAM I/O Model in JuliaFOAM

## Overview

JuliaFOAM follows the OpenFOAM I/O model where each processor reads and writes to its own separate directory. This is **not** parallel I/O in the HDF5/MPI-IO sense, but rather a simple and robust approach where file system parallelism comes from multiple processes accessing different files.

## Directory Structure

```
case/
├── system/                    # Shared configuration
├── constant/                  # Original mesh
│   └── polyMesh/
├── 0/                        # Initial conditions (original)
├── processor0/               # Processor 0 subdomain
│   ├── constant/
│   │   └── polyMesh/        # Processor 0 mesh
│   ├── 0/                   # Processor 0 initial fields
│   │   ├── U
│   │   └── p
│   └── 100/                 # Processor 0 results at t=100
│       ├── U
│       └── p
├── processor1/               # Processor 1 subdomain
│   ├── constant/
│   │   └── polyMesh/
│   ├── 0/
│   └── 100/
└── processorN/               # Processor N subdomain
    └── ...
```

## I/O Operations

### 1. Decomposition (Serial)

`decomposePar` runs as a single process and:
- Reads the global mesh from `constant/polyMesh/`
- Partitions the mesh
- Writes each processor's mesh to `processorN/constant/polyMesh/`
- Writes each processor's fields to `processorN/timeDir/`

```julia
# Serial process writes all processor directories
for proc in 0:n_procs-1
    proc_dir = "processor$proc"
    write_mesh("$proc_dir/constant/polyMesh/", processor_meshes[proc])
    write_fields("$proc_dir/0/", processor_fields[proc])
end
```

### 2. Parallel Execution (Independent I/O)

During parallel simulation, each MPI process:
- Reads only from its own processor directory
- Writes only to its own processor directory
- No coordination needed for I/O

```julia
# Each MPI process independently
rank = MPI.Comm_rank(comm)
my_mesh = read_mesh("processor$rank/constant/polyMesh/")
my_fields = read_fields("processor$rank/0/")

# ... compute ...

write_fields("processor$rank/100/", my_fields)
```

### 3. Reconstruction (Serial)

`reconstructPar` runs as a single process and:
- Reads from all processor directories
- Combines the data
- Writes the global fields

```julia
# Serial process reads all processor directories
global_fields = []
for proc in 0:n_procs-1
    proc_fields = read_fields("processor$proc/100/")
    append!(global_fields, proc_fields)
end
write_fields("100/", reconstruct(global_fields))
```

## Key Points

1. **No MPI-IO or HDF5 Parallel**: Each process handles its own files
2. **File System Does the Work**: Parallel performance comes from the file system handling multiple simultaneous file operations
3. **Simple and Robust**: No complex parallel I/O libraries needed
4. **Scalable**: Works well up to thousands of processors on modern parallel file systems

## Implementation Notes

### What "parallel_io" Flag Means

In the JuliaFOAM implementation, the `parallel_io` flag does NOT mean using MPI-IO. Instead:

- `parallel_io=false`: Serial mode - one process does all I/O sequentially
- `parallel_io=true`: Parallel mode - multiple processes do I/O simultaneously to different files

### Example: Parallel Decomposition

```julia
if config.parallel_io && MPI.Initialized()
    # Each MPI rank handles some processors
    my_processors = distribute_work(0:n_procs-1, rank, mpi_size)
    for proc in my_processors
        write_processor_data("processor$proc/", data[proc])
    end
else
    # Serial mode - one process writes everything
    for proc in 0:n_procs-1
        write_processor_data("processor$proc/", data[proc])
    end
end
```

## Advantages of This Approach

1. **Simplicity**: No complex parallel I/O libraries needed
2. **Portability**: Works on any file system
3. **Debugging**: Easy to inspect individual processor files
4. **Restart**: Can restart with different processor counts
5. **Post-processing**: Can process individual processor results

## Performance Considerations

- **Small Files**: May stress metadata operations on some file systems
- **Large Processor Counts**: May hit file system limits
- **NFS**: Can be slow due to locking; prefer parallel file systems
- **Lustre/GPFS**: Excellent performance due to parallel file system design

## Best Practices

1. Use local scratch for temporary files
2. Limit output frequency to reduce I/O load
3. Use binary format for large fields
4. Consider processor count vs file system capabilities
5. Monitor file system quotas (inodes and space)