# JuliaFOAM User Guide

## Table of Contents
1. [Getting Started](#getting-started)
2. [Basic Concepts](#basic-concepts)
3. [Creating Meshes](#creating-meshes)
4. [Setting Boundary Conditions](#setting-boundary-conditions)
5. [Running Simulations](#running-simulations)
6. [Post-Processing](#post-processing)
7. [Common Workflows](#common-workflows)

## Getting Started

### Installation

```julia
using Pkg
Pkg.add(url="https://github.com/mberto79/JuliaFOAM.jl")
```

### First Simulation

Here's a complete example of a 2D lid-driven cavity simulation:

```julia
using JuliaFOAM

# Create mesh
mesh = create_2d_mesh_as_3d(20, 20, 1.0, 1.0)

# Define boundary conditions
bcs = Dict(
    "velocity" => Dict(
        "top" => MovingWallBC(SVector(1.0, 0.0, 0.0)),
        "bottom" => NoSlipBC(),
        "left" => NoSlipBC(),
        "right" => NoSlipBC(),
        "front" => EmptyBC(),
        "back" => EmptyBC()
    ),
    "pressure" => Dict(
        "top" => ZeroGradientBC(),
        "bottom" => ZeroGradientBC(),
        "left" => ZeroGradientBC(),
        "right" => ZeroGradientBC(),
        "front" => EmptyBC(),
        "back" => EmptyBC()
    )
)

# Configure solver
config = NavierStokesConfig(
    ρ = 1.0,
    μ = 0.01,
    max_iter = 1000,
    tolerance = 1e-6
)

# Initialize and solve
state = NavierStokesState(mesh)
solve_navier_stokes_enhanced!(state, mesh, config, bcs)
```

## Basic Concepts

### Mesh Structure

JuliaFOAM uses an unstructured mesh representation:
- **Cells**: Control volumes where equations are solved
- **Faces**: Interfaces between cells
- **Nodes**: Vertices of the mesh
- **Boundaries**: Named patches for applying boundary conditions

### Fields

Fields store solution variables:
- **Scalar fields**: Temperature, pressure, etc.
- **Vector fields**: Velocity, gradients, etc.

```julia
# Create fields
pressure = ScalarField(mesh, 0.0)
velocity = VectorField(mesh, SVector(0.0, 0.0, 0.0))
```

### Boundary Conditions

Available boundary condition types:
- `FixedValueBC(value)`: Dirichlet condition
- `FixedGradientBC(gradient)`: Neumann condition
- `ZeroGradientBC()`: Zero normal gradient
- `NoSlipBC()`: Wall with no velocity
- `MovingWallBC(velocity)`: Moving wall
- `SymmetryPlaneBC()`: Symmetry boundary
- `EmptyBC()`: For 2D simulations

## Creating Meshes

### Simple Box Mesh

```julia
# 3D box mesh
mesh = create_box_mesh(nx=10, ny=10, nz=10, 
                      Lx=1.0, Ly=1.0, Lz=1.0)

# 2D mesh (thin 3D)
mesh = create_2d_mesh_as_3d(nx=20, ny=20, 
                           Lx=1.0, Ly=1.0, 
                           thickness=0.1)
```

### Import OpenFOAM Mesh

```julia
mesh = read_openfoam_mesh("path/to/case/constant/polyMesh")
```

### Unstructured Mesh

```julia
# Define nodes
nodes = [
    SVector(0.0, 0.0, 0.0),
    SVector(1.0, 0.0, 0.0),
    SVector(1.0, 1.0, 0.0),
    SVector(0.0, 1.0, 0.0)
]

# Define faces (node indices)
faces = [
    [1, 2],  # bottom
    [2, 3],  # right
    [3, 4],  # top
    [4, 1]   # left
]

# Define cells (face indices)
cells = [[1, 2, 3, 4]]

# Create mesh
mesh = UnstructuredMesh(nodes, faces, cells)
```

## Setting Boundary Conditions

### Basic Setup

```julia
bcs = Dict(
    "velocity" => Dict(
        "inlet" => FixedValueBC(SVector(1.0, 0.0, 0.0)),
        "outlet" => ZeroGradientBC(),
        "walls" => NoSlipBC()
    ),
    "pressure" => Dict(
        "inlet" => ZeroGradientBC(),
        "outlet" => FixedValueBC(0.0),
        "walls" => ZeroGradientBC()
    )
)
```

### Time-Varying Conditions

```julia
# Pulsating inlet
inlet_velocity(t) = SVector(1.0 + 0.5*sin(2π*t), 0.0, 0.0)
bcs["velocity"]["inlet"] = TimeVaryingBC(inlet_velocity)
```

## Running Simulations

### Basic Solver

```julia
# Create configuration
config = NavierStokesConfig(
    ρ = 1.0,              # Density
    μ = 0.001,            # Dynamic viscosity
    max_iter = 1000,      # Maximum iterations
    tolerance = 1e-6,     # Convergence tolerance
    under_relaxation = Dict(
        "velocity" => 0.7,
        "pressure" => 0.3
    )
)

# Initialize state
state = NavierStokesState(mesh)

# Solve
solve_navier_stokes_enhanced!(state, mesh, config, bcs)
```

### Monitor Convergence

```julia
# Custom convergence monitoring
config.monitor_frequency = 10  # Print every 10 iterations
config.convergence_history = true  # Store convergence history

# Solve and get history
history = solve_navier_stokes_enhanced!(state, mesh, config, bcs)

# Plot convergence
using Plots
plot(history.residuals, yscale=:log10, 
     labels=["Momentum" "Continuity"],
     xlabel="Iteration", ylabel="Residual")
```

### Transient Simulations

```julia
# Time-dependent configuration
config = TransientNavierStokesConfig(
    ρ = 1.0,
    μ = 0.001,
    dt = 0.01,           # Time step
    end_time = 10.0,     # End time
    write_interval = 0.1, # Output frequency
    adaptive_dt = true,   # Enable adaptive time stepping
    max_cfl = 0.5        # Maximum CFL number
)

# Run transient simulation
solve_transient!(state, mesh, config, bcs)
```

## Post-Processing

### Write Results

```julia
# VTK format (requires WriteVTK.jl)
write_vtk("results/cavity", mesh, state)

# OpenFOAM format
write_openfoam_fields("results", mesh, state, time=0.0)
```

### Extract Data

```julia
# Get field values
u_max = maximum(norm.(state.velocity.values))
p_min = minimum(state.pressure.values)

# Extract along line
x_line = range(0, 1, length=100)
y = 0.5
u_profile = [interpolate(state.velocity, SVector(x, y, 0.0), mesh) 
             for x in x_line]

# Calculate derived quantities
vorticity = calculate_vorticity(state.velocity, mesh)
q_criterion = calculate_q_criterion(state.velocity, mesh)
```

### Validation Metrics

```julia
# Mass conservation check
divergence = calculate_divergence(state.velocity, mesh)
mass_error = maximum(abs.(divergence))
println("Mass conservation error: $mass_error")

# Compare with analytical solution
analytical_u(x, y) = # ... your analytical solution
error = calculate_l2_error(state.velocity, analytical_u, mesh)
println("L2 error: $error")
```

## Common Workflows

### Parameter Study

```julia
# Reynolds number sweep
Re_values = [100, 500, 1000, 5000]
results = []

for Re in Re_values
    # Update viscosity
    config.μ = 1.0 / Re
    
    # Reset state
    state = NavierStokesState(mesh)
    
    # Solve
    solve_navier_stokes_enhanced!(state, mesh, config, bcs)
    
    # Store results
    push!(results, (Re=Re, state=deepcopy(state)))
end
```

### Mesh Refinement Study

```julia
# Grid convergence study
mesh_sizes = [10, 20, 40, 80]
errors = []

for n in mesh_sizes
    # Create mesh
    mesh = create_2d_mesh_as_3d(n, n, 1.0, 1.0)
    
    # Solve
    state = NavierStokesState(mesh)
    solve_navier_stokes_enhanced!(state, mesh, config, bcs)
    
    # Calculate error
    error = calculate_error_metric(state, analytical_solution)
    push!(errors, error)
end

# Check convergence order
order = log.(errors[1:end-1] ./ errors[2:end]) ./ log(2)
```

### Restart Simulation

```julia
# Save checkpoint
save_checkpoint("checkpoint.jld2", state, mesh, config)

# Load and continue
state, mesh, config = load_checkpoint("checkpoint.jld2")
config.max_iter += 1000  # Run more iterations
solve_navier_stokes_enhanced!(state, mesh, config, bcs)
```

## Tips and Best Practices

1. **Mesh Quality**: Check mesh quality before simulation
   ```julia
   quality = check_mesh_quality(mesh)
   println("Min orthogonality: $(quality.min_orthogonality)")
   println("Max skewness: $(quality.max_skewness)")
   ```

2. **Under-Relaxation**: Start with conservative values
   - Velocity: 0.7
   - Pressure: 0.3
   - Turbulence: 0.5

3. **Initial Conditions**: Provide good initial guesses
   ```julia
   # Initialize with potential flow
   initialize_potential_flow!(state, mesh, bcs)
   ```

4. **Convergence Issues**: Use robust solver
   ```julia
   # Switch to robust mode if needed
   config.use_robust_mode = true
   config.fallback_iterations = 10
   ```

5. **Performance**: Enable optimizations
   ```julia
   # Enable SIMD vectorization
   config.use_simd = true
   
   # Use multigrid for pressure
   config.pressure_solver = GeometricMultigrid()
   ```