Key Requirements
Automatic Domain Decomposition & Partitioning

The framework must partition any input mesh (structured/unstructured) across MPI ranks without user involvement.

Use METIS/ParMETIS or equivalent for partitioning. Pre-partitioned meshes should be accepted but not required.

Transparent Halo Communication

Implement a Field class that automatically synchronizes halo regions before stencil operations (e.g., gradients, reconstructions).

No manual MPI calls or boundary condition handling in user code.

Physics-Focused API

Users interact with global mesh indices; the framework maps these to local partitions.

Provide physics templates (e.g., NavierStokesSolver) where users implement:

computeFlux() (physics-specific calculations)

applyBoundaryConditions() (local cell operations only)

Mesh Abstraction

Mesh class with globalToLocal(index) method for seamless access.

Automatic handling of ghost cell indexing.

Parallel Workflow

Hide MPI initialization/finalization inside CFDContext::init(argc, argv) and CFDContext::finalize().

Time-stepping handled by Scheduler::run(solver, steps).


