# Transparent Parallelism in JuliaFOAM

## Overview

JuliaFOAM now includes a transparent parallelism layer that allows existing serial code to run efficiently in parallel with minimal modifications. This implementation provides:

- **Distributed Fields**: Automatic data distribution and halo exchange
- **Distributed Meshes**: Transparent mesh partitioning and ghost cell management
- **Parallel Operations**: Finite volume operators that work seamlessly across processes
- **Automatic Synchronization**: Fields are synchronized automatically when needed
- **Load Balancing**: Dynamic repartitioning for optimal performance

## Key Components

### 1. DistributedField

The `DistributedField` type wraps regular `Field` objects and adds parallel capabilities:

```julia
# Create a distributed field (automatically partitioned)
T = DistributedField("temperature", mesh, 300.0)  # Scalar field
U = DistributedField("velocity", mesh, SVector(0.0, 0.0, 0.0))  # Vector field

# Use like regular fields
T[i] = 350.0  # Set local value (automatic synchronization)
T_sum = sum(T)  # Global sum across all processes
```

Key features:
- Automatic halo cell synchronization
- Transparent arithmetic operations
- Global reductions (sum, max, min, norm)
- Broadcasting support

### 2. DistributedMeshData

The `DistributedMeshData` type provides transparent mesh partitioning:

```julia
# Wrap existing mesh
dmesh = DistributedMeshData(mesh)

# Automatic partitioning on first use
local_mesh = get_local_mesh(dmesh)

# Query mesh information
is_owner(dmesh, cell_idx)  # Check if this process owns a cell
stats = mesh_stats(dmesh)   # Get load balance statistics
```

Features:
- Lazy partitioning (only when needed)
- Multiple partitioning methods (METIS, SCOTCH, simple)
- Ghost layer management
- Dynamic repartitioning

### 3. Parallel Operations

All finite volume operations work transparently with distributed fields:

```julia
# Gradient operator
grad_T = grad(T, dmesh)

# Divergence operator
div_U = div(U, dmesh)

# Laplacian operator
lap_T = laplacian(T, dmesh, diffusion_coeff=0.1)

# Operations automatically handle:
# - Halo synchronization
# - Boundary conditions
# - Cross-process communication
```

## Usage Examples

### Basic Parallel Simulation

```julia
using JuliaFOAM
using JuliaFOAM.TransparentParallel

# Initialize MPI
init_parallel()

# Load mesh (same as serial)
mesh = load_mesh("case/constant/polyMesh")

# Create distributed mesh
dmesh = DistributedMeshData(mesh)

# Create fields (automatically distributed)
p = DistributedField("p", mesh, 0.0)
U = DistributedField("U", mesh, SVector(0.0, 0.0, 0.0))
T = DistributedField("T", mesh, 300.0)

# Time loop (same as serial!)
for step in 1:n_steps
    # Momentum equation
    U_flux = div(U ⊗ U, dmesh)
    U_diffusion = laplacian(U, dmesh, diffusion_coeff=nu)
    
    # Update velocity
    U += dt * (-U_flux + U_diffusion - grad(p, dmesh))
    
    # Pressure correction
    div_U = div(U, dmesh)
    p_correction = solve_poisson(div_U, dmesh)
    p += p_correction
    
    # Temperature equation
    T_flux = div(U * T, dmesh)
    T_diffusion = laplacian(T, dmesh, diffusion_coeff=alpha)
    T += dt * (-T_flux + T_diffusion)
    
    # Fields automatically synchronized!
end

# Finalize
finalize_parallel()
```

### Converting Existing Code

To parallelize existing serial code:

1. **Add initialization**:
```julia
# Add at start
init_parallel()
dmesh = DistributedMeshData(mesh)

# Add at end
finalize_parallel()
```

2. **Convert fields**:
```julia
# Serial
p = Field("p", mesh, 0.0)

# Parallel
p = DistributedField("p", mesh, 0.0)
```

3. **Update operators** (add dmesh parameter):
```julia
# Serial
grad_p = grad(p, mesh)

# Parallel
grad_p = grad(p, dmesh)
```

That's it! The code now runs in parallel.

### Advanced Features

#### Asynchronous Operations

```julia
# Start halo exchange
sync_async!(field)

# Do computation on interior cells while communication happens
compute_interior_cells!(field)

# Complete synchronization
wait_sync!(field)
```

#### Dynamic Load Balancing

```julia
# Check load balance
stats = mesh_stats(dmesh)

# Repartition if needed
repartition!(dmesh, method=:metis, imbalance_threshold=0.1)
```

#### Parallel I/O

```julia
# Write distributed field
parallel_write(field, "results/")

# Read distributed field
field = parallel_read("T", mesh, "results/", Float64)
```

## Implementation Details

### Communication Patterns

The implementation uses several optimization strategies:

1. **Lazy Synchronization**: Halo cells are only synchronized when needed
2. **Communication Aggregation**: Small messages are bundled
3. **Computation-Communication Overlap**: Interior cells are computed while halo exchange happens
4. **Non-blocking Communication**: MPI non-blocking operations throughout

### Mesh Partitioning

Three partitioning methods are available:

1. **Simple**: Recursive coordinate bisection (good for structured meshes)
2. **METIS**: Graph-based partitioning (optimal for unstructured meshes)
3. **SCOTCH**: Alternative graph partitioner

The partitioner aims to:
- Balance computational load
- Minimize communication volume
- Reduce edge cuts between partitions

### Memory Layout

Distributed fields store:
- Local cell values (owned by this process)
- Halo cell values (ghost cells from neighbors)
- Communication lists (send/receive patterns)

Memory access patterns are optimized for:
- Cache efficiency
- Vectorization
- Minimal data movement

## Performance Considerations

### Strong Scaling

The implementation shows good strong scaling up to hundreds of processes for typical CFD problems. Key factors:

- Communication is overlapped with computation
- Load balancing minimizes idle time
- Efficient collective operations

### Weak Scaling

Weak scaling is near-ideal for problems with sufficient work per process:
- Constant communication-to-computation ratio
- Scalable mesh partitioning
- Efficient ghost layer updates

### Best Practices

1. **Use appropriate mesh partitioning**:
   - METIS for complex unstructured meshes
   - Simple for structured grids

2. **Monitor load balance**:
   ```julia
   stats = mesh_stats(dmesh)
   if stats.load_imbalance > 0.1
       repartition!(dmesh)
   end
   ```

3. **Minimize synchronization**:
   - Batch operations when possible
   - Use asynchronous synchronization
   - Overlap communication with computation

4. **Choose ghost layers wisely**:
   - 1 layer for first-order schemes
   - 2 layers for second-order schemes
   - More for higher-order methods

## Limitations and Future Work

Current limitations:
- Adaptive mesh refinement not yet supported
- Limited to MPI (no GPU support yet)
- Some advanced boundary conditions need work

Planned improvements:
- GPU acceleration via CUDA.jl
- Hybrid MPI+threads parallelism
- Support for PartitionedArrays.jl
- Advanced I/O with parallel HDF5

## Example Programs

See the `examples/` directory for complete parallel examples:
- `parallel_cavity_flow.jl`: Lid-driven cavity benchmark
- `parallel_channel_flow.jl`: Turbulent channel flow
- `parallel_heat_transfer.jl`: Conjugate heat transfer

## Running Parallel Programs

To run JuliaFOAM in parallel:

```bash
# With mpiexec
mpiexec -np 4 julia my_simulation.jl

# With mpirun
mpirun -np 8 julia my_simulation.jl

# On HPC cluster (example SLURM)
srun -n 64 julia my_simulation.jl
```

## Debugging Parallel Code

Enable debug output:
```julia
ENV["JULIA_DEBUG"] = "TransparentParallel"
```

Use parallel timers:
```julia
timer = start_timer("My operation")
# ... do work ...
stop_timer(timer)  # Prints timing statistics
```

Check field synchronization:
```julia
info = get_halo_info(field)
println("Halo cells: ", info.n_halo)
println("Send neighbors: ", info.send_neighbors)
```