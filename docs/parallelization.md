# JuliaFOAM Parallelization Framework

This document describes the parallelization framework in JuliaFOAM, which enables efficient parallel computations using both distributed memory (MPI) and shared memory (threading) parallelism.

## Table of Contents

1. [Overview](#overview)
2. [Hierarchical Parallelism](#hierarchical-parallelism)
3. [Non-blocking Communication](#non-blocking-communication)
4. [Parallel Mesh Structure](#parallel-mesh-structure)
5. [Load Balancing](#load-balancing)
6. [Performance Monitoring](#performance-monitoring)
7. [Scaling Tests](#scaling-tests)
8. [Usage Examples](#usage-examples)

## Overview

The JuliaFOAM parallelization framework is designed to enable efficient parallel computations on modern computing architectures. It combines distributed memory parallelism using MPI with shared memory parallelism using Julia's native threading capabilities. This hierarchical approach allows JuliaFOAM to efficiently utilize multi-core, multi-node computing architectures.

Key features of the parallelization framework include:

- Hierarchical parallelism (MPI + threading)
- Non-blocking communication for overlapping computation and communication
- Parallel-optimized mesh structure
- Advanced load balancing
- Performance monitoring and profiling
- Scaling tests

## Hierarchical Parallelism

Hierarchical parallelism combines distributed memory parallelism (MPI) with shared memory parallelism (threading) to efficiently utilize modern computing architectures. In JuliaFOAM, this is implemented as follows:

1. The domain is first decomposed into subdomains, with each subdomain assigned to an MPI process.
2. Within each MPI process, the computation is further parallelized using Julia's native threading capabilities.

This approach has several advantages:

- Reduced communication overhead compared to pure MPI parallelism
- Better utilization of shared memory within a node
- Improved cache locality
- Better scalability on modern multi-core, multi-node architectures

### Example: Hierarchical SIMPLE Algorithm

The hierarchical SIMPLE algorithm is implemented in `ParallelOptimizations.jl` and can be used as follows:

```julia
# Create mesh
mesh = create_box_mesh(nx, ny, nz, lx, ly, lz)
opt_mesh = convert_to_optimized_mesh(mesh)

# Create fields
U = Field("U", opt_mesh, SVector{3,Float64}(0.0, 0.0, 0.0), :cellCenters)
p = Field("p", opt_mesh, 0.0, :cellCenters)

# Set fluid properties
properties = FluidProperties(nu=0.01, rho=1.0)

# Solve using hierarchical SIMPLE algorithm
U, p, iterations, U_residual, p_residual = hierarchical_solve_simple!(
    U, p, opt_mesh, properties, 0.7, 0.3, 1000, 1e-6
)
```

## Non-blocking Communication

Non-blocking communication allows overlapping computation and communication, which can significantly improve performance in parallel computations. JuliaFOAM implements non-blocking communication for both scalar and vector fields.

### Scalar Fields

```julia
# Exchange halo data for scalar field
requests = exchange_halo_data_nonblocking!(field, mesh)

# Do some computation while communication is happening
# ...

# Wait for communication to complete
wait_for_halo_exchange!(requests, field, mesh)
```

### Vector Fields

```julia
# Exchange halo data for vector field
requests = exchange_halo_data_nonblocking_vector!(field, mesh)

# Do some computation while communication is happening
# ...

# Wait for communication to complete
wait_for_halo_exchange_vector!(requests, field, mesh)
```

## Parallel Mesh Structure

JuliaFOAM provides a parallel-optimized mesh structure (`ParallelMesh`) that is designed for efficient parallel computations. This mesh structure includes:

- Local and halo cells and faces
- Send and receive maps for communication
- Thread partitioning for shared memory parallelism
- Cache optimization for better performance

### Creating a Parallel Mesh

```julia
# Create sequential mesh
mesh = create_box_mesh(nx, ny, nz, lx, ly, lz)
opt_mesh = convert_to_optimized_mesh(mesh)

# Create cell partition
cell_partition = decompose_mesh_metis(opt_mesh, nprocs)

# Create parallel mesh
parallel_mesh = create_parallel_mesh(opt_mesh, cell_partition, MPI.COMM_WORLD)
```

## Load Balancing

JuliaFOAM includes advanced load balancing capabilities to ensure efficient utilization of computing resources. This includes:

- Performance metrics tracking
- Dynamic load balancing
- Topology-aware partitioning

### Performance Metrics

```julia
# Create performance metrics
metrics = create_performance_metrics(mesh, nprocs)

# Update metrics with new timing information
update_performance_metrics!(metrics, proc_times, comm_times)
```

### Load Balancing

```julia
# Optimize load balance
new_partition = optimize_load_balance(mesh, cell_partition, metrics, nprocs)
```

### Topology-aware Partitioning

```julia
# Create topology-aware partition
cell_partition = create_topology_aware_partition(mesh, nprocs, n_nodes)
```

## Performance Monitoring

JuliaFOAM includes tools for monitoring and profiling the performance of parallel computations. This includes:

- Performance monitoring
- Profiling
- Scaling tests

### Performance Monitoring

```julia
# Create performance monitor
monitor = PerformanceMonitor()

# Start monitoring
start_monitoring!(monitor, "solver")

# Run solver
# ...

# Stop monitoring
stop_monitoring!(monitor, "solver")

# Get performance data
perf_data = get_performance_data(monitor, "solver")
```

### Profiling

```julia
# Create profile
profile = ParallelProfile()

# Profile a region
profile_region!(profile, "momentum_predictor", :computation)

# Run momentum predictor
# ...

# End profiling
profile_region!(profile, "momentum_predictor")

# Print profile summary
print_profile_summary(profile)
```

## Scaling Tests

JuliaFOAM includes tools for running scaling tests to evaluate the performance of parallel computations. This includes:

- Strong scaling tests
- Weak scaling tests
- Benchmarking against OpenFOAM

### Strong Scaling Tests

```julia
# Create scaling test
test = ScalingTest(
    "lid_driven_cavity",
    [(20, 20, 1), (40, 40, 1), (80, 80, 1)],
    [1, 2, 4, 8],
    [1, 2, 4]
)

# Run strong scaling test
run_strong_scaling_test(test, lid_driven_cavity_solver)

# Generate report
generate_scaling_report(test, "lid_driven_cavity_strong_scaling.md")
```

### Weak Scaling Tests

```julia
# Create scaling test
test = ScalingTest(
    "lid_driven_cavity",
    [(20, 20, 1)],
    [1, 2, 4, 8],
    [1, 2, 4]
)

# Run weak scaling test
run_weak_scaling_test(test, lid_driven_cavity_solver)

# Generate report
generate_scaling_report(test, "lid_driven_cavity_weak_scaling.md")
```

## Usage Examples

### Parallel SIMPLE Solver

```julia
# Create mesh
mesh = create_box_mesh(nx, ny, nz, lx, ly, lz)
opt_mesh = convert_to_optimized_mesh(mesh)

# Create fields
U = Field("U", opt_mesh, SVector{3,Float64}(0.0, 0.0, 0.0), :cellCenters)
p = Field("p", opt_mesh, 0.0, :cellCenters)

# Set fluid properties
properties = FluidProperties(nu=0.01, rho=1.0)

# Create solver settings
config = SimpleSolverConfig(
    max_iterations=1000,
    tolerance=1e-6,
    relaxation_factors=Dict("U"=>0.7, "p"=>0.3),
    track_residuals=true,
    residual_output_interval=10
)

# Solve using parallel SIMPLE solver
fields, residuals = solve_simple_parallel!(
    opt_mesh,
    Dict("U"=>U, "p"=>p),
    opt_mesh.boundary_conditions,
    config
)
```

### Parallel PISO Solver

```julia
# Create mesh
mesh = create_box_mesh(nx, ny, nz, lx, ly, lz)
opt_mesh = convert_to_optimized_mesh(mesh)

# Create fields
U = Field("U", opt_mesh, SVector{3,Float64}(0.0, 0.0, 0.0), :cellCenters)
p = Field("p", opt_mesh, 0.0, :cellCenters)

# Set fluid properties
properties = FluidProperties(nu=0.01, rho=1.0)

# Initialize PISO solver settings
settings = initialize_piso_solver(
    n_correctors=2,
    n_non_orthogonal_correctors=1,
    momentum_predictor=true,
    consistent_flux_reconstruction=true,
    tolerance=1e-6,
    max_iterations=1000,
    relaxation_p=0.3,
    relaxation_U=0.7
)

# Time step
dt = 0.01

# Solve using parallel PISO solver
face_fluxes = solve_piso_parallel!(U, p, opt_mesh, properties, dt, settings)
```
