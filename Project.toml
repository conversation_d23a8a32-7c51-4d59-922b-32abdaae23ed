name = "JuliaFOAM"
uuid = "d1a3a3b4-3e58-4f90-8cbb-123456789abc"
authors = ["JuliaFOAM Team"]
version = "0.1.0"

[deps]
AlgebraicMultigrid = "2169fc97-5a83-5252-b627-83903c6c433c"
BenchmarkTools = "6e4b80f9-dd63-53aa-95a3-0cdb28fa8baf"
CUDA = "052768ef-5323-5732-b1bb-66c8b64840ba"
Dates = "ade2ca70-3891-5945-98fb-dc099432e06a"
HDF5 = "f67ccb44-e63f-5c2f-98bd-6dc0ccc4ba2f"
IncompleteLU = "*************-5561-ab4c-a76e7d0d7895"
IterativeSolvers = "42fd0dbc-a981-5370-80f2-aaf504508153"
JSON = "682c06a0-de6a-54ab-a142-c8b1cf79cde6"
LinearAlgebra = "37e2e46d-f89d-539d-b4ee-838fcccc9c8e"
LinearSolve = "7ed4a6bd-45f5-4d41-b270-4a48e9bafcae"
LoopVectorization = "bdcacae8-1622-11e9-2a5c-************"
METIS_jll = "d00139f3-1899-568f-a2f0-47f597d42d70"
MPI = "da04e1cc-30fd-572f-bb4f-1f8673147195"
Metis = "2679e427-3c69-5b7f-982b-ece356f1e94b"
PETSc = "ace2c81b-2b5f-4b1e-a30d-d662738edfe0"
PETSc_jll = "8fa3689e-f0b9-5420-9873-adf6ccf46f2d"
PartitionedArrays = "5a9dfac6-5c52-46f7-8278-5e2210713be9"
Plots = "91a5bcdd-55d7-5caf-9e0b-520d859cae80"
Printf = "de0858da-6303-5e67-8744-51eddeeeb8d7"
Profile = "9abbd945-dff8-562f-b5e8-e1ebf5ef1b79"
SIMD = "fdea26ae-647d-5447-a871-4b548cad5224"
Serialization = "9e88b42a-f829-5b0c-bbe9-9e923198166b"
SparseArrays = "2f01184e-e22b-5df5-ae63-d93ebab69eaf"
StaticArrays = "90137ffa-7385-5640-81b9-e52037218182"
Statistics = "10745b16-79ce-11e8-11f9-7d13ad32a3b2"
Test = "8dfed614-e22c-5e08-85e1-65c5234f0b40"
WriteVTK = "64499a7a-5c06-52f2-abe2-ccb03c286192"

[compat]
BenchmarkTools = "1.6.0"
CUDA = "5.7.3"
IncompleteLU = "0.2.1"
Metis = "1.5.0"
PartitionedArrays = "0.5.10"
SIMD = "3.7.1"
Test = "1.11.0"
WriteVTK = "1.21.2"
julia = "1.6"

[extras]
Test = "8dfed614-e22c-5e08-85e1-65c5234f0b40"

[targets]
test = ["Test"]

[test]
path = "test/runtests.jl"
