#!/usr/bin/env julia

"""
Integration test for k-epsilon turbulent solver
This demonstrates our enhanced JuliaFOAM working with the k-epsilon model
"""

println("🧪 k-epsilon Turbulent Solver Integration Test")
println("="^60)

# Include our modules
include("src/TurbulenceModels.jl")
include("src/TurbulentSolver.jl")

# Create a realistic test case mimicking pitzDaily
test_case = (
    name = "pitzDaily_test",
    control_dict = Dict(
        "application" => "foamRun",
        "solver" => "incompressibleFluid", 
        "deltaT" => "0.0001",
        "endTime" => "0.01"  # Short test
    ),
    transport_properties = Dict("nu" => "1e-05"),  # Air-like viscosity
    points = Float64[],  # Will use fallback mesh
    fields = Dict(
        "k" => Dict("internalField" => "uniform 0.01"),
        "epsilon" => Dict("internalField" => "uniform 0.001"),
        "nut" => Dict("internalField" => "uniform 0.0"),
        "U" => Dict("internalField" => "uniform (1 0 0)"),
        "p" => Dict("internalField" => "uniform 0")
    )
)

try
    println("1. 🔧 Creating k-epsilon turbulence model...")
    model = create_turbulence_model(:k_epsilon)
    println("   ✅ Model: $(get_model_info(model))")
    println("   ✅ Ready: $(is_model_ready(model))")
    
    if is_model_ready(model)
        println("\n2. 🌪️  Running k-epsilon turbulent flow solver...")
        
        start_time = time()
        result = solve_turbulent_flow(test_case, model)
        total_time = time() - start_time
        
        println("\n3. 📊 Results Summary:")
        println("   ✅ Solver completed successfully!")
        println("   ⏱️  Total time: $(round(total_time, digits=3))s")
        println("   🔄 Iterations: $(result[\"iterations\"])")
        println("   📈 Final residuals:")
        for (field, residual) in result["residuals"]
            println("      $field: $(round(residual, sigdigits=3))")
        end
        
        println("\n4. 🎯 Validation checks:")
        
        # Check fields exist
        fields = result["fields"]
        required_fields = ["U", "p", "k", "epsilon", "nut"]
        all_present = true
        for field in required_fields
            if haskey(fields, field)
                println("   ✅ Field '$field' present")
            else
                println("   ❌ Field '$field' missing")
                all_present = false
            end
        end
        
        # Check turbulent viscosity is reasonable
        if haskey(fields, "nut")
            max_nut = maximum(fields["nut"])
            avg_nut = sum(fields["nut"]) / length(fields["nut"])
            println("   📊 Turbulent viscosity: max=$(round(max_nut, sigdigits=3)), avg=$(round(avg_nut, sigdigits=3))")
            
            if max_nut > 0 && max_nut < 1.0  # Reasonable range
                println("   ✅ Turbulent viscosity in reasonable range")
            else
                println("   ⚠️  Turbulent viscosity may be outside expected range")
            end
        end
        
        println("\n🎉 k-epsilon Integration Test: SUCCESS")
        println("   The turbulent solver is working and ready for benchmarking!")
        
    else
        println("❌ k-epsilon model not ready")
    end
    
catch e
    println("❌ Integration test failed: $e")
    println("\nStacktrace:")
    for (exc, bt) in Base.catch_stack()
        showerror(stdout, exc, bt)
        println()
    end
end

println("\n" * "="^60)