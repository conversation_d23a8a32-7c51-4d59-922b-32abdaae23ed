#!/usr/bin/env julia

include("src/OpenFOAMCaseImporter.jl")

# Test controlDict parsing specifically
case_path = "test_cases/pitzDaily"
control_file = joinpath(case_path, "system", "controlDict")

println("🔍 Debugging controlDict parsing")
println("=" ^ 50)

if isfile(control_file)
    println("✅ controlDict file exists: $control_file")
    
    # Read raw content
    content = read(control_file, String)
    println("\n📄 Raw controlDict content (first 500 chars):")
    println(content[1:min(500, length(content))])
    
    println("\n📄 Full controlDict content:")
    println(content)
    
    # Test parsing
    println("\n🔧 Parsing result:")
    control_dict = parse_openfoam_dict(content)
    println("Parsed keys: $(keys(control_dict))")
    for (key, value) in control_dict
        println("   $key: $value")
    end
    
else
    println("❌ controlDict file not found: $control_file")
end