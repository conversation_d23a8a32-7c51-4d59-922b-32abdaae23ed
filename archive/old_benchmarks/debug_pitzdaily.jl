#!/usr/bin/env julia

include("src/SolverComparison.jl")

# Test pitzDaily case analysis
case_path = "test_cases/pitzDaily"

println("🔍 Debugging pitzDaily case analysis")
println("=" ^ 50)

# Import the case
if isdir(case_path)
    println("✅ Case directory exists: $case_path")
    
    # Test case import
    try
        of_case = import_openfoam_case(case_path)
        println("✅ Case imported successfully")
        
        # Print control dict contents
        println("\n📋 Control Dictionary:")
        for (key, value) in of_case.control_dict
            println("   $key: $value")
        end
        
        # Test turbulence analysis
        println("\n🌪️ Turbulence Analysis:")
        solver_info = analyze_openfoam_case(of_case)
        println("   Flow type: $(solver_info.flow_type)")
        println("   Solver type: $(solver_info.solver_type)")
        println("   Turbulence model: $(solver_info.turbulence_model)")
        println("   Has turbulent fields: $(solver_info.has_turbulent_fields)")
        println("   Application: $(solver_info.application)")
        
        # Check momentumTransport file
        momentum_file = joinpath(case_path, "constant", "momentumTransport")
        if isfile(momentum_file)
            println("\n📄 momentumTransport file contents:")
            content = read(momentum_file, String)
            println(content[1:min(200, length(content))] * "...")
        end
        
    catch e
        println("❌ Error importing case: $e")
    end
else
    println("❌ Case directory not found: $case_path")
    println("Available directories:")
    for item in readdir("test_cases", join=true)
        if isdir(item)
            println("   - $(basename(item))")
        end
    end
end