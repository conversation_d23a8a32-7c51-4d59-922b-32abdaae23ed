"""
Fixed turbulent function - this will replace the broken one
"""
function run_juliafoam_turbulent(of_case::OpenFOAMCase, solver_info)
    println("   🌪️  Turbulent case detected: $(of_case.name)")
    println("      Solver type: $(solver_info.solver_type)")
    println("      Turbulence model: $(solver_info.turbulence_model)")
    println("      Application: $(solver_info.application)")
    
    # List detected turbulent fields
    turbulent_fields = ["k", "epsilon", "omega", "nut", "nuTilda"]
    detected_fields = [field for field in turbulent_fields if field in keys(of_case.fields)]
    if !isempty(detected_fields)
        println("      Turbulent fields: $(join(detected_fields, ", "))")
    end
    
    # Try to create and run turbulent solver
    try
        # Load turbulent solver
        include_turbulent_solver()
        
        # Create appropriate turbulence model
        turbulence_model = create_turbulence_model(solver_info.turbulence_model)
        
        if !is_model_ready(turbulence_model)
            guidance = get_turbulence_implementation_guidance(solver_info.turbulence_model)
            error_message = "Turbulent solver not yet implemented\\n" *
                           "Detected: $(solver_info.turbulence_model) model\\n" *
                           "Next steps: $guidance"
            
            return SolverResult(
                "JuliaFOAM",
                of_case.name,
                false,
                0.0,
                0,
                Dict{String, Float64}(),
                Dict{String, Vector{Float64}}(),
                Dict{String, Any}(),
                0.0,
                error_message
            )
        end
        
        println("      Model: $(get_model_info(turbulence_model))")
        
        # Run turbulent solver
        start_time = time()
        result = solve_turbulent_flow(of_case, turbulence_model)
        solve_time = time() - start_time
        
        println("   ✅ JuliaFOAM turbulent solver completed successfully")
        println("      Time: $(round(solve_time, digits=3))s")
        println("      Iterations: $(result[\"iterations\"])")
        
        return SolverResult(
            "JuliaFOAM",
            of_case.name,
            true,
            solve_time,
            result["iterations"],
            result["residuals"],
            result["convergence_history"],
            result["fields"],
            0.0,
            ""
        )
        
    catch e
        error_message = "Turbulent solver failed: $e"
        println("   ❌ JuliaFOAM turbulent solver failed: $e")
        
        return SolverResult(
            "JuliaFOAM",
            of_case.name,
            false,
            0.0,
            0,
            Dict{String, Float64}(),
            Dict{String, Vector{Float64}}(),
            Dict{String, Any}(),
            0.0,
            error_message
        )
    end
end