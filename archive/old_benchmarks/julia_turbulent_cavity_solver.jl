#!/usr/bin/env julia

"""
JuliaFOAM turbulent cavity solver using enhanced k-epsilon model
"""

println("🌊 JuliaFOAM Enhanced Turbulent Cavity Solver")
println("=" ^ 45)

# Load the enhanced turbulence framework
include("src/turbulence/TurbulenceModels.jl")
include("src/turbulence/Common/MeshUtilities.jl")
include("src/turbulence/Common/TurbulentFields.jl")
include("src/turbulence/RAS/KEpsilonSolver.jl")

using LinearAlgebra

function solve_turbulent_cavity()
    println("\n🏗️ Setting up mesh and flow fields...")
    
    # Create 50x50x1 mesh to match OpenFOAM
    mesh = create_cavity_mesh(50, L=1.0)
    println("  Mesh: $(mesh.nx)×$(mesh.ny)×$(mesh.nz)")
    
    # Initialize flow fields with same properties as OpenFOAM
    fields = TurbulentFlowFields(mesh, 1e-5, 1.0)  # nu = 1e-5 m²/s
    
    # Initialize with cavity flow conditions
    initialize_k_epsilon_cavity!(fields, 1.0, 0.05)  # U_lid = 1 m/s, TI = 5%
    
    println("  Initial conditions:")
    println("    k_avg = $(sum(fields.k)/length(fields.k))")
    println("    ε_avg = $(sum(fields.epsilon)/length(fields.epsilon))")
    
    # Create k-epsilon model with same coefficients as OpenFOAM
    coeffs = KEpsilonCoefficients(
        Cmu = 0.09,
        C1 = 1.44, 
        C2 = 1.92,
        sigmak = 1.0,
        sigmaEps = 1.3
    )
    
    println("\n⚡ Running enhanced k-epsilon solver...")
    
    # Time stepping parameters
    dt = 1.0        # Same as OpenFOAM
    max_time = 100.0  # Run for 100 iterations
    n_steps = Int(max_time / dt)
    
    # Storage for convergence history
    k_residuals = Float64[]
    eps_residuals = Float64[]
    u_residuals = Float64[]
    
    println("  Time step: $dt s")
    println("  Max time: $max_time s")
    println("  Steps: $n_steps")
    
    for step in 1:n_steps
        t_start = time()
        
        # Store previous fields for convergence check
        k_old = copy(fields.k)
        eps_old = copy(fields.epsilon)
        u_old = copy(fields.u)
        
        # Solve k-epsilon equations
        solve_k_epsilon_equations!(fields, coeffs, dt)
        
        # Calculate residuals
        k_res = norm(fields.k - k_old) / (norm(k_old) + 1e-12)
        eps_res = norm(fields.epsilon - eps_old) / (norm(eps_old) + 1e-12)  
        u_res = norm(fields.u - u_old) / (norm(u_old) + 1e-12)
        
        push!(k_residuals, k_res)
        push!(eps_residuals, eps_res)
        push!(u_residuals, u_res)
        
        t_elapsed = time() - t_start
        
        # Print progress every 10 steps
        if step % 10 == 0
            println("  Step $step: k_res=$(k_res), ε_res=$(eps_res), u_res=$(u_res) ($(round(t_elapsed*1000, digits=1)) ms)")
        end
        
        # Check convergence
        if k_res < 1e-5 && eps_res < 1e-5 && u_res < 1e-5
            println("  ✅ Converged at step $step")
            break
        end
    end
    
    println("\n📊 Final Results:")
    println("  Final residuals:")
    println("    k: $(k_residuals[end])")
    println("    ε: $(eps_residuals[end])")
    println("    u: $(u_residuals[end])")
    
    println("  Field statistics:")
    println("    k: min=$(minimum(fields.k)), max=$(maximum(fields.k))")
    println("    ε: min=$(minimum(fields.epsilon)), max=$(maximum(fields.epsilon))")
    println("    νt: min=$(minimum(fields.nut)), max=$(maximum(fields.nut))")
    
    # Save results for comparison
    return fields, k_residuals, eps_residuals, u_residuals
end

function save_results_for_comparison(fields, output_dir="julia_results")
    println("\n💾 Saving JuliaFOAM results...")
    
    mkpath(output_dir)
    
    # Extract centerline data for comparison
    nx, ny = fields.mesh.nx, fields.mesh.ny
    mid_j = div(ny, 2)
    mid_i = div(nx, 2)
    
    # Vertical centerline (x = 0.5)
    y_coords = [fields.mesh.yc[mid_i, j, 1] for j in 1:ny]
    u_centerline_v = [fields.u[mid_i, j, 1] for j in 1:ny]
    k_centerline_v = [fields.k[mid_i, j, 1] for j in 1:ny]
    eps_centerline_v = [fields.epsilon[mid_i, j, 1] for j in 1:ny]
    
    # Horizontal centerline (y = 0.5)
    x_coords = [fields.mesh.xc[i, mid_j, 1] for i in 1:nx]
    u_centerline_h = [fields.u[i, mid_j, 1] for i in 1:nx]
    v_centerline_h = [fields.v[i, mid_j, 1] for i in 1:nx]
    
    # Save to files
    open("$output_dir/vertical_centerline.csv", "w") do f
        println(f, "y,u,k,epsilon")
        for i in 1:length(y_coords)
            println(f, "$(y_coords[i]),$(u_centerline_v[i]),$(k_centerline_v[i]),$(eps_centerline_v[i])")
        end
    end
    
    open("$output_dir/horizontal_centerline.csv", "w") do f
        println(f, "x,u,v")
        for i in 1:length(x_coords)
            println(f, "$(x_coords[i]),$(u_centerline_h[i]),$(v_centerline_h[i])")
        end
    end
    
    println("  ✅ Results saved to $output_dir/")
end

# Run the enhanced solver
println("\n🚀 Starting enhanced turbulent cavity simulation...")
fields, k_res, eps_res, u_res = solve_turbulent_cavity()

# Save results
save_results_for_comparison(fields)

println("\n✅ JuliaFOAM enhanced turbulent cavity solver complete!")
println("   Ready for comparison with OpenFOAM results")