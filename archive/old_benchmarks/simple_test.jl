#!/usr/bin/env julia

println("🧪 Testing TurbulentSolver modules...")

try
    println("1. Loading TurbulenceModels.jl...")
    include("src/TurbulenceModels.jl")
    println("   ✅ TurbulenceModels.jl loaded")
    
    println("2. Testing model creation...")
    model = create_turbulence_model(:k_epsilon)
    println("   Model info: $(get_model_info(model))")
    println("   Ready: $(is_model_ready(model))")
    
    println("3. Loading TurbulentSolver.jl...")
    include("src/TurbulentSolver.jl")
    println("   ✅ TurbulentSolver.jl loaded")
    
    println("🎉 Basic loading tests passed!")
    
catch e
    println("❌ Error: $e")
end