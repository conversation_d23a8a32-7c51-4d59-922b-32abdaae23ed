#!/usr/bin/env julia

println("🌪️ k-epsilon Turbulent Solver Demo")
println("="^50)

# Load modules
include("src/TurbulenceModels.jl")
include("src/TurbulentSolver.jl")

# Create k-epsilon model
model = create_turbulence_model(:k_epsilon)
println("Model: $(get_model_info(model))")
println("Ready: $(is_model_ready(model))")

# Simple test case
test_case = (
    name = "demo_cavity",
    control_dict = Dict("deltaT" => "0.01", "endTime" => "0.05"),
    transport_properties = Dict("nu" => "0.01"),
    points = Float64[],
    fields = Dict("k" => Dict("internalField" => "uniform 0.01"), 
                 "epsilon" => Dict("internalField" => "uniform 0.001"))
)

println("\nRunning solver...")
result = solve_turbulent_flow(test_case, model)

println("✅ Success!")
println("Iterations: $(result["iterations"])")
println("Time: $(result["solve_time"])s")
println("Fields: $(keys(result["fields"]))")

println("\n🎉 k-epsilon solver working!")