#!/usr/bin/env julia

# Test the turbulent solver modules independently
println("🧪 Testing TurbulentSolver modules...")

try
    println("1. Loading TurbulenceModels.jl...")
    include("src/TurbulenceModels.jl")
    println("   ✅ TurbulenceModels.jl loaded successfully")
    
    println("2. Testing model creation...")
    model = create_turbulence_model(:k_epsilon)
    println("   ✅ k-epsilon model created: $(get_model_info(model))")
    println("   Ready: $(is_model_ready(model))")
    
    println("3. Loading TurbulentSolver.jl...")
    include("src/TurbulentSolver.jl")
    println("   ✅ TurbulentSolver.jl loaded successfully")
    
    # Create a dummy case for testing
    println("4. Testing with dummy case...")
    dummy_case = (
        name = "test_cavity",
        control_dict = Dict("deltaT" => "0.01", "endTime" => "0.1"),
        transport_properties = Dict("nu" => "0.01"),
        points = Float64[],  # Empty points for fallback
        fields = Dict("k" => Dict("internalField" => "uniform 0.01"),
                     "epsilon" => Dict("internalField" => "uniform 0.001"))
    )
    
    if is_model_ready(model)
        println("5. Running solve_turbulent_flow...")
        result = solve_turbulent_flow(dummy_case, model)
        println("   ✅ Solver completed!")
        println("      Iterations: $(result[\"iterations\"])")
        println("      Solve time: $(result[\"solve_time\"]) seconds")
    else
        println("   ⚠️  Model not ready, but modules loaded successfully")
    end
    
    println("\n🎉 All turbulent solver tests passed!")
    
catch e
    println("❌ Error in turbulent solver test: $e")
    println("Stacktrace:")
    for (exc, bt) in Base.catch_stack()
        showerror(stdout, exc, bt)
        println()
    end
end