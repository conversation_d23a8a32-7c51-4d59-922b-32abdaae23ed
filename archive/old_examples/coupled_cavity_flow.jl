#!/usr/bin/env julia

"""
    coupled_cavity_flow.jl

Example of using the coupled incompressible solver for a 2D lid-driven cavity flow.
"""

using LinearAlgebra
using StaticArrays

# Check if WriteVTK is available
const HAS_WRITEVTK = try
    using WriteVTK
    true
catch
    @warn "WriteVTK package not available. VTK output will be disabled."
    false
end

# Add JuliaFOAM to the load path
push!(LOAD_PATH, joinpath(dirname(@__DIR__), "src"))

using JuliaFOAM
using JuliaFOAM.CoupledSolver

function main()
    println("JuliaFOAM: Coupled Solver - Lid-Driven Cavity Flow Example")
    
    # Check if PETSc is available
    has_petsc = try
        using PETSc
        true
    catch
        @warn "PETSc not available, using native solver"
        false
    end
    
    # Create a 2D lid-driven cavity mesh (50x50 cells)
    nx, ny = 50, 50
    lx, ly = 1.0, 1.0
    println("Creating mesh: $(nx)x$(ny) cells")
    mesh = create_rectangular_mesh(nx, ny, lx, ly)
    
    # Initialize velocity and pressure fields
    println("Initializing fields")
    U = Field{SVector{3,Float64}}(length(mesh.cells))
    p = Field{Float64}(length(mesh.cells))
    
    # Set initial values
    for i in 1:length(mesh.cells)
        U.internal_field[i] = SVector{3,Float64}(0.0, 0.0, 0.0)
        p.internal_field[i] = 0.0
    end
    
    # Set boundary conditions for lid-driven cavity
    # Top wall (lid) moving at u = 1.0
    # Other walls are no-slip
    
    # Set fluid properties (Reynolds number = 100)
    properties = FluidProperties(
        density = 1.0,
        viscosity = 0.01,  # Re = UL/ν = 1*1/0.01 = 100
        specific_heat = 1.0,
        thermal_conductivity = 0.01
    )
    
    # Create solver settings
    settings = CoupledSolverSettings(
        tolerance = 1e-6,
        max_iterations = 1000,
        relaxation_factor = 0.7,
        use_petsc = has_petsc,
        petsc_solver_type = "gmres",
        petsc_preconditioner = "bjacobi",
        non_orthogonal_correctors = 1
    )
    
    # Run the solver
    println("Running coupled solver")
    iterations = solve_coupled!(U, p, mesh, properties, settings)
    
    println("Solver completed in $iterations iterations")
    
    # Write results to VTK file
    println("Writing results to VTK file")
    write_vtk_file(mesh, U, p, "cavity_flow_coupled")
    
    println("Done!")
end

"""
    write_vtk_file(mesh, U, p, filename)

Write the mesh and fields to a VTK file.
"""
function write_vtk_file(mesh, U, p, filename)
    if !HAS_WRITEVTK
        @warn "Cannot write VTK file: WriteVTK package not available"
        # Write a simple CSV file instead
        write_csv_file(mesh, U, p, filename)
        return
    end
    
    # Extract cell centers
    points = zeros(Float64, 3, length(mesh.cell_centers))
    for i in 1:length(mesh.cell_centers)
        points[1, i] = mesh.cell_centers[i][1]
        points[2, i] = mesh.cell_centers[i][2]
        points[3, i] = mesh.cell_centers[i][3]
    end
    
    # Create a VTK file
    vtkfile = vtk_grid(filename, points)
    
    # Extract velocity components
    u = zeros(Float64, length(U.internal_field))
    v = zeros(Float64, length(U.internal_field))
    w = zeros(Float64, length(U.internal_field))
    
    for i in 1:length(U.internal_field)
        u[i] = U.internal_field[i][1]
        v[i] = U.internal_field[i][2]
        w[i] = U.internal_field[i][3]
    end
    
    # Add fields to the VTK file
    vtkfile["velocity"] = (u, v, w)
    vtkfile["pressure"] = p.internal_field
    
    # Save the VTK file
    vtk_save(vtkfile)
end

"""
    write_csv_file(mesh, U, p, filename)

Write the mesh and fields to a CSV file as a fallback when VTK is not available.
"""
function write_csv_file(mesh, U, p, filename)
    open("$(filename).csv", "w") do f
        # Write header
        write(f, "x,y,z,u,v,w,p\n")
        
        # Write data
        for i in 1:length(mesh.cell_centers)
            x = mesh.cell_centers[i][1]
            y = mesh.cell_centers[i][2]
            z = mesh.cell_centers[i][3]
            u = U.internal_field[i][1]
            v = U.internal_field[i][2]
            w = U.internal_field[i][3]
            pressure = p.internal_field[i]
            
            write(f, "$x,$y,$z,$u,$v,$w,$pressure\n")
        end
    end
    
    println("Results written to $(filename).csv")
end

"""
    create_rectangular_mesh(nx, ny, lx, ly)

Create a 2D rectangular mesh with nx x ny cells and dimensions lx x ly.
"""
function create_rectangular_mesh(nx, ny, lx, ly)
    # Create a simple 2D rectangular mesh for the cavity
    # This is a simplified version for the example
    
    dx = lx / nx
    dy = ly / ny
    
    # Create cells
    cells = Vector{Cell}(undef, nx * ny)
    for j in 1:ny
        for i in 1:nx
            idx = (j-1) * nx + i
            cells[idx] = Cell(idx, [])  # Will fill faces later
        end
    end
    
    # Create faces
    # For a 2D mesh, we need:
    # - nx*(ny+1) horizontal faces
    # - (nx+1)*ny vertical faces
    n_faces = nx*(ny+1) + (nx+1)*ny
    faces = Vector{Face}(undef, n_faces)
    
    # Create horizontal faces
    face_idx = 1
    for j in 0:ny
        for i in 1:nx
            owner = j == 0 ? 0 : (j-1) * nx + i  # Bottom boundary if j=0
            neighbor = j == ny ? 0 : j * nx + i  # Top boundary if j=ny
            faces[face_idx] = Face(face_idx, owner, neighbor)
            face_idx += 1
        end
    end
    
    # Create vertical faces
    for j in 1:ny
        for i in 0:nx
            owner = i == 0 ? 0 : (j-1) * nx + i  # Left boundary if i=0
            neighbor = i == nx ? 0 : (j-1) * nx + i + 1  # Right boundary if i=nx
            faces[face_idx] = Face(face_idx, owner, neighbor)
            face_idx += 1
        end
    end
    
    # Calculate cell volumes and face areas
    cell_volumes = ones(Float64, nx * ny) .* (dx * dy)
    face_areas = ones(Float64, n_faces)
    
    # Horizontal faces have area dx
    for i in 1:(nx*(ny+1))
        face_areas[i] = dx
    end
    
    # Vertical faces have area dy
    for i in (nx*(ny+1)+1):n_faces
        face_areas[i] = dy
    end
    
    # Calculate face normals
    face_normals = Vector{SVector{3,Float64}}(undef, n_faces)
    
    # Horizontal faces have normal (0,1,0) or (0,-1,0)
    for j in 0:ny
        for i in 1:nx
            idx = j * nx + i
            normal = j == ny ? SVector{3,Float64}(0.0, -1.0, 0.0) : SVector{3,Float64}(0.0, 1.0, 0.0)
            face_normals[idx] = normal
        end
    end
    
    # Vertical faces have normal (1,0,0) or (-1,0,0)
    offset = nx*(ny+1)
    for j in 1:ny
        for i in 0:nx
            idx = offset + j * (nx+1) + i + 1
            normal = i == nx ? SVector{3,Float64}(-1.0, 0.0, 0.0) : SVector{3,Float64}(1.0, 0.0, 0.0)
            face_normals[idx] = normal
        end
    end
    
    # Calculate cell centers
    cell_centers = Vector{SVector{3,Float64}}(undef, nx * ny)
    for j in 1:ny
        for i in 1:nx
            idx = (j-1) * nx + i
            x = (i - 0.5) * dx
            y = (j - 0.5) * dy
            cell_centers[idx] = SVector{3,Float64}(x, y, 0.0)
        end
    end
    
    # Create cell_faces mapping
    cell_faces = Vector{Vector{Int}}(undef, nx * ny)
    for cell_idx in 1:length(cells)
        cell_faces[cell_idx] = Int[]
    end
    
    # Add faces to cells
    for face_idx in 1:n_faces
        face = faces[face_idx]
        if face.owner > 0
            push!(cell_faces[face.owner], face_idx)
        end
        if face.neighbor > 0
            push!(cell_faces[face.neighbor], face_idx)
        end
    end
    
    # Create the mesh
    return Mesh(
        cells,
        faces,
        cell_volumes,
        face_areas,
        face_normals,
        cell_centers,
        cell_faces,
        Dict()  # No boundary patches for this simple example
    )
end

# Run the main function
main()
