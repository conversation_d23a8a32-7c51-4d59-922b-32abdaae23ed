"""
    optimized_simple_solver.jl

This example demonstrates the use of the optimized SIMPLE solver for solving
the incompressible Navier-Stokes equations. It compares the performance of the
standard SIMPLE solver with the optimized SIMPLE solver on a lid-driven cavity
test case.
"""

using JuliaFOAM
using LinearAlgebra
using SparseArrays
using StaticArrays
using Printf
using Statistics

# Import specific types
import JuliaFOAM: Cell, Face, Mesh, Field, BoundaryCondition, FluidProperties

# Set up a simple test case
function create_simple_test_case(nx::Int, ny::Int, nz::Int=1)
    println("  Creating simple test case with $(nx)x$(ny)x$(nz) mesh...")

    # Create a simple mesh with cells and faces
    n_cells = nx * ny * nz
    n_faces = (nx+1) * ny * nz + nx * (ny+1) * nz + nx * ny * (nz+1)

    # Create cells
    cells = []
    for k in 1:nz
        for j in 1:ny
            for i in 1:nx
                # Cell center
                center = SVector{3, Float64}(
                    (i - 0.5) / nx,
                    (j - 0.5) / ny,
                    (k - 0.5) / nz
                )

                # Cell volume
                volume = 1.0 / (nx * ny * nz)

                # Create cell
                push!(cells, Cell(Int32[], center, volume))
            end
        end
    end

    # Create faces (simplified)
    faces = []
    for i in 1:n_faces
        # Face center (simplified)
        center = SVector{3, Float64}(0.5, 0.5, 0.5)

        # Face area (simplified)
        area = SVector{3, Float64}(1.0, 0.0, 0.0)

        # Owner and neighbor cells (simplified)
        owner = min(i, n_cells)
        neighbour = -1  # Boundary face

        # Create face
        push!(faces, Face(Int32(owner), Int32(neighbour), area, center))
    end

    # Create mesh
    boundary_faces = Int32[]
    boundary_patches = Dict{String, Vector{Int32}}()
    boundary_conditions = Dict{String, BoundaryCondition}()
    mesh = Mesh(cells, faces, boundary_faces, boundary_patches, boundary_conditions)

    # Create velocity field
    U = Field{SVector{3, Float64}}(
        "U",
        [SVector{3, Float64}(0.0, 0.0, 0.0) for _ in 1:n_cells],
        Dict{String, Vector{SVector{3, Float64}}}()
    )

    # Create pressure field
    p = Field{Float64}(
        "p",
        [0.0 for _ in 1:n_cells],
        Dict{String, Vector{Float64}}()
    )

    # Set up fluid properties
    fluid_properties = FluidProperties(1.0, 0.01)  # density = 1.0, viscosity = 0.01

    # Create boundary conditions dictionary
    boundary_conditions = Dict{String, Any}(
        "properties" => fluid_properties
    )

    # Create fields dictionary
    fields = Dict{String, Field}(
        "U" => U,
        "p" => p
    )

    return mesh, fields, boundary_conditions
end

# Run benchmark comparing standard and optimized solvers
function run_benchmark(nx::Int, ny::Int, nz::Int=1)
    println("\nRunning benchmark for $(nx)x$(ny)x$(nz) mesh...")

    # Create test case
    mesh, fields, boundary_conditions = create_simple_test_case(nx, ny, nz)

    # Create standard solver configuration
    standard_config = SimpleSolverConfig(
        max_iterations=100,
        tolerance=1e-4,
        relaxation_factors=Dict("U"=>0.7, "p"=>0.3),
        track_residuals=true,
        residual_output_interval=10
    )

    # Create optimized solver configuration
    optimized_config = OptimizedSimpleSolverConfig(
        max_iterations=100,
        tolerance=1e-4,
        relaxation_factors=Dict("U"=>0.7, "p"=>0.3),
        track_residuals=true,
        residual_output_interval=10,
        solver_type=:bicgstab,
        use_threading=true
    )

    # Clone fields for each solver
    standard_fields = Dict{String, Field}(
        "U" => deepcopy(fields["U"]),
        "p" => deepcopy(fields["p"])
    )

    optimized_fields = Dict{String, Field}(
        "U" => deepcopy(fields["U"]),
        "p" => deepcopy(fields["p"])
    )

    # Run standard solver
    println("  Running standard solver...")
    standard_start = time()
    standard_result, standard_residuals = solve_simple(
        mesh,
        standard_fields,
        boundary_conditions,
        standard_config
    )
    standard_end = time()
    standard_time = standard_end - standard_start

    # Run optimized solver
    println("  Running optimized solver...")
    optimized_start = time()
    optimized_result, optimized_residuals = solve_simple_optimized(
        mesh,
        optimized_fields,
        boundary_conditions,
        optimized_config
    )
    optimized_end = time()
    optimized_time = optimized_end - optimized_start

    # Calculate speedup
    speedup = standard_time / optimized_time

    # Print results
    println("  Results:")
    println("    Standard solver: $(standard_time) seconds")
    println("    Optimized solver: $(optimized_time) seconds")
    println("    Speedup: $(speedup)x")

    # Compare final residuals
    standard_final_U_res = standard_residuals["U"][end]
    standard_final_p_res = standard_residuals["p"][end]
    optimized_final_U_res = optimized_residuals["U"][end]
    optimized_final_p_res = optimized_residuals["p"][end]

    println("    Standard solver final residuals: U=$(standard_final_U_res), p=$(standard_final_p_res)")
    println("    Optimized solver final residuals: U=$(optimized_final_U_res), p=$(optimized_final_p_res)")

    # Compare convergence rates
    standard_iterations = length(standard_residuals["U"])
    optimized_iterations = length(optimized_residuals["U"])

    println("    Standard solver iterations: $(standard_iterations)")
    println("    Optimized solver iterations: $(optimized_iterations)")

    return Dict(
        "mesh_size" => "$(nx)x$(ny)x$(nz)",
        "standard_time" => standard_time,
        "optimized_time" => optimized_time,
        "speedup" => speedup,
        "standard_iterations" => standard_iterations,
        "optimized_iterations" => optimized_iterations,
        "standard_final_U_res" => standard_final_U_res,
        "standard_final_p_res" => standard_final_p_res,
        "optimized_final_U_res" => optimized_final_U_res,
        "optimized_final_p_res" => optimized_final_p_res
    )
end

# Run benchmarks for different mesh sizes
function run_all_benchmarks()
    println("================================================================================")
    println("JuliaFOAM Optimized SIMPLE Solver Benchmark")
    println("================================================================================")

    # Run benchmarks for different mesh sizes
    results = []

    # Small mesh
    push!(results, run_benchmark(20, 20))

    # Medium mesh
    push!(results, run_benchmark(50, 50))

    # Large mesh
    push!(results, run_benchmark(100, 100))

    # Print summary
    println("\n================================================================================")
    println("Benchmark Summary")
    println("================================================================================")
    println("\n| Mesh Size | Standard Time (s) | Optimized Time (s) | Speedup | Standard Iter | Optimized Iter |")
    println("|-----------|-------------------|-------------------|---------|--------------|----------------|")

    for result in results
        @printf("| %s | %.4f | %.4f | %.2fx | %d | %d |\n",
            result["mesh_size"],
            result["standard_time"],
            result["optimized_time"],
            result["speedup"],
            result["standard_iterations"],
            result["optimized_iterations"]
        )
    end

    # Calculate average speedup
    avg_speedup = mean([result["speedup"] for result in results])
    println("\nAverage speedup: $(avg_speedup)x")

    return results
end

# Run the benchmarks
run_all_benchmarks()
