# =========================================================================
# EnhancedLinearSolvers Module - Advanced linear solvers with best-in-class packages
# =========================================================================

"""
    EnhancedLinearSolvers

This module provides enhanced linear solvers using the best available Julia packages:
- AlgebraicMultigrid.jl for high-performance AMG preconditioning
- IterativeSolvers.jl for robust Krylov methods
- LinearSolve.jl for unified solver interface
- PETSc.jl for parallel computing and advanced algorithms

This is the recommended interface for production CFD simulations requiring
maximum accuracy and performance.
"""
module EnhancedLinearSolvers

# Standard library
using LinearAlgebra
using SparseArrays
using Printf

# Enhanced solver packages
using AlgebraicMultigrid
using IterativeSolvers
using LinearSolve
using IncompleteLU

# Import specific functions
import IterativeSolvers: cg!, minres!, bicgstabl!, gmres!

# Optional parallel support
const MPI_AVAILABLE = try
    using MPI
    true
catch
    false
end

const PETSC_AVAILABLE = try
    using PETSc
    true
catch
    false
end

# =========================================================================
# Enhanced Solver Configuration
# =========================================================================

"""
    EnhancedSolverConfig

Configuration for enhanced linear solvers with automatic solver selection
based on matrix properties and problem characteristics.

# Fields
- `tolerance::Float64`: Convergence tolerance
- `max_iterations::Int`: Maximum iterations
- `solver_type::Symbol`: Solver type (:auto, :cg, :minres, :bicgstabl, :gmres, :petsc)
- `preconditioner::Symbol`: Preconditioner (:auto, :amg, :ilu, :jacobi, :none)
- `amg_params::Dict`: AMG-specific parameters
- `verbose::Bool`: Enable detailed output
- `check_convergence::Bool`: Enable convergence monitoring
- `restart_gmres::Int`: GMRES restart parameter
"""
mutable struct EnhancedSolverConfig
    tolerance::Float64
    max_iterations::Int
    solver_type::Symbol
    preconditioner::Symbol
    amg_params::Dict{Symbol, Any}
    verbose::Bool
    check_convergence::Bool
    restart_gmres::Int
    
    function EnhancedSolverConfig(;
        tolerance=1e-8,
        max_iterations=1000,
        solver_type=:auto,
        preconditioner=:auto,
        amg_params=Dict{Symbol, Any}(
            :max_levels => 10,
            :max_coarse => 50,
            :strength_threshold => 0.25,
            :interpolation => :extended,
            :smoother => :gauss_seidel,
            :smooth_steps => 1
        ),
        verbose=false,
        check_convergence=true,
        restart_gmres=30
    )
        new(tolerance, max_iterations, solver_type, preconditioner, 
            amg_params, verbose, check_convergence, restart_gmres)
    end
end

"""
    SolverDiagnostics

Comprehensive diagnostics from enhanced solver runs.
"""
struct SolverDiagnostics
    iterations::Int
    final_residual::Float64
    initial_residual::Float64
    convergence_factor::Float64
    solve_time::Float64
    setup_time::Float64
    solver_used::Symbol
    preconditioner_used::Symbol
    matrix_properties::Dict{Symbol, Any}
    convergence_history::Vector{Float64}
    warnings::Vector{String}
    recommendations::Vector{String}
end

# =========================================================================
# Matrix Analysis for Automatic Solver Selection
# =========================================================================

"""
    analyze_matrix_properties(A::SparseMatrixCSC{Float64, Int})

Analyze matrix properties to guide automatic solver and preconditioner selection.
"""
function analyze_matrix_properties(A::SparseMatrixCSC{Float64, Int})
    n = size(A, 1)
    properties = Dict{Symbol, Any}()
    
    # Basic properties
    properties[:size] = n
    properties[:nnz] = nnz(A)
    properties[:density] = nnz(A) / (n * n)
    
    # Symmetry check
    properties[:symmetric] = issymmetric(A)
    
    # Diagonal dominance
    diag_dominant_count = 0
    for i in 1:n
        diag_val = abs(A[i, i])
        off_diag_sum = sum(abs(A[i, j]) for j in 1:n if i != j)
        if diag_val >= off_diag_sum
            diag_dominant_count += 1
        end
    end
    properties[:diag_dominant_ratio] = diag_dominant_count / n
    
    # Condition number estimation (for smaller matrices)
    if n <= 1000
        try
            eigs = eigen(Matrix(A))
            real_eigs = real(eigs.values)
            properties[:condition_number] = maximum(real_eigs) / minimum(real_eigs)
            properties[:positive_definite] = all(real_eigs .> 0)
        catch
            properties[:condition_number] = Inf
            properties[:positive_definite] = false
        end
    else
        properties[:condition_number] = NaN
        properties[:positive_definite] = false
    end
    
    # Structure analysis
    properties[:bandwidth] = maximum([maximum(A.rowval[nzrange(A, j)]) - j 
                                     for j in 1:n if !isempty(nzrange(A, j))])
    
    return properties
end

"""
    select_optimal_solver(properties::Dict{Symbol, Any}, config::EnhancedSolverConfig)

Automatically select optimal solver and preconditioner based on matrix properties.
"""
function select_optimal_solver(properties::Dict{Symbol, Any}, config::EnhancedSolverConfig)
    solver = config.solver_type
    precond = config.preconditioner
    
    # Auto-select solver
    if solver == :auto
        if properties[:symmetric] && get(properties, :positive_definite, false)
            solver = :cg  # CG for SPD systems
        elseif properties[:size] < 5000
            solver = :gmres  # GMRES for small to medium systems
        else
            solver = :bicgstabl  # BiCGStab(l) for large systems
        end
    end
    
    # Auto-select preconditioner
    if precond == :auto
        if properties[:size] > 10000
            precond = :amg  # AMG for large systems
        elseif properties[:diag_dominant_ratio] > 0.7
            precond = :jacobi  # Jacobi for diagonally dominant
        else
            precond = :ilu  # ILU as general fallback
        end
    end
    
    return solver, precond
end

# =========================================================================
# Enhanced AMG Preconditioners
# =========================================================================

"""
    create_amg_preconditioner(A::SparseMatrixCSC{Float64, Int}, config::EnhancedSolverConfig)

Create optimized AMG preconditioner using AlgebraicMultigrid.jl.
"""
function create_amg_preconditioner(A::SparseMatrixCSC{Float64, Int}, config::EnhancedSolverConfig)
    params = config.amg_params
    
    # Configure AMG based on matrix properties
    ml = ruge_stuben(A,
        max_levels=params[:max_levels],
        max_coarse=params[:max_coarse],
        θ=params[:strength_threshold]
    )
    
    return ml
end

"""
    create_simple_ilu(A::SparseMatrixCSC{Float64, Int})

Create a simple ILU(0) preconditioner.
"""
function create_simple_ilu(A::SparseMatrixCSC{Float64, Int})
    n = size(A, 1)
    
    # Initialize L and U with same sparsity pattern as A
    L = LowerTriangular(copy(A))
    U = UpperTriangular(copy(A))
    
    # Set diagonal of L to 1
    for i in 1:n
        L[i, i] = 1.0
    end
    
    # Perform incomplete LU factorization
    for k in 1:n-1
        if abs(U[k, k]) < 1e-14
            continue
        end
        
        for i in k+1:n
            if L[i, k] != 0.0
                L[i, k] = L[i, k] / U[k, k]
                
                for j in k+1:n
                    if U[i, j] != 0.0 && U[k, j] != 0.0
                        U[i, j] -= L[i, k] * U[k, j]
                    end
                end
            end
        end
    end
    
    return (L, U)
end

# =========================================================================
# Enhanced Solver Interface
# =========================================================================

"""
    enhanced_solve!(A::SparseMatrixCSC{Float64, Int}, b::Vector{Float64}, 
                   x::Vector{Float64}, config::EnhancedSolverConfig)

Enhanced linear solver with automatic algorithm selection and comprehensive diagnostics.

# Returns
- `SolverDiagnostics`: Comprehensive solution diagnostics
"""
function enhanced_solve!(
    A::SparseMatrixCSC{Float64, Int},
    b::Vector{Float64},
    x::Vector{Float64},
    config::EnhancedSolverConfig
)
    start_time = time()
    
    # Analyze matrix properties
    properties = analyze_matrix_properties(A)
    
    # Select optimal solver and preconditioner
    solver_type, precond_type = select_optimal_solver(properties, config)
    
    # Setup phase
    setup_start = time()
    
    # Create preconditioner with proper AMG support
    preconditioner = nothing
    if precond_type == :amg
        try
            # Use AlgebraicMultigrid.jl with aspreconditioner
            ml = ruge_stuben(A, 
                max_levels=config.amg_params[:max_levels],
                max_coarse=config.amg_params[:max_coarse], 
                θ=config.amg_params[:strength_threshold]
            )
            preconditioner = AlgebraicMultigrid.aspreconditioner(ml)
            if config.verbose
                println("   ✅ AMG preconditioner created: $(length(ml.levels)) levels")
            end
        catch e
            push!(warnings, "AMG preconditioner failed: $(string(e)). Falling back to diagonal.")
            preconditioner = Diagonal(diag(A))
        end
    elseif precond_type == :ilu
        try
            # Use IncompleteLU.jl with drop tolerance
            preconditioner = ilu(A, τ=0.01)
            if config.verbose
                println("   ✅ ILU preconditioner created")
            end
        catch e
            push!(warnings, "ILU preconditioner failed: $(string(e)). Falling back to diagonal.")
            preconditioner = Diagonal(diag(A))
        end
    elseif precond_type == :jacobi
        preconditioner = Diagonal(diag(A))
    else
        preconditioner = I  # Identity preconditioner
    end
    
    setup_time = time() - setup_start
    
    # Initialize tracking
    convergence_history = Float64[]
    warnings = String[]
    recommendations = String[]
    
    # Custom convergence callback
    function convergence_callback(x, history)
        if config.check_convergence
            residual = norm(b - A * x)
            push!(convergence_history, residual)
            
            if config.verbose && length(convergence_history) % 10 == 0
                @printf "Iteration %d: residual = %.2e\n" length(convergence_history) residual
            end
        end
        return false  # Continue iteration
    end
    
    # Solve the system
    solve_start = time()
    initial_residual = norm(b - A * x)
    
    result = nothing
    iterations = 0
    final_residual = 0.0
    
    try
        # Handle special preconditioner types
        if isa(preconditioner, Tuple)  # ILU preconditioner
            L, U = preconditioner
            
            # Create a function for ILU preconditioning
            function ilu_preconditioner!(y, x)
                # Solve Ly = x
                ldiv!(y, L, x)
                # Solve Uz = y (overwriting y with z)
                ldiv!(y, U, y)
            end
            
            # Use the function as preconditioner
            Pl = ilu_preconditioner!
        else
            Pl = preconditioner
        end
        
        if solver_type == :cg
            result = cg!(x, A, b, 
                        Pl=Pl,
                        reltol=config.tolerance,
                        maxiter=config.max_iterations,
                        log=true,
                        verbose=config.verbose)
            
        elseif solver_type == :minres
            result = minres!(x, A, b,
                           Pl=Pl,
                           reltol=config.tolerance,
                           maxiter=config.max_iterations,
                           log=true,
                           verbose=config.verbose)
            
        elseif solver_type == :bicgstabl
            # BiCGStab(l) - note: l is positional parameter
            result = bicgstabl!(x, A, b, 2,  # l=2 for BiCGStab(2)
                              Pl=Pl,
                              reltol=config.tolerance,
                              max_mv_products=config.max_iterations,  # BiCGStab uses max_mv_products, not maxiter
                              log=true,
                              verbose=config.verbose)
            
        elseif solver_type == :gmres
            result = gmres!(x, A, b,
                          Pl=Pl,
                          reltol=config.tolerance,
                          maxiter=config.max_iterations,
                          restart=config.restart_gmres,
                          log=true,
                          verbose=config.verbose)
            
        elseif solver_type == :petsc && PETSC_AVAILABLE
            # Use PETSc solver
            push!(recommendations, "PETSc solver integration not yet implemented")
            # Fallback to BiCGStab
            result = bicgstabl!(x, A, b, 2,
                              Pl=preconditioner,
                              reltol=config.tolerance,
                              max_mv_products=config.max_iterations,
                              log=true,
                              verbose=config.verbose)
        else
            # Default fallback
            result = bicgstabl!(x, A, b, 2,
                              Pl=preconditioner,
                              reltol=config.tolerance,
                              max_mv_products=config.max_iterations,
                              log=true,
                              verbose=config.verbose)
        end
        
        # Extract results
        if result !== nothing
            if length(result) >= 2
                iterations = result[2].iters
                # Try different field names for residual
                try
                    final_residual = result[2].residual
                catch
                    try
                        final_residual = result[2].resnorm
                    catch
                        final_residual = norm(A * x - b)  # Calculate manually
                    end
                end
            end
        end
        
    catch e
        push!(warnings, "Solver failed: $(string(e))")
        final_residual = norm(b - A * x)
        iterations = config.max_iterations
    end
    
    solve_time = time() - solve_start
    total_time = time() - start_time
    
    # Calculate convergence factor
    convergence_factor = initial_residual > 0 ? 
                        (final_residual / initial_residual)^(1.0 / max(iterations, 1)) : 0.0
    
    # Generate recommendations
    if iterations >= config.max_iterations
        push!(recommendations, "Solver did not converge. Consider: increasing max_iterations, improving preconditioner, or checking matrix conditioning")
    end
    
    if convergence_factor > 0.9
        push!(recommendations, "Slow convergence detected. Consider: better preconditioner, different solver, or matrix regularization")
    end
    
    if properties[:condition_number] > 1e12
        push!(recommendations, "Matrix is ill-conditioned. Consider: regularization, better preconditioner, or iterative refinement")
    end
    
    # Return comprehensive diagnostics
    return SolverDiagnostics(
        iterations,
        final_residual,
        initial_residual,
        convergence_factor,
        solve_time,
        setup_time,
        solver_type,
        precond_type,
        properties,
        convergence_history,
        warnings,
        recommendations
    )
end

# =========================================================================
# Simplified Interface for CFD Applications
# =========================================================================

"""
    solve_cfd_system!(A, b, x; tolerance=1e-8, max_iterations=1000, 
                     problem_type=:general, verbose=false)

Simplified interface optimized for CFD applications with automatic parameter selection.

# Arguments
- `problem_type`: :pressure, :momentum, :turbulence, :temperature, :general
"""
function solve_cfd_system!(
    A::SparseMatrixCSC{Float64, Int},
    b::Vector{Float64},
    x::Vector{Float64};
    tolerance=1e-8,
    max_iterations=1000,
    problem_type=:general,
    verbose=false
)
    # Configure based on CFD problem type
    config = EnhancedSolverConfig(
        tolerance=tolerance,
        max_iterations=max_iterations,
        verbose=verbose
    )
    
    # Adjust parameters for specific CFD problems with real AMG
    if problem_type == :pressure
        # Pressure systems often benefit from AMG
        config.preconditioner = :amg
        config.solver_type = :cg
        config.amg_params[:strength_threshold] = 0.5
    elseif problem_type == :momentum
        # Momentum systems may be less symmetric
        config.solver_type = :bicgstabl
        config.preconditioner = :amg
    elseif problem_type == :turbulence
        # Turbulence equations can be stiff
        config.solver_type = :gmres
        config.restart_gmres = 50
        config.preconditioner = :amg  # Try AMG first, fallback to diagonal
    elseif problem_type == :temperature
        # Temperature equations are often well-conditioned
        config.solver_type = :cg
        config.preconditioner = :amg
    end
    
    return enhanced_solve!(A, b, x, config)
end

"""
    solve_amg_direct!(A, b, x; tolerance=1e-8, max_iterations=1, verbose=false)

Direct AMG solver using the high-level CommonSolve interface from AlgebraicMultigrid.jl.
This is often the most efficient approach for well-conditioned elliptic problems.
"""
function solve_amg_direct!(
    A::SparseMatrixCSC{Float64, Int},
    b::Vector{Float64},
    x::Vector{Float64};
    tolerance=1e-8,
    max_iterations=1,
    verbose=false
)
    try
        # Use the high-level API: solve(A, b, RugeStubenAMG(), maxiter = 1, abstol = 1e-6)
        start_time = time()
        result = AlgebraicMultigrid.solve(A, b, RugeStubenAMG(), 
                                         maxiter=max_iterations, 
                                         abstol=tolerance)
        solve_time = time() - start_time
        
        # Copy result to output vector
        x .= result
        
        # Calculate final residual
        final_residual = norm(A * x - b) / norm(b)
        
        if verbose
            throughput = length(b) / solve_time
            @printf "AMG Direct: %.4fs, %.2e res, %.0f DOF/s\n" solve_time final_residual throughput
        end
        
        # Create minimal diagnostics
        properties = analyze_matrix_properties(A)
        return SolverDiagnostics(
            max_iterations,  # iterations (AMG cycles)
            final_residual,
            norm(b),        # initial residual
            final_residual / norm(b),  # convergence factor
            solve_time,
            0.0,  # setup time (included in solve_time)
            :amg_direct,
            :amg,
            properties,
            [final_residual],  # convergence history
            String[],         # warnings
            String[]          # recommendations
        )
        
    catch e
        # Fallback to enhanced solver with AMG preconditioning
        if verbose
            println("Direct AMG failed: $e. Falling back to CG+AMG...")
        end
        
        config = EnhancedSolverConfig(
            solver_type=:cg,
            preconditioner=:amg,
            tolerance=tolerance,
            max_iterations=100,
            verbose=verbose
        )
        
        return enhanced_solve!(A, b, x, config)
    end
end

# =========================================================================
# Export Interface
# =========================================================================

export EnhancedSolverConfig, SolverDiagnostics
export enhanced_solve!, solve_cfd_system!, solve_amg_direct!
export analyze_matrix_properties, create_amg_preconditioner, create_simple_ilu

end # module EnhancedLinearSolvers