# =========================================================================
# LinearSolvers Module - Comprehensive linear solvers for JuliaFOAM
# =========================================================================

"""
    LinearSolvers

This module provides comprehensive linear solvers for JuliaFOAM, including both
standard and robust implementations with extensive error handling and numerical
stability features.

# Features
- Standard iterative solvers (CG, BiCGSTAB, GMRES)
- Robust variants with numerical stability checks
- Multiple preconditioners (Diagonal, ILU, DIC, AMG)
- PETSc integration for parallel computing
- OpenFOAM compatibility

# Solver Variants
All solvers support both standard and robust modes:
- Standard mode: Optimal performance for well-conditioned problems
- Robust mode: Enhanced stability checks and recovery mechanisms
"""
module LinearSolvers

# External dependencies
using LinearAlgebra    # For linear algebra operations
using SparseArrays     # For sparse matrix operations
using StaticArrays     # For small fixed-size vectors
using Printf          # For formatted output
using Base.Threads    # For threading support

# Optional MPI support
const MPI_AVAILABLE = try
    using MPI
    true
catch
    false
end

# Optional PETSc support
const PETSC_AVAILABLE = try
    using PETSc
    true
catch
    false
end

# =========================================================================
# Type Definitions
# =========================================================================

"""
    SolverSettings

Configuration for linear solvers.

# Fields
- `tolerance::Float64`: Convergence tolerance
- `max_iterations::Int`: Maximum number of iterations
- `robust::Bool`: Enable robust mode with stability checks
- `verbose::Bool`: Enable verbose output
- `check_frequency::Int`: Frequency of stability checks in robust mode
- `regularization::Float64`: Regularization factor for ill-conditioned matrices
"""
mutable struct SolverSettings
    tolerance::Float64
    max_iterations::Int
    robust::Bool
    verbose::Bool
    check_frequency::Int
    regularization::Float64
    
    function SolverSettings(;
        tolerance=1e-6,
        max_iterations=1000,
        robust=false,
        verbose=false,
        check_frequency=10,
        regularization=1e-12
    )
        new(tolerance, max_iterations, robust, verbose, check_frequency, regularization)
    end
end

"""
    Preconditioner

Abstract base type for all preconditioners.
"""
abstract type Preconditioner end

"""
    DiagonalPreconditioner

Simple diagonal preconditioner.
"""
struct DiagonalPreconditioner <: Preconditioner
    diagonal::Vector{Float64}
end

"""
    ILUPreconditioner

Incomplete LU preconditioner.
"""
struct ILUPreconditioner <: Preconditioner
    L::SparseMatrixCSC{Float64,Int}
    U::SparseMatrixCSC{Float64,Int}
end

"""
    DICPreconditioner

Diagonal Incomplete Cholesky preconditioner.
"""
struct DICPreconditioner <: Preconditioner
    diagonal::Vector{Float64}
end

"""
    IdentityPreconditioner

No preconditioning (identity operation).
"""
struct IdentityPreconditioner <: Preconditioner end

# =========================================================================
# Exported Interface
# =========================================================================

export SolverSettings, Preconditioner
export DiagonalPreconditioner, ILUPreconditioner, DICPreconditioner, IdentityPreconditioner
export solve!, apply_preconditioner!
export cg_solve!, bicgstab_solve!, gmres_solve!
export build_diagonal_preconditioner, build_ilu_preconditioner, build_dic_preconditioner
export check_matrix_properties, diagnose_linear_system, regularize_matrix!

# =========================================================================
# Preconditioner Construction
# =========================================================================

"""
    build_diagonal_preconditioner(A::SparseMatrixCSC{Float64,Int})

Build a diagonal preconditioner from matrix A.
"""
function build_diagonal_preconditioner(A::SparseMatrixCSC{Float64,Int})
    diagonal = [A[i,i] != 0.0 ? 1.0/A[i,i] : 1.0 for i in 1:size(A,1)]
    return DiagonalPreconditioner(diagonal)
end

"""
    build_ilu_preconditioner(A::SparseMatrixCSC{Float64,Int}; drop_tol=1e-3)

Build an incomplete LU preconditioner.
"""
function build_ilu_preconditioner(A::SparseMatrixCSC{Float64,Int}; drop_tol=1e-3)
    # Simplified ILU(0) implementation
    n = size(A, 1)
    L = sparse(I, n, n)  # Initialize L as identity
    U = copy(A)          # Initialize U as A
    
    # Basic ILU(0) factorization
    for k in 1:n-1
        if abs(U[k,k]) < 1e-14
            continue  # Skip if pivot is too small
        end
        
        for i in k+1:n
            if U[i,k] != 0.0
                multiplier = U[i,k] / U[k,k]
                L[i,k] = multiplier
                
                for j in k+1:n
                    if U[k,j] != 0.0
                        U[i,j] -= multiplier * U[k,j]
                    end
                end
                U[i,k] = 0.0
            end
        end
    end
    
    return ILUPreconditioner(L, U)
end

"""
    build_dic_preconditioner(A::SparseMatrixCSC{Float64,Int})

Build a Diagonal Incomplete Cholesky preconditioner.
"""
function build_dic_preconditioner(A::SparseMatrixCSC{Float64,Int})
    n = size(A, 1)
    diagonal = zeros(Float64, n)
    
    for i in 1:n
        diagonal[i] = sqrt(max(A[i,i], 1e-14))
    end
    
    return DICPreconditioner(diagonal)
end

# =========================================================================
# Preconditioner Application
# =========================================================================

"""
    apply_preconditioner!(y::Vector{Float64}, x::Vector{Float64}, precond::Preconditioner)

Apply preconditioner to vector x, storing result in y.
"""
function apply_preconditioner!(y::Vector{Float64}, x::Vector{Float64}, precond::DiagonalPreconditioner)
    @. y = precond.diagonal * x
    return nothing
end

function apply_preconditioner!(y::Vector{Float64}, x::Vector{Float64}, precond::ILUPreconditioner)
    # Solve Ly = x
    ldiv!(y, precond.L, x)
    # Solve Uz = y (overwriting y with z)
    ldiv!(y, precond.U, y)
    return nothing
end

function apply_preconditioner!(y::Vector{Float64}, x::Vector{Float64}, precond::DICPreconditioner)
    @. y = x / (precond.diagonal * precond.diagonal)
    return nothing
end

function apply_preconditioner!(y::Vector{Float64}, x::Vector{Float64}, precond::IdentityPreconditioner)
    y .= x
    return nothing
end

# =========================================================================
# Matrix Analysis and Diagnostics
# =========================================================================

"""
    check_matrix_properties(A::SparseMatrixCSC{Float64, Int})

Analyze matrix properties for numerical stability.
"""
function check_matrix_properties(A::SparseMatrixCSC{Float64, Int})
    n = size(A, 1)
    properties = Dict{Symbol, Any}()
    
    # Check for zero diagonals
    zero_diagonals = Int[]
    for i in 1:n
        if abs(A[i,i]) < 1e-14
            push!(zero_diagonals, i)
        end
    end
    properties[:zero_diagonals] = zero_diagonals
    
    # Check diagonal dominance
    diag_dominant = true
    for i in 1:n
        row_sum = sum(abs(A[i,j]) for j in 1:n if j != i)
        if abs(A[i,i]) <= row_sum
            diag_dominant = false
            break
        end
    end
    properties[:diag_dominant] = diag_dominant
    
    # Estimate condition number (rough approximation)
    try
        singular_values = svdvals(Matrix(A))
        cond_num = maximum(singular_values) / minimum(singular_values)
        properties[:condition_number] = cond_num
        properties[:ill_conditioned] = cond_num > 1e12
    catch
        properties[:condition_number] = Inf
        properties[:ill_conditioned] = true
    end
    
    # Check for zero rows/columns
    zero_rows = Int[]
    zero_cols = Int[]
    for i in 1:n
        if nnz(A[i, :]) == 0
            push!(zero_rows, i)
        end
        if nnz(A[:, i]) == 0
            push!(zero_cols, i)
        end
    end
    properties[:zero_rows] = zero_rows
    properties[:zero_cols] = zero_cols
    
    return properties
end

"""
    diagnose_linear_system(A::SparseMatrixCSC{Float64, Int}, b::Vector{Float64}, x::Vector{Float64})

Comprehensive diagnosis of linear system issues.
"""
function diagnose_linear_system(A::SparseMatrixCSC{Float64, Int}, b::Vector{Float64}, x::Vector{Float64})
    diagnostics = Dict{Symbol, Any}()
    
    # Check matrix properties
    matrix_props = check_matrix_properties(A)
    diagnostics[:matrix_properties] = matrix_props
    
    # Check right-hand side
    b_norm = norm(b)
    diagnostics[:rhs_norm] = b_norm
    diagnostics[:zero_rhs] = b_norm < 1e-15
    
    # Check current residual
    r = b - A * x
    r_norm = norm(r)
    rel_residual = b_norm > 0 ? r_norm / b_norm : r_norm
    
    diagnostics[:residual_norm] = r_norm
    diagnostics[:relative_residual] = rel_residual
    
    # Check for NaN or Inf
    diagnostics[:solution_has_nan_inf] = any(isnan.(x)) || any(isinf.(x))
    diagnostics[:residual_has_nan_inf] = any(isnan.(r)) || any(isinf.(r))
    
    # Generate recommendations
    recommendations = String[]
    if !isempty(matrix_props[:zero_diagonals])
        push!(recommendations, "Matrix has zero diagonal entries. Consider regularization.")
    end
    if !isempty(matrix_props[:zero_rows]) || !isempty(matrix_props[:zero_cols])
        push!(recommendations, "Matrix has zero rows/columns. Check boundary conditions.")
    end
    if get(matrix_props, :ill_conditioned, false)
        push!(recommendations, "Matrix is ill-conditioned. Use preconditioning or regularization.")
    end
    if !matrix_props[:diag_dominant]
        push!(recommendations, "Matrix is not diagonally dominant. Consider robust solver variants.")
    end
    
    diagnostics[:recommendations] = recommendations
    return diagnostics
end

"""
    regularize_matrix!(A::SparseMatrixCSC{Float64, Int}, regularization::Float64)

Add regularization to improve matrix conditioning.
"""
function regularize_matrix!(A::SparseMatrixCSC{Float64, Int}, regularization::Float64)
    n = size(A, 1)
    for i in 1:n
        A[i,i] += regularization
    end
    return A
end

# =========================================================================
# Conjugate Gradient Solver
# =========================================================================

"""
    cg_solve!(A::SparseMatrixCSC{Float64,Int}, b::Vector{Float64}, x::Vector{Float64},
              precond::Preconditioner, settings::SolverSettings)

Conjugate Gradient solver with optional robust mode.
"""
function cg_solve!(
    A::SparseMatrixCSC{Float64,Int},
    b::Vector{Float64},
    x::Vector{Float64},
    precond::Preconditioner,
    settings::SolverSettings
)
    n = length(b)
    r = similar(b)
    z = similar(b)
    p = similar(b)
    Ap = similar(b)
    
    # Initial residual
    mul!(r, A, x)
    r .= b .- r
    
    initial_residual = norm(r)
    if initial_residual < settings.tolerance
        return 0, initial_residual
    end
    
    apply_preconditioner!(z, r, precond)
    p .= z
    rzold = dot(r, z)
    
    for iter in 1:settings.max_iterations
        # Robust mode checks
        if settings.robust && (iter % settings.check_frequency == 0)
            if any(isnan.(x)) || any(isinf.(x))
                settings.verbose && @warn "NaN/Inf detected in solution at iteration $iter"
                return iter, NaN
            end
            
            current_residual = norm(r)
            if current_residual > 1e6 * initial_residual
                settings.verbose && @warn "Residual growing at iteration $iter"
                return iter, current_residual
            end
        end
        
        mul!(Ap, A, p)
        alpha = rzold / dot(p, Ap)
        
        # Check for breakdown
        if !isfinite(alpha) || abs(alpha) < 1e-16
            settings.verbose && @warn "CG breakdown at iteration $iter"
            return iter, norm(r)
        end
        
        x .+= alpha .* p
        r .-= alpha .* Ap
        
        residual = norm(r)
        if settings.verbose && iter % 10 == 0
            @printf "CG iteration %d: residual = %.2e\n" iter residual
        end
        
        if residual < settings.tolerance
            return iter, residual
        end
        
        apply_preconditioner!(z, r, precond)
        rznew = dot(r, z)
        beta = rznew / rzold
        p .= z .+ beta .* p
        rzold = rznew
    end
    
    final_residual = norm(r)
    settings.verbose && @warn "CG did not converge in $(settings.max_iterations) iterations. Final residual: $(final_residual)"
    return settings.max_iterations, final_residual
end

# =========================================================================
# BiCGSTAB Solver
# =========================================================================

"""
    bicgstab_solve!(A::SparseMatrixCSC{Float64,Int}, b::Vector{Float64}, x::Vector{Float64},
                    precond::Preconditioner, settings::SolverSettings)

BiCGSTAB solver with optional robust mode.
"""
function bicgstab_solve!(
    A::SparseMatrixCSC{Float64,Int},
    b::Vector{Float64},
    x::Vector{Float64},
    precond::Preconditioner,
    settings::SolverSettings
)
    n = length(b)
    r = similar(b)
    r_hat = similar(b)
    v = similar(b)
    p = similar(b)
    h = similar(b)
    s = similar(b)
    t = similar(b)
    
    # Initial residual
    mul!(r, A, x)
    r .= b .- r
    r_hat .= r
    
    initial_residual = norm(r)
    if initial_residual < settings.tolerance
        return 0, initial_residual
    end
    
    rho = 1.0
    alpha = 1.0
    omega = 1.0
    v .= 0.0
    p .= 0.0
    
    for iter in 1:settings.max_iterations
        # Robust mode checks
        if settings.robust && (iter % settings.check_frequency == 0)
            if any(isnan.(x)) || any(isinf.(x))
                settings.verbose && @warn "NaN/Inf detected in solution at iteration $iter"
                return iter, NaN
            end
        end
        
        rho_new = dot(r_hat, r)
        
        if abs(rho_new) < 1e-16
            settings.verbose && @warn "BiCGSTAB breakdown (rho) at iteration $iter"
            return iter, norm(r)
        end
        
        beta = (rho_new / rho) * (alpha / omega)
        p .= r .+ beta .* (p .- omega .* v)
        
        apply_preconditioner!(h, p, precond)
        mul!(v, A, h)
        
        alpha = rho_new / dot(r_hat, v)
        
        if !isfinite(alpha)
            settings.verbose && @warn "BiCGSTAB breakdown (alpha) at iteration $iter"
            return iter, norm(r)
        end
        
        s .= r .- alpha .* v
        
        # Check intermediate residual
        residual_s = norm(s)
        if residual_s < settings.tolerance
            x .+= alpha .* h
            return iter, residual_s
        end
        
        apply_preconditioner!(h, s, precond)
        mul!(t, A, h)
        
        omega = dot(t, s) / dot(t, t)
        
        if !isfinite(omega) || abs(omega) < 1e-16
            settings.verbose && @warn "BiCGSTAB breakdown (omega) at iteration $iter"
            return iter, norm(s)
        end
        
        x .+= alpha .* p .+ omega .* h
        r .= s .- omega .* t
        
        residual = norm(r)
        if settings.verbose && iter % 10 == 0
            @printf "BiCGSTAB iteration %d: residual = %.2e\n" iter residual
        end
        
        if residual < settings.tolerance
            return iter, residual
        end
        
        rho = rho_new
    end
    
    final_residual = norm(r)
    settings.verbose && @warn "BiCGSTAB did not converge in $(settings.max_iterations) iterations. Final residual: $(final_residual)"
    return settings.max_iterations, final_residual
end

# =========================================================================
# GMRES Solver (Simplified)
# =========================================================================

"""
    gmres_solve!(A::SparseMatrixCSC{Float64,Int}, b::Vector{Float64}, x::Vector{Float64},
                 precond::Preconditioner, settings::SolverSettings; restart=20)

GMRES solver with optional robust mode.
"""
function gmres_solve!(
    A::SparseMatrixCSC{Float64,Int},
    b::Vector{Float64},
    x::Vector{Float64},
    precond::Preconditioner,
    settings::SolverSettings;
    restart=20
)
    n = length(b)
    r = similar(b)
    
    # Initial residual
    mul!(r, A, x)
    r .= b .- r
    
    initial_residual = norm(r)
    if initial_residual < settings.tolerance
        return 0, initial_residual
    end
    
    # GMRES implementation with Arnoldi process
    n = length(b)
    m = min(20, n)  # Restart parameter
    max_restarts = div(settings.max_iterations, m) + 1
    
    # Initialize (don't zero out x, keep initial guess)
    
    for restart in 1:max_restarts
        # Compute initial residual
        mul!(r, A, x)
        r .= b .- r
        beta = norm(r)
        
        if beta < settings.tolerance
            return (restart - 1) * m, beta
        end
        
        # Krylov subspace basis
        V = zeros(n, m + 1)
        V[:, 1] = r / beta
        
        # Hessenberg matrix
        H = zeros(m + 1, m)
        
        # Arnoldi process
        for j in 1:m
            # Matrix-vector product
            w_temp = A * V[:, j]
            
            # Apply preconditioner
            w = zeros(n)
            apply_preconditioner!(w, w_temp, precond)
            
            # Modified Gram-Schmidt orthogonalization
            for i in 1:j
                H[i, j] = dot(w, V[:, i])
                w -= H[i, j] * V[:, i]
            end
            
            H[j + 1, j] = norm(w)
            
            if H[j + 1, j] < 1e-14
                # Lucky breakdown
                m = j
                break
            end
            
            V[:, j + 1] = w / H[j + 1, j]
        end
        
        # Solve least squares problem: min ||beta * e_1 - H * y||
        e1 = zeros(m + 1)
        e1[1] = beta
        
        # QR factorization of H using Givens rotations
        c = zeros(m)
        s = zeros(m)
        g = copy(e1)
        
        for i in 1:m
            # Apply previous rotations
            for k in 1:i-1
                temp = c[k] * H[k, i] + s[k] * H[k + 1, i]
                H[k + 1, i] = -s[k] * H[k, i] + c[k] * H[k + 1, i]
                H[k, i] = temp
            end
            
            # Compute new rotation
            if abs(H[i + 1, i]) < 1e-14
                c[i] = 1.0
                s[i] = 0.0
            else
                if abs(H[i + 1, i]) > abs(H[i, i])
                    tau = H[i, i] / H[i + 1, i]
                    s[i] = 1.0 / sqrt(1.0 + tau * tau)
                    c[i] = s[i] * tau
                else
                    tau = H[i + 1, i] / H[i, i]
                    c[i] = 1.0 / sqrt(1.0 + tau * tau)
                    s[i] = c[i] * tau
                end
            end
            
            # Apply new rotation
            temp = c[i] * H[i, i] + s[i] * H[i + 1, i]
            H[i + 1, i] = -s[i] * H[i, i] + c[i] * H[i + 1, i]
            H[i, i] = temp
            
            # Rotate RHS
            temp = c[i] * g[i] + s[i] * g[i + 1]
            g[i + 1] = -s[i] * g[i] + c[i] * g[i + 1]
            g[i] = temp
            
            # Check convergence
            if abs(g[i + 1]) < settings.tolerance
                m = i
                break
            end
        end
        
        # Back substitution
        y = zeros(m)
        for i in m:-1:1
            y[i] = g[i]
            for j in i+1:m
                y[i] -= H[i, j] * y[j]
            end
            y[i] /= H[i, i]
        end
        
        # Update solution
        for j in 1:m
            x .+= y[j] .* V[:, j]
        end
        
        # Check convergence
        residual = norm(b - A * x)
        if residual < settings.tolerance
            return restart * m, residual
        end
    end
    
    final_residual = norm(b - A * x)
    return max_restarts * m, final_residual
end

# =========================================================================
# High-Level Solver Interface
# =========================================================================

"""
    solve!(A::SparseMatrixCSC{Float64,Int}, b::Vector{Float64}, x::Vector{Float64};
           method=:bicgstab, preconditioner=:diagonal, settings=SolverSettings())

High-level interface for solving linear systems.

# Arguments
- `A`: System matrix
- `b`: Right-hand side vector  
- `x`: Initial guess (modified in-place)
- `method`: Solver method (:cg, :bicgstab, :gmres)
- `preconditioner`: Preconditioner type (:none, :diagonal, :ilu, :dic)
- `settings`: Solver settings
"""
function solve!(
    A::SparseMatrixCSC{Float64,Int},
    b::Vector{Float64},
    x::Vector{Float64};
    method=:bicgstab,
    preconditioner=:diagonal,
    settings=SolverSettings()
)
    # Apply regularization if in robust mode
    if settings.robust && settings.regularization > 0
        A_reg = copy(A)
        regularize_matrix!(A_reg, settings.regularization)
        A = A_reg
    end
    
    # Build preconditioner
    precond = if preconditioner == :none
        IdentityPreconditioner()
    elseif preconditioner == :diagonal
        build_diagonal_preconditioner(A)
    elseif preconditioner == :ilu
        build_ilu_preconditioner(A)
    elseif preconditioner == :dic
        build_dic_preconditioner(A)
    else
        @warn "Unknown preconditioner $preconditioner, using diagonal"
        build_diagonal_preconditioner(A)
    end
    
    # Diagnose system if in robust mode
    if settings.robust
        diagnostics = diagnose_linear_system(A, b, x)
        if settings.verbose && !isempty(diagnostics[:recommendations])
            @info "Linear system diagnostics:"
            for rec in diagnostics[:recommendations]
                @info "  - $rec"
            end
        end
    end
    
    # Call appropriate solver
    if method == :cg
        return cg_solve!(A, b, x, precond, settings)
    elseif method == :bicgstab
        return bicgstab_solve!(A, b, x, precond, settings)
    elseif method == :gmres
        return gmres_solve!(A, b, x, precond, settings)
    else
        @warn "Unknown method $method, using BiCGSTAB"
        return bicgstab_solve!(A, b, x, precond, settings)
    end
end

end # module LinearSolvers