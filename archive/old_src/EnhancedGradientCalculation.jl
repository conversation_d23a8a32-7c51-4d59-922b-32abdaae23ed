"""
    EnhancedGradientCalculation.jl

This module provides highly optimized gradient calculation implementations
for different mesh sizes with adaptive strategy selection.
"""

using LinearAlgebra
using StaticArrays
using SparseArrays
using LoopVectorization
using Base.Threads

"""
    compute_gradient_small_mesh!(gradients::Matrix{Float64}, face_values::Vector{Float64}, 
                               face_normals::Matrix{Float64}, face_owners::Vector{Int},
                               face_neighbors::Vector{Int}, cell_faces::Vector{Vector{Int}},
                               cell_volumes::Vector{Float64})

Optimized gradient calculation for small meshes (< 1000 cells).
"""
function compute_gradient_small_mesh!(gradients::Matrix{Float64}, face_values::Vector{Float64}, 
                                    face_normals::Matrix{Float64}, face_owners::Vector{Int},
                                    face_neighbors::Vector{Int}, cell_faces::Vector{Vector{Int}},
                                    cell_volumes::Vector{Float64})
    n_cells = length(cell_volumes)
    
    # Initialize gradients to zero
    @turbo for i in 1:n_cells
        gradients[1, i] = 0.0
        gradients[2, i] = 0.0
        gradients[3, i] = 0.0
    end
    
    # Simple loop approach for small meshes - avoids overhead of more complex strategies
    for cell_idx in 1:n_cells
        for face_idx in cell_faces[cell_idx]
            # Check if cell is owner or neighbor
            sign = (face_owners[face_idx] == cell_idx) ? 1.0 : -1.0
            
            # Add contribution to gradient
            face_value = face_values[face_idx]
            gradients[1, cell_idx] += sign * face_value * face_normals[1, face_idx]
            gradients[2, cell_idx] += sign * face_value * face_normals[2, face_idx]
            gradients[3, cell_idx] += sign * face_value * face_normals[3, face_idx]
        end
        
        # Divide by cell volume
        inv_vol = 1.0 / cell_volumes[cell_idx]
        gradients[1, cell_idx] *= inv_vol
        gradients[2, cell_idx] *= inv_vol
        gradients[3, cell_idx] *= inv_vol
    end
end

"""
    compute_gradient_medium_mesh!(gradients::Matrix{Float64}, face_values::Vector{Float64}, 
                                face_normals::Matrix{Float64}, face_owners::Vector{Int},
                                face_neighbors::Vector{Int}, cell_faces::Vector{Vector{Int}},
                                cell_volumes::Vector{Float64})

Optimized gradient calculation for medium meshes (1,000 - 50,000 cells).
Uses cache-friendly block processing with SIMD acceleration.
"""
function compute_gradient_medium_mesh!(gradients::Matrix{Float64}, face_values::Vector{Float64}, 
                                     face_normals::Matrix{Float64}, face_owners::Vector{Int},
                                     face_neighbors::Vector{Int}, cell_faces::Vector{Vector{Int}},
                                     cell_volumes::Vector{Float64})
    n_cells = length(cell_volumes)
    
    # Initialize gradients to zero
    @turbo for i in 1:n_cells
        gradients[1, i] = 0.0
        gradients[2, i] = 0.0
        gradients[3, i] = 0.0
    end
    
    # Pre-compute face signs for faster lookup
    face_signs = zeros(length(face_values))
    
    # First pass: compute face signs
    for cell_idx in 1:n_cells
        for face_idx in cell_faces[cell_idx]
            face_signs[face_idx] = (face_owners[face_idx] == cell_idx) ? 1.0 : -1.0
        end
    end
    
    # Second pass: compute gradients with better cache locality
    # Process cells in blocks for better cache performance
    block_size = 64  # Typical L1 cache line size in elements
    for block_start in 1:block_size:n_cells
        block_end = min(block_start + block_size - 1, n_cells)
        
        # Process all faces for cells in this block
        for cell_idx in block_start:block_end
            # Get all faces for this cell
            faces = cell_faces[cell_idx]
            
            # Process faces in a vectorized fashion
            cell_grad_x = 0.0
            cell_grad_y = 0.0
            cell_grad_z = 0.0
            
            for face_idx in faces
                sign = face_signs[face_idx]
                val = face_values[face_idx]
                
                cell_grad_x += sign * val * face_normals[1, face_idx]
                cell_grad_y += sign * val * face_normals[2, face_idx]
                cell_grad_z += sign * val * face_normals[3, face_idx]
            end
            
            # Divide by cell volume
            inv_vol = 1.0 / cell_volumes[cell_idx]
            gradients[1, cell_idx] = cell_grad_x * inv_vol
            gradients[2, cell_idx] = cell_grad_y * inv_vol
            gradients[3, cell_idx] = cell_grad_z * inv_vol
        end
    end
end

"""
    compute_gradient_large_mesh!(gradients::Matrix{Float64}, face_values::Vector{Float64}, 
                               face_normals::Matrix{Float64}, face_owners::Vector{Int},
                               face_neighbors::Vector{Int}, cell_faces::Vector{Vector{Int}},
                               cell_volumes::Vector{Float64})

Optimized gradient calculation for large meshes (> 50,000 cells).
Uses aggressive multi-threading with SIMD acceleration and cache-optimized access patterns.
"""
function compute_gradient_large_mesh!(gradients::Matrix{Float64}, face_values::Vector{Float64}, 
                                    face_normals::Matrix{Float64}, face_owners::Vector{Int},
                                    face_neighbors::Vector{Int}, cell_faces::Vector{Vector{Int}},
                                    cell_volumes::Vector{Float64})
    n_cells = length(cell_volumes)
    
    # Initialize gradients to zero
    @turbo for i in 1:n_cells
        gradients[1, i] = 0.0
        gradients[2, i] = 0.0
        gradients[3, i] = 0.0
    end
    
    # Pre-compute face signs for faster lookup
    n_faces = length(face_values)
    face_signs = zeros(n_faces)
    
    # Split face sign computation across threads
    chunk_size = max(1, n_cells ÷ Threads.nthreads())
    Threads.@threads for thread_id in 1:Threads.nthreads()
        start_idx = (thread_id - 1) * chunk_size + 1
        end_idx = thread_id == Threads.nthreads() ? n_cells : thread_id * chunk_size
        
        for cell_idx in start_idx:end_idx
            for face_idx in cell_faces[cell_idx]
                face_signs[face_idx] = (face_owners[face_idx] == cell_idx) ? 1.0 : -1.0
            end
        end
    end
    
    # Calculate gradients using Gauss theorem with multi-threading
    Threads.@threads for cell_idx in 1:n_cells
        # Process faces in chunks for better cache locality
        faces = cell_faces[cell_idx]
        n_faces = length(faces)
        
        # Use SIMD-friendly accumulation
        grad_x = 0.0
        grad_y = 0.0
        grad_z = 0.0
        
        # Process 4 faces at a time if possible (manual SIMD unrolling)
        i = 1
        while i <= n_faces - 3
            # Face 1
            f1 = faces[i]
            s1 = face_signs[f1]
            v1 = face_values[f1]
            
            # Face 2
            f2 = faces[i+1]
            s2 = face_signs[f2]
            v2 = face_values[f2]
            
            # Face 3
            f3 = faces[i+2]
            s3 = face_signs[f3]
            v3 = face_values[f3]
            
            # Face 4
            f4 = faces[i+3]
            s4 = face_signs[f4]
            v4 = face_values[f4]
            
            # X component
            grad_x += s1 * v1 * face_normals[1, f1]
            grad_x += s2 * v2 * face_normals[1, f2]
            grad_x += s3 * v3 * face_normals[1, f3]
            grad_x += s4 * v4 * face_normals[1, f4]
            
            # Y component
            grad_y += s1 * v1 * face_normals[2, f1]
            grad_y += s2 * v2 * face_normals[2, f2]
            grad_y += s3 * v3 * face_normals[2, f3]
            grad_y += s4 * v4 * face_normals[2, f4]
            
            # Z component
            grad_z += s1 * v1 * face_normals[3, f1]
            grad_z += s2 * v2 * face_normals[3, f2]
            grad_z += s3 * v3 * face_normals[3, f3]
            grad_z += s4 * v4 * face_normals[3, f4]
            
            i += 4
        end
        
        # Process remaining faces
        while i <= n_faces
            face_idx = faces[i]
            sign = face_signs[face_idx]
            val = face_values[face_idx]
            
            grad_x += sign * val * face_normals[1, face_idx]
            grad_y += sign * val * face_normals[2, face_idx]
            grad_z += sign * val * face_normals[3, face_idx]
            
            i += 1
        end
        
        # Divide by cell volume
        inv_vol = 1.0 / cell_volumes[cell_idx]
        gradients[1, cell_idx] = grad_x * inv_vol
        gradients[2, cell_idx] = grad_y * inv_vol
        gradients[3, cell_idx] = grad_z * inv_vol
    end
end

"""
    compute_gradient_adaptive!(gradients::Matrix{Float64}, face_values::Vector{Float64}, 
                             face_normals::Matrix{Float64}, face_owners::Vector{Int},
                             face_neighbors::Vector{Int}, cell_faces::Vector{Vector{Int}},
                             cell_volumes::Vector{Float64})

Adaptively selects the best gradient calculation strategy based on mesh size.
"""
function compute_gradient_adaptive!(gradients::Matrix{Float64}, face_values::Vector{Float64}, 
                                  face_normals::Matrix{Float64}, face_owners::Vector{Int},
                                  face_neighbors::Vector{Int}, cell_faces::Vector{Vector{Int}},
                                  cell_volumes::Vector{Float64})
    n_cells = length(cell_volumes)
    
    if n_cells < 1000
        # Small mesh - use simple approach
        compute_gradient_small_mesh!(gradients, face_values, face_normals, face_owners, face_neighbors, cell_faces, cell_volumes)
    elseif n_cells < 50000
        # Medium mesh - use cache-friendly approach
        compute_gradient_medium_mesh!(gradients, face_values, face_normals, face_owners, face_neighbors, cell_faces, cell_volumes)
    else
        # Large mesh - use aggressive parallelization
        compute_gradient_large_mesh!(gradients, face_values, face_normals, face_owners, face_neighbors, cell_faces, cell_volumes)
    end
end

# Export functions
export compute_gradient_small_mesh!, compute_gradient_medium_mesh!, compute_gradient_large_mesh!
export compute_gradient_adaptive!
