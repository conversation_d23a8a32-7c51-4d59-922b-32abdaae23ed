"""
    OpenFOAM import/export functionality for JuliaFOAM.
"""

using StaticArrays
using ..JuliaFOAM

export import_openfoam_case, export_to_openfoam, compare_with_openfoam

"""
    import_openfoam_case(case_dir::String)

Import an OpenFOAM case into JuliaFOAM.

# Arguments
- `case_dir`: Path to the OpenFOAM case directory

# Returns
- `Tuple`: Mesh, fields, properties, and configuration
"""
function import_openfoam_case(case_dir::String)
    # 1. Read mesh
    mesh_dir = joinpath(case_dir, "constant", "polyMesh")
    if !isdir(mesh_dir)
        error("Mesh directory not found: $mesh_dir")
    end
    
    println("Reading mesh from $mesh_dir")
    mesh = read_openfoam_mesh(mesh_dir)
    
    # 2. Read case configuration
    config = read_case_configuration(case_dir)
    
    # 3. Read physical properties
    properties_file = joinpath(case_dir, "constant", "transportProperties")
    if isfile(properties_file)
        println("Reading transport properties from $properties_file")
        properties = read_transport_properties(properties_file)
    else
        println("Using default transport properties")
        properties = FluidProperties()
    end
    
    # 4. Find latest time directory
    time_dirs = filter(isdir, readdir(case_dir, join=true))
    time_dirs = filter(d -> occursin(r"^\d+(\.\d+)?$", basename(d)), time_dirs)
    
    if isempty(time_dirs)
        time_dir = joinpath(case_dir, "0")
        if !isdir(time_dir)
            error("No time directories found in $case_dir")
        end
    else
        # Sort by time value
        sort!(time_dirs, by=d -> parse(Float64, basename(d)))
        time_dir = time_dirs[end]
    end
    
    println("Reading fields from $time_dir")
    
    # 5. Read fields
    fields = Dict{String,Any}()
    
    # Check for velocity field
    U_file = joinpath(time_dir, "U")
    if isfile(U_file)
        println("Reading velocity field from $U_file")
        fields["U"] = initialize_field(SVector{3,Float64}, mesh, U_file)
    end
    
    # Check for pressure field
    p_file = joinpath(time_dir, "p")
    if isfile(p_file)
        println("Reading pressure field from $p_file")
        fields["p"] = initialize_field(Float64, mesh, p_file)
    end
    
    # Check for other scalar fields
    for field_name in ["k", "epsilon", "omega", "T"]
        field_file = joinpath(time_dir, field_name)
        if isfile(field_file)
            println("Reading $field_name field from $field_file")
            fields[field_name] = initialize_field(Float64, mesh, field_file)
        end
    end
    
    return mesh, fields, properties, config
end

"""
    export_to_openfoam(case_dir::String, mesh::Mesh, fields::Dict, time::Union{Float64,String}="0")

Export JuliaFOAM data to OpenFOAM format.

# Arguments
- `case_dir`: Path to the output OpenFOAM case directory
- `mesh`: The mesh
- `fields`: Dictionary of fields
- `time`: Time value or directory name
"""
function export_to_openfoam(case_dir::String, mesh::Mesh, fields::Dict, time::Union{Float64,String}="0")
    # 1. Create directory structure
    mkpath(joinpath(case_dir, string(time)))
    mkpath(joinpath(case_dir, "constant", "polyMesh"))
    mkpath(joinpath(case_dir, "system"))
    
    # 2. Write mesh
    write_openfoam_mesh(joinpath(case_dir, "constant", "polyMesh"), mesh)
    
    # 3. Write fields
    for (field_name, field) in fields
        write_field(joinpath(case_dir, string(time)), field, mesh, field_name)
    end
    
    println("Exported to OpenFOAM format in $case_dir")
end

"""
    write_openfoam_mesh(mesh_dir::String, mesh::Mesh)

Write mesh to OpenFOAM format.

# Arguments
- `mesh_dir`: Path to the output mesh directory
- `mesh`: The mesh
"""
function write_openfoam_mesh(mesh_dir::String, mesh::Mesh)
    # This is a simplified version - would need more complete implementation
    # to handle all OpenFOAM mesh features
    
    # 1. Extract points from mesh
    points = extract_points_from_mesh(mesh)
    
    # 2. Write points file
    write_points_file(joinpath(mesh_dir, "points"), points)
    
    # 3. Write faces file
    write_faces_file(joinpath(mesh_dir, "faces"), mesh)
    
    # 4. Write owner and neighbour files
    write_owner_neighbour_files(mesh_dir, mesh)
    
    # 5. Write boundary file
    write_boundary_file(joinpath(mesh_dir, "boundary"), mesh)
end

"""
    extract_points_from_mesh(mesh::Mesh)

Extract points from mesh for OpenFOAM export.

# Arguments
- `mesh`: The mesh

# Returns
- `Vector{SVector{3,Float64}}`: List of unique points
"""
function extract_points_from_mesh(mesh::Mesh)
    points = Vector{SVector{3,Float64}}(undef, length(mesh.points))
    
    for (i, pt) in enumerate(mesh.points)
        points[i] = SVector{3,Float64}(pt[1], pt[2], pt[3])
    end
    
    return points
end

"""
    write_points_file(filename::String, points::Vector{SVector{3,Float64}})

Write points to OpenFOAM format.

# Arguments
- `filename`: Output file name
- `points`: List of points
"""
function write_points_file(filename::String, points::Vector{SVector{3,Float64}})
    open(filename, "w") do file
        # Write header
        write(file, """
/*--------------------------------*- C++ -*----------------------------------*\\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  v2012                                 |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    class       vectorField;
    location    "constant/polyMesh";
    object      points;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

""")
        # Write points
        write(file, "$(length(points))\n(\n")
        
        for pt in points
            write(file, "(" * join(string.(pt), " ") * ")\n")
        end
        
        write(file, ")\n\n// ************************************************************************* //")
    end
    return nothing
end

"""
    write_faces_file(filename::String, mesh::Mesh)

Write faces to OpenFOAM format.

# Arguments
- `filename`: Output file name
- `mesh`: The mesh
"""
function write_faces_file(filename::String, mesh::Mesh)
    # This is a placeholder - would need more complete implementation
    open(filename, "w") do file
        # Write header
        write(file, """
        /*--------------------------------*- C++ -*----------------------------------*\\
        | =========                 |                                                 |
        | \\\\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
        |  \\\\    /   O peration     | Version:  v2012                                |
        |   \\\\  /    A nd           | Website:  www.openfoam.com                      |
        |    \\\\/     M anipulation  |                                                 |
        \\*---------------------------------------------------------------------------*/
        FoamFile
        {
            version     2.0;
            format      ascii;
            class       faceList;
            object      faces;
        }
        // * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //
        
        $(length(mesh.faces))
        (
        """)
        
        # Write actual face connectivity
        for face in mesh.faces
            n_vertices = length(face.vertices)
            write(file, "$(n_vertices)(")
            for (j, vertex) in enumerate(face.vertices)
                write(file, "$(vertex - 1)")  # OpenFOAM uses 0-based indexing
                if j < n_vertices
                    write(file, " ")
                end
            end
            write(file, ")\n")
        end
        
        write(file, ")\n\n// ************************************************************************* //\n")
    end
end

"""
    write_owner_neighbour_files(mesh_dir::String, mesh::Mesh)

Write OpenFOAM owner and neighbor files from mesh data.

# Arguments
- `mesh_dir`: Directory to write the files
- `mesh`: The mesh containing cell and face data
"""
function write_owner_neighbour_files(mesh_dir::String, mesh::Mesh)
    # Create mesh directory if it doesn't exist
    mkpath(mesh_dir)
    
    # Write owner file
    open(joinpath(mesh_dir, "owner"), "w") do file
        # Write header
        write(file, """
        /*--------------------------------*- C++ -*----------------------------------*\\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  v2012                                 |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    class       labelList;
    location    "constant/polyMesh";
    object      owner;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //
""")
        
        # Write number of faces
        nFaces = length(mesh.faces)
        write(file, "$nFaces\n(\n")
        
        # Write owner for each face
        for face in mesh.faces
            # In OpenFOAM, owner is the cell with lower index
            write(file, "$(min(face.owner, face.neighbor))\n")
        end
        
        write(file, ")\n\n// ************************************************************************* //")
    end
    
    # Write neighbour file
    open(joinpath(mesh_dir, "neighbour"), "w") do file
        # Write header
        write(file, """
/*--------------------------------*- C++ -*----------------------------------*\\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  v2012                                 |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    class       labelList;
    location    "constant/polyMesh";
    object      neighbour;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //
""")
        
        # Get internal faces (those with a neighbor)
        internal_faces = filter(f -> f.neighbor > 0, mesh.faces)
        nInternalFaces = length(internal_faces)
        
        write(file, "$nInternalFaces\n(\n")
        
        # Write neighbor for each internal face
        for face in internal_faces
            # Neighbor is the cell with higher index
            write(file, "$(max(face.owner, face.neighbor))\n")
        end
        
        write(file, ")\n\n// ************************************************************************* //")
    end
end

"""
    write_boundary_file(filename::String, mesh::Mesh)

Write boundary file in OpenFOAM format.

# Arguments
- `filename`: Output file name
- `mesh`: The mesh containing boundary information
"""
function write_boundary_file(filename::String, mesh::Mesh)
    open(filename, "w") do file
        # Write header
        write(file, """
/*--------------------------------*- C++ -*----------------------------------*\\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  v2012                                |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    class       polyBoundaryMesh;
    location    "constant/polyMesh";
    object      boundary;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //
""")
        
        # Count boundary patches (excluding default patches)
        boundary_patches = filter(p -> !(p.name in ["defaultFaces", "defaultFaces"]), mesh.boundaryPatches)
        nPatches = length(boundary_patches)
        
        write(file, "$nPatches\n(\n")
        
        # Sort patches by start face for consistent ordering
        sort!(boundary_patches, by=p -> p.startFace)
        
        # Write boundary patches
        for (i, patch) in enumerate(boundary_patches)
            # Determine patch type
            patch_type = get_patch_type(patch)
            
            write(file, "    $(patch.name)\n    {\n")
            write(file, "        type            $patch_type;\n")
            write(file, "        nFaces          $(length(patch.faces));\n")
            write(file, "        startFace       $(patch.startFace);\n")
            
            # Add physical type if available
            if hasproperty(patch, :physicalType)
                write(file, "        physicalType    $(patch.physicalType);\n")
            end
            
            # Add specific patch settings based on type
            if patch_type == "patch"
                write(file, "        inGroups       1($(patch.name));\n")
            elseif patch_type == "wall"
                write(file, "        inGroups       1(wall);\n")
            end
            
            # Close the patch definition
            write(file, "    }")
            
            # Add newline between patches, but not after the last one
            if i < nPatches
                write(file, "\n\n")
            else
                write(file, "\n")
            end
        end
        
        write(file, ")\n\n// ************************************************************************* //\n")
    end
end

"""
    get_patch_type(patch)

Determine the OpenFOAM patch type from a boundary patch.

# Arguments
- `patch`: The boundary patch

# Returns
- `String`: OpenFOAM patch type (patch, wall, symmetry, etc.)
"""
function get_patch_type(patch)
    # Default to patch type
    patch_type = "patch"
    
    # Try to determine patch type from name
    lower_name = lowercase(patch.name)
    if occursin(r"wall", lower_name)
        patch_type = "wall"
    elseif occursin(r"symmetry", lower_name)
        patch_type = "symmetry"
    elseif occursin(r"inlet", lower_name)
        patch_type = "patch"
    elseif occursin(r"outlet", lower_name)
        patch_type = "patch"
    end
    
    # Override with explicit type if available
    if hasproperty(patch, :type)
        patch_type = patch.type
    end
    
    return patch_type
end

"""
    write_field(time_dir::String, field::AbstractArray{T}, mesh::Mesh, field_name::String)

Write a field to OpenFOAM format file.

# Arguments
- `time_dir`: Output directory (e.g., "0.001" for time 0.001)
- `field`: The field data (scalar or vector)
- `mesh`: The mesh containing boundary information
- `field_name`: Name of the field (e.g., "U", "p", "k", "omega")
"""
function write_field(time_dir::String, field::AbstractArray{T}, mesh::Mesh, field_name::String) where {T}
    # Create time directory if it doesn't exist
    mkpath(time_dir)
    
    # Determine field type and dimensions
    is_vector = T <: SVector{3}
    field_class = is_vector ? "volVectorField" : "volScalarField"
    value_type = is_vector ? "vector" : "scalar"
    
    # Default dimensions based on field name (OpenFOAM convention)
    dimensions = get_field_dimensions(field_name, is_vector)
    
    open(joinpath(time_dir, field_name), "w") do file
        # Write header
        write(file, """
/*--------------------------------*- C++ -*----------------------------------*\\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  v2012                                |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    class       $field_class;
    location    "$time_dir";
    object      $field_name;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //
""")
        
        # Write dimensions
        write(file, "dimensions      $dimensions;\n")
        
        # Write internal field
        write(file, "internalField   nonuniform List<$value_type>\n")
        write(file, "$(length(field))\n(\n")
        
        # Write field values
        for val in field
            if is_vector
                write(file, "($(val[1]) $(val[2]) $(val[3]))\n")
            else
                write(file, "$val\n")
            end
        end
        write(file, ");\n")
        
        # Write boundary field
        write(file, "\nboundaryField\n{\n")
        
        # Sort patches by start face for consistent ordering
        boundary_patches = sort(collect(mesh.boundaryPatches), by=p -> p.startFace)
        
        for patch in boundary_patches
            patch_name = patch.name
            patch_type = get_patch_type(patch)
            
            write(file, "    $patch_name\n    {\n")
            
            # Determine boundary condition type
            bc_type, bc_value = get_boundary_condition(patch, field_name, is_vector)
            write(file, "        type            $bc_type;\n")
            
            # Write boundary condition value
            if bc_type != "empty" && !isempty(bc_value)
                if is_vector
                    write(file, "        value           uniform ($(bc_value[1]) $(bc_value[2]) $(bc_value[3]));\n")
                else
                    write(file, "        value           uniform $bc_value;\n")
                end
            end
            
            # Add patch-specific settings
            if patch_type == "wall" && field_name == "omega"
                write(file, "        Cmu             0.09;\n")
                write(file, "        kappa           0.41;\n")
                write(file, "        E               9.8;\n")
            end
            
            # Close the patch definition
            write(file, "    }\n")
        end
        
        # Close the boundary field dictionary
        write(file, "}\n")
        
        # Write footer
        write(file, "\n// ************************************************************************* //\n")
    end
end

"""
    get_field_dimensions(field_name::String, is_vector::Bool)

Get the OpenFOAM dimensions for a field based on its name and type.

# Arguments
- `field_name`: Name of the field
- `is_vector`: Whether the field is a vector field

# Returns
- `String`: Dimensions in OpenFOAM format [kg m s K mol A cd]
"""
function get_field_dimensions(field_name::String, is_vector::Bool)
    # Default dimensions (dimensionless)
    dims = [0, 0, 0, 0, 0, 0, 0]
    
    # Set dimensions based on field name (OpenFOAM conventions)
    if field_name == "U"  # Velocity
        dims = [0, 1, -1, 0, 0, 0, 0]
    elseif field_name == "p"  # Pressure
        dims = [1, -1, -2, 0, 0, 0, 0]
    elseif field_name == "p_rgh"  # Dynamic pressure
        dims = [1, -1, -2, 0, 0, 0, 0]
    elseif field_name == "k"  # Turbulent kinetic energy
        dims = [0, 2, -2, 0, 0, 0, 0]
    elseif field_name == "omega" || field_name == "epsilon"  # Specific dissipation rate
        dims = [0, 0, -1, 0, 0, 0, 0]
    elseif field_name == "nut" || field_name == "nuTilda"  # Turbulent viscosity
        dims = [0, 2, -1, 0, 0, 0, 0]
    elseif field_name == "T"  # Temperature
        dims = [0, 0, 0, 1, 0, 0, 0]
    end
    
    # Format as OpenFOAM dimensions
    return "[$(join(dims, ' '))]"
end

"""
    get_boundary_condition(patch, field_name::String, is_vector::Bool)

Get the boundary condition type and value for a field on a given patch.

# Arguments
- `patch`: The boundary patch
- `field_name`: Name of the field
- `is_vector`: Whether the field is a vector field

# Returns
- `Tuple{String, Any}`: (bc_type, bc_value)
"""
function get_boundary_condition(patch, field_name::String, is_vector::Bool)
    patch_type = get_patch_type(patch)
    
    # Default values
    bc_type = "calculated"
    bc_value = is_vector ? [0.0, 0.0, 0.0] : 0.0
    
    # Set boundary condition type based on patch type and field name
    if patch_type == "wall"
        if field_name == "U"
            bc_type = "noSlip"
            bc_value = [0.0, 0.0, 0.0]
        elseif field_name == "p"
            bc_type = "zeroGradient"
            bc_value = []
        elseif field_name == "k"
            bc_type = "kqRWallFunction"
            bc_value = 0.0
        elseif field_name == "omega"
            bc_type = "omegaWallFunction"
            bc_value = 0.0
        end
    elseif patch_type == "inlet"
        if field_name == "U"
            bc_type = "fixedValue"
            bc_value = [1.0, 0.0, 0.0]  # Default inlet velocity
        elseif field_name == "p"
            bc_type = "zeroGradient"
            bc_value = []
        end
    elseif patch_type == "outlet"
        if field_name == "U"
            bc_type = "inletOutlet"
            bc_value = [0.0, 0.0, 0.0]
        elseif field_name == "p"
            bc_type = "fixedValue"
            bc_value = 0.0  # Reference pressure
        end
    elseif patch_type == "symmetry"
        bc_type = "symmetry"
        bc_value = []
    elseif patch_type == "empty"
        bc_type = "empty"
        bc_value = []
    end
    
    # Override with patch-specific boundary conditions if available
    if hasproperty(patch, :boundaryConditions) && haskey(patch.boundaryConditions, field_name)
        bc = patch.boundaryConditions[field_name]
        if hasproperty(bc, :type)
            bc_type = bc.type
        end
        if hasproperty(bc, :value)
            bc_value = bc.value
        end
    end
    
    return bc_type, bc_value
end

"""
    compare_with_openfoam(julia_case_dir::String, openfoam_case_dir::String)

Compare JuliaFOAM results with OpenFOAM results.

# Arguments
- `julia_case_dir`: Path to the JuliaFOAM case directory
- `openfoam_case_dir`: Path to the OpenFOAM case directory

# Returns
- `Dict`: Dictionary of error metrics
"""
function compare_with_openfoam(julia_case_dir::String, openfoam_case_dir::String)
    # 1. Import both cases
    julia_mesh, julia_fields, _, _ = import_openfoam_case(julia_case_dir)
    openfoam_mesh, openfoam_fields, _, _ = import_openfoam_case(openfoam_case_dir)
    
    # 2. Compare fields
    errors = Dict{String,Dict{String,Float64}}()
    
    for field_name in intersect(keys(julia_fields), keys(openfoam_fields))
        julia_field = julia_fields[field_name]
        openfoam_field = openfoam_fields[field_name]
        
        # Calculate error metrics
        if eltype(julia_field.internal_field) <: SVector{3,Float64}
            # Vector field
            diff = [norm(julia_field.internal_field[i] - openfoam_field.internal_field[i]) 
                   for i in 1:length(julia_field.internal_field)]
            
            max_diff = maximum(diff)
            mean_diff = mean(diff)
            rms_diff = sqrt(mean(diff.^2))
            
            errors[field_name] = Dict{String,Float64}(
                "max_error" => max_diff,
                "mean_error" => mean_diff,
                "rms_error" => rms_diff
            )
        else
            # Scalar field
            diff = abs.(julia_field.internal_field - openfoam_field.internal_field)
            
            max_diff = maximum(diff)
            mean_diff = mean(diff)
            rms_diff = sqrt(mean(diff.^2))
            
            errors[field_name] = Dict{String,Float64}(
                "max_error" => max_diff,
                "mean_error" => mean_diff,
                "rms_error" => rms_diff
            )
        end
    end
    
    return errors
end

"""
    read_mesh(mesh_dir::String)

Read mesh from OpenFOAM format.

# Arguments
- `mesh_dir`: Path to the mesh directory

# Returns
- `Tuple`: Points, faces, owner, neighbour, and boundary
"""
function read_openfoam_mesh(mesh_dir::String)
    points_file = joinpath(mesh_dir, "points")
    faces_file = joinpath(mesh_dir, "faces")
    owner_file = joinpath(mesh_dir, "owner")
    neighbour_file = joinpath(mesh_dir, "neighbour")
    boundary_file = joinpath(mesh_dir, "boundary")

    if !isdir(mesh_dir)
        error("Mesh directory $mesh_dir does not exist.")
    end

    if !isfile(points_file)
        error("Points file $points_file does not exist.")
    end

    if !isfile(faces_file)
        error("Faces file $faces_file does not exist.")
    end

    if !isfile(owner_file)
        error("Owner file $owner_file does not exist.")
    end

    if !isfile(neighbour_file)
        error("Neighbour file $neighbour_file does not exist.")
    end

    if !isfile(boundary_file)
        error("Boundary file $boundary_file does not exist.")
    end

    try
        # Read mesh data
        points = read_points(points_file)
        faces = read_faces(faces_file)
        owner = read_vector(owner_file, Int64)
        neighbour = read_vector(neighbour_file, Int64)
        boundary = read_boundary(boundary_file)

        return points, faces, owner, neighbour, boundary
    catch e
        println("Error reading mesh: ", e)
        rethrow(e)
    end
end

"""
    read_points(file_path::String)

Read points from OpenFOAM format.

# Arguments
- `file_path`: Path to the points file

# Returns
- `Vector{SVector{3,Float64}}`: List of points
"""
function read_points(file_path::String)
    if !isfile(file_path)
        error("Points file $file_path does not exist.")
    end

    try
        return read_vector(file_path, Float64)
    catch e
        println("Error reading points: ", e)
        rethrow(e)
    end
end

"""
    read_faces(file_path::String)

Read faces from OpenFOAM format.

# Arguments
- `file_path`: Path to the faces file

# Returns
- `Vector{Face}`: List of faces
"""
function read_faces(file_path::String)
    if !isfile(file_path)
        error("Faces file $file_path does not exist.")
    end

    try
        return read_cell_list(file_path)
    catch e
        println("Error reading faces: ", e)
        rethrow(e)
    end
end

"""
    read_vector(file_path::String, ::Type{T}) where T

Read a vector from OpenFOAM format.

# Arguments
- `file_path`: Path to the vector file
- `T`: Type of the vector elements

# Returns
- `Vector{T}`: The vector
"""
function read_vector(file_path::String, ::Type{T}) where T
    if !isfile(file_path)
        error("Vector file $file_path does not exist.")
    end

    try
        # Implementation of read_vector
        # ...
    catch e
        println("Error reading vector: ", e)
        rethrow(e)
    end
end

"""
    read_boundary(file_path::String)

Read boundary from OpenFOAM format.

# Arguments
- `file_path`: Path to the boundary file

# Returns
- `Dict{String,Vector{Int64}}`: Dictionary of boundary patches
"""
function read_boundary(file_path::String)
    if !isfile(file_path)
        error("Boundary file $file_path does not exist.")
    end

    try
        # Implementation of read_boundary
        # ...
    catch e
        println("Error reading boundary: ", e)
        rethrow(e)
    end
end
