"""
    OpenFOAM turbulence properties import/export functionality for JuliaFOAM.
"""
module TurbulencePropertiesIO

export read_turbulence_properties, write_turbulence_properties
export TurbulenceProperties

"""
    TurbulenceProperties

Structure to hold OpenFOAM turbulence properties.
"""
struct TurbulenceProperties
    simulationType::String
    RAS::Dict{String, Any}
    LES::Dict{String, Any}
end

# Default constructor with sensible defaults
function TurbulenceProperties(;
    simulationType="RAS",
    RAS=Dict("model" => "kEpsilon", "turbulence" => "on", "printCoeffs" => "on"),
    LES=Dict("model" => "Smagorinsky", "turbulence" => "on", "printCoeffs" => "on")
)
    return TurbulenceProperties(simulationType, RAS, LES)
end

"""
    read_turbulence_properties(file_path::String)

Read the turbulenceProperties file from an OpenFOAM case.

# Arguments
- `file_path`: Path to the turbulenceProperties file

# Returns
- `TurbulenceProperties`: The turbulence properties
"""
function read_turbulence_properties(file_path::String)
    if !isfile(file_path)
        @warn "turbulenceProperties file $file_path does not exist. Using defaults."
        return TurbulenceProperties()
    end
    
    # Initialize with defaults
    simulationType = "RAS"
    RAS = Dict{String, Any}("model" => "kEpsilon", "turbulence" => "on", "printCoeffs" => "on")
    LES = Dict{String, Any}("model" => "Smagorinsky", "turbulence" => "on", "printCoeffs" => "on")
    
    # Parse the file
    open(file_path, "r") do file
        current_section = ""
        
        for line in eachline(file)
            line = strip(line)
            
            # Skip comments and empty lines
            if startswith(line, "//") || startswith(line, "/*") || isempty(line)
                continue
            end
            
            # Parse simulationType
            if occursin("simulationType", line) && occursin(";", line)
                m = match(r"simulationType\s+([\w]+)", line)
                if m !== nothing
                    simulationType = m.captures[1]
                end
            end
            
            # Check for section headers
            if occursin("RAS", line) && occursin("{", line)
                current_section = "RAS"
                continue
            elseif occursin("LES", line) && occursin("{", line)
                current_section = "LES"
                continue
            end
            
            # End of section
            if line == "}"
                current_section = ""
                continue
            end
            
            # Process entries within sections
            if current_section == "RAS" && occursin(";", line)
                parts = split(replace(line, ";" => ""), " ", keepempty=false)
                
                if length(parts) >= 2
                    key = parts[1]
                    value = join(parts[2:end], " ")
                    RAS[key] = value
                end
            elseif current_section == "LES" && occursin(";", line)
                parts = split(replace(line, ";" => ""), " ", keepempty=false)
                
                if length(parts) >= 2
                    key = parts[1]
                    value = join(parts[2:end], " ")
                    LES[key] = value
                end
            end
        end
    end
    
    return TurbulenceProperties(simulationType, RAS, LES)
end

"""
    write_turbulence_properties(file_path::String, turbulence_properties::TurbulenceProperties)

Write the turbulenceProperties file for an OpenFOAM case.

# Arguments
- `file_path`: Path to the output turbulenceProperties file
- `turbulence_properties`: The turbulence properties
"""
function write_turbulence_properties(file_path::String, turbulence_properties::TurbulenceProperties)
    open(file_path, "w") do file
        # Write header
        write(file, """
/*--------------------------------*- C++ -*----------------------------------*\\
  =========                 |
  \\\\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox
   \\\\    /   O peration     | Website:  https://openfoam.org
    \\\\  /    A nd           | Version:  v2006
     \\\\/     M anipulation  |
\\*---------------------------------------------------------------------------*/
        """)
        
        # Write FoamFile header
        write(file, """
FoamFile
{
    version     2.0;
    format      ascii;
    class       dictionary;
    location    "constant";
    object      turbulenceProperties;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

        """)
        
        # Write simulation type
        write(file, "simulationType  $(turbulence_properties.simulationType);\n\n")
        
        # Write RAS model if applicable
        if turbulence_properties.simulationType == "RAS"
            write(file, "RAS\n{\n")
            for (key, value) in turbulence_properties.RAS
                write(file, "    $key        $value;\n")
            end
            write(file, "}\n\n")
        end
        
        # Write LES model if applicable
        if turbulence_properties.simulationType == "LES"
            write(file, "LES\n{\n")
            for (key, value) in turbulence_properties.LES
                write(file, "    $key        $value;\n")
            end
            write(file, "}\n\n")
        end
        
        # Write footer
        write(file, "// ************************************************************************* //")
    end
end

end # module
