"""
    SolverOptimizedSolvers

A module providing optimized linear solvers for JuliaFOAM while maintaining
full OpenFOAM interoperability.
"""
module SolverOptimizedSolvers

using LinearAlgebra
using SparseArrays
using Base.Threads

export solve_cg_optimized, solve_bicgstab_optimized, get_solver_settings_from_openfoam

"""
    solve_cg_optimized(A, b, x0=nothing; tol=1e-6, max_iter=1000, track_residuals=true)

Solve the linear system Ax = b using an optimized Conjugate Gradient method.
This implementation uses multi-threading for better performance.

# Arguments
- `A`: Coefficient matrix (sparse or dense)
- `b`: Right-hand side vector
- `x0`: Initial guess (optional)
- `tol`: Convergence tolerance
- `max_iter`: Maximum number of iterations
- `track_residuals`: Whether to track residual history

# Returns
- `x`: Solution vector
- `residuals`: Residual history (if track_residuals=true)
- `iterations`: Number of iterations performed
"""
function solve_cg_optimized(A, b, x0=nothing; tol=1e-6, max_iter=1000, track_residuals=true)
    n = length(b)
    
    # Initialize solution
    x = x0 === nothing ? zeros(n) : copy(x0)
    
    # Initialize residual and direction vectors
    r = b - A * x
    p = copy(r)
    
    # Initial residual norm
    r_norm = norm(r)
    initial_r_norm = r_norm
    
    # Residual history
    residuals = track_residuals ? [r_norm] : Float64[]
    
    # Iteration counter
    iter = 0
    
    # Main CG loop
    while r_norm > tol * initial_r_norm && iter < max_iter
        iter += 1
        
        # Compute A*p
        Ap = A * p
        
        # Compute step size
        alpha = dot(r, r) / (dot(p, Ap) + 1e-15)
        
        # Update solution and residual
        # Use multi-threading for large vectors
        if n > 10000
            @threads for i in 1:n
                x[i] += alpha * p[i]
                r[i] -= alpha * Ap[i]
            end
        else
            # For smaller vectors, avoid threading overhead
            x .+= alpha .* p
            r .-= alpha .* Ap
        end
        
        # Compute new residual norm
        r_norm_new = norm(r)
        if track_residuals
            push!(residuals, r_norm_new)
        end
        
        # Check for convergence
        if r_norm_new < tol * initial_r_norm
            r_norm = r_norm_new
            break
        end
        
        # Compute beta
        beta = (r_norm_new / r_norm)^2
        r_norm = r_norm_new
        
        # Update direction vector
        if n > 10000
            @threads for i in 1:n
                p[i] = r[i] + beta * p[i]
            end
        else
            p .= r .+ beta .* p
        end
    end
    
    return x, residuals, iter
end

"""
    solve_bicgstab_optimized(A, b, x0=nothing; tol=1e-6, max_iter=1000, track_residuals=true)

Solve the linear system Ax = b using an optimized BiCGSTAB method.
This implementation uses multi-threading for better performance.

# Arguments
- `A`: Coefficient matrix (sparse or dense)
- `b`: Right-hand side vector
- `x0`: Initial guess (optional)
- `tol`: Convergence tolerance
- `max_iter`: Maximum number of iterations
- `track_residuals`: Whether to track residual history

# Returns
- `x`: Solution vector
- `residuals`: Residual history (if track_residuals=true)
- `iterations`: Number of iterations performed
"""
function solve_bicgstab_optimized(A, b, x0=nothing; tol=1e-6, max_iter=1000, track_residuals=true)
    n = length(b)
    
    # Initialize solution
    x = x0 === nothing ? zeros(n) : copy(x0)
    
    # Initialize residual vectors
    r = b - A * x
    r_hat = copy(r)  # Shadow residual
    
    # Initial residual norm
    r_norm = norm(r)
    initial_r_norm = r_norm
    
    # Residual history
    residuals = track_residuals ? [r_norm] : Float64[]
    
    # Iteration counter
    iter = 0
    
    # Temporary vectors
    p = zeros(n)
    v = zeros(n)
    s = zeros(n)
    t = zeros(n)
    
    # Main BiCGSTAB loop
    while r_norm > tol * initial_r_norm && iter < max_iter
        iter += 1
        
        # Compute dot product
        rho = dot(r_hat, r)
        
        if abs(rho) < 1e-15
            break  # Method breakdown
        end
        
        # First iteration
        if iter == 1
            if n > 10000
                @threads for i in 1:n
                    p[i] = r[i]
                end
            else
                p .= r
            end
        else
            beta = (rho / rho_prev) * (alpha / omega)
            
            if n > 10000
                @threads for i in 1:n
                    p[i] = r[i] + beta * (p[i] - omega * v[i])
                end
            else
                p .= r .+ beta .* (p .- omega .* v)
            end
        end
        
        # Compute A*p
        v = A * p
        
        alpha = rho / (dot(r_hat, v) + 1e-15)
        
        if n > 10000
            @threads for i in 1:n
                s[i] = r[i] - alpha * v[i]
            end
        else
            s .= r .- alpha .* v
        end
        
        # Check for early convergence
        if norm(s) < tol * initial_r_norm
            if n > 10000
                @threads for i in 1:n
                    x[i] += alpha * p[i]
                end
            else
                x .+= alpha .* p
            end
            r_norm = norm(s)
            if track_residuals
                push!(residuals, r_norm)
            end
            break
        end
        
        # Compute A*s
        t = A * s
        
        omega = dot(t, s) / (dot(t, t) + 1e-15)
        
        # Update solution and residual
        if n > 10000
            @threads for i in 1:n
                x[i] += alpha * p[i] + omega * s[i]
                r[i] = s[i] - omega * t[i]
            end
        else
            x .+= alpha .* p .+ omega .* s
            r .= s .- omega .* t
        end
        
        # Compute new residual norm
        r_norm = norm(r)
        if track_residuals
            push!(residuals, r_norm)
        end
        
        # Check for convergence
        if r_norm < tol * initial_r_norm
            break
        end
        
        # Check for method breakdown
        if abs(omega) < 1e-15
            break
        end
        
        # Store rho for next iteration
        rho_prev = rho
    end
    
    return x, residuals, iter
end

"""
    get_solver_settings_from_openfoam(fv_solution, field_name)

Extract solver settings for a specific field from OpenFOAM's fvSolution dictionary.
This ensures compatibility with OpenFOAM solver settings.

# Arguments
- `fv_solution`: FvSolution dictionary from EnhancedOpenFOAMIO
- `field_name`: Name of the field (e.g., "p", "U")

# Returns
- `settings`: Dictionary with solver settings
"""
function get_solver_settings_from_openfoam(fv_solution, field_name)
    settings = Dict{String, Any}()
    
    # Default settings
    settings["solver"] = "CG"
    settings["tolerance"] = 1e-6
    settings["relTol"] = 0.01
    settings["maxIter"] = 1000
    
    # Extract settings from fvSolution if available
    if hasfield(typeof(fv_solution), :solvers) && haskey(fv_solution.solvers, field_name)
        solver_dict = fv_solution.solvers[field_name]
        
        if haskey(solver_dict, "solver")
            solver_type = solver_dict["solver"]
            if solver_type == "PCG" || solver_type == "GAMG" || solver_type == "smoothSolver"
                settings["solver"] = "CG"
            elseif solver_type == "PBiCGStab" || solver_type == "BiCGStab"
                settings["solver"] = "BiCGSTAB"
            end
        end
        
        if haskey(solver_dict, "tolerance")
            settings["tolerance"] = solver_dict["tolerance"]
        end
        
        if haskey(solver_dict, "relTol")
            settings["relTol"] = solver_dict["relTol"]
        end
        
        if haskey(solver_dict, "maxIter")
            settings["maxIter"] = solver_dict["maxIter"]
        end
    end
    
    return settings
end

"""
    solve_system_with_openfoam_settings(A, b, fv_solution, field_name, x0=nothing)

Solve a linear system using solver settings from OpenFOAM's fvSolution dictionary.
This ensures compatibility with OpenFOAM solver settings.

# Arguments
- `A`: Coefficient matrix
- `b`: Right-hand side vector
- `fv_solution`: FvSolution dictionary from EnhancedOpenFOAMIO
- `field_name`: Name of the field (e.g., "p", "U")
- `x0`: Initial guess (optional)

# Returns
- `x`: Solution vector
- `residuals`: Residual history
- `iterations`: Number of iterations performed
"""
function solve_system_with_openfoam_settings(A, b, fv_solution, field_name, x0=nothing)
    settings = get_solver_settings_from_openfoam(fv_solution, field_name)
    
    tol = settings["tolerance"]
    rel_tol = settings["relTol"]
    max_iter = settings["maxIter"]
    
    # Adjust tolerance based on initial residual if relTol is used
    if x0 !== nothing
        initial_residual = norm(b - A * x0)
        tol = min(tol, rel_tol * initial_residual)
    end
    
    if settings["solver"] == "CG"
        return solve_cg_optimized(A, b, x0, tol=tol, max_iter=max_iter)
    elseif settings["solver"] == "BiCGSTAB"
        return solve_bicgstab_optimized(A, b, x0, tol=tol, max_iter=max_iter)
    else
        # Default to CG
        return solve_cg_optimized(A, b, x0, tol=tol, max_iter=max_iter)
    end
end

end # module
