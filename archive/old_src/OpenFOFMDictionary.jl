module OpenFOFMDictionary

"""
    read_dictionary(path::String)::Dict{String,Any}

Parse an OpenFOAM dictionary file and return a nested Dict. Comments (//, /* */) and semicolons are stripped.
"""
function read_dictionary(path::String)::Dict{String,Any}
    text = read(path, String)
    # remove line comments
    text = replace(text, r"//.*" => "")
    # remove block comments
    text = replace(text, r"/\*.*?\*/"s => "")
    # split tokens by whitespace, braces, and semicolons
    tokens = collect(matchall(r"[{}]|[^\s{};]+", text))
    pos = Ref(1)
    return _parse_dict(tokens, pos)
end

function _parse_dict(tokens::Vector{String}, pos::Base.RefValue{Int})::Dict{String,Any}
    dict = Dict{String,Any}()
    while pos[] <= length(tokens)
        key = tokens[pos[]]
        pos[] += 1
        if key == "}"  # end of this block
            break
        elseif pos[] <= length(tokens) && tokens[pos[]] == "{"
            pos[] += 1
            dict[key] = _parse_dict(tokens, pos)
        else
            # value
            if pos[] > length(tokens)
                break
            end
            val = tokens[pos[]]
            pos[] += 1
            # try parse number
            num = tryparse(Float64, val)
            dict[key] = num === nothing ? val : num
        end
    end
    return dict
end

# High-level readers

import ..JuliaFOAM: CaseConfiguration, FluidProperties
import ..solvers.EnhancedSimpleSolver: EnhancedSimpleSolverConfig

"""
    read_case_configuration(case_path::String)::CaseConfiguration

Read system/controlDict and map to CaseConfiguration
"""
function read_case_configuration(case_path::String)::CaseConfiguration
    dict = read_dictionary(joinpath(case_path, "system", "controlDict"))
    cfg = CaseConfiguration(
        dict["application"],
        get(dict, "startTime", 0.0),
        get(dict, "endTime", 1.0),
        get(dict, "deltaT", 0.1),
        get(dict, "writeInterval", 0.1)
    )
    return cfg
end

"""
    read_transport_properties(case_path::String)::FluidProperties

Read constant/transportProperties and map to FluidProperties
"""
function read_transport_properties(case_path::String)::FluidProperties
    dict = read_dictionary(joinpath(case_path, "constant", "transportProperties"))
    nu = get(dict, "nu", 1e-5)
    rho = get(dict, "rho", 1.0)
    return FluidProperties(nu, rho)
end

"""
    read_solver_settings(case_path::String)::EnhancedSimpleSolverConfig

Read system/fvSolution (SIMPLE sub-dict) into solver config
"""
function read_solver_settings(case_path::String)::EnhancedSimpleSolverConfig
    dict = read_dictionary(joinpath(case_path, "system", "fvSolution"))
    simp = get(dict, "SIMPLE", Dict{String,Any}())
    relax = get(simp, "relaxationFactors", Dict{String,Any}())
    return EnhancedSimpleSolverConfig(
        max_iterations = Int(get(simp, "maxIterations", 1000)),
        tolerance = get(simp, "tolerance", 1e-6),
        relaxation_factors = Dict(
            "U" => Float64(get(relax, "U", 0.7)),
            "p" => Float64(get(relax, "p", 0.3))
        ),
        residual_output_interval = Int(get(simp, "residualControl", 10)),
        residual_output_file = get(simp, "residualOutputFile", "residuals.csv"),
        track_residuals = get(simp, "trackResiduals", true),
        use_analytical_solution = false
    )
end

end # module
