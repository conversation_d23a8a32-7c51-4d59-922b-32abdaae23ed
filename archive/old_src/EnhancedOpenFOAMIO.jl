"""
    Enhanced OpenFOAM import/export functionality for JuliaFOAM.
    
    This module provides comprehensive support for importing and exporting
    complete OpenFOAM cases, including mesh, fields, boundary conditions,
    solver settings, and physical properties.
"""
module EnhancedOpenFOAMIO

using StaticArrays
using SparseArrays
using LinearAlgebra
using Printf
using ..JuliaFOAM

export import_openfoam_case, export_to_openfoam
export read_solver_settings, write_solver_settings
export read_boundary_conditions, write_boundary_conditions
export read_transport_properties, write_transport_properties
export read_turbulence_properties, write_turbulence_properties
export read_fv_schemes, write_fv_schemes
export read_fv_solution, write_fv_solution
export read_control_dict, write_control_dict
export compare_with_openfoam

# Type definitions for OpenFOAM dictionaries
abstract type OpenFOAMDict end

struct ControlDict <: OpenFOAMDict
    application::String
    startTime::Float64
    endTime::Float64
    deltaT::Float64
    writeControl::String
    writeInterval::Float64
    purgeWrite::Int
    writeFormat::String
    writePrecision::Int
    writeCompression::Bool
    timeFormat::String
    timePrecision::Int
    runTimeModifiable::Bool
    adjustTimeStep::Bool
    maxCo::Float64
    maxDeltaT::Float64
    functions::Dict{String, Any}
end

# Default constructor with sensible defaults
function ControlDict(;
    application="simpleFoam",
    startTime=0.0,
    endTime=1000.0,
    deltaT=1.0,
    writeControl="timeStep",
    writeInterval=100.0,
    purgeWrite=0,
    writeFormat="ascii",
    writePrecision=6,
    writeCompression=false,
    timeFormat="general",
    timePrecision=6,
    runTimeModifiable=true,
    adjustTimeStep=false,
    maxCo=1.0,
    maxDeltaT=1.0,
    functions=Dict{String, Any}()
)
    return ControlDict(
        application, startTime, endTime, deltaT, writeControl, writeInterval,
        purgeWrite, writeFormat, writePrecision, writeCompression, timeFormat,
        timePrecision, runTimeModifiable, adjustTimeStep, maxCo, maxDeltaT, functions
    )
end

struct FvSchemes <: OpenFOAMDict
    ddtSchemes::Dict{String, String}
    gradSchemes::Dict{String, String}
    divSchemes::Dict{String, String}
    laplacianSchemes::Dict{String, String}
    interpolationSchemes::Dict{String, String}
    snGradSchemes::Dict{String, String}
    fluxRequired::Dict{String, String}
end

# Default constructor with sensible defaults
function FvSchemes(;
    ddtSchemes=Dict("default" => "Euler"),
    gradSchemes=Dict("default" => "Gauss linear"),
    divSchemes=Dict("default" => "none", "div(phi,U)" => "bounded Gauss upwind"),
    laplacianSchemes=Dict("default" => "Gauss linear corrected"),
    interpolationSchemes=Dict("default" => "linear"),
    snGradSchemes=Dict("default" => "corrected"),
    fluxRequired=Dict("default" => "no", "p" => "")
)
    return FvSchemes(
        ddtSchemes, gradSchemes, divSchemes, laplacianSchemes,
        interpolationSchemes, snGradSchemes, fluxRequired
    )
end

struct FvSolution <: OpenFOAMDict
    solvers::Dict{String, Dict{String, Any}}
    SIMPLE::Dict{String, Any}
    PISO::Dict{String, Any}
    PIMPLE::Dict{String, Any}
    relaxationFactors::Dict{String, Dict{String, Float64}}
end

# Default constructor with sensible defaults
function FvSolution(;
    solvers=Dict(
        "p" => Dict("solver" => "GAMG", "tolerance" => 1e-6, "relTol" => 0.01),
        "U" => Dict("solver" => "smoothSolver", "tolerance" => 1e-6, "relTol" => 0.01)
    ),
    SIMPLE=Dict("nNonOrthogonalCorrectors" => 0, "consistent" => true),
    PISO=Dict("nCorrectors" => 2, "nNonOrthogonalCorrectors" => 0),
    PIMPLE=Dict("nOuterCorrectors" => 1, "nCorrectors" => 2, "nNonOrthogonalCorrectors" => 0),
    relaxationFactors=Dict(
        "fields" => Dict("p" => 0.3),
        "equations" => Dict("U" => 0.7)
    )
)
    return FvSolution(solvers, SIMPLE, PISO, PIMPLE, relaxationFactors)
end

struct TransportProperties <: OpenFOAMDict
    transportModel::String
    nu::Float64  # kinematic viscosity
    rho::Float64 # density
    Pr::Float64  # Prandtl number
    Prt::Float64 # Turbulent Prandtl number
    Cp::Float64  # Specific heat capacity
    # Additional properties for non-Newtonian fluids
    properties::Dict{String, Any}
end

# Default constructor with sensible defaults for Newtonian fluid
function TransportProperties(;
    transportModel="Newtonian",
    nu=1e-5,
    rho=1.0,
    Pr=0.7,
    Prt=0.85,
    Cp=1000.0,
    properties=Dict{String, Any}()
)
    return TransportProperties(transportModel, nu, rho, Pr, Prt, Cp, properties)
end

struct TurbulenceProperties <: OpenFOAMDict
    simulationType::String
    RAS::Dict{String, Any}
    LES::Dict{String, Any}
end

# Default constructor with sensible defaults
function TurbulenceProperties(;
    simulationType="RAS",
    RAS=Dict("model" => "kEpsilon", "turbulence" => "on", "printCoeffs" => "on"),
    LES=Dict("model" => "Smagorinsky", "turbulence" => "on", "printCoeffs" => "on")
)
    return TurbulenceProperties(simulationType, RAS, LES)
end

struct BoundaryCondition
    type::String
    subtype::String
    value::Any
    parameters::Dict{String, Any}
end

# Default constructor for boundary conditions
function BoundaryCondition(;
    type="fixedValue",
    subtype="",
    value=0.0,
    parameters=Dict{String, Any}()
)
    return BoundaryCondition(type, subtype, value, parameters)
end

# Boundary conditions for a field on all patches
struct BoundaryConditions
    field_name::String
    conditions::Dict{String, BoundaryCondition}
end

"""
    import_openfoam_case(case_dir::String)

Import a complete OpenFOAM case into JuliaFOAM.

# Arguments
- `case_dir`: Path to the OpenFOAM case directory

# Returns
- `Tuple`: Mesh, fields, boundary conditions, solver settings, and physical properties
"""
function import_openfoam_case(case_dir::String)
    if !isdir(case_dir)
        error("Case directory not found: $case_dir")
    end
    
    println("Importing OpenFOAM case from $case_dir")
    
    # 1. Read mesh
    mesh_dir = joinpath(case_dir, "constant", "polyMesh")
    if !isdir(mesh_dir)
        error("Mesh directory not found: $mesh_dir")
    end
    
    println("Reading mesh from $mesh_dir")
    mesh = read_openfoam_mesh(mesh_dir)
    
    # 2. Read case configuration
    println("Reading case configuration")
    control_dict = read_control_dict(joinpath(case_dir, "system", "controlDict"))
    fv_schemes = read_fv_schemes(joinpath(case_dir, "system", "fvSchemes"))
    fv_solution = read_fv_solution(joinpath(case_dir, "system", "fvSolution"))
    
    # 3. Read physical properties
    println("Reading physical properties")
    transport_props_file = joinpath(case_dir, "constant", "transportProperties")
    transport_properties = isfile(transport_props_file) ? 
        read_transport_properties(transport_props_file) : 
        TransportProperties()
    
    turbulence_props_file = joinpath(case_dir, "constant", "turbulenceProperties")
    turbulence_properties = isfile(turbulence_props_file) ? 
        read_turbulence_properties(turbulence_props_file) : 
        TurbulenceProperties()
    
    # 4. Find latest time directory
    time_dirs = filter(isdir, readdir(case_dir, join=true))
    time_dirs = filter(d -> occursin(r"^\d+(\.\d+)?$", basename(d)), time_dirs)
    
    if isempty(time_dirs)
        time_dir = joinpath(case_dir, "0")
        if !isdir(time_dir)
            error("No time directories found in $case_dir")
        end
    else
        # Sort by time value
        sort!(time_dirs, by=d -> parse(Float64, basename(d)))
        time_dir = time_dirs[end]
    end
    
    println("Reading fields from $time_dir")
    
    # 5. Read fields and boundary conditions
    fields = Dict{String,Any}()
    boundary_conditions = Dict{String,BoundaryConditions}()
    
    # Common field names to check for
    field_names = ["U", "p", "k", "epsilon", "omega", "T", "alpha.water", "alpha.air"]
    
    for field_name in field_names
        field_file = joinpath(time_dir, field_name)
        if isfile(field_file)
            println("Reading $field_name field from $field_file")
            field_type = field_name == "U" ? SVector{3,Float64} : Float64
            fields[field_name] = read_field(field_type, field_file, mesh)
            boundary_conditions[field_name] = read_boundary_conditions(field_file, field_name)
        end
    end
    
    # 6. Check for custom fields
    all_files = readdir(time_dir)
    for file in all_files
        if !in(file, field_names) && !startswith(file, ".") && isfile(joinpath(time_dir, file))
            println("Reading custom field $file")
            # Try to determine if vector or scalar
            is_vector = false
            open(joinpath(time_dir, file), "r") do f
                for line in eachline(f)
                    if occursin("type", line) && occursin("vector", line)
                        is_vector = true
                        break
                    end
                end
            end
            
            field_type = is_vector ? SVector{3,Float64} : Float64
            fields[file] = read_field(field_type, joinpath(time_dir, file), mesh)
            boundary_conditions[file] = read_boundary_conditions(joinpath(time_dir, file), file)
        end
    end
    
    # 7. Collect all settings
    solver_settings = Dict{String, Any}(
        "controlDict" => control_dict,
        "fvSchemes" => fv_schemes,
        "fvSolution" => fv_solution
    )
    
    physical_properties = Dict{String, Any}(
        "transportProperties" => transport_properties,
        "turbulenceProperties" => turbulence_properties
    )
    
    return mesh, fields, boundary_conditions, solver_settings, physical_properties
end

"""
    export_to_openfoam(case_dir::String, mesh::Mesh, fields::Dict, boundary_conditions::Dict, 
                      solver_settings::Dict, physical_properties::Dict, time::Union{Float64,String}="0")

Export a complete JuliaFOAM case to OpenFOAM format.

# Arguments
- `case_dir`: Path to the output OpenFOAM case directory
- `mesh`: The mesh
- `fields`: Dictionary of fields
- `boundary_conditions`: Dictionary of boundary conditions
- `solver_settings`: Dictionary of solver settings
- `physical_properties`: Dictionary of physical properties
- `time`: Time value or directory name
"""
function export_to_openfoam(case_dir::String, mesh::Mesh, fields::Dict, boundary_conditions::Dict, 
                          solver_settings::Dict, physical_properties::Dict, time::Union{Float64,String}="0")
    # 1. Create directory structure
    mkpath(joinpath(case_dir, string(time)))
    mkpath(joinpath(case_dir, "constant", "polyMesh"))
    mkpath(joinpath(case_dir, "system"))
    
    # 2. Write mesh
    println("Writing mesh to $(joinpath(case_dir, "constant", "polyMesh"))")
    write_openfoam_mesh(joinpath(case_dir, "constant", "polyMesh"), mesh)
    
    # 3. Write solver settings
    println("Writing solver settings")
    if haskey(solver_settings, "controlDict")
        write_control_dict(joinpath(case_dir, "system", "controlDict"), solver_settings["controlDict"])
    end
    
    if haskey(solver_settings, "fvSchemes")
        write_fv_schemes(joinpath(case_dir, "system", "fvSchemes"), solver_settings["fvSchemes"])
    end
    
    if haskey(solver_settings, "fvSolution")
        write_fv_solution(joinpath(case_dir, "system", "fvSolution"), solver_settings["fvSolution"])
    end
    
    # 4. Write physical properties
    println("Writing physical properties")
    if haskey(physical_properties, "transportProperties")
        write_transport_properties(
            joinpath(case_dir, "constant", "transportProperties"), 
            physical_properties["transportProperties"]
        )
    end
    
    if haskey(physical_properties, "turbulenceProperties")
        write_turbulence_properties(
            joinpath(case_dir, "constant", "turbulenceProperties"), 
            physical_properties["turbulenceProperties"]
        )
    end
    
    # 5. Write fields and boundary conditions
    println("Writing fields to $(joinpath(case_dir, string(time)))")
    for (field_name, field) in fields
        write_field_with_boundary_conditions(
            joinpath(case_dir, string(time)), 
            field_name, 
            field, 
            get(boundary_conditions, field_name, nothing),
            mesh
        )
    end
    
    println("Exported to OpenFOAM format in $case_dir")
end

"""
    read_field(::Type{T}, file_path::String, mesh::Mesh) where T

Read a field from an OpenFOAM file.

# Arguments
- `T`: Type of the field (Float64 for scalar, SVector{3,Float64} for vector)
- `file_path`: Path to the field file
- `mesh`: The mesh

# Returns
- Field values for internal cells
"""
function read_field(::Type{T}, file_path::String, mesh::Mesh) where T
    if !isfile(file_path)
        error("Field file $file_path does not exist.")
    end
    
    # Initialize field with zeros
    n_cells = length(mesh.cells)
    field = zeros(T, n_cells)
    
    # Parse OpenFOAM field file
    open(file_path, "r") do file
        # Skip header until internalField
        line = ""
        while !eof(file)
            line = readline(file)
            if occursin("internalField", line)
                break
            end
        end
        
        # Check if uniform or nonuniform
        if occursin("uniform", line)
            # Parse uniform value
            if T <: Number
                # Scalar field
                value_str = match(r"uniform\s+([\-\+\d\.eE]+)", line).captures[1]
                uniform_value = parse(Float64, value_str)
                fill!(field, uniform_value)
            else
                # Vector field
                value_match = match(r"uniform\s+\(([\-\+\d\.eE]+)\s+([\-\+\d\.eE]+)\s+([\-\+\d\.eE]+)\)", line)
                if value_match !== nothing
                    x = parse(Float64, value_match.captures[1])
                    y = parse(Float64, value_match.captures[2])
                    z = parse(Float64, value_match.captures[3])
                    uniform_value = SVector{3,Float64}(x, y, z)
                    fill!(field, uniform_value)
                end
            end
        else
            # Parse nonuniform values
            # Skip to the beginning of the data
            while !eof(file)
                line = readline(file)
                if occursin("(", line)
                    break
                end
            end
            
            # Read values
            for i in 1:n_cells
                if eof(file)
                    error("Unexpected end of file while reading field values")
                end
                
                line = readline(file)
                if T <: Number
                    # Scalar field
                    field[i] = parse(Float64, strip(line))
                else
                    # Vector field
                    # Remove parentheses and split by whitespace
                    value_str = replace(line, "(" => "")
                    value_str = replace(value_str, ")" => "")
                    components = split(strip(value_str))
                    
                    if length(components) >= 3
                        x = parse(Float64, components[1])
                        y = parse(Float64, components[2])
                        z = parse(Float64, components[3])
                        field[i] = SVector{3,Float64}(x, y, z)
                    end
                end
            end
        end
    end
    
    return field
end

"""
    read_boundary_conditions(file_path::String, field_name::String)

Read boundary conditions for a field from an OpenFOAM file.

# Arguments
- `file_path`: Path to the field file
- `field_name`: Name of the field

# Returns
- `BoundaryConditions`: Boundary conditions for the field
"""
function read_boundary_conditions(file_path::String, field_name::String)
    if !isfile(file_path)
        error("Field file $file_path does not exist.")
    end
    
    conditions = Dict{String, BoundaryCondition}()
    
    open(file_path, "r") do file
        # Skip to boundaryField section
        in_boundary_field = false
        current_patch = ""
        current_type = ""
        current_subtype = ""
        current_value = nothing
        current_params = Dict{String, Any}()
        
        while !eof(file)
            line = readline(file)
            line = strip(line)
            
            if occursin("boundaryField", line)
                in_boundary_field = true
                continue
            end
            
            if !in_boundary_field
                continue
            end
            
            # End of boundaryField section
            if line == "}" && current_patch == ""
                break
            end
            
            # New patch
            if current_patch == "" && !occursin("{", line) && !occursin("}", line) && length(line) > 0
                current_patch = line
                continue
            end
            
            # Start of patch definition
            if current_patch != "" && line == "{"
                continue
            end
            
            # End of patch definition
            if current_patch != "" && line == "}"
                # Save the boundary condition
                conditions[current_patch] = BoundaryCondition(
                    type=current_type,
                    subtype=current_subtype,
                    value=current_value,
                    parameters=current_params
                )
                
                # Reset for next patch
                current_patch = ""
                current_type = ""
                current_subtype = ""
                current_value = nothing
                current_params = Dict{String, Any}()
                continue
            end
            
            # Parse boundary condition properties
            if current_patch != "" && occursin("type", line)
                type_match = match(r"type\s+([\w]+)", line)
                if type_match !== nothing
                    current_type = type_match.captures[1]
                end
                continue
            end
            
            # Parse value for fixedValue type
            if current_patch != "" && current_type == "fixedValue" && occursin("value", line)
                if occursin("uniform", line)
                    # Vector or scalar uniform value
                    if occursin("(", line)
                        # Vector
                        value_match = match(r"value\s+uniform\s+\(([\-\+\d\.eE]+)\s+([\-\+\d\.eE]+)\s+([\-\+\d\.eE]+)\)", line)
                        if value_match !== nothing
                            x = parse(Float64, value_match.captures[1])
                            y = parse(Float64, value_match.captures[2])
                            z = parse(Float64, value_match.captures[3])
                            current_value = SVector{3,Float64}(x, y, z)
                        end
                    else
                        # Scalar
                        value_match = match(r"value\s+uniform\s+([\-\+\d\.eE]+)", line)
                        if value_match !== nothing
                            current_value = parse(Float64, value_match.captures[1])
                        end
                    end
                end
                continue
            end
            
            # Parse other parameters
            param_match = match(r"(\w+)\s+([\w\.\-\+\d]+)", line)
            if param_match !== nothing && current_patch != ""
                param_name = param_match.captures[1]
                param_value = param_match.captures[2]
                
                # Try to convert to appropriate type
                try
                    # Try as number first
                    current_params[param_name] = parse(Float64, param_value)
                catch
                    # Otherwise keep as string
                    current_params[param_name] = param_value
                end
            end
        end
    end
    
    return BoundaryConditions(field_name, conditions)
end

"""
    write_field_with_boundary_conditions(time_dir::String, field_name::String, field::Vector, 
                                       boundary_conditions::Union{BoundaryConditions, Nothing}, mesh::Mesh)

Write a field with boundary conditions to an OpenFOAM file.

# Arguments
- `time_dir`: Path to the time directory
- `field_name`: Name of the field
- `field`: Field values
- `boundary_conditions`: Boundary conditions for the field
- `mesh`: The mesh
"""
function write_field_with_boundary_conditions(time_dir::String, field_name::String, field::Vector, 
                                           boundary_conditions::Union{BoundaryConditions, Nothing}, mesh::Mesh)
    # Determine field type (scalar or vector)
    is_vector = eltype(field) <: SVector
    
    open(joinpath(time_dir, field_name), "w") do file
        # Write header
        write(file, """
        /*--------------------------------*- C++ -*----------------------------------*\\n
  =========                 |
  \\\\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox
   \\\\    /   O peration     | Website:  https://openfoam.org
    \\\\  /    A nd           | Version:  v2006
     \\\\/     M anipulation  |
\\*---------------------------------------------------------------------------*/
        """)
        
        # Write FoamFile header
        write(file, """
FoamFile
{
    version     2.0;
    format      ascii;
    class       $(is_vector ? "volVectorField" : "volScalarField");
    location    "$time_dir";
    object      $field_name;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

        """)
        
        # Write dimensions (assume dimensionless for simplicity)
        write(file, "dimensions      [0 0 0 0 0 0 0];

")
        
        # Write internal field
        write(file, "internalField   nonuniform List<$(is_vector ? "vector" : "scalar")>\n")
        write(file, "$(length(field))\n(\n")
        
        for value in field
            if is_vector
                write(file, "($(value[1]) $(value[2]) $(value[3]))\n")
            else
                write(file, "$value\n")
            end
        end
        
        write(file, ");

")
        
        # Write boundary field
        write(file, "boundaryField\n{\n")
        
        if boundary_conditions !== nothing
            for (patch_name, condition) in boundary_conditions.conditions
                write(file, "    $patch_name\n    {\n")
                write(file, "        type            $(condition.type);\n")
                
                if !isempty(condition.subtype)
                    write(file, "        $(condition.subtype);\n")
                end
                
                if condition.type == "fixedValue" && condition.value !== nothing
                    if is_vector && condition.value isa SVector
                        write(file, "        value           uniform ($(condition.value[1]) $(condition.value[2]) $(condition.value[3]));\n")
                    elseif !is_vector && condition.value isa Number
                        write(file, "        value           uniform $(condition.value);\n")
                    end
                end
                
                # Write additional parameters
                for (param_name, param_value) in condition.parameters
                    write(file, "        $param_name        $param_value;\n")
                end
                
                write(file, "    }\n")
            end
        else
            # Default boundary conditions if none provided
            for patch in mesh.boundaries
                write(file, "    $(patch.name)\n    {\n")
                if patch.type == "wall"
                    if is_vector
                        write(file, "        type            noSlip;\n")
                    else
                        write(file, "        type            zeroGradient;\n")
                    end
                elseif patch.type == "inlet"
                    if is_vector
                        write(file, "        type            fixedValue;\n")
                        write(file, "        value           uniform (0 0 0);\n")
                    else
                        write(file, "        type            fixedValue;\n")
                        write(file, "        value           uniform 0;\n")
                    end
                elseif patch.type == "outlet"
                    write(file, "        type            zeroGradient;\n")
                else
                    write(file, "        type            zeroGradient;\n")
                end
                write(file, "    }\n")
            end
        end
        
        write(file, "}\n")
        
        # Write footer
        write(file, "
// ************************************************************************* //")
    end

"""
    read_fv_solution(file_path::String)

Read the fvSolution file from an OpenFOAM case.

# Arguments
- `file_path`: Path to the fvSolution file

# Returns
- `FvSolution`: The finite volume solution settings
"""
function read_fv_solution(file_path::String)
    if !isfile(file_path)
        @warn "fvSolution file $file_path does not exist. Using defaults."
        return FvSolution()
    end
    
    # Initialize with empty dictionaries
    solvers = Dict{String, Dict{String, Any}}()
    SIMPLE = Dict{String, Any}()
    PISO = Dict{String, Any}()
    PIMPLE = Dict{String, Any}()
    relaxationFactors = Dict{String, Dict{String, Float64}}()
    
    # Parse the file
    open(file_path, "r") do file
        current_section = ""
        current_solver = ""
        in_relaxation_factors = false
        current_relaxation_type = ""
        
        for line in eachline(file)
            line = strip(line)
            
            # Skip comments and empty lines
            if startswith(line, "//") || startswith(line, "/*") || isempty(line)
                continue
            end
            
            # Check for section headers
            if occursin("solvers", line) && occursin("{", line)
                current_section = "solvers"
                continue
            elseif occursin("SIMPLE", line) && occursin("{", line)
                current_section = "SIMPLE"
                continue
            elseif occursin("PISO", line) && occursin("{", line)
                current_section = "PISO"
                continue
            elseif occursin("PIMPLE", line) && occursin("{", line)
                current_section = "PIMPLE"
                continue
            elseif occursin("relaxationFactors", line) && occursin("{", line)
                current_section = "relaxationFactors"
                in_relaxation_factors = true
                continue
            end
            
            # End of section
            if line == "}" && !in_relaxation_factors
                current_section = ""
                current_solver = ""
                continue
            elseif line == "}" && in_relaxation_factors && !isempty(current_relaxation_type)
                current_relaxation_type = ""
                continue
            elseif line == "}" && in_relaxation_factors && isempty(current_relaxation_type)
                in_relaxation_factors = false
                current_section = ""
                continue
            end
            
            # Process entries within sections
            if current_section == "solvers"
                if !occursin("{", line) && !occursin("}", line) && isempty(current_solver) && !isempty(line)
                    current_solver = line
                    solvers[current_solver] = Dict{String, Any}()
                    continue
                end
                
                if !isempty(current_solver) && occursin(";", line)
                    parts = split(replace(line, ";" => ""), " ", keepempty=false)
                    
                    if length(parts) >= 2
                        key = parts[1]
                        value = join(parts[2:end], " ")
                        
                        # Try to convert to appropriate type
                        try
                            # Try as number first
                            solvers[current_solver][key] = parse(Float64, value)
                        catch
                            # Otherwise keep as string
                            solvers[current_solver][key] = value
                        end
                    end
                end
            elseif current_section == "SIMPLE" && occursin(";", line)
                parts = split(replace(line, ";" => ""), " ", keepempty=false)
                
                if length(parts) >= 2
                    key = parts[1]
                    value = join(parts[2:end], " ")
                    
                    # Try to convert to appropriate type
                    try
                        # Try as number first
                        SIMPLE[key] = parse(Float64, value)
                    catch
                        # Try as boolean
                        if value == "true" || value == "yes" || value == "on"
                            SIMPLE[key] = true
                        elseif value == "false" || value == "no" || value == "off"
                            SIMPLE[key] = false
                        else
                            # Otherwise keep as string
                            SIMPLE[key] = value
                        end
                    end
                end
            elseif current_section == "PISO" && occursin(";", line)
                parts = split(replace(line, ";" => ""), " ", keepempty=false)
                
                if length(parts) >= 2
                    key = parts[1]
                    value = join(parts[2:end], " ")
                    
                    # Try to convert to appropriate type
                    try
                        # Try as number first
                        PISO[key] = parse(Int, value)
                    catch
                        try
                            PISO[key] = parse(Float64, value)
                        catch
                            # Otherwise keep as string
                            PISO[key] = value
                        end
                    end
                end
            elseif current_section == "PIMPLE" && occursin(";", line)
                parts = split(replace(line, ";" => ""), " ", keepempty=false)
                
                if length(parts) >= 2
                    key = parts[1]
                    value = join(parts[2:end], " ")
                    
                    # Try to convert to appropriate type
                    try
                        # Try as number first
                        PIMPLE[key] = parse(Int, value)
                    catch
                        try
                            PIMPLE[key] = parse(Float64, value)
                        catch
                            # Otherwise keep as string
                            PIMPLE[key] = value
                        end
                    end
                end
            elseif current_section == "relaxationFactors"
                if occursin("fields", line) && occursin("{", line)
                    current_relaxation_type = "fields"
                    relaxationFactors["fields"] = Dict{String, Float64}()
                    continue
                elseif occursin("equations", line) && occursin("{", line)
                    current_relaxation_type = "equations"
                    relaxationFactors["equations"] = Dict{String, Float64}()
                    continue
                end
                
                if !isempty(current_relaxation_type) && occursin(";", line)
                    parts = split(replace(line, ";" => ""), " ", keepempty=false)
                    
                    if length(parts) >= 2
                        key = parts[1]
                        value = parse(Float64, parts[2])
                        relaxationFactors[current_relaxation_type][key] = value
                    end
                end
            end
        end
    end
    
    # Set defaults if empty
    if isempty(solvers)
        solvers["p"] = Dict("solver" => "GAMG", "tolerance" => 1e-6, "relTol" => 0.01)
        solvers["U"] = Dict("solver" => "smoothSolver", "tolerance" => 1e-6, "relTol" => 0.01)
    end
    
    if isempty(SIMPLE)
        SIMPLE["nNonOrthogonalCorrectors"] = 0
        SIMPLE["consistent"] = true
    end
    
    if isempty(PISO)
        PISO["nCorrectors"] = 2
        PISO["nNonOrthogonalCorrectors"] = 0
    end
    
    if isempty(PIMPLE)
        PIMPLE["nOuterCorrectors"] = 1
        PIMPLE["nCorrectors"] = 2
        PIMPLE["nNonOrthogonalCorrectors"] = 0
    end
    
    if isempty(relaxationFactors)
        relaxationFactors["fields"] = Dict("p" => 0.3)
        relaxationFactors["equations"] = Dict("U" => 0.7)
    end
    
    return FvSolution(solvers, SIMPLE, PISO, PIMPLE, relaxationFactors)
end

"""
    write_fv_solution(file_path::String, fv_solution::FvSolution)

Write the fvSolution file for an OpenFOAM case.

# Arguments
- `file_path`: Path to the output fvSolution file
- `fv_solution`: The finite volume solution settings
"""
function write_fv_solution(file_path::String, fv_solution::FvSolution)
    open(file_path, "w") do file
        # Write header
        write(file, """
/*--------------------------------*- C++ -*----------------------------------*\\n
  =========                 |
  \\\\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox
   \\\\    /   O peration     | Website:  https://openfoam.org
    \\\\  /    A nd           | Version:  v2006
     \\\\/     M anipulation  |
\\*---------------------------------------------------------------------------*/
        """)
        
        # Write FoamFile header
        write(file, """
FoamFile
{
    version     2.0;
    format      ascii;
    class       dictionary;
    location    "system";
    object      fvSolution;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

        """)
        
        # Write solvers
        write(file, "solvers\n{\n")
        for (field, settings) in fv_solution.solvers
            write(file, "    $field\n    {\n")
            for (key, value) in settings
                if value isa String
                    write(file, "        $key        $value;\n")
                else
                    write(file, "        $key        $value;\n")
                end
            end
            write(file, "    }\n")
        end
        write(file, "}\n\n")
        
        # Write SIMPLE
        if !isempty(fv_solution.SIMPLE)
            write(file, "SIMPLE\n{\n")
            for (key, value) in fv_solution.SIMPLE
                if value isa Bool
                    write(file, "    $key        $(value ? "true" : "false");\n")
                else
                    write(file, "    $key        $value;\n")
                end
            end
            write(file, "}\n\n")
        end
        
        # Write PISO
        if !isempty(fv_solution.PISO)
            write(file, "PISO\n{\n")
            for (key, value) in fv_solution.PISO
                write(file, "    $key        $value;\n")
            end
            write(file, "}\n\n")
        end
        
        # Write PIMPLE
        if !isempty(fv_solution.PIMPLE)
            write(file, "PIMPLE\n{\n")
            for (key, value) in fv_solution.PIMPLE
                write(file, "    $key        $value;\n")
            end
            write(file, "}\n\n")
        end
        
        # Write relaxationFactors
        if !isempty(fv_solution.relaxationFactors)
            write(file, "relaxationFactors\n{\n")
            
            if haskey(fv_solution.relaxationFactors, "fields")
                write(file, "    fields\n    {\n")
                for (field, factor) in fv_solution.relaxationFactors["fields"]
                    write(file, "        $field        $factor;\n")
                end
                write(file, "    }\n")
            end
            
            if haskey(fv_solution.relaxationFactors, "equations")
                write(file, "    equations\n    {\n")
                for (field, factor) in fv_solution.relaxationFactors["equations"]
                    write(file, "        $field        $factor;\n")
                end
                write(file, "    }\n")
            end
            
            write(file, "}\n\n")
        end
        
        # Write footer
        write(file, "// ************************************************************************* //")
    end
end

"""
    read_transport_properties(file_path::String)

Read the transportProperties file from an OpenFOAM case.

# Arguments
- `file_path`: Path to the transportProperties file

# Returns
- `TransportProperties`: The transport properties
"""
function read_transport_properties(file_path::String)
    if !isfile(file_path)
        @warn "transportProperties file $file_path does not exist. Using defaults."
        return TransportProperties()
    end
    
    # Initialize with defaults
    transportModel = "Newtonian"
    nu = 1e-5  # kinematic viscosity
    rho = 1.0  # density
    Pr = 0.7   # Prandtl number
    Prt = 0.85 # Turbulent Prandtl number
    Cp = 1000.0 # Specific heat capacity
    properties = Dict{String, Any}()
    
    # Parse the file
    open(file_path, "r") do file
        for line in eachline(file)
            line = strip(line)
            
            # Skip comments and empty lines
            if startswith(line, "//") || startswith(line, "/*") || isempty(line)
                continue
            end
            
            # Parse transportModel
            if occursin("transportModel", line) && occursin(";", line)
                m = match(r"transportModel\s+([\w]+)", line)
                if m !== nothing
                    transportModel = m.captures[1]
                end
            end
            
            # Parse nu (kinematic viscosity)
            if occursin("nu", line) && occursin(";", line) && !occursin("[", line)
                m = match(r"nu\s+([\d\.eE\-\+]+)", line)
                if m !== nothing
                    nu = parse(Float64, m.captures[1])
                end
            end
            
            # Parse rho (density)
            if occursin("rho", line) && occursin(";", line) && !occursin("[", line)
                m = match(r"rho\s+([\d\.eE\-\+]+)", line)
                if m !== nothing
                    rho = parse(Float64, m.captures[1])
                end
            end
            
            # Parse Pr (Prandtl number)
            if occursin("Pr", line) && occursin(";", line) && !occursin("[", line)
                m = match(r"Pr\s+([\d\.eE\-\+]+)", line)
                if m !== nothing
                    Pr = parse(Float64, m.captures[1])
                end
            end
            
            # Parse Prt (Turbulent Prandtl number)
            if occursin("Prt", line) && occursin(";", line) && !occursin("[", line)
                m = match(r"Prt\s+([\d\.eE\-\+]+)", line)
                if m !== nothing
                    Prt = parse(Float64, m.captures[1])
                end
            end
            
            # Parse Cp (Specific heat capacity)
            if occursin("Cp", line) && occursin(";", line) && !occursin("[", line)
                m = match(r"Cp\s+([\d\.eE\-\+]+)", line)
                if m !== nothing
                    Cp = parse(Float64, m.captures[1])
                end
            end
            
            # Parse other properties
            m = match(r"([\w]+)\s+([\d\.eE\-\+]+)", line)
            if m !== nothing && m.captures[1] ∉ ["transportModel", "nu", "rho", "Pr", "Prt", "Cp"]
                properties[m.captures[1]] = parse(Float64, m.captures[2])
            end
        end
    end
    
    return TransportProperties(transportModel, nu, rho, Pr, Prt, Cp, properties)
end

"""
    write_transport_properties(file_path::String, transport_properties::TransportProperties)

Write the transportProperties file for an OpenFOAM case.

# Arguments
- `file_path`: Path to the output transportProperties file
- `transport_properties`: The transport properties
"""
function write_transport_properties(file_path::String, transport_properties::TransportProperties)
    open(file_path, "w") do file
        # Write header
        write(file, """
/*--------------------------------*- C++ -*----------------------------------*\\n
  =========                 |
  \\\\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox
   \\\\    /   O peration     | Website:  https://openfoam.org
    \\\\  /    A nd           | Version:  v2006
     \\\\/     M anipulation  |
\\*---------------------------------------------------------------------------*/
        """)
        
        # Write FoamFile header
        write(file, """
FoamFile
{
    version     2.0;
    format      ascii;
    class       dictionary;
    location    "constant";
    object      transportProperties;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

        """)
        
        # Write transport model
        write(file, "transportModel  $(transport_properties.transportModel);\n\n")
        
        # Write standard properties
        write(file, "// Kinematic viscosity\n")
        write(file, "nu              $(transport_properties.nu);\n\n")
        
        write(file, "// Density\n")
        write(file, "rho             $(transport_properties.rho);\n\n")
        
        write(file, "// Prandtl number\n")
        write(file, "Pr              $(transport_properties.Pr);\n\n")
        
        write(file, "// Turbulent Prandtl number\n")
        write(file, "Prt             $(transport_properties.Prt);\n\n")
        
        write(file, "// Specific heat capacity\n")
        write(file, "Cp              $(transport_properties.Cp);\n\n")
        
        # Write additional properties
        if !isempty(transport_properties.properties)
            write(file, "// Additional properties\n")
            for (name, value) in transport_properties.properties
                write(file, "$name            $value;\n")
            end
            write(file, "\n")
        end
        
        # Write footer
        write(file, "// ************************************************************************* //")
    end
end

end

end # module EnhancedOpenFOAMIO
