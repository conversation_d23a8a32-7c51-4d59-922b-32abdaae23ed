# =========================================================================
# OptimizedMesh Module - High-performance mesh data structures
# =========================================================================

"""
    OptimizedMesh

This module provides high-performance mesh data structures optimized for:
- Memory efficiency and cache-friendly data layout
- Large-scale simulations
- Parallel processing
- OpenFOAM compatibility

The module supports multiple mesh representations that can be selected
based on performance requirements and available memory.
"""
module OptimizedMesh

using StaticArrays
using SparseArrays
using LinearAlgebra
using LoopVectorization

# =========================================================================
# Exports
# =========================================================================

export Mesh, CacheOptimizedMesh, OptimizedMeshData
export Face, Cell, BoundaryCondition
export create_optimized_mesh, convert_to_cache_optimized
export calculate_geometric_properties!, update_connectivity!
export get_cell_neighbors, get_face_cells

# =========================================================================
# Basic Data Types
# =========================================================================

"""
    Face

Basic face data structure.
"""
mutable struct Face
    vertices::Vector{Int32}
    owner::Int32
    neighbor::Int32  # -1 for boundary faces
    center::SVector{3,Float64}
    area::Float64
    normal::SVector{3,Float64}
end

"""
    Cell

Basic cell data structure.
"""
mutable struct Cell
    vertices::Vector{Int32}
    faces::Vector{Int32}
    center::SVector{3,Float64}
    volume::Float64
end

"""
    BoundaryCondition

Basic boundary condition data structure.
"""
mutable struct BoundaryCondition
    type::String
    value::Any
    patch_name::String
end

# =========================================================================
# Standard Mesh Structure
# =========================================================================

"""
    Mesh

Standard mesh data structure compatible with JuliaFOAM.

# Fields
- `vertices::Vector{SVector{3,Float64}}`: Mesh vertices
- `faces::Vector{Face}`: Mesh faces
- `cells::Vector{Cell}`: Mesh cells
- `boundary_patches::Dict{String,Vector{Int32}}`: Boundary patches
- `boundary_conditions::Dict{String,BoundaryCondition}`: Boundary conditions
"""
mutable struct Mesh
    vertices::Vector{SVector{3,Float64}}
    faces::Vector{Face}
    cells::Vector{Cell}
    boundary_patches::Dict{String,Vector{Int32}}
    boundary_conditions::Dict{String,BoundaryCondition}
    
    function Mesh()
        new(
            Vector{SVector{3,Float64}}(),
            Vector{Face}(),
            Vector{Cell}(),
            Dict{String,Vector{Int32}}(),
            Dict{String,BoundaryCondition}()
        )
    end
end

# =========================================================================
# Cache-Optimized Mesh Structure
# =========================================================================

"""
    CacheOptimizedMesh

Cache-friendly mesh data structure with structure-of-arrays layout
for improved memory access patterns and vectorization.

# Fields
- `n_cells::Int`: Number of cells
- `n_faces::Int`: Number of faces
- `n_vertices::Int`: Number of vertices
- `cell_centers::Matrix{Float64}`: Cell centers (3 x n_cells)
- `cell_volumes::Vector{Float64}`: Cell volumes
- `face_centers::Matrix{Float64}`: Face centers (3 x n_faces)
- `face_areas::Vector{Float64}`: Face areas
- `face_normals::Matrix{Float64}`: Face normals (3 x n_faces)
- `face_owners::Vector{Int32}`: Owner cell for each face
- `face_neighbors::Vector{Int32}`: Neighbor cell for each face (-1 for boundary)
- `cell_faces::Vector{Vector{Int32}}`: Faces for each cell
- `vertex_coords::Matrix{Float64}`: Vertex coordinates (3 x n_vertices)
- `boundary_patches::Dict{String,Vector{Int32}}`: Boundary patches
- `connectivity_matrix::SparseMatrixCSC{Bool,Int32}`: Cell-cell connectivity
"""
struct CacheOptimizedMesh
    # Dimensions
    n_cells::Int
    n_faces::Int
    n_vertices::Int
    
    # Cell data (structure-of-arrays)
    cell_centers::Matrix{Float64}  # 3 x n_cells
    cell_volumes::Vector{Float64}  # n_cells
    
    # Face data (structure-of-arrays)
    face_centers::Matrix{Float64}  # 3 x n_faces
    face_areas::Vector{Float64}    # n_faces
    face_normals::Matrix{Float64}  # 3 x n_faces
    face_owners::Vector{Int32}     # n_faces
    face_neighbors::Vector{Int32}  # n_faces (-1 for boundary)
    
    # Vertex data
    vertex_coords::Matrix{Float64} # 3 x n_vertices
    
    # Connectivity
    cell_faces::Vector{Vector{Int32}}  # Variable size per cell
    
    # Boundary information
    boundary_patches::Dict{String,Vector{Int32}}
    
    # Precomputed sparse connectivity matrix
    connectivity_matrix::SparseMatrixCSC{Bool,Int32}
end

"""
    OptimizedMeshData

Additional optimized data structures for specific operations.
"""
struct OptimizedMeshData
    # Fast neighbor lookup
    cell_neighbors::Vector{Vector{Int32}}
    
    # Face-cell mapping
    face_to_cells::Matrix{Int32}  # 2 x n_faces (owner, neighbor)
    
    # Geometric data for fast access
    face_deltas::Matrix{Float64}  # 3 x n_faces (center-to-center vectors)
    face_weights::Vector{Float64} # n_faces (interpolation weights)
    
    # Boundary face indices
    boundary_face_indices::Vector{Int32}
    internal_face_indices::Vector{Int32}
end

# =========================================================================
# Mesh Creation and Conversion
# =========================================================================

"""
    create_optimized_mesh(vertices::Vector{SVector{3,Float64}}, 
                         faces::Vector{Face}, 
                         cells::Vector{Cell};
                         boundary_patches=Dict{String,Vector{Int32}}())

Create an optimized mesh from basic mesh components.
"""
function create_optimized_mesh(
    vertices::Vector{SVector{3,Float64}}, 
    faces::Vector{Face}, 
    cells::Vector{Cell};
    boundary_patches=Dict{String,Vector{Int32}}()
)
    mesh = Mesh()
    mesh.vertices = vertices
    mesh.faces = faces
    mesh.cells = cells
    mesh.boundary_patches = boundary_patches
    
    # Calculate geometric properties
    calculate_geometric_properties!(mesh)
    
    return mesh
end

"""
    convert_to_cache_optimized(mesh::Mesh)

Convert a standard mesh to cache-optimized format.
"""
function convert_to_cache_optimized(mesh::Mesh)
    n_cells = length(mesh.cells)
    n_faces = length(mesh.faces)
    n_vertices = length(mesh.vertices)
    
    # Preallocate structure-of-arrays
    cell_centers = Matrix{Float64}(undef, 3, n_cells)
    cell_volumes = Vector{Float64}(undef, n_cells)
    
    face_centers = Matrix{Float64}(undef, 3, n_faces)
    face_areas = Vector{Float64}(undef, n_faces)
    face_normals = Matrix{Float64}(undef, 3, n_faces)
    face_owners = Vector{Int32}(undef, n_faces)
    face_neighbors = Vector{Int32}(undef, n_faces)
    
    vertex_coords = Matrix{Float64}(undef, 3, n_vertices)
    
    # Fill cell data
    for (i, cell) in enumerate(mesh.cells)
        cell_centers[:, i] = [cell.center[1], cell.center[2], cell.center[3]]
        cell_volumes[i] = cell.volume
    end
    
    # Fill face data
    cell_faces = Vector{Vector{Int32}}(undef, n_cells)
    for i in 1:n_cells
        cell_faces[i] = mesh.cells[i].faces
    end
    
    for (i, face) in enumerate(mesh.faces)
        face_centers[:, i] = [face.center[1], face.center[2], face.center[3]]
        face_areas[i] = face.area
        face_normals[:, i] = [face.normal[1], face.normal[2], face.normal[3]]
        face_owners[i] = face.owner
        face_neighbors[i] = face.neighbor
    end
    
    # Fill vertex data
    for (i, vertex) in enumerate(mesh.vertices)
        vertex_coords[:, i] = [vertex[1], vertex[2], vertex[3]]
    end
    
    # Build connectivity matrix
    connectivity_matrix = build_connectivity_matrix(n_cells, mesh.faces)
    
    return CacheOptimizedMesh(
        n_cells, n_faces, n_vertices,
        cell_centers, cell_volumes,
        face_centers, face_areas, face_normals,
        face_owners, face_neighbors,
        vertex_coords, cell_faces,
        mesh.boundary_patches,
        connectivity_matrix
    )
end

"""
    build_connectivity_matrix(n_cells::Int, faces::Vector{Face})

Build sparse cell-cell connectivity matrix.
"""
function build_connectivity_matrix(n_cells::Int, faces::Vector{Face})
    I = Int32[]
    J = Int32[]
    
    for face in faces
        if face.neighbor > 0  # Internal face
            push!(I, face.owner)
            push!(J, face.neighbor)
            push!(I, face.neighbor)
            push!(J, face.owner)
        end
    end
    
    # Create boolean sparse matrix
    return sparse(I, J, true, n_cells, n_cells)
end

# =========================================================================
# Geometric Calculations
# =========================================================================

"""
    calculate_geometric_properties!(mesh::Mesh)

Calculate and update geometric properties for all mesh entities.
"""
function calculate_geometric_properties!(mesh::Mesh)
    # Calculate face properties
    for face in mesh.faces
        calculate_face_properties!(face, mesh.vertices)
    end
    
    # Calculate cell properties
    for cell in mesh.cells
        calculate_cell_properties!(cell, mesh.vertices, mesh.faces)
    end
end

"""
    calculate_face_properties!(face::Face, vertices::Vector{SVector{3,Float64}})

Calculate face center, area, and normal.
"""
function calculate_face_properties!(face::Face, vertices::Vector{SVector{3,Float64}})
    n_vertices = length(face.vertices)
    
    if n_vertices < 3
        error("Face must have at least 3 vertices")
    end
    
    # Calculate face center as average of vertices
    center = SVector(0.0, 0.0, 0.0)
    for vertex_id in face.vertices
        center += vertices[vertex_id]
    end
    face.center = center / n_vertices
    
    # Calculate area and normal using triangle fan method
    total_area = 0.0
    total_normal = SVector(0.0, 0.0, 0.0)
    
    v0 = vertices[face.vertices[1]]
    for i in 2:n_vertices-1
        v1 = vertices[face.vertices[i]]
        v2 = vertices[face.vertices[i+1]]
        
        # Triangle area and normal
        edge1 = v1 - v0
        edge2 = v2 - v0
        triangle_normal = cross(edge1, edge2)
        triangle_area = 0.5 * norm(triangle_normal)
        
        total_area += triangle_area
        total_normal += triangle_normal
    end
    
    face.area = total_area
    if norm(total_normal) > 1e-14
        face.normal = normalize(total_normal)
    else
        face.normal = SVector(1.0, 0.0, 0.0)  # Default normal
    end
end

"""
    calculate_cell_properties!(cell::Cell, vertices::Vector{SVector{3,Float64}}, faces::Vector{Face})

Calculate cell center and volume.
"""
function calculate_cell_properties!(cell::Cell, vertices::Vector{SVector{3,Float64}}, faces::Vector{Face})
    # Simple approach: cell center as average of face centers
    center = SVector(0.0, 0.0, 0.0)
    for face_id in cell.faces
        center += faces[face_id].center
    end
    cell.center = center / length(cell.faces)
    
    # Volume calculation using divergence theorem
    # V = (1/3) * Σ(face_area · (face_center - reference_point))
    cell.volume = 0.0
    reference_point = cell.center
    
    for face_id in cell.faces
        face = faces[face_id]
        # Vector from reference point to face center
        r_vec = face.center - reference_point
        # Volume contribution from this face
        volume_contrib = dot(face.area * face.normal, r_vec) / 3.0
        cell.volume += volume_contrib
    end
    
    # Ensure positive volume
    cell.volume = abs(cell.volume)
end

# =========================================================================
# Connectivity Operations
# =========================================================================

"""
    get_cell_neighbors(mesh::CacheOptimizedMesh, cell_id::Int32)

Get neighbor cells for a given cell using the cached connectivity matrix.
"""
function get_cell_neighbors(mesh::CacheOptimizedMesh, cell_id::Int32)
    neighbors = Int32[]
    
    # Use sparse matrix for fast neighbor lookup
    for j in mesh.connectivity_matrix.colptr[cell_id]:mesh.connectivity_matrix.colptr[cell_id+1]-1
        neighbor_id = mesh.connectivity_matrix.rowval[j]
        if neighbor_id != cell_id  # Exclude self
            push!(neighbors, neighbor_id)
        end
    end
    
    return neighbors
end

"""
    get_face_cells(mesh::CacheOptimizedMesh, face_id::Int32)

Get owner and neighbor cells for a given face.
"""
function get_face_cells(mesh::CacheOptimizedMesh, face_id::Int32)
    owner = mesh.face_owners[face_id]
    neighbor = mesh.face_neighbors[face_id]
    
    if neighbor > 0
        return (owner, neighbor)
    else
        return (owner, -1)  # Boundary face
    end
end

"""
    update_connectivity!(mesh::Mesh)

Update connectivity information after mesh modification.
"""
function update_connectivity!(mesh::Mesh)
    # Rebuild connectivity
    calculate_geometric_properties!(mesh)
end

# =========================================================================
# Cache-Optimized Operations
# =========================================================================

"""
    vectorized_face_flux_calculation!(fluxes::Vector{Float64}, 
                                     mesh::CacheOptimizedMesh,
                                     face_values::Vector{Float64})

Compute face fluxes using vectorized operations on cache-optimized data.
"""
function vectorized_face_flux_calculation!(
    fluxes::Vector{Float64}, 
    mesh::CacheOptimizedMesh,
    face_values::Vector{Float64}
)
    @inbounds @simd for i in 1:mesh.n_faces
        fluxes[i] = face_values[i] * mesh.face_areas[i]
    end
end

"""
    parallel_cell_loop!(result::Vector{Float64}, 
                        mesh::CacheOptimizedMesh, 
                        operation::Function)

Perform parallel loop over cells using cache-optimized data.
"""
function parallel_cell_loop!(
    result::Vector{Float64}, 
    mesh::CacheOptimizedMesh, 
    operation::Function
)
    Threads.@threads for cell_id in 1:mesh.n_cells
        result[cell_id] = operation(cell_id, mesh)
    end
end

end # module OptimizedMesh