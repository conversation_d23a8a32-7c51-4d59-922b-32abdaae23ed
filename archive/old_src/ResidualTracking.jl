"""
    Residual tracking functionality for JuliaFOAM solvers.
    
    This module provides tools for tracking, storing, and visualizing
    residuals during the solution process.
"""
module ResidualTracking

using LinearAlgebra
using Printf
using Plots

export track_residuals, save_residuals, plot_residuals
export ResidualHistory

"""
    ResidualHistory

Structure to store residual history for different fields.
"""
struct ResidualHistory
    fields::Vector{String}
    values::Dict{String, Vector{Float64}}
    iterations::Vector{Int}
end

"""
    ResidualHistory()

Create an empty residual history.
"""
function ResidualHistory()
    return ResidualHistory(String[], Dict{String, Vector{Float64}}(), Int[])
end

"""
    track_residuals(history, field_name, residual, iteration)

Add a residual value to the history.

# Arguments
- `history`: ResidualHistory object
- `field_name`: Name of the field (e.g., "u", "p")
- `residual`: Residual value
- `iteration`: Iteration number
"""
function track_residuals(history::ResidualHistory, field_name::String, residual::Float64, iteration::Int)
    # Add field to the list if not already present
    if !(field_name in history.fields)
        push!(history.fields, field_name)
        history.values[field_name] = Float64[]
    end
    
    # Add residual value
    push!(history.values[field_name], residual)
    
    # Add iteration number if it's a new iteration
    if isempty(history.iterations) || history.iterations[end] != iteration
        push!(history.iterations, iteration)
    end
    
    return history
end

"""
    save_residuals(history, filename)

Save residual history to a CSV file.

# Arguments
- `history`: ResidualHistory object
- `filename`: Output filename
"""
function save_residuals(history::ResidualHistory, filename::String)
    open(filename, "w") do io
        # Write header
        write(io, "Iteration")
        for field in history.fields
            write(io, ",", field)
        end
        write(io, "\n")
        
        # Write data
        for i in 1:length(history.iterations)
            write(io, "$(history.iterations[i])")
            for field in history.fields
                if i <= length(history.values[field])
                    write(io, ",$(history.values[field][i])")
                else
                    write(io, ",")
                end
            end
            write(io, "\n")
        end
    end
    
    println("Residuals saved to $filename")
end

"""
    plot_residuals(history, filename; log_scale=true)

Plot residual history and save to a file.

# Arguments
- `history`: ResidualHistory object
- `filename`: Output filename
- `log_scale`: Whether to use logarithmic scale for y-axis
"""
function plot_residuals(history::ResidualHistory, filename::String; log_scale::Bool=true)
    p = plot(
        title = "Convergence History",
        xlabel = "Iteration",
        ylabel = "Residual",
        legend = :topright,
        yaxis = log_scale ? :log10 : :identity,
        grid = true,
        size = (800, 600),
        dpi = 300
    )
    
    # Plot each field's residuals
    for field in history.fields
        iterations = 1:length(history.values[field])
        plot!(p, iterations, history.values[field], label=field)
    end
    
    # Save plot
    savefig(p, filename)
    println("Residual plot saved to $filename")
    
    return p
end

"""
    print_residuals(history, iteration; frequency=1)

Print residuals to the console.

# Arguments
- `history`: ResidualHistory object
- `iteration`: Current iteration
- `frequency`: Print frequency (print every N iterations)
"""
function print_residuals(history::ResidualHistory, iteration::Int; frequency::Int=1)
    if iteration % frequency != 0
        return
    end
    
    # Print header on first iteration
    if iteration == 1
        print("Iteration")
        for field in history.fields
            print(@sprintf(" | %10s", field))
        end
        println()
        println("-" ^ (11 + 14 * length(history.fields)))
    end
    
    # Print residuals
    print(@sprintf("%9d", iteration))
    for field in history.fields
        if !isempty(history.values[field])
            print(@sprintf(" | %10.6e", history.values[field][end]))
        else
            print(@sprintf(" | %10s", "N/A"))
        end
    end
    println()
end

end # module
