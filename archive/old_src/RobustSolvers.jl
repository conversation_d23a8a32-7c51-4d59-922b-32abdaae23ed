"""
    RobustSolvers.jl

This module provides robust solvers for JuliaFOAM that can handle numerical instabilities.
It combines robust linear solvers with diagnostic tools to ensure reliable convergence.
"""
module RobustSolvers

using LinearAlgebra
using SparseArrays
using Printf
using Base.Threads

# Import from JuliaFOAM
import ..JuliaFOAM: Mesh, FluidProperties, Field, apply_boundary_conditions!
import ..JuliaFOAM: build_momentum_matrix, build_pressure_equation

# Import RobustLinearSolvers directly from its module path
include("../optimizations/RobustLinearSolvers.jl")
using .RobustLinearSolvers

# Import other required modules
# Note: EnhancedPreconditioners will be available in parent scope
# using ..Tools.LinearSystemDiagnostics

# Re-export the symbols we want to make available
export RobustLinearSolvers

# Export our solver interfaces
export RobustSimpleSolverConfig, solve_robust_simple
export RobustPisoSolverConfig, solve_robust_piso
export create_robust_preconditioner

"""
    RobustSimpleSolverConfig

Configuration for the robust SIMPLE solver.
"""
struct RobustSimpleSolverConfig
    max_iterations::Int
    tolerance::Float64
    relaxation_factors::Dict{String,Float64}
    track_residuals::Bool
    residual_output_interval::Int
    solver_type::Symbol  # :cg, :bicgstab, or :gmres
    preconditioner_type::Symbol  # :diagonal, :ilu, :ilut, :amg, :block_ilu, :spai, :schur
    problem_type::Symbol  # :general, :convection_dominated, :saddle_point
    auto_fix_issues::Bool
    diagnostic_level::Symbol  # :none, :basic, or :detailed
end

function RobustSimpleSolverConfig(;
    max_iterations::Int=1000,
    tolerance::Float64=1e-6,
    relaxation_factors::Dict{String,Float64}=Dict("U"=>0.7,"p"=>0.3),
    track_residuals::Bool=true,
    residual_output_interval::Int=10,
    solver_type::Symbol=:bicgstab,
    preconditioner_type::Symbol=:diagonal,
    problem_type::Symbol=:general,
    auto_fix_issues::Bool=true,
    diagnostic_level::Symbol=:basic
)
    return RobustSimpleSolverConfig(
        max_iterations,
        tolerance,
        relaxation_factors,
        track_residuals,
        residual_output_interval,
        solver_type,
        preconditioner_type,
        problem_type,
        auto_fix_issues,
        diagnostic_level
    )
end

"""
    create_robust_preconditioner(A::SparseMatrixCSC{Float64, Int}, precond_type::Symbol; problem_type::Symbol=:general)

Create a preconditioner function based on the specified type.

# Arguments
- `A`: System matrix
- `precond_type`: Type of preconditioner (:diagonal, :ilu, :ilut, :amg, :block_ilu, :spai, :schur)
- `problem_type`: Type of problem (:general, :convection_dominated, :saddle_point)

# Returns
- `Function`: Preconditioner function
"""
function create_robust_preconditioner(A::SparseMatrixCSC{Float64, Int}, precond_type::Symbol; problem_type::Symbol=:general)
    if precond_type == :diagonal
        # Diagonal preconditioner
        n = size(A, 1)
        d_inv = zeros(Float64, n)

        for i in 1:n
            if abs(A[i, i]) > 1e-15
                d_inv[i] = 1.0 / A[i, i]
            else
                d_inv[i] = 1.0
            end
        end

        return (z, r) -> z .= d_inv .* r
    elseif precond_type == :ilu
        # ILU preconditioner
        L, U = AdvancedPreconditioners.ilu_preconditioner(A)
        return (z, r) -> AdvancedPreconditioners.apply_ilu_preconditioner!(z, r, L, U)
    elseif precond_type == :ilut
        # ILUT preconditioner with threshold dropping
        droptol = problem_type == :convection_dominated ? 1e-5 : 1e-4
        lfil = problem_type == :convection_dominated ? 20 : 10
        L, U = EnhancedPreconditioners.ilut_preconditioner(A, droptol, lfil)
        return (z, r) -> EnhancedPreconditioners.apply_ilut_preconditioner!(z, r, L, U)
    elseif precond_type == :amg
        # AMG preconditioner
        hierarchy = AdvancedPreconditioners.amg_preconditioner(A)
        return (z, r) -> AdvancedPreconditioners.apply_amg_preconditioner!(z, r, hierarchy)
    elseif precond_type == :block_jacobi
        # Block Jacobi preconditioner
        block_inverses = AdvancedPreconditioners.block_jacobi_preconditioner(A, 4)
        return (z, r) -> AdvancedPreconditioners.apply_block_jacobi_preconditioner!(z, r, block_inverses, 4)
    elseif precond_type == :block_ilu
        # Block ILU preconditioner
        block_size = 3  # Assuming 3D velocity components
        L, U = EnhancedPreconditioners.block_ilu_preconditioner(A, block_size)
        return (z, r) -> EnhancedPreconditioners.apply_block_ilu_preconditioner!(z, r, L, U, block_size)
    elseif precond_type == :spai
        # Sparse Approximate Inverse preconditioner
        M = EnhancedPreconditioners.spai_adaptive_preconditioner(A)
        return (z, r) -> EnhancedPreconditioners.apply_spai_adaptive_preconditioner!(z, r, M)
    elseif precond_type == :schur
        # Schur complement preconditioner for saddle point problems
        if problem_type == :saddle_point
            n = size(A, 1)
            n_velocity = div(2*n, 3)  # Assuming 2/3 of unknowns are velocity
            precond_data = EnhancedPreconditioners.schur_complement_preconditioner(A, n_velocity)
            return (z, r) -> EnhancedPreconditioners.apply_schur_complement_preconditioner!(z, r, precond_data)
        else
            @warn "Schur complement preconditioner is only applicable to saddle point problems, falling back to ILU"
            L, U = AdvancedPreconditioners.ilu_preconditioner(A)
            return (z, r) -> AdvancedPreconditioners.apply_ilu_preconditioner!(z, r, L, U)
        end
    else
        # Default: identity preconditioner
        return (z, r) -> copyto!(z, r)
    end
end

"""
    solve_robust_simple(
        mesh::Mesh,
        fields::Dict{String,Field},
        boundary_conditions::Dict{String,Any},
        config::RobustSimpleSolverConfig
    )

Robust SIMPLE solver with automatic diagnosis and fixing of numerical issues.

# Arguments
- `mesh`: Computational mesh
- `fields`: Dictionary of fields (must include "U" and "p")
- `boundary_conditions`: Dictionary of boundary conditions
- `config`: Solver configuration

# Returns
- `Tuple{Dict{String,Field}, Dict{String,Vector{Float64}}, Dict}`: Updated fields, residual history, and diagnostics
"""
function solve_robust_simple(
    mesh::Mesh,
    fields::Dict{String,Field},
    boundary_conditions::Dict{String,Any},
    config::RobustSimpleSolverConfig
)
    # Extract fields
    U = fields["U"]
    p = fields["p"]

    # Extract fluid properties
    props = boundary_conditions["properties"]

    # Initialize residual storage
    residuals = Dict("U"=>Float64[], "p"=>Float64[])

    # Initialize diagnostics
    diagnostics = Dict{Symbol, Any}()
    diagnostics[:solver_type] = config.solver_type
    diagnostics[:preconditioner_type] = config.preconditioner_type
    diagnostics[:auto_fix_issues] = config.auto_fix_issues
    diagnostics[:issues_detected] = false
    diagnostics[:issues_fixed] = false
    diagnostics[:linear_system_diagnostics] = Dict{Symbol, Any}()

    # Main SIMPLE loop
    for iter in 1:config.max_iterations
        # 1. Momentum predictor: assemble and solve
        A_U, b_U = build_momentum_matrix(U, p, mesh, props, config.relaxation_factors["U"])

        # Diagnose momentum equation
        if config.diagnostic_level != :none
            momentum_diagnostics = LinearSystemDiagnostics.diagnose_linear_system(A_U, b_U)

            if !isempty(get(momentum_diagnostics, :issues, String[]))
                diagnostics[:issues_detected] = true

                if config.diagnostic_level == :detailed
                    diagnostics[:linear_system_diagnostics][:momentum] = momentum_diagnostics
                end

                if config.auto_fix_issues
                    A_U, b_U = LinearSystemDiagnostics.fix_linear_system(A_U, b_U, momentum_diagnostics)
                    diagnostics[:issues_fixed] = true
                end
            end
        end

        # Create preconditioner
        precond = create_robust_preconditioner(A_U, config.preconditioner_type, problem_type=config.problem_type)

        # Solve momentum equation
        U_vec = zeros(Float64, length(U.values))

        # Flatten U if vector field
        if eltype(U.values) <: Number
            U_vec .= U.values
        else
            # Take first component for vector fields
            for i in 1:length(U.values)
                U_vec[i] = U.values[i][1]
            end
        end

        # Solve momentum equation with robust solver
        if config.solver_type == :cg
            iter_U, res_U = RobustLinearSolvers.robust_cg_solve!(
                A_U, b_U, U_vec, precond, config.tolerance, config.max_iterations,
                verbose=config.track_residuals
            )
        elseif config.solver_type == :bicgstab
            iter_U, res_U = RobustLinearSolvers.robust_bicgstab_solve!(
                A_U, b_U, U_vec, precond, config.tolerance, config.max_iterations,
                verbose=config.track_residuals
            )
        else
            # Default to BiCGSTAB
            iter_U, res_U = RobustLinearSolvers.robust_bicgstab_solve!(
                A_U, b_U, U_vec, precond, config.tolerance, config.max_iterations,
                verbose=config.track_residuals
            )
        end

        # Check for NaN residual
        if isnan(res_U)
            @warn "NaN residual detected in momentum equation at iteration $iter"
            diagnostics[:nan_residual_detected] = true

            if config.auto_fix_issues
                # Try again with more robust settings
                iter_U, res_U = RobustLinearSolvers.robust_bicgstab_solve!(
                    A_U, b_U, U_vec, (z, r) -> z .= r, config.tolerance, config.max_iterations,
                    verbose=true
                )

                if isnan(res_U)
                    @error "Unable to recover from NaN residual in momentum equation"
                    diagnostics[:recovery_failed] = true
                    break
                else
                    diagnostics[:recovery_succeeded] = true
                end
            else
                break
            end
        end

        # Assign back to U field
        if eltype(U.values) <: Number
            U.values .= U_vec
        else
            # For vector fields, update only the first component
            for i in 1:length(U.values)
                U.values[i] = SVector(U_vec[i], U.values[i][2], U.values[i][3])
            end
        end

        # Apply boundary conditions
        apply_boundary_conditions!(U, mesh)

        # 2. Pressure equation: assemble and solve
        A_p, b_p = build_pressure_equation(U, p, mesh, props, config.relaxation_factors["p"])

        # Diagnose pressure equation
        if config.diagnostic_level != :none
            pressure_diagnostics = LinearSystemDiagnostics.diagnose_linear_system(A_p, b_p)

            if !isempty(get(pressure_diagnostics, :issues, String[]))
                diagnostics[:issues_detected] = true

                if config.diagnostic_level == :detailed
                    diagnostics[:linear_system_diagnostics][:pressure] = pressure_diagnostics
                end

                if config.auto_fix_issues
                    A_p, b_p = LinearSystemDiagnostics.fix_linear_system(A_p, b_p, pressure_diagnostics)
                    diagnostics[:issues_fixed] = true
                end
            end
        end

        # Create preconditioner
        precond = create_robust_preconditioner(A_p, config.preconditioner_type, problem_type=config.problem_type)

        # Solve pressure equation
        p_vec = copy(p.values)

        # Solve pressure equation with robust solver
        if config.solver_type == :cg
            iter_p, res_p = RobustLinearSolvers.robust_cg_solve!(
                A_p, b_p, p_vec, precond, config.tolerance, config.max_iterations,
                verbose=config.track_residuals
            )
        elseif config.solver_type == :bicgstab
            iter_p, res_p = RobustLinearSolvers.robust_bicgstab_solve!(
                A_p, b_p, p_vec, precond, config.tolerance, config.max_iterations,
                verbose=config.track_residuals
            )
        else
            # Default to BiCGSTAB
            iter_p, res_p = RobustLinearSolvers.robust_bicgstab_solve!(
                A_p, b_p, p_vec, precond, config.tolerance, config.max_iterations,
                verbose=config.track_residuals
            )
        end

        # Check for NaN residual
        if isnan(res_p)
            @warn "NaN residual detected in pressure equation at iteration $iter"
            diagnostics[:nan_residual_detected] = true

            if config.auto_fix_issues
                # Try again with more robust settings
                iter_p, res_p = RobustLinearSolvers.robust_bicgstab_solve!(
                    A_p, b_p, p_vec, (z, r) -> z .= r, config.tolerance, config.max_iterations,
                    verbose=true
                )

                if isnan(res_p)
                    @error "Unable to recover from NaN residual in pressure equation"
                    diagnostics[:recovery_failed] = true
                    break
                else
                    diagnostics[:recovery_succeeded] = true
                end
            else
                break
            end
        end

        # Update pressure field
        p.values .= p_vec

        # Apply boundary conditions
        apply_boundary_conditions!(p, mesh)

        # 3. Velocity correction
        correct_velocity_robust!(U, p, mesh, props)

        # Apply boundary conditions
        apply_boundary_conditions!(U, mesh)

        # 4. Compute residuals
        rU = norm([u[1] for u in U.values])
        rp = norm(p.values)
        push!(residuals["U"], rU)
        push!(residuals["p"], rp)

        # 5. Track and print
        if config.track_residuals && (iter == 1 || iter % config.residual_output_interval == 0)
            @printf("Iter %d: U_res=%.3e, p_res=%.3e\n", iter, rU, rp)
        end

        # 6. Convergence check
        if max(rU, rp) < config.tolerance
            if config.track_residuals
                @printf("Converged in %d iterations\n", iter)
            end
            break
        end
    end

    # Analyze convergence history
    if config.diagnostic_level != :none
        U_convergence = LinearSystemDiagnostics.analyze_convergence_history(residuals["U"])
        p_convergence = LinearSystemDiagnostics.analyze_convergence_history(residuals["p"])

        diagnostics[:convergence_analysis] = Dict(
            "U" => U_convergence,
            "p" => p_convergence
        )
    end

    return fields, residuals, diagnostics
end

"""
    correct_velocity_robust!(U::Field, p::Field, mesh::Mesh, props::FluidProperties)

Correct the velocity field using the pressure gradient with robust error checking.

# Arguments
- `U`: Velocity field (will be modified)
- `p`: Pressure field
- `mesh`: Computational mesh
- `props`: Fluid properties
"""
function correct_velocity_robust!(U::Field, p::Field, mesh::Mesh, props::FluidProperties)
    n_cells = length(mesh.cells)

    # Calculate pressure gradient
    grad_p = calculate_pressure_gradient_robust(p, mesh)

    # Correct velocity field
    for i in 1:n_cells
        # Get current velocity
        u_old = U.values[i]

        # Calculate correction
        correction = grad_p[i] / props.density

        # Check for NaN or Inf
        if any(isnan.(correction)) || any(isinf.(correction))
            # Skip this correction
            continue
        end

        # Apply correction
        U.values[i] = SVector(
            u_old[1] - correction[1],
            u_old[2] - correction[2],
            u_old[3] - correction[3]
        )
    end
end

"""
    calculate_pressure_gradient_robust(p::Field, mesh::Mesh)

Calculate the pressure gradient with robust error checking.

# Arguments
- `p`: Pressure field
- `mesh`: Computational mesh

# Returns
- `Vector{SVector{3, Float64}}`: Pressure gradient at cell centers
"""
function calculate_pressure_gradient_robust(p::Field, mesh::Mesh)
    n_cells = length(mesh.cells)
    n_faces = length(mesh.faces)

    # Allocate gradient field
    grad_p = [SVector{3, Float64}(0.0, 0.0, 0.0) for _ in 1:n_cells]

    # Calculate gradient using Gauss theorem
    for face_idx in 1:n_faces
        face = mesh.faces[face_idx]
        owner = face.owner
        neighbour = face.neighbour

        # Face normal and area
        normal = face.area

        if neighbour > 0  # Internal face
            # Owner and neighbor cell values
            p_owner = p.values[owner]
            p_neighbor = p.values[neighbour]

            # Check for NaN or Inf
            if isnan(p_owner) || isinf(p_owner) || isnan(p_neighbor) || isinf(p_neighbor)
                continue
            end

            # Face value (linear interpolation)
            p_face = 0.5 * (p_owner + p_neighbor)

            # Accumulate contribution to gradient
            grad_p[owner] += p_face * normal
            grad_p[neighbour] -= p_face * normal
        else  # Boundary face
            # Owner cell value
            p_owner = p.values[owner]

            # Check for NaN or Inf
            if isnan(p_owner) || isinf(p_owner)
                continue
            end

            # Boundary value (use owner value for simplicity)
            p_boundary = p_owner

            # Accumulate contribution to gradient
            grad_p[owner] += p_boundary * normal
        end
    end

    # Normalize by cell volumes
    for i in 1:n_cells
        cell = mesh.cells[i]

        # Check for zero volume
        if cell.volume > 1e-15
            grad_p[i] /= cell.volume
        end
    end

    return grad_p
end

end # module RobustSolvers
