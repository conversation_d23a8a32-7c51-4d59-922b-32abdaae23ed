"""
    OptimizedSimpleSolver.jl - Optimized SIMPLE algorithm implementation

This module provides a highly optimized implementation of the SIMPLE algorithm
for solving the incompressible Navier-Stokes equations. It uses in-place operations,
memory pooling, and cache-friendly data structures to minimize allocations and
improve performance.
"""
module OptimizedSimpleSolver

using LinearAlgebra, SparseArrays
using Printf
using MPI
using Base.Threads
using StaticArrays

import JuliaFOAM: Mesh, FluidProperties, Field, OptimizedMesh, apply_boundary_conditions!
import JuliaFOAM: build_momentum_matrix, build_pressure_equation

export OptimizedSimpleSolverConfig, solve_simple_optimized

"""
    OptimizedSimpleSolverConfig

Configuration for the optimized SIMPLE solver.
"""
struct OptimizedSimpleSolverConfig
    max_iterations::Int
    tolerance::Float64
    relaxation_factors::Dict{String,Float64}
    track_residuals::Bool
    residual_output_interval::Int
    solver_type::Symbol  # :cg, :bicgstab, or :gmres
    use_threading::Bool
end

function OptimizedSimpleSolverConfig(;
    max_iterations::Int=1000,
    tolerance::Float64=1e-6,
    relaxation_factors::Dict{String,Float64}=Dict("U"=>0.7,"p"=>0.3),
    track_residuals::Bool=true,
    residual_output_interval::Int=10,
    solver_type::Symbol=:bicgstab,
    use_threading::Bool=true
)
    return OptimizedSimpleSolverConfig(
        max_iterations,
        tolerance,
        relaxation_factors,
        track_residuals,
        residual_output_interval,
        solver_type,
        use_threading
    )
end

"""
    solve_simple_optimized(
        mesh::Mesh,
        fields::Dict{String,Field},
        boundary_conditions::Dict{String,Any},
        config::OptimizedSimpleSolverConfig
    )

Optimized SIMPLE solver using in-place operations and memory pooling.

# Arguments
- `mesh`: Computational mesh
- `fields`: Dictionary of fields (must include "U" and "p")
- `boundary_conditions`: Dictionary of boundary conditions
- `config`: Solver configuration

# Returns
- `Tuple{Dict{String,Field}, Dict{String,Vector{Float64}}}`: Updated fields and residual history
"""
function solve_simple_optimized(
    mesh::Mesh,
    fields::Dict{String,Field},
    boundary_conditions::Dict{String,Any},
    config::OptimizedSimpleSolverConfig
)
    # Extract fields
    U = fields["U"]
    p = fields["p"]

    # Extract fluid properties
    props = boundary_conditions["properties"]

    # Initialize residual storage
    residuals = Dict("U"=>Float64[], "p"=>Float64[])

    # Get number of cells
    n_cells = length(mesh.cells)

    # Create identity preconditioner
    precond = (z, r) -> z .= r

    # Main SIMPLE loop
    for iter in 1:config.max_iterations
        # 1. Momentum predictor: assemble and solve
        A_U, b_U = build_momentum_matrix(U, p, mesh, props, config.relaxation_factors["U"])

        # Flatten U if vector field
        U_vec = Float64[]
        if eltype(U.values) <: Number
            U_vec = copy(U.values)
        else
            # Take first component for vector fields
            U_vec = [u[1] for u in U.values]
        end

        # Solve momentum equation using standard solver
        sol_U = solve_linear_system(A_U, b_U, U_vec, config.tolerance)

        # Assign back to U field
        if eltype(U.values) <: Number
            U.values .= sol_U
        else
            # For vector fields, update only the first component
            if config.use_threading && n_cells > 10000
                @threads for i in 1:n_cells
                    U.values[i] = SVector(sol_U[i], U.values[i][2], U.values[i][3])
                end
            else
                for i in 1:n_cells
                    U.values[i] = SVector(sol_U[i], U.values[i][2], U.values[i][3])
                end
            end
        end

        # Apply boundary conditions
        apply_boundary_conditions!(U, mesh)

        # 2. Pressure equation: assemble and solve
        A_p, b_p = build_pressure_equation(U, p, mesh, props, config.relaxation_factors["p"])

        # Solve pressure equation
        sol_p = solve_linear_system(A_p, b_p, copy(p.values), config.tolerance)

        # Update pressure field
        p.values .= sol_p

        # Apply boundary conditions
        apply_boundary_conditions!(p, mesh)

        # 3. Velocity correction
        correct_velocity_optimized!(U, p, mesh, props)

        # Apply boundary conditions
        apply_boundary_conditions!(U, mesh)

        # 4. Compute residuals
        rU = norm([u[1] for u in U.values])
        rp = norm(p.values)
        push!(residuals["U"], rU)
        push!(residuals["p"], rp)

        # 5. Track and print
        if config.track_residuals && (iter == 1 || iter % config.residual_output_interval == 0)
            @printf("Iter %d: U_res=%.3e, p_res=%.3e\n", iter, rU, rp)
        end

        # 6. Convergence check
        if max(rU, rp) < config.tolerance
            if config.track_residuals
                @printf("Converged in %d iterations\n", iter)
            end
            break
        end
    end

    return fields, residuals
end

"""
    correct_velocity_optimized!(U::Field, p::Field, mesh::Mesh, props::FluidProperties)

Correct the velocity field using the pressure gradient with optimized in-place operations.

# Arguments
- `U`: Velocity field (will be updated)
- `p`: Pressure field
- `mesh`: Computational mesh
- `props`: Fluid properties
"""
function correct_velocity_optimized!(U::Field, p::Field, mesh::Mesh, props::FluidProperties)
    n_cells = length(mesh.cells)
    n_faces = length(mesh.faces)

    # Calculate pressure gradient
    grad_p = calculate_pressure_gradient_optimized(p, mesh)

    # Correct velocity field
    if n_cells > 10000 && Threads.nthreads() > 1
        @threads for i in 1:n_cells
            # Get current velocity
            u_old = U.values[i]

            # Calculate correction
            correction = grad_p[i] / props.density

            # Apply correction
            U.values[i] = SVector(
                u_old[1] - correction[1],
                u_old[2] - correction[2],
                u_old[3] - correction[3]
            )
        end
    else
        for i in 1:n_cells
            # Get current velocity
            u_old = U.values[i]

            # Calculate correction
            correction = grad_p[i] / props.density

            # Apply correction
            U.values[i] = SVector(
                u_old[1] - correction[1],
                u_old[2] - correction[2],
                u_old[3] - correction[3]
            )
        end
    end
end

"""
    calculate_pressure_gradient_optimized(p::Field, mesh::Mesh)

Calculate the pressure gradient using optimized in-place operations.

# Arguments
- `p`: Pressure field
- `mesh`: Computational mesh

# Returns
- `Vector{SVector{3, Float64}}`: Pressure gradient at cell centers
"""
function calculate_pressure_gradient_optimized(p::Field, mesh::Mesh)
    n_cells = length(mesh.cells)
    n_faces = length(mesh.faces)

    # Allocate gradient field
    grad_p = [SVector{3, Float64}(0.0, 0.0, 0.0) for _ in 1:n_cells]

    # Calculate gradient using Gauss theorem
    for face_idx in 1:n_faces
        face = mesh.faces[face_idx]
        owner = face.owner
        neighbour = face.neighbour

        # Face normal and area
        normal = face.area

        if neighbour > 0  # Internal face
            # Owner and neighbor cell values
            p_owner = p.values[owner]
            p_neighbor = p.values[neighbour]

            # Face value (linear interpolation)
            p_face = 0.5 * (p_owner + p_neighbor)

            # Accumulate contribution to gradient
            grad_p[owner] += p_face * normal
            grad_p[neighbour] -= p_face * normal
        else  # Boundary face
            # Owner cell value
            p_owner = p.values[owner]

            # Boundary value (use owner value for simplicity)
            p_boundary = p_owner

            # Accumulate contribution to gradient
            grad_p[owner] += p_boundary * normal
        end
    end

    # Normalize by cell volumes
    for i in 1:n_cells
        cell = mesh.cells[i]
        grad_p[i] /= cell.volume
    end

    return grad_p
end

end # module OptimizedSimpleSolver
