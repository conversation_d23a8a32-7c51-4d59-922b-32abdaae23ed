module OptimizedCore

"""
    OptimizedCore

A module that provides optimized implementations of core JuliaFOAM functionality.
This module integrates optimized mesh generation, matrix assembly, and solver components
to significantly improve performance while maintaining accuracy.
"""

# Export optimized components
export OptimizedMesh, assemble_laplacian, assemble_convection_diffusion, 
       parallel_solve_cg, parallel_solve_bicgstab

# Include optimized components
include("mesh/OptimizedMeshGenerator.jl")
include("matrix/OptimizedMatrixAssembly.jl")
include("solvers/ParallelSolver.jl")

# Re-export components from submodules
using .OptimizedMeshGenerator: MeshData
using .OptimizedMatrixAssembly: assemble_laplacian, assemble_convection_diffusion, assemble_pressure_correction
using .ParallelSolver: parallel_solve_cg, parallel_solve_bicgstab

"""
    OptimizedMesh

A type alias for the MeshData struct from OptimizedMeshGenerator.
Provides a clean interface for using the optimized mesh data structure.
"""
const OptimizedMesh = MeshData

"""
    create_mesh(nx, ny, nz, length, height, depth=1.0)

Create an optimized mesh with the specified dimensions.
This is a convenience function that calls generate_structured_mesh from OptimizedMeshGenerator.

# Arguments
- `nx::Int`: Number of cells in x-direction
- `ny::Int`: Number of cells in y-direction
- `nz::Int`: Number of cells in z-direction (use 1 for 2D)
- `length::Float64`: Domain length in x-direction
- `height::Float64`: Domain height in y-direction
- `depth::Float64`: Domain depth in z-direction (use 1.0 for 2D)

# Returns
- `OptimizedMesh`: An optimized mesh data structure
"""
function create_mesh(nx::Int, ny::Int, nz::Int, length::Float64, height::Float64, depth::Float64=1.0)
    return OptimizedMeshGenerator.generate_structured_mesh(nx, ny, nz, length, height, depth)
end

"""
    solve_system(A, b; method="cg", tol=1e-6, max_iter=1000)

Solve the linear system Ax = b using the specified method.
This is a convenience function that calls the appropriate solver from ParallelSolver.

# Arguments
- `A`: Coefficient matrix (sparse or dense)
- `b`: Right-hand side vector
- `method`: Solver method ("cg" for Conjugate Gradient, "bicgstab" for BiCGSTAB)
- `tol`: Convergence tolerance
- `max_iter`: Maximum number of iterations

# Returns
- `x`: Solution vector
- `residuals`: Residual history
- `iterations`: Number of iterations performed
"""
function solve_system(A, b; method="cg", tol=1e-6, max_iter=1000)
    if method == "cg"
        return ParallelSolver.parallel_solve_cg(A, b, nothing, tol=tol, max_iter=max_iter)
    elseif method == "bicgstab"
        return ParallelSolver.parallel_solve_bicgstab(A, b, nothing, tol=tol, max_iter=max_iter)
    else
        error("Unknown solver method: $method. Use 'cg' or 'bicgstab'.")
    end
end

end # module
