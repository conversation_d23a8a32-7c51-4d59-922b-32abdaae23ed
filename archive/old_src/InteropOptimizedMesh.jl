"""
    Module for optimized mesh operations with OpenFOAM interoperability.
    
    This module provides performance-optimized mesh operations while maintaining
    full compatibility with OpenFOAM mesh formats and structures.
"""
module InteropOptimizedMesh

using StaticArrays
using SparseArrays
using LinearAlgebra
using Base.Threads

# Import necessary JuliaFOAM modules
using ..JuliaFOAM

export optimize_mesh_access, create_face_connectivity, optimize_boundary_access
export MeshOptimizationData

"""
    MeshOptimizationData

Structure to hold optimized mesh data for faster access while maintaining
full OpenFOAM interoperability.
"""
struct MeshOptimizationData
    # Original mesh reference (for OpenFOAM interoperability)
    original_mesh::JuliaFOAM.Mesh
    
    # Optimized arrays for cell centers
    center_x::Vector{Float64}
    center_y::Vector{Float64}
    center_z::Vector{Float64}
    
    # Optimized array for cell volumes
    volumes::Vector{Float64}
    
    # Face connectivity data
    face_owner::Vector{Int}
    face_neighbor::Vector{Int}
    face_area::Vector{Float64}
    face_normal_x::Vector{Float64}
    face_normal_y::Vector{Float64}
    face_normal_z::Vector{Float64}
    
    # Boundary data
    boundary_faces::Dict{String, Vector{Int}}
    
    # Dimensions
    n_cells::Int
    n_faces::Int
    dimensions::Tuple{Float64, Float64, Float64}
end

"""
    optimize_mesh_access(mesh)

Create optimized data structures for mesh access while maintaining
the original mesh structure for OpenFOAM compatibility.

# Arguments
- `mesh`: The original JuliaFOAM mesh structure

# Returns
- `optimization_data`: An MeshOptimizationData structure with optimized data
"""
function optimize_mesh_access(mesh::JuliaFOAM.Mesh)
    # Extract mesh dimensions
    n_cells = length(mesh.cells)
    
    # Create optimized data structures for cell centers and volumes
    center_x = Vector{Float64}(undef, n_cells)
    center_y = Vector{Float64}(undef, n_cells)
    center_z = Vector{Float64}(undef, n_cells)
    volumes = Vector{Float64}(undef, n_cells)
    
    # Fill optimized arrays
    for (i, cell) in enumerate(mesh.cells)
        center = cell.center
        center_x[i] = center[1]
        center_y[i] = center[2]
        center_z[i] = center[3]
        volumes[i] = cell.volume
    end
    
    # Extract face data
    n_faces = length(mesh.faces)
    face_owner = Vector{Int}(undef, n_faces)
    face_neighbor = Vector{Int}(undef, n_faces)
    face_area = Vector{Float64}(undef, n_faces)
    face_normal_x = Vector{Float64}(undef, n_faces)
    face_normal_y = Vector{Float64}(undef, n_faces)
    face_normal_z = Vector{Float64}(undef, n_faces)
    
    for (i, face) in enumerate(mesh.faces)
        face_owner[i] = face.owner
        face_neighbor[i] = hasfield(typeof(face), :neighbor) ? face.neighbor : -1
        
        # Extract normal and area
        normal = face.normal
        face_normal_x[i] = normal[1]
        face_normal_y[i] = normal[2]
        face_normal_z[i] = normal[3]
        face_area[i] = face.area
    end
    
    # Extract boundary data
    boundary_faces = Dict{String, Vector{Int}}()
    if hasfield(typeof(mesh), :boundaries)
        for (name, boundary) in mesh.boundaries
            boundary_faces[name] = boundary.face_indices
        end
    end
    
    # Extract or estimate dimensions
    dimensions = if hasfield(typeof(mesh), :dimensions)
        mesh.dimensions
    else
        # Estimate dimensions from cell centers
        x_min, x_max = extrema(center_x)
        y_min, y_max = extrema(center_y)
        z_min, z_max = extrema(center_z)
        
        (x_max - x_min, y_max - y_min, z_max - z_min)
    end
    
    # Create and return MeshOptimizationData
    return MeshOptimizationData(
        mesh,
        center_x, center_y, center_z,
        volumes,
        face_owner, face_neighbor,
        face_area, face_normal_x, face_normal_y, face_normal_z,
        boundary_faces,
        n_cells, n_faces, dimensions
    )
end

"""
    create_face_connectivity(mesh)

Extract face connectivity information from the mesh.
This function is included for backward compatibility.

# Arguments
- `mesh`: The original JuliaFOAM mesh structure

# Returns
- `optimization_data`: An MeshOptimizationData structure with optimized data
"""
function create_face_connectivity(mesh::JuliaFOAM.Mesh)
    return optimize_mesh_access(mesh)
end

"""
    optimize_boundary_access(mesh)

Extract boundary information from the mesh.
This function is included for backward compatibility.

# Arguments
- `mesh`: The original JuliaFOAM mesh structure

# Returns
- `optimization_data`: An MeshOptimizationData structure with optimized data
"""
function optimize_boundary_access(mesh::JuliaFOAM.Mesh)
    return optimize_mesh_access(mesh)
end

"""
    get_cell_data(mesh_opt, cell_idx)

Get optimized cell data for a specific cell index.

# Arguments
- `mesh_opt`: MeshOptimizationData structure
- `cell_idx`: Cell index

# Returns
- `cell_data`: Dictionary with cell data
"""
function get_cell_data(mesh_opt::MeshOptimizationData, cell_idx::Int)
    return Dict(
        "center" => SVector(
            mesh_opt.center_x[cell_idx],
            mesh_opt.center_y[cell_idx],
            mesh_opt.center_z[cell_idx]
        ),
        "volume" => mesh_opt.volumes[cell_idx]
    )
end

"""
    get_face_data(mesh_opt, face_idx)

Get optimized face data for a specific face index.

# Arguments
- `mesh_opt`: MeshOptimizationData structure
- `face_idx`: Face index

# Returns
- `face_data`: Dictionary with face data
"""
function get_face_data(mesh_opt::MeshOptimizationData, face_idx::Int)
    return Dict(
        "owner" => mesh_opt.face_owner[face_idx],
        "neighbor" => mesh_opt.face_neighbor[face_idx],
        "area" => mesh_opt.face_area[face_idx],
        "normal" => SVector(
            mesh_opt.face_normal_x[face_idx],
            mesh_opt.face_normal_y[face_idx],
            mesh_opt.face_normal_z[face_idx]
        )
    )
end

"""
    get_boundary_faces(mesh_opt, boundary_name)

Get face indices for a specific boundary.

# Arguments
- `mesh_opt`: MeshOptimizationData structure
- `boundary_name`: Name of the boundary

# Returns
- `face_indices`: Vector of face indices
"""
function get_boundary_faces(mesh_opt::MeshOptimizationData, boundary_name::String)
    return get(mesh_opt.boundary_faces, boundary_name, Int[])
end

"""
    convert_to_standard_mesh(mesh_opt)

Convert optimized mesh data back to standard JuliaFOAM mesh structure.
This ensures interoperability with OpenFOAM.

# Arguments
- `mesh_opt`: MeshOptimizationData structure

# Returns
- `mesh`: Standard JuliaFOAM mesh structure
"""
function convert_to_standard_mesh(mesh_opt::MeshOptimizationData)
    return mesh_opt.original_mesh
end

"""
    get_face_cells(mesh_opt, face_idx)

Get owner and neighbor cells for a specific face.

# Arguments
- `mesh_opt`: MeshOptimizationData structure
- `face_idx`: Face index

# Returns
- `owner`: Owner cell index
- `neighbor`: Neighbor cell index
"""
function get_face_cells(mesh_opt::MeshOptimizationData, face_idx::Int)
    owner = mesh_opt.face_owner[face_idx]
    neighbor = mesh_opt.face_neighbor[face_idx]
    return owner, neighbor
end

"""
    get_cell_faces(mesh, cell_idx)

Get faces connected to a specific cell.
This function ensures compatibility with the original mesh structure.

# Arguments
- `mesh`: The original JuliaFOAM mesh structure
- `cell_idx`: Cell index

# Returns
- `face_indices`: Vector of face indices
"""
function get_cell_faces(mesh::JuliaFOAM.Mesh, cell_idx::Int)
    return mesh.cells[cell_idx].faces
end

end # module
