"""
    HybridCommunication.jl

This module provides a hybrid communication approach that automatically selects
the best communication method based on message size and system characteristics.
It combines the strengths of different communication patterns:
1. Neighborhood collectives for small to medium messages
2. One-sided communication for medium to large messages
3. Computation-communication overlap for all message sizes
"""
module HybridCommunication

using MPI
using StaticArrays
using LinearAlgebra
using SparseArrays
using Base.Threads

# Import types from parent module
import ..JuliaFOAM: Field, Mesh, OptimizedMesh
import ..OptimizedCommunication: MessageAggregationInfo, CommunicationScheduleV2

# Import optimized communication functions
import ..OptimizedCommunication: aggregate_messages!, create_communication_schedule_v2
import ..OptimizedCommunication: neighborhood_collective_exchange_v2!, one_sided_communication_v2!
import ..OptimizedCommunication: compute_with_overlap!

"""
    CommunicationMethod

Enum-like struct to represent different communication methods.
"""
struct CommunicationMethod
    id::Int
    name::String

    # Constructor with predefined methods
    function CommunicationMethod(id::Int)
        names = ["Auto", "Neighborhood Collective", "One-Sided", "Persistent", "Computation-Communication Overlap"]
        if id < 1 || id > length(names)
            id = 1  # Default to Auto
        end
        new(id, names[id])
    end
end

# Define constants for communication methods
const AUTO = CommunicationMethod(1)
const NEIGHBORHOOD_COLLECTIVE = CommunicationMethod(2)
const ONE_SIDED = CommunicationMethod(3)
const PERSISTENT = CommunicationMethod(4)
const OVERLAP = CommunicationMethod(5)

"""
    HybridCommunicationInfo

Structure to store information about hybrid communication.

# Fields
- `method`: The selected communication method
- `message_sizes`: Dictionary of message sizes for each process
- `threshold_collective`: Threshold for using neighborhood collectives
- `threshold_onesided`: Threshold for using one-sided communication
- `aggregation_info`: Information about message aggregation
- `schedule`: Communication schedule
"""
struct HybridCommunicationInfo
    method::CommunicationMethod
    message_sizes::Dict{Int, Int}
    threshold_collective::Int
    threshold_onesided::Int
    aggregation_info::MessageAggregationInfo
    schedule::CommunicationScheduleV2
end

"""
    select_communication_method(mesh::Any, method::CommunicationMethod=AUTO)

Select the best communication method based on message size and system characteristics.

# Arguments
- `mesh`: The optimized mesh
- `method`: The communication method to use (default: AUTO)

# Returns
- `HybridCommunicationInfo`: Information about the selected communication method
"""
function select_communication_method(mesh::Any, method::CommunicationMethod=AUTO)
    # Initialize MPI if not already initialized
    if !MPI.Initialized()
        MPI.Init()
    end

    # Get MPI info
    comm = mesh.comm

    # Skip if single process
    if MPI.Comm_size(comm) == 1
        return HybridCommunicationInfo(
            AUTO,
            Dict{Int, Int}(),
            1024,
            4096,
            aggregate_messages!(mesh),
            create_communication_schedule_v2(mesh)
        )
    end

    # Optimize communication pattern
    optimize_communication_pattern!(mesh)

    # Aggregate messages
    aggregation_info = aggregate_messages!(mesh)

    # Create communication schedule
    schedule = create_communication_schedule_v2(mesh)

    # Calculate message sizes
    message_sizes = Dict{Int, Int}()
    total_size = 0
    max_size = 0

    for (proc, indices) in aggregation_info.aggregated_send_indices
        size = length(indices)
        message_sizes[proc] = size
        total_size += size
        max_size = max(max_size, size)
    end

    # Set thresholds based on system characteristics
    # These thresholds can be tuned based on benchmarks
    threshold_collective = 1024  # 1KB of data (assuming 8 bytes per element)
    threshold_onesided = 4096    # 4KB of data

    # Select method based on message sizes if AUTO is specified
    selected_method = method

    if method.id == AUTO.id
        if max_size <= threshold_collective
            selected_method = NEIGHBORHOOD_COLLECTIVE
        elseif max_size <= threshold_onesided
            selected_method = ONE_SIDED
        else
            selected_method = OVERLAP
        end
    end

    return HybridCommunicationInfo(
        selected_method,
        message_sizes,
        threshold_collective,
        threshold_onesided,
        aggregation_info,
        schedule
    )
end

"""
    optimize_communication_pattern!(mesh::Any)

Optimize the communication pattern for a mesh.

# Arguments
- `mesh`: The optimized mesh
"""
function optimize_communication_pattern!(mesh::Any)
    # Use the optimized communication pattern from OptimizedCommunication
    OptimizedCommunication.optimize_communication_pattern_v2!(mesh)
end

"""
    hybrid_exchange!(field::Vector{T}, mesh::Any, info::HybridCommunicationInfo) where T

Exchange data using the selected communication method.

# Arguments
- `field`: The field to exchange
- `mesh`: The optimized mesh
- `info`: The hybrid communication information

# Returns
- `Nothing`
"""
function hybrid_exchange!(field::Vector{T}, mesh::Any, info::HybridCommunicationInfo) where T
    # Use the selected communication method
    if info.method.id == NEIGHBORHOOD_COLLECTIVE.id
        neighborhood_collective_exchange_v2!(field, mesh)
    elseif info.method.id == ONE_SIDED.id
        one_sided_communication_v2!(field, mesh)
    elseif info.method.id == PERSISTENT.id
        # Create persistent requests
        requests = OptimizedCommunication.create_persistent_requests_v2(field, mesh)

        # Start persistent communication
        OptimizedCommunication.start_persistent_communication_v2!(requests, field, mesh)

        # Complete persistent communication
        OptimizedCommunication.complete_persistent_communication_v2!(requests, field, mesh)
    elseif info.method.id == OVERLAP.id
        # Use computation-communication overlap with a no-op computation
        compute_with_overlap!(field, mesh, (field, indices) -> nothing)
    else
        # Auto: use the best method based on message sizes
        if isempty(info.message_sizes)
            # No messages to exchange
            return
        end

        max_size = maximum(values(info.message_sizes))

        if max_size <= info.threshold_collective
            neighborhood_collective_exchange_v2!(field, mesh)
        elseif max_size <= info.threshold_onesided
            one_sided_communication_v2!(field, mesh)
        else
            compute_with_overlap!(field, mesh, (field, indices) -> nothing)
        end
    end
end

"""
    hybrid_exchange_with_computation!(field::Vector{T}, mesh::Any, info::HybridCommunicationInfo, compute_fn::Function) where T

Exchange data using the selected communication method with computation overlap.

# Arguments
- `field`: The field to exchange
- `mesh`: The optimized mesh
- `info`: The hybrid communication information
- `compute_fn`: Function to compute on cells (takes field and cell indices as arguments)

# Returns
- `Nothing`
"""
function hybrid_exchange_with_computation!(field::Vector{T}, mesh::Any, info::HybridCommunicationInfo, compute_fn::Function) where T
    # Always use computation-communication overlap
    compute_with_overlap!(field, mesh, compute_fn)
end

"""
    adaptive_hybrid_exchange!(field::Vector{T}, mesh::Any) where T

Exchange data using an adaptive hybrid approach that automatically selects the best method.

# Arguments
- `field`: The field to exchange
- `mesh`: The optimized mesh

# Returns
- `Nothing`
"""
function adaptive_hybrid_exchange!(field::Vector{T}, mesh::Any) where T
    # Select communication method
    info = select_communication_method(mesh)

    # Exchange data
    hybrid_exchange!(field, mesh, info)
end

# Export functions
export select_communication_method, optimize_communication_pattern!
export hybrid_exchange!, hybrid_exchange_with_computation!, adaptive_hybrid_exchange!
export AUTO, NEIGHBORHOOD_COLLECTIVE, ONE_SIDED, PERSISTENT, OVERLAP

end # module HybridCommunication
