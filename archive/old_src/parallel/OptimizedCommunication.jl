"""
    OptimizedCommunication.jl

This module provides highly optimized communication patterns for distributed memory parallelism.
Key improvements over the original OptimizedCommunication module:
1. Message aggregation to reduce the number of small messages
2. Better computation-communication overlap
3. Asynchronous progress for MPI operations
4. Reduced synchronization points
"""
module OptimizedCommunication

using MPI
using StaticArrays
using LinearAlgebra
using SparseArrays
using Base.Threads
using ..JuliaFOAM

# Import functions from OptimizedCommunication
import ..OptimizedCommunication: optimize_communication_pattern!, create_communication_schedule

export optimize_communication_pattern_v2!, create_communication_schedule_v2
export execute_communication_schedule_v2!, create_persistent_requests_v2
export start_persistent_communication_v2!, complete_persistent_communication_v2!
export neighborhood_collective_exchange_v2!, one_sided_communication_v2!
export aggregate_messages!, compute_with_overlap!

"""
    MessageAggregationInfo

Structure to store information about message aggregation.

# Fields
- `send_procs`: List of processes to send data to
- `recv_procs`: List of processes to receive data from
- `aggregated_send_indices`: Aggregated indices for sending
- `aggregated_recv_indices`: Aggregated indices for receiving
- `message_sizes`: Size of messages to each process
- `threshold`: Threshold for message aggregation
"""
struct MessageAggregationInfo
    send_procs::Vector{Int}
    recv_procs::Vector{Int}
    aggregated_send_indices::Dict{Int, Vector{Int}}
    aggregated_recv_indices::Dict{Int, Vector{Int}}
    message_sizes::Dict{Int, Int}
    threshold::Int
end

"""
    CommunicationScheduleV2

Structure to represent an optimized communication schedule.

# Fields
- `phases`: List of communication phases
- `phase_send_procs`: Processes to send to in each phase
- `phase_recv_procs`: Processes to receive from in each phase
- `overlap_regions`: Regions that can be computed while communication is in progress
- `aggregation_info`: Information about message aggregation
"""
struct CommunicationScheduleV2
    phases::Int
    phase_send_procs::Vector{Vector{Int}}
    phase_recv_procs::Vector{Vector{Int}}
    overlap_regions::Vector{Vector{Int}}
    aggregation_info::MessageAggregationInfo
end

"""
    PersistentRequestsV2

Structure to represent optimized persistent MPI requests.

# Fields
- `send_requests`: Persistent send requests
- `recv_requests`: Persistent receive requests
- `aggregated_send_buffers`: Aggregated send buffers
- `aggregated_recv_buffers`: Aggregated receive buffers
"""
struct PersistentRequestsV2{T}
    send_requests::Dict{Int, MPI.Request}
    recv_requests::Dict{Int, MPI.Request}
    aggregated_send_buffers::Dict{Int, Vector{T}}
    aggregated_recv_buffers::Dict{Int, Vector{T}}
end

"""
    aggregate_messages!(mesh::Any, threshold::Int=1024)

Aggregate small messages to reduce communication overhead.

# Arguments
- `mesh`: The optimized mesh
- `threshold`: Threshold for message aggregation (in number of elements)

# Returns
- `MessageAggregationInfo`: Information about message aggregation
"""
function aggregate_messages!(mesh::Any, threshold::Int=1024)
    # Initialize MPI if not already initialized
    if !MPI.Initialized()
        MPI.Init()
    end

    # Get MPI info
    comm = hasfield(typeof(mesh), :comm) ? mesh.comm : MPI.COMM_WORLD
    rank = MPI.Comm_rank(comm)
    nprocs = MPI.Comm_size(comm)

    # Skip if single process
    if nprocs == 1
        return MessageAggregationInfo(
            Int[], Int[], Dict{Int, Vector{Int}}(), Dict{Int, Vector{Int}}(),
            Dict{Int, Int}(), threshold
        )
    end

    # Check if mesh has send/recv maps
    if !hasfield(typeof(mesh), :send_maps) || !hasfield(typeof(mesh), :recv_maps)
        return MessageAggregationInfo(
            Int[], Int[], Dict{Int, Vector{Int}}(), Dict{Int, Vector{Int}}(),
            Dict{Int, Int}(), threshold
        )
    end

    # Get send/recv maps
    send_maps = mesh.send_maps
    recv_maps = mesh.recv_maps

    # Get send/recv processes
    send_procs = collect(keys(send_maps))
    recv_procs = collect(keys(recv_maps))

    # Compute message sizes
    message_sizes = Dict{Int, Int}()

    for proc in send_procs
        message_sizes[proc] = length(send_maps[proc])
    end

    # Identify small messages that need aggregation
    small_message_procs = [proc for proc in send_procs if message_sizes[proc] < threshold]

    # Aggregate small messages
    aggregated_send_indices = Dict{Int, Vector{Int}}()
    aggregated_recv_indices = Dict{Int, Vector{Int}}()

    # Copy large messages as is
    for proc in send_procs
        if !(proc in small_message_procs)
            aggregated_send_indices[proc] = send_maps[proc]
        end
    end

    for proc in recv_procs
        if message_sizes[proc] >= threshold
            aggregated_recv_indices[proc] = recv_maps[proc]
        end
    end

    # Aggregate small messages by proximity
    if !isempty(small_message_procs)
        # Sort small message processes by rank
        sort!(small_message_procs)

        # Group processes by proximity
        groups = Vector{Int}[]
        current_group = Int[]

        for proc in small_message_procs
            if isempty(current_group) || proc - current_group[end] <= 2
                push!(current_group, proc)
            else
                push!(groups, copy(current_group))
                current_group = [proc]
            end
        end

        if !isempty(current_group)
            push!(groups, current_group)
        end

        # Aggregate messages within each group
        for (i, group) in enumerate(groups)
            # Create aggregated indices
            aggregated_indices = Int[]

            for proc in group
                append!(aggregated_indices, send_maps[proc])
            end

            # Store aggregated indices
            aggregated_send_indices[i + nprocs] = aggregated_indices

            # Update message sizes
            message_sizes[i + nprocs] = length(aggregated_indices)

            # Communicate aggregation info to receivers
            for proc in group
                MPI.Send([i + nprocs, length(send_maps[proc])], proc, 0, comm)
            end
        end

        # Receive aggregation info
        for proc in recv_procs
            if message_sizes[proc] < threshold
                # Receive aggregation info
                buffer = Vector{Int}(undef, 2)
                MPI.Recv!(buffer, proc, 0, comm)

                # Extract aggregation info
                group_id = buffer[1]
                message_size = buffer[2]

                # Store aggregated indices
                if !haskey(aggregated_recv_indices, group_id)
                    aggregated_recv_indices[group_id] = Int[]
                end

                append!(aggregated_recv_indices[group_id], recv_maps[proc])
            end
        end
    end

    return MessageAggregationInfo(
        send_procs, recv_procs, aggregated_send_indices, aggregated_recv_indices,
        message_sizes, threshold
    )
end

"""
    optimize_communication_pattern_v2!(mesh::Any)

Optimize the communication pattern for a mesh with advanced techniques.

# Arguments
- `mesh`: The optimized mesh
"""
function optimize_communication_pattern_v2!(mesh::Any)
    # First apply basic optimizations
    optimize_communication_pattern!(mesh)

    # Then apply advanced optimizations
    aggregation_info = aggregate_messages!(mesh)

    # Store aggregation info in mesh
    mesh.aggregation_info = aggregation_info
end

"""
    create_communication_schedule_v2(mesh::Any)

Create an optimized communication schedule to minimize contention and maximize overlap.

# Arguments
- `mesh`: The optimized mesh

# Returns
- `CommunicationScheduleV2`: The optimized communication schedule
"""
function create_communication_schedule_v2(mesh::Any)
    # Initialize MPI if not already initialized
    if !MPI.Initialized()
        MPI.Init()
    end

    # Get MPI info
    comm = hasfield(typeof(mesh), :comm) ? mesh.comm : MPI.COMM_WORLD
    rank = MPI.Comm_rank(comm)
    nprocs = MPI.Comm_size(comm)

    # Skip if single process
    if nprocs == 1
        return CommunicationScheduleV2(
            1, [Int[]], [Int[]], [Int[]],
            MessageAggregationInfo(Int[], Int[], Dict{Int, Vector{Int}}(), Dict{Int, Vector{Int}}(), Dict{Int, Int}(), 1024)
        )
    end

    # Check if mesh has send/recv maps
    if !hasfield(typeof(mesh), :send_maps) || !hasfield(typeof(mesh), :recv_maps)
        return CommunicationScheduleV2(
            1, [Int[]], [Int[]], [Int[]],
            MessageAggregationInfo(Int[], Int[], Dict{Int, Vector{Int}}(), Dict{Int, Vector{Int}}(), Dict{Int, Int}(), 1024)
        )
    end

    # Get aggregation info
    aggregation_info = !isnothing(mesh.aggregation_info) ? mesh.aggregation_info : aggregate_messages!(mesh)

    # Create basic communication schedule
    basic_schedule = create_communication_schedule(mesh)

    # Identify regions that can be computed while communication is in progress
    overlap_regions = Vector{Int}[]

    # For each phase, identify cells that don't depend on halo data
    for phase in 1:basic_schedule.phases
        # Get processes involved in this phase
        phase_recv_procs = basic_schedule.phase_recv_procs[phase]

        # Get cells that don't depend on data from these processes
        independent_cells = Int[]

        for cell_idx in 1:length(mesh.cells)
            # Check if cell depends on halo data
            is_independent = true

            for proc in phase_recv_procs
                if haskey(mesh.recv_maps, proc) && cell_idx in mesh.recv_maps[proc]
                    is_independent = false
                    break
                end
            end

            if is_independent
                push!(independent_cells, cell_idx)
            end
        end

        push!(overlap_regions, independent_cells)
    end

    return CommunicationScheduleV2(
        basic_schedule.phases,
        basic_schedule.phase_send_procs,
        basic_schedule.phase_recv_procs,
        overlap_regions,
        aggregation_info
    )
end

"""
    execute_communication_schedule_v2!(field::Vector{T}, mesh::Any, schedule::CommunicationScheduleV2) where T

Execute an optimized communication schedule with computation-communication overlap.

# Arguments
- `field`: The field to communicate
- `mesh`: The optimized mesh
- `schedule`: The optimized communication schedule

# Returns
- `Nothing`
"""
function execute_communication_schedule_v2!(field::Vector{T}, mesh::Any, schedule::CommunicationScheduleV2) where T
    # Initialize MPI if not already initialized
    if !MPI.Initialized()
        MPI.Init()
    end

    # Get MPI info
    comm = hasfield(typeof(mesh), :comm) ? mesh.comm : MPI.COMM_WORLD

    # Skip if single process
    if MPI.Comm_size(comm) == 1
        return
    end

    # Check if mesh has send/recv maps
    if !hasfield(typeof(mesh), :send_maps) || !hasfield(typeof(mesh), :recv_maps)
        return
    end

    # Get send/recv maps
    send_maps = mesh.send_maps
    recv_maps = mesh.recv_maps

    # Get aggregation info
    aggregation_info = schedule.aggregation_info

    # Execute communication schedule with overlap
    for phase in 1:schedule.phases
        # Create send and receive requests
        send_requests = MPI.Request[]
        recv_requests = MPI.Request[]
        recv_buffers = Dict{Int, Vector{T}}()

        # Post non-blocking receives
        for proc in schedule.phase_recv_procs[phase]
            if haskey(aggregation_info.aggregated_recv_indices, proc)
                recv_indices = aggregation_info.aggregated_recv_indices[proc]
                recv_buffer = Vector{T}(undef, length(recv_indices))
                recv_buffers[proc] = recv_buffer

                # Post non-blocking receive
                request = MPI.Irecv!(recv_buffer, proc, 0, comm)
                push!(recv_requests, request)
            end
        end

        # Post non-blocking sends
        for proc in schedule.phase_send_procs[phase]
            if haskey(aggregation_info.aggregated_send_indices, proc)
                send_indices = aggregation_info.aggregated_send_indices[proc]
                send_buffer = field[send_indices]

                # Post non-blocking send
                request = MPI.Isend(send_buffer, proc, 0, comm)
                push!(send_requests, request)
            end
        end

        # Perform computation on independent cells while communication is in progress
        if phase <= length(schedule.overlap_regions)
            independent_cells = schedule.overlap_regions[phase]

            # This is where you would do work on independent cells
            # For example, if we're computing gradients, we can compute them for independent cells now
            # compute_gradients!(field, independent_cells)
        end

        # Test for completion of receives
        completed = false
        while !completed
            # Test if all receives are complete
            completed = MPI.Testall(recv_requests)

            if !completed
                # Do more computation on independent cells
                # This ensures we make progress on computation while waiting for communication
            end
        end

        # Update field with received data
        for (proc, buffer) in recv_buffers
            if haskey(aggregation_info.aggregated_recv_indices, proc)
                recv_indices = aggregation_info.aggregated_recv_indices[proc]
                field[recv_indices] .= buffer
            end
        end

        # Wait for all sends to complete
        if !isempty(send_requests)
            MPI.Waitall!(send_requests)
        end
    end
end

"""
    create_persistent_requests_v2(field::Vector{T}, mesh::Any) where T

Create optimized persistent MPI requests for communication.

# Arguments
- `field`: The field to communicate
- `mesh`: The optimized mesh

# Returns
- `PersistentRequestsV2`: The optimized persistent requests
"""
function create_persistent_requests_v2(field::Vector{T}, mesh::Any) where T
    # Initialize MPI if not already initialized
    if !MPI.Initialized()
        MPI.Init()
    end

    # Get MPI info
    comm = hasfield(typeof(mesh), :comm) ? mesh.comm : MPI.COMM_WORLD

    # Skip if single process
    if MPI.Comm_size(comm) == 1
        return PersistentRequestsV2{T}(
            Dict{Int, MPI.Request}(), Dict{Int, MPI.Request}(),
            Dict{Int, Vector{T}}(), Dict{Int, Vector{T}}()
        )
    end

    # Check if mesh has send/recv maps
    if !hasfield(typeof(mesh), :send_maps) || !hasfield(typeof(mesh), :recv_maps)
        return PersistentRequestsV2{T}(
            Dict{Int, MPI.Request}(), Dict{Int, MPI.Request}(),
            Dict{Int, Vector{T}}(), Dict{Int, Vector{T}}()
        )
    end

    # Get aggregation info
    aggregation_info = !isnothing(mesh.aggregation_info) ? mesh.aggregation_info : aggregate_messages!(mesh)

    # Create persistent requests
    send_requests = Dict{Int, MPI.Request}()
    recv_requests = Dict{Int, MPI.Request}()
    aggregated_send_buffers = Dict{Int, Vector{T}}()
    aggregated_recv_buffers = Dict{Int, Vector{T}}()

    # Create persistent receives
    for (proc, indices) in aggregation_info.aggregated_recv_indices
        recv_buffer = Vector{T}(undef, length(indices))
        aggregated_recv_buffers[proc] = recv_buffer

        # Create persistent receive
        request = MPI.Recv_init(recv_buffer, proc, 0, comm)
        recv_requests[proc] = request
    end

    # Create persistent sends
    for (proc, indices) in aggregation_info.aggregated_send_indices
        send_buffer = field[indices]
        aggregated_send_buffers[proc] = send_buffer

        # Create persistent send
        request = MPI.Send_init(send_buffer, proc, 0, comm)
        send_requests[proc] = request
    end

    return PersistentRequestsV2{T}(
        send_requests, recv_requests,
        aggregated_send_buffers, aggregated_recv_buffers
    )
end

"""
    start_persistent_communication_v2!(requests::PersistentRequestsV2, field::Vector{T}, mesh::Any) where T

Start optimized persistent communication.

# Arguments
- `requests`: The optimized persistent requests
- `field`: The field to communicate
- `mesh`: The optimized mesh

# Returns
- `Nothing`
"""
function start_persistent_communication_v2!(requests::PersistentRequestsV2{T}, field::Vector{T}, mesh::Any) where T
    # Get aggregation info
    aggregation_info = !isnothing(mesh.aggregation_info) ? mesh.aggregation_info : aggregate_messages!(mesh)

    # Update send buffers with current field values
    for (proc, indices) in aggregation_info.aggregated_send_indices
        if haskey(requests.aggregated_send_buffers, proc)
            requests.aggregated_send_buffers[proc] .= field[indices]
        end
    end

    # Start persistent receives
    for (_, request) in requests.recv_requests
        MPI.Start!(request)
    end

    # Start persistent sends
    for (_, request) in requests.send_requests
        MPI.Start!(request)
    end
end

"""
    complete_persistent_communication_v2!(requests::PersistentRequestsV2{T}, field::Vector{T}, mesh::Any) where T

Complete optimized persistent communication with computation-communication overlap.

# Arguments
- `requests`: The optimized persistent requests
- `field`: The field to update
- `mesh`: The optimized mesh

# Returns
- `Nothing`
"""
function complete_persistent_communication_v2!(requests::PersistentRequestsV2{T}, field::Vector{T}, mesh::Any) where T
    # Get aggregation info
    aggregation_info = !isnothing(mesh.aggregation_info) ? mesh.aggregation_info : aggregate_messages!(mesh)

    # Create array of receive requests
    recv_requests = [request for (_, request) in requests.recv_requests]

    # Test for completion of receives with computation overlap
    completed = false
    while !completed
        # Test if all receives are complete
        completed = MPI.Testall(recv_requests)

        if !completed
            # Do computation on independent cells
            # This ensures we make progress on computation while waiting for communication
        end
    end

    # Update field with received data
    for (proc, buffer) in requests.aggregated_recv_buffers
        if haskey(aggregation_info.aggregated_recv_indices, proc)
            recv_indices = aggregation_info.aggregated_recv_indices[proc]
            field[recv_indices] .= buffer
        end
    end

    # Wait for all sends to complete
    send_requests = [request for (_, request) in requests.send_requests]
    if !isempty(send_requests)
        MPI.Waitall!(send_requests)
    end
end

"""
    neighborhood_collective_exchange_v2!(field::Vector{T}, mesh::Any) where T

Exchange data using optimized MPI neighborhood collectives.

# Arguments
- `field`: The field to exchange
- `mesh`: The optimized mesh

# Returns
- `Nothing`
"""
function neighborhood_collective_exchange_v2!(field::Vector{T}, mesh::Any) where T
    # Initialize MPI if not already initialized
    if !MPI.Initialized()
        MPI.Init()
    end

    # Get MPI info
    comm = hasfield(typeof(mesh), :comm) ? mesh.comm : MPI.COMM_WORLD

    # Skip if single process
    if MPI.Comm_size(comm) == 1
        return
    end

    # Check if mesh has send/recv maps
    if !hasfield(typeof(mesh), :send_maps) || !hasfield(typeof(mesh), :recv_maps)
        return
    end

    # Get aggregation info
    aggregation_info = !isnothing(mesh.aggregation_info) ? mesh.aggregation_info : aggregate_messages!(mesh)

    # Create neighborhood communicator
    sources = collect(keys(aggregation_info.aggregated_recv_indices))
    destinations = collect(keys(aggregation_info.aggregated_send_indices))

    # Create distributed graph communicator
    graph_comm = MPI.Dist_graph_create_adjacent(comm, sources, destinations)

    # Prepare send and receive buffers
    send_buffers = Dict{Int, Vector{T}}()
    recv_buffers = Dict{Int, Vector{T}}()

    for (proc, indices) in aggregation_info.aggregated_send_indices
        send_buffers[proc] = field[indices]
    end

    for (proc, indices) in aggregation_info.aggregated_recv_indices
        recv_buffers[proc] = Vector{T}(undef, length(indices))
    end

    # Perform neighborhood exchange
    MPI.Neighbor_alltoallv!(send_buffers, recv_buffers, graph_comm)

    # Update field with received data
    for (proc, buffer) in recv_buffers
        if haskey(aggregation_info.aggregated_recv_indices, proc)
            recv_indices = aggregation_info.aggregated_recv_indices[proc]
            field[recv_indices] .= buffer
        end
    end

    # Free communicator
    MPI.free(graph_comm)
end

"""
    one_sided_communication_v2!(field::Vector{T}, mesh::Any) where T

Exchange data using optimized MPI one-sided communication.

# Arguments
- `field`: The field to exchange
- `mesh`: The optimized mesh

# Returns
- `Nothing`
"""
function one_sided_communication_v2!(field::Vector{T}, mesh::Any) where T
    # Initialize MPI if not already initialized
    if !MPI.Initialized()
        MPI.Init()
    end

    # Get MPI info
    comm = hasfield(typeof(mesh), :comm) ? mesh.comm : MPI.COMM_WORLD

    # Skip if single process
    if MPI.Comm_size(comm) == 1
        return
    end

    # Check if mesh has send/recv maps
    if !hasfield(typeof(mesh), :send_maps) || !hasfield(typeof(mesh), :recv_maps)
        return
    end

    # Get aggregation info
    aggregation_info = !isnothing(mesh.aggregation_info) ? mesh.aggregation_info : aggregate_messages!(mesh)

    # Create window
    win = MPI.Win_create(field, comm)

    # Start access epoch
    MPI.Win_fence(0, win)

    # Perform gets with aggregation
    for (proc, indices) in aggregation_info.aggregated_recv_indices
        # Create a temporary buffer for the aggregated data
        temp_buffer = Vector{T}(undef, length(indices))

        # Get data from remote process in a single operation
        MPI.Get!(temp_buffer, proc, 0, length(indices), win)

        # Update field with received data
        field[indices] .= temp_buffer
    end

    # End access epoch
    MPI.Win_fence(0, win)

    # Free window
    MPI.free(win)
end

"""
    compute_with_overlap!(field::Vector{T}, mesh::Any, compute_fn::Function) where T

Perform computation with communication-computation overlap.

# Arguments
- `field`: The field to compute on
- `mesh`: The optimized mesh
- `compute_fn`: Function to compute on cells (takes field and cell indices as arguments)

# Returns
- `Nothing`
"""
function compute_with_overlap!(field::Vector{T}, mesh::Any, compute_fn::Function) where T
    # Initialize MPI if not already initialized
    if !MPI.Initialized()
        MPI.Init()
    end

    # Get MPI info
    comm = hasfield(typeof(mesh), :comm) ? mesh.comm : MPI.COMM_WORLD
    rank = MPI.Comm_rank(comm)
    nprocs = MPI.Comm_size(comm)

    # Skip if single process
    if nprocs == 1
        # Compute on all cells
        compute_fn(field, 1:length(mesh.cells))
        return
    end

    # Check if mesh has send/recv maps
    if !hasfield(typeof(mesh), :send_maps) || !hasfield(typeof(mesh), :recv_maps)
        # Compute on all cells
        compute_fn(field, 1:length(mesh.cells))
        return
    end

    # Get send/recv maps
    send_maps = mesh.send_maps
    recv_maps = mesh.recv_maps

    # Create optimized communication schedule
    schedule = create_communication_schedule_v2(mesh)

    # Identify interior cells (cells that don't depend on halo data)
    interior_cells = Int[]
    boundary_cells = Int[]

    for cell_idx in 1:length(mesh.cells)
        is_interior = true

        for (_, indices) in recv_maps
            if cell_idx in indices
                is_interior = false
                break
            end
        end

        if is_interior
            push!(interior_cells, cell_idx)
        else
            push!(boundary_cells, cell_idx)
        end
    end

    # Start non-blocking communication
    send_requests = MPI.Request[]
    recv_requests = MPI.Request[]
    recv_buffers = Dict{Int, Vector{T}}()

    # Post non-blocking receives
    for (proc, indices) in recv_maps
        recv_buffer = Vector{T}(undef, length(indices))
        recv_buffers[proc] = recv_buffer

        # Post non-blocking receive
        request = MPI.Irecv!(recv_buffer, proc, 0, comm)
        push!(recv_requests, request)
    end

    # Post non-blocking sends
    for (proc, indices) in send_maps
        send_buffer = field[indices]

        # Post non-blocking send
        request = MPI.Isend(send_buffer, proc, 0, comm)
        push!(send_requests, request)
    end

    # Compute on interior cells while communication is in progress
    compute_fn(field, interior_cells)

    # Wait for all receives to complete
    if !isempty(recv_requests)
        MPI.Waitall!(recv_requests)
    end

    # Update field with received data
    for (proc, buffer) in recv_buffers
        field[recv_maps[proc]] .= buffer
    end

    # Compute on boundary cells
    compute_fn(field, boundary_cells)

    # Wait for all sends to complete
    if !isempty(send_requests)
        MPI.Waitall!(send_requests)
    end
end

end # module OptimizedCommunication
