"""
    Enhanced parallel execution capabilities for improved scaling efficiency.
    This module provides optimized parallel algorithms and communication patterns.
"""
module Enhanced<PERSON><PERSON><PERSON><PERSON>

using MPI
using StaticArrays
using SparseArrays
using LinearAlgebra
using LoopVectorization
const HAVE_PARTITIONED_ARRAYS = try
    using PartitionedArrays
    true
catch
    @warn "PartitionedArrays package not available. Some parallel features will be limited."
    false
end
const HAVE_METIS = try
    using Metis
    true
catch
    @warn "Metis package not available. Some parallel decomposition features will be limited."
    false
end
using PETSc

# Import types from parent module
import ..JuliaFOAM: OptimizedMesh, Field

export init_parallel, finalize_parallel, decompose_mesh_metis, create_distributed_mesh
export sync_field!, create_distributed_matrix, solve_parallel, gather_field
export compute_load_balance, optimize_communication_pattern
export create_local_mesh

"""
    init_parallel()

Initialize parallel environment with optimized settings.

# Returns
- `Tuple{MPI.Comm,Int,Int}`: MPI communicator, rank, and size
"""
function init_parallel()
    if !MPI.Initialized()
        MPI.Init()
    end

    comm = MPI.COMM_WORLD
    rank = MPI.Comm_rank(comm)
    size = MPI.Comm_size(comm)

    # Set MPI buffer size for improved performance
    if rank == 0
        MPI.set_buffer_size(2^28)  # 256 MB buffer
    end

    # Initialize PETSc for parallel linear solvers
    PETSc.initialize()

    return comm, rank, size
end

"""
    finalize_parallel()

Finalize parallel environment.
"""
function finalize_parallel()
    PETSc.finalize()

    if MPI.Initialized() && !MPI.Finalized()
        MPI.Finalize()
    end
end

"""
    decompose_mesh_metis(mesh::Any, n_procs::Int; options=Dict())

Decompose mesh for parallel execution using METIS for optimal load balancing.

# Arguments
- `mesh`: The optimized mesh
- `n_procs`: Number of processes
- `options`: METIS options dictionary

# Returns
- `Vector{Int32}`: Cell partition array
"""
function decompose_mesh_metis(mesh::Any, n_procs::Int; options=Dict())
    # Create METIS graph from cell-cell connectivity
    n_cells = length(mesh.cells)

    # Extract CSR representation of cell-cell connectivity
    I, J, _ = findnz(mesh.cell_cell_connectivity)

    # Convert to METIS format (0-indexed)
    xadj = zeros(Int32, n_cells + 1)
    adjncy = zeros(Int32, length(I))

    # Build xadj array (CSR row pointers)
    current_row = 1
    count = 0
    for i in 1:length(I)
        if I[i] != current_row
            xadj[current_row+1:I[i]] .= count
            current_row = I[i]
        end
        count += 1
    end
    xadj[current_row+1:end] .= count

    # Build adjncy array (CSR column indices, 0-indexed)
    for i in 1:length(J)
        adjncy[i] = J[i] - 1
    end

    # Add vertex weights based on cell volumes for better load balancing
    vwgt = [Int32(round(cell.volume * 1000)) for cell in mesh.cells]

    # Set METIS options
    metis_options = zeros(Int32, 40)
    metis_options[1] = 1  # Use options

    # Apply user options
    for (key, value) in options
        metis_options[key] = value
    end

    # Call METIS for graph partitioning
    _, cell_partition = METIS.partition(xadj, adjncy, n_procs; vwgt=vwgt, options=metis_options)

    return cell_partition
end

"""
    create_distributed_mesh(mesh::Any, cell_partition::Vector{Int32}, comm::MPI.Comm)

Create a distributed mesh for parallel execution.

# Arguments
- `mesh`: The optimized mesh
- `cell_partition`: Cell partition array
- `comm`: MPI communicator

# Returns
- `Tuple{OptimizedMesh,Vector{Int32},Vector{Int32}}`: Local mesh, global-to-local cell mapping, halo cells
"""
function create_distributed_mesh(mesh::Any, cell_partition::Vector{Int32}, comm::MPI.Comm)
    rank = MPI.Comm_rank(comm)
    size = MPI.Comm_size(comm)

    # Find cells owned by this process
    local_cells = findall(i -> cell_partition[i] == rank, 1:length(mesh.cells))

    # Find halo cells (neighbors of local cells that are owned by other processes)
    halo_cells = Int32[]
    for cell_idx in local_cells
        for neighbor_idx in mesh.cell_neighbors[cell_idx]
            if cell_partition[neighbor_idx] != rank
                push!(halo_cells, neighbor_idx)
            end
        end
    end
    unique!(halo_cells)

    # Create global-to-local cell mapping
    all_cells = vcat(local_cells, halo_cells)
    global_to_local = Dict{Int32,Int32}()
    for (local_idx, global_idx) in enumerate(all_cells)
        global_to_local[global_idx] = Int32(local_idx)
    end

    # Create local mesh
    local_mesh = create_local_mesh(mesh, all_cells, global_to_local, length(local_cells))

    return local_mesh, all_cells, halo_cells
end

"""
    create_local_mesh(mesh::Any, all_cells::Vector{Int32}, global_to_local::Dict{Int32,Int32}, n_local_cells::Int)

Create a local mesh for a process.

# Arguments
- `mesh`: The optimized mesh
- `all_cells`: All cells (local + halo)
- `global_to_local`: Global-to-local cell mapping
- `n_local_cells`: Number of local cells (excluding halo)

# Returns
- `OptimizedMesh`: Local mesh
"""
function create_local_mesh(mesh::Any, all_cells::Vector{Int32}, global_to_local::Dict{Int32,Int32}, n_local_cells::Int)
    # Extract faces for these cells
    face_set = Set{Int32}()
    for cell_idx in all_cells
        for face_idx in mesh.cell_faces[cell_idx]
            push!(face_set, face_idx)
        end
    end
    all_faces = collect(face_set)

    # Create global-to-local face mapping
    global_to_local_face = Dict{Int32,Int32}()
    for (local_idx, global_idx) in enumerate(all_faces)
        global_to_local_face[global_idx] = Int32(local_idx)
    end

    # Create local cells
    local_cells = Vector{Cell}(undef, length(all_cells))
    for (local_idx, global_idx) in enumerate(all_cells)
        global_cell = mesh.cells[global_idx]

        # Map face indices to local indices
        local_faces = Int32[]
        for global_face_idx in global_cell.faces
            if haskey(global_to_local_face, global_face_idx)
                push!(local_faces, global_to_local_face[global_face_idx])
            end
        end

        local_cells[local_idx] = Cell(
            local_faces,
            global_cell.center,
            global_cell.volume
        )
    end

    # Create local faces
    local_faces = Vector{Face}(undef, length(all_faces))
    local_face_owner = Vector{Int32}(undef, length(all_faces))
    local_face_neighbour = Vector{Int32}(undef, length(all_faces))

    for (local_idx, global_idx) in enumerate(all_faces)
        global_face = mesh.faces[global_idx]
        global_owner = global_face.owner
        global_neighbour = global_face.neighbour

        # Map owner and neighbour to local indices
        local_owner = haskey(global_to_local, global_owner) ? global_to_local[global_owner] : Int32(-1)
        local_neighbour = (global_neighbour > 0 && haskey(global_to_local, global_neighbour)) ?
                         global_to_local[global_neighbour] : Int32(-1)

        local_faces[local_idx] = Face(
            local_owner,
            local_neighbour,
            global_face.area,
            global_face.center
        )

        local_face_owner[local_idx] = local_owner
        local_face_neighbour[local_idx] = local_neighbour
    end

    # Create local cell_faces array
    local_cell_faces = [Int32[] for _ in 1:length(all_cells)]
    for (local_face_idx, local_face) in enumerate(local_faces)
        if local_face.owner > 0
            push!(local_cell_faces[local_face.owner], Int32(local_face_idx))
        end
        if local_face.neighbour > 0
            push!(local_cell_faces[local_face.neighbour], Int32(local_face_idx))
        end
    end

    # Create local cell_neighbors array
    local_cell_neighbors = [Int32[] for _ in 1:length(all_cells)]
    for (local_face_idx, local_face) in enumerate(local_faces)
        if local_face.owner > 0 && local_face.neighbour > 0
            push!(local_cell_neighbors[local_face.owner], local_face.neighbour)
            push!(local_cell_neighbors[local_face.neighbour], local_face.owner)
        end
    end

    # Create local boundary patches
    local_boundary_patches = Dict{String,Vector{Int32}}()
    local_boundary_conditions = Dict{String,BoundaryCondition}()

    # Identify boundary faces
    boundary_faces = findall(f -> f.neighbour == -1, local_faces)

    # For simplicity, create a single boundary patch
    # In reality, we would need to map from global patches
    local_boundary_patches["boundary"] = boundary_faces
    local_boundary_conditions["boundary"] = ZeroGradientBC()

    # Create local connectivity matrices
    # Face-cell connectivity
    I = Int32[]
    J = Int32[]
    for (cell_idx, faces) in enumerate(local_cell_faces)
        for face_idx in faces
            push!(I, face_idx)
            push!(J, cell_idx)
        end
    end
    local_face_cells = sparse(I, J, true, length(all_faces), length(all_cells))

    # Cell-cell connectivity
    I = Int32[]
    J = Int32[]
    for (cell_idx, neighbors) in enumerate(local_cell_neighbors)
        for neighbor_idx in neighbors
            push!(I, cell_idx)
            push!(J, neighbor_idx)
        end
    end
    local_cell_cell_connectivity = sparse(I, J, true, length(all_cells), length(all_cells))

    # Create optimized mesh
    local_mesh = OptimizedMesh(
        mesh.points,  # Reuse points
        local_faces,
        local_cells,
        local_face_owner,
        local_face_neighbour,
        local_cell_faces,
        local_boundary_patches,
        local_boundary_conditions,
        local_cell_neighbors,
        local_face_cells,
        local_cell_cell_connectivity
    )

    return local_mesh
end

"""
    sync_field!(field::Field{T}, local_mesh::Any, all_cells::Vector{Int32},
               halo_cells::Vector{Int32}, global_to_local::Dict{Int32,Int32}, cell_partition::Vector{Int32}, comm::MPI.Comm) where T

Synchronize field values at halo cells using non-blocking communication for better performance.

# Arguments
- `field`: Field to synchronize
- `local_mesh`: Local mesh
- `all_cells`: All cells (local + halo)
- `halo_cells`: Halo cells
- `global_to_local`: Global-to-local cell mapping
- `cell_partition`: Cell partition array
- `comm`: MPI communicator
"""
function sync_field!(field::Field{T}, local_mesh::Any, all_cells::Vector{Int32},
                   halo_cells::Vector{Int32}, global_to_local::Dict{Int32,Int32}, cell_partition::Vector{Int32}, comm::MPI.Comm) where T
    rank = MPI.Comm_rank(comm)
    size = MPI.Comm_size(comm)

    # Group halo cells by owner process
    halo_cells_by_proc = [Int32[] for _ in 0:size-1]
    for halo_cell in halo_cells
        owner_proc = cell_partition[halo_cell]
        push!(halo_cells_by_proc[owner_proc+1], halo_cell)
    end

    # Prepare send/receive requests
    send_requests = MPI.Request[]
    recv_requests = MPI.Request[]

    # For each process
    for p in 0:size-1
        if p == rank
            continue
        end

        # Skip if no halo cells from this process
        if isempty(halo_cells_by_proc[p+1])
            continue
        end

        # Prepare receive buffer
        if T <: SVector{3,Float64}
            # Vector field
            recv_data = zeros(Float64, 3 * length(halo_cells_by_proc[p+1]))
        else
            # Scalar field
            recv_data = zeros(T, length(halo_cells_by_proc[p+1]))
        end

        # Post non-blocking receive
        recv_req = MPI.Irecv!(recv_data, p, 0, comm)
        push!(recv_requests, recv_req)

        # Find cells owned by this process that are halo cells for process p
        # This requires communication to determine
        # For simplicity, we'll use a synchronous approach here
        send_cells_buffer = Int32[]
        MPI.Send(halo_cells_by_proc[p+1], p, 1, comm)
        MPI.Recv!(send_cells_buffer, p, 1, comm)

        # Prepare send data
        if T <: SVector{3,Float64}
            # Vector field - flatten to array
            send_data = zeros(Float64, 3 * length(send_cells_buffer))
            for (i, global_cell) in enumerate(send_cells_buffer)
                local_cell = global_to_local[global_cell]
                send_data[3*(i-1) + 1] = field.internal_field[local_cell][1]
                send_data[3*(i-1) + 2] = field.internal_field[local_cell][2]
                send_data[3*(i-1) + 3] = field.internal_field[local_cell][3]
            end
        else
            # Scalar field
            send_data = zeros(T, length(send_cells_buffer))
            for (i, global_cell) in enumerate(send_cells_buffer)
                local_cell = global_to_local[global_cell]
                send_data[i] = field.internal_field[local_cell]
            end
        end

        # Post non-blocking send
        send_req = MPI.Isend(send_data, p, 0, comm)
        push!(send_requests, send_req)
    end

    # Wait for all receives to complete
    statuses = MPI.Waitall(recv_requests)

    # Update halo cells
    recv_idx = 1
    for p in 0:size-1
        if p == rank || isempty(halo_cells_by_proc[p+1])
            continue
        end

        if T <: SVector{3,Float64}
            # Vector field - unflatten from array
            for global_cell in halo_cells_by_proc[p+1]
                local_cell = global_to_local[global_cell]
                field.internal_field[local_cell] = SVector{3,Float64}(
                    recv_data[recv_idx],
                    recv_data[recv_idx + 1],
                    recv_data[recv_idx + 2]
                )
                recv_idx += 3
            end
        else
            # Scalar field
            for global_cell in halo_cells_by_proc[p+1]
                local_cell = global_to_local[global_cell]
                field.internal_field[local_cell] = recv_data[recv_idx]
                recv_idx += 1
            end
        end
    end

    # Wait for all sends to complete
    MPI.Waitall(send_requests)
end

"""
    create_distributed_matrix(local_matrix::SparseMatrixCSC, local_mesh::Any,
                            all_cells::Vector{Int32}, n_local_cells::Int, comm::MPI.Comm)

Create a distributed PETSc matrix from local matrix.

# Arguments
- `local_matrix`: Local sparse matrix
- `local_mesh`: Local mesh
- `all_cells`: All cells (local + halo)
- `n_local_cells`: Number of local cells (excluding halo)
- `comm`: MPI communicator

# Returns
- `PETSc.Mat`: PETSc distributed matrix
"""
function create_distributed_matrix(local_matrix::SparseMatrixCSC, local_mesh::Any,
                                 all_cells::Vector{Int32}, n_local_cells::Int, comm::MPI.Comm)
    rank = MPI.Comm_rank(comm)
    size = MPI.Comm_size(comm)

    # Get global matrix size
    local_rows, local_cols = size(local_matrix)
    global_rows = MPI.Allreduce(local_rows, +, comm)
    global_cols = MPI.Allreduce(local_cols, +, comm)

    # Create PETSc matrix
    A = PETSc.Mat(PETSc.MPIAIJ, (global_rows, global_cols), comm=comm)

    # Set local-to-global mapping
    row_map = all_cells[1:n_local_cells] .- 1  # 0-indexed
    col_map = all_cells .- 1  # 0-indexed

    PETSc.setLocalToGlobalMapping(A, row_map, col_map)

    # Preallocate matrix
    I, J, V = findnz(local_matrix)
    nnz_per_row = zeros(Int, local_rows)
    for i in 1:length(I)
        nnz_per_row[I[i]] += 1
    end

    PETSc.setPreallocationWithArrays(A, nnz_per_row, nnz_per_row)

    # Set matrix values
    for i in 1:length(I)
        row = I[i]
        col = J[i]
        val = V[i]

        if row <= n_local_cells  # Only set values for local rows
            PETSc.setValuesLocal(A, [row-1], [col-1], [val], PETSc.INSERT_VALUES)
        end
    end

    # Assemble matrix
    PETSc.assemblyBegin(A)
    PETSc.assemblyEnd(A)

    return A
end

"""
    solve_parallel(A::PETSc.Mat, b::PETSc.Vec, comm::MPI.Comm;
                 tol=1e-6, max_it=1000, solver_type=:cg, preconditioner=:jacobi)

Solve a linear system in parallel using PETSc.

# Arguments
- `A`: PETSc matrix
- `b`: PETSc right-hand side vector
- `comm`: MPI communicator
- `tol`: Tolerance
- `max_it`: Maximum number of iterations
- `solver_type`: Solver type (:cg, :gmres, :bicgstab)
- `preconditioner`: Preconditioner type (:jacobi, :sor, :ilu, :amg)

# Returns
- `PETSc.Vec`: Solution vector
"""
function solve_parallel(A::PETSc.Mat, b::PETSc.Vec, comm::MPI.Comm;
                      tol=1e-6, max_it=1000, solver_type=:cg, preconditioner=:jacobi)
    # Create KSP solver
    ksp = PETSc.KSP(comm)

    # Set operator
    PETSc.setOperators(ksp, A, A)

    # Set solver type
    if solver_type == :cg
        PETSc.setType(ksp, PETSc.KSP_CG)
    elseif solver_type == :gmres
        PETSc.setType(ksp, PETSc.KSP_GMRES)
    elseif solver_type == :bicgstab
        PETSc.setType(ksp, PETSc.KSP_BCGS)
    else
        error("Unknown solver type: $solver_type")
    end

    # Set preconditioner
    pc = PETSc.getPC(ksp)
    if preconditioner == :jacobi
        PETSc.setType(pc, PETSc.PC_JACOBI)
    elseif preconditioner == :sor
        PETSc.setType(pc, PETSc.PC_SOR)
    elseif preconditioner == :ilu
        PETSc.setType(pc, PETSc.PC_ILU)
    elseif preconditioner == :amg
        PETSc.setType(pc, PETSc.PC_HYPRE)
        PETSc.setHYPREType(pc, "boomeramg")
    else
        error("Unknown preconditioner type: $preconditioner")
    end

    # Set tolerances
    PETSc.setTolerances(ksp, rtol=tol, max_it=max_it)

    # Create solution vector
    x = PETSc.Vec(PETSc.getSize(b), comm=comm)

    # Solve system
    PETSc.solve(ksp, b, x)

    # Get convergence info
    its = PETSc.getIterationNumber(ksp)
    res_norm = PETSc.getResidualNorm(ksp)

    # Print convergence info
    rank = MPI.Comm_rank(comm)
    if rank == 0
        println("Converged in $its iterations with residual norm $res_norm")
    end

    return x
end

"""
    gather_field(field::Field{T}, local_mesh::Any, all_cells::Vector{Int32},
               n_local_cells::Int, comm::MPI.Comm) where T

Gather a distributed field to the root process.

# Arguments
- `field`: Local field
- `local_mesh`: Local mesh
- `all_cells`: All cells (local + halo)
- `n_local_cells`: Number of local cells (excluding halo)
- `comm`: MPI communicator

# Returns
- `Field{T}`: Global field (only valid on root process)
"""
function gather_field(field::Field{T}, local_mesh::Any, all_cells::Vector{Int32},
                    n_local_cells::Int, comm::MPI.Comm) where T
    rank = MPI.Comm_rank(comm)
    size = MPI.Comm_size(comm)

    # Get global number of cells
    n_global_cells = MPI.Allreduce(n_local_cells, +, comm)

    # Create global field on root process
    if rank == 0
        global_field = Field{T}(field.name, Vector{T}(undef, n_global_cells), Dict{String,Vector{T}}())
    else
        global_field = Field{T}(field.name, T[], Dict{String,Vector{T}}())
    end

    # Gather local field values
    if T <: SVector{3,Float64}
        # Vector field - flatten to array
        local_data = zeros(Float64, 3 * n_local_cells)
        for i in 1:n_local_cells
            local_data[3*(i-1) + 1] = field.internal_field[i][1]
            local_data[3*(i-1) + 2] = field.internal_field[i][2]
            local_data[3*(i-1) + 3] = field.internal_field[i][3]
        end

        # Gather to root
        if rank == 0
            recv_counts = zeros(Int, size)
            MPI.Gather!(Ref(n_local_cells * 3), recv_counts, 0, comm)

            recv_data = zeros(Float64, sum(recv_counts))
            MPI.Gatherv!(local_data, recv_data, recv_counts, 0, comm)

            # Unflatten to vector field
            for i in 1:n_global_cells
                global_field.internal_field[i] = SVector{3,Float64}(
                    recv_data[3*(i-1) + 1],
                    recv_data[3*(i-1) + 2],
                    recv_data[3*(i-1) + 3]
                )
            end
        else
            MPI.Gather!(Ref(n_local_cells * 3), nothing, 0, comm)
            MPI.Gatherv!(local_data, nothing, nothing, 0, comm)
        end
    else
        # Scalar field
        local_data = field.internal_field[1:n_local_cells]

        # Gather to root
        if rank == 0
            recv_counts = zeros(Int, size)
            MPI.Gather!(Ref(n_local_cells), recv_counts, 0, comm)

            recv_data = zeros(T, sum(recv_counts))
            MPI.Gatherv!(local_data, recv_data, recv_counts, 0, comm)

            global_field.internal_field = recv_data
        else
            MPI.Gather!(Ref(n_local_cells), nothing, 0, comm)
            MPI.Gatherv!(local_data, nothing, nothing, 0, comm)
        end
    end

    return global_field
end

"""
    compute_load_balance(cell_partition::Vector{Int32}, mesh::Any, n_procs::Int)

Compute load balance metrics for a given partitioning.

# Arguments
- `cell_partition`: Cell partition array
- `mesh`: The optimized mesh
- `n_procs`: Number of processes

# Returns
- `Tuple{Float64,Float64,Float64}`: Load imbalance, communication volume, edge cut
"""
function compute_load_balance(cell_partition::Vector{Int32}, mesh::Any, n_procs::Int)
    # Count cells per process
    cells_per_proc = zeros(Int, n_procs)
    for p in cell_partition
        cells_per_proc[p+1] += 1
    end

    # Compute load imbalance
    avg_cells = sum(cells_per_proc) / n_procs
    max_cells = maximum(cells_per_proc)
    load_imbalance = max_cells / avg_cells - 1.0

    # Compute communication volume and edge cut
    comm_volume = 0
    edge_cut = 0

    for (i, face) in enumerate(mesh.faces)
        if face.neighbour > 0  # Internal face
            owner = face.owner
            neighbor = face.neighbour

            if cell_partition[owner] != cell_partition[neighbor]
                edge_cut += 1
                comm_volume += 2  # Both cells need to communicate
            end
        end
    end

    return load_imbalance, comm_volume, edge_cut
end

"""
    optimize_communication_pattern(local_mesh::Any, all_cells::Vector{Int32},
                                 halo_cells::Vector{Int32}, cell_partition::Vector{Int32}, comm::MPI.Comm)

Optimize communication pattern for better parallel performance.

# Arguments
- `local_mesh`: Local mesh
- `all_cells`: All cells (local + halo)
- `halo_cells`: Halo cells
- `cell_partition`: Cell partition array
- `comm`: MPI communicator

# Returns
- `Dict{Int,Vector{Int32}}`: Optimized communication schedule
"""
function optimize_communication_pattern(local_mesh::Any, all_cells::Vector{Int32},
                                      halo_cells::Vector{Int32}, cell_partition::Vector{Int32}, comm::MPI.Comm)
    rank = MPI.Comm_rank(comm)
    size = MPI.Comm_size(comm)

    # Group halo cells by owner process
    halo_cells_by_proc = Dict{Int,Vector{Int32}}()
    for halo_cell in halo_cells
        owner_proc = cell_partition[halo_cell]
        if !haskey(halo_cells_by_proc, owner_proc)
            halo_cells_by_proc[owner_proc] = Int32[]
        end
        push!(halo_cells_by_proc[owner_proc], halo_cell)
    end

    # Create communication schedule
    # For now, just return the grouped halo cells
    # In a more advanced implementation, we could optimize the order of communication
    # to minimize contention and maximize overlap
    return halo_cells_by_proc
end

end # module EnhancedParallel
