"""
    DynamicLoadBalancing.jl

This module provides dynamic load balancing capabilities for JuliaFOAM.
"""
module DynamicLoadBalancing

using MPI
using StaticArrays
using LinearAlgebra
using SparseArrays
using Statistics
using Base.Threads
using ..JuliaFOAM

export LoadBalancer, create_load_balancer, update_load_statistics!
export rebalance_mesh!, migrate_cells!, compute_imbalance
export DiffusionLoadBalancer, GraphPartitionLoadBalancer, HierarchicalLoadBalancer
export AdaptiveLoadBalancer, create_adaptive_load_balancer
export set_rebalance_threshold, get_rebalance_threshold, should_rebalance
export register_load_metric, get_load_metrics, get_load_distribution

"""
    LoadMetric

Structure to store load metrics for a cell.

# Fields
- `computation_cost`: Computational cost of the cell
- `communication_cost`: Communication cost of the cell
- `memory_usage`: Memory usage of the cell
- `custom_metrics`: Custom metrics for the cell
"""
struct LoadMetric
    computation_cost::Float64
    communication_cost::Float64
    memory_usage::Float64
    custom_metrics::Dict{Symbol,Float64}
end

"""
    LoadBalancer

Abstract type for load balancers.
"""
abstract type LoadBalancer end

"""
    DiffusionLoadBalancer <: LoadBalancer

Load balancer based on diffusion algorithm.

# Fields
- `mesh::Any`: The mesh to balance
- `cell_weights::Vector{Float64}`: Weight of each cell
- `proc_weights::Vector{Float64}`: Weight of each process
- `tolerance::Float64`: Tolerance for load imbalance
- `max_iterations::Int`: Maximum number of iterations
- `metrics::Vector{LoadMetric}`: Load metrics for each cell
"""
mutable struct DiffusionLoadBalancer <: LoadBalancer
    mesh::Any
    cell_weights::Vector{Float64}
    proc_weights::Vector{Float64}
    tolerance::Float64
    max_iterations::Int
    metrics::Vector{LoadMetric}
    rebalance_threshold::Float64
    last_rebalance_time::Float64

    function DiffusionLoadBalancer(mesh::Any, tolerance::Float64=0.05, max_iterations::Int=100)
        n_cells = length(mesh.cells)

        # Initialize MPI if not already initialized
        if !MPI.Initialized()
            MPI.Init()
        end

        # Get MPI info
        comm = hasfield(typeof(mesh), :comm) ? mesh.comm : MPI.COMM_WORLD
        nprocs = MPI.Comm_size(comm)

        # Initialize cell weights (uniform by default)
        cell_weights = ones(n_cells)

        # Initialize process weights (uniform by default)
        proc_weights = ones(nprocs)

        # Initialize metrics
        metrics = [LoadMetric(1.0, 0.0, 1.0, Dict{Symbol,Float64}()) for _ in 1:n_cells]

        new(mesh, cell_weights, proc_weights, tolerance, max_iterations, metrics, 0.1, time())
    end
end

"""
    GraphPartitionLoadBalancer <: LoadBalancer

Load balancer based on graph partitioning.

# Fields
- `mesh::Any`: The mesh to balance
- `cell_weights::Vector{Float64}`: Weight of each cell
- `edge_weights::Vector{Float64}`: Weight of each edge
- `proc_weights::Vector{Float64}`: Weight of each process
- `tolerance::Float64`: Tolerance for load imbalance
- `metrics::Vector{LoadMetric}`: Load metrics for each cell
"""
mutable struct GraphPartitionLoadBalancer <: LoadBalancer
    mesh::Any
    cell_weights::Vector{Float64}
    edge_weights::Vector{Float64}
    proc_weights::Vector{Float64}
    tolerance::Float64
    metrics::Vector{LoadMetric}
    rebalance_threshold::Float64
    last_rebalance_time::Float64

    function GraphPartitionLoadBalancer(mesh::Any, tolerance::Float64=0.05)
        n_cells = length(mesh.cells)

        # Initialize MPI if not already initialized
        if !MPI.Initialized()
            MPI.Init()
        end

        # Get MPI info
        comm = hasfield(typeof(mesh), :comm) ? mesh.comm : MPI.COMM_WORLD
        nprocs = MPI.Comm_size(comm)

        # Initialize cell weights (uniform by default)
        cell_weights = ones(n_cells)

        # Initialize edge weights (uniform by default)
        n_edges = nnz(mesh.cell_cell_connectivity)
        edge_weights = ones(n_edges)

        # Initialize process weights (uniform by default)
        proc_weights = ones(nprocs)

        # Initialize metrics
        metrics = [LoadMetric(1.0, 0.0, 1.0, Dict{Symbol,Float64}()) for _ in 1:n_cells]

        new(mesh, cell_weights, edge_weights, proc_weights, tolerance, metrics, 0.1, time())
    end
end

"""
    HierarchicalLoadBalancer <: LoadBalancer

Hierarchical load balancer for multi-level parallelism.

# Fields
- `mesh::Any`: The mesh to balance
- `node_balancer::LoadBalancer`: Load balancer for inter-node balancing
- `thread_balancer::LoadBalancer`: Load balancer for intra-node balancing
- `metrics::Vector{LoadMetric}`: Load metrics for each cell
"""
mutable struct HierarchicalLoadBalancer <: LoadBalancer
    mesh::Any
    node_balancer::LoadBalancer
    thread_balancer::LoadBalancer
    metrics::Vector{LoadMetric}
    rebalance_threshold::Float64
    last_rebalance_time::Float64

    function HierarchicalLoadBalancer(mesh::Any, node_tolerance::Float64=0.05, thread_tolerance::Float64=0.1)
        n_cells = length(mesh.cells)

        # Create node balancer (graph partitioning)
        node_balancer = GraphPartitionLoadBalancer(mesh, node_tolerance)

        # Create thread balancer (diffusion)
        thread_balancer = DiffusionLoadBalancer(mesh, thread_tolerance)

        # Initialize metrics
        metrics = [LoadMetric(1.0, 0.0, 1.0, Dict{Symbol,Float64}()) for _ in 1:n_cells]

        new(mesh, node_balancer, thread_balancer, metrics, 0.1, time())
    end
end

"""
    AdaptiveLoadBalancer <: LoadBalancer

Adaptive load balancer that switches between different strategies.

# Fields
- `mesh::Any`: The mesh to balance
- `balancers::Vector{LoadBalancer}`: Available load balancers
- `active_balancer::Int`: Index of the currently active balancer
- `metrics::Vector{LoadMetric}`: Load metrics for each cell
- `performance_history::Vector{Float64}`: History of performance measurements
"""
mutable struct AdaptiveLoadBalancer <: LoadBalancer
    mesh::Any
    balancers::Vector{LoadBalancer}
    active_balancer::Int
    metrics::Vector{LoadMetric}
    performance_history::Vector{Float64}
    rebalance_threshold::Float64
    last_rebalance_time::Float64

    function AdaptiveLoadBalancer(mesh::Any)
        n_cells = length(mesh.cells)

        # Create different load balancers
        diffusion_balancer = DiffusionLoadBalancer(mesh)
        graph_balancer = GraphPartitionLoadBalancer(mesh)
        hierarchical_balancer = HierarchicalLoadBalancer(mesh)

        # Initialize with diffusion balancer
        balancers = [diffusion_balancer, graph_balancer, hierarchical_balancer]
        active_balancer = 1

        # Initialize metrics
        metrics = [LoadMetric(1.0, 0.0, 1.0, Dict{Symbol,Float64}()) for _ in 1:n_cells]

        # Initialize performance history
        performance_history = Float64[]

        new(mesh, balancers, active_balancer, metrics, performance_history, 0.1, time())
    end
end

"""
    create_load_balancer(mesh::Any, strategy::Symbol)

Create a load balancer for a mesh.

# Arguments
- `mesh`: The mesh to balance
- `strategy`: Load balancing strategy (:diffusion, :graph, :hierarchical, or :adaptive)

# Returns
- `LoadBalancer`: The created load balancer
"""
function create_load_balancer(mesh::Any, strategy::Symbol)
    if strategy == :diffusion
        return DiffusionLoadBalancer(mesh)
    elseif strategy == :graph
        return GraphPartitionLoadBalancer(mesh)
    elseif strategy == :hierarchical
        return HierarchicalLoadBalancer(mesh)
    elseif strategy == :adaptive
        return AdaptiveLoadBalancer(mesh)
    else
        error("Unknown load balancing strategy: $strategy")
    end
end

"""
    create_adaptive_load_balancer(mesh::Any)

Create an adaptive load balancer for a mesh.

# Arguments
- `mesh`: The mesh to balance

# Returns
- `AdaptiveLoadBalancer`: The created adaptive load balancer
"""
function create_adaptive_load_balancer(mesh::Any)
    return AdaptiveLoadBalancer(mesh)
end

"""
    update_load_statistics!(balancer::LoadBalancer, cell_idx::Int, computation_time::Float64, communication_time::Float64, memory_usage::Float64)

Update load statistics for a cell.

# Arguments
- `balancer`: The load balancer
- `cell_idx`: Index of the cell
- `computation_time`: Computation time for the cell
- `communication_time`: Communication time for the cell
- `memory_usage`: Memory usage for the cell

# Returns
- `Nothing`
"""
function update_load_statistics!(balancer::LoadBalancer, cell_idx::Int, computation_time::Float64, communication_time::Float64, memory_usage::Float64)
    # Update metrics
    balancer.metrics[cell_idx] = LoadMetric(
        computation_time,
        communication_time,
        memory_usage,
        balancer.metrics[cell_idx].custom_metrics
    )

    # Update cell weight based on computation time
    balancer.cell_weights[cell_idx] = computation_time
end

"""
    register_load_metric(balancer::LoadBalancer, cell_idx::Int, metric_name::Symbol, value::Float64)

Register a custom load metric for a cell.

# Arguments
- `balancer`: The load balancer
- `cell_idx`: Index of the cell
- `metric_name`: Name of the metric
- `value`: Value of the metric

# Returns
- `Nothing`
"""
function register_load_metric(balancer::LoadBalancer, cell_idx::Int, metric_name::Symbol, value::Float64)
    # Update custom metrics
    balancer.metrics[cell_idx].custom_metrics[metric_name] = value
end

"""
    get_load_metrics(balancer::LoadBalancer, cell_idx::Int)

Get load metrics for a cell.

# Arguments
- `balancer`: The load balancer
- `cell_idx`: Index of the cell

# Returns
- `LoadMetric`: Load metrics for the cell
"""
function get_load_metrics(balancer::LoadBalancer, cell_idx::Int)
    return balancer.metrics[cell_idx]
end

"""
    get_load_distribution(balancer::LoadBalancer)

Get the load distribution across processes.

# Arguments
- `balancer`: The load balancer

# Returns
- `Vector{Float64}`: Load distribution
"""
function get_load_distribution(balancer::LoadBalancer)
    # Initialize MPI if not already initialized
    if !MPI.Initialized()
        MPI.Init()
    end

    # Get MPI info
    comm = hasfield(typeof(balancer.mesh), :comm) ? balancer.mesh.comm : MPI.COMM_WORLD
    rank = MPI.Comm_rank(comm)
    nprocs = MPI.Comm_size(comm)

    # Compute local load
    local_load = sum(balancer.cell_weights)

    # Gather loads from all processes
    loads = MPI.Allgather(local_load, comm)

    return loads
end

"""
    compute_imbalance(balancer::LoadBalancer)

Compute the load imbalance.

# Arguments
- `balancer`: The load balancer

# Returns
- `Float64`: Load imbalance (0.0 = perfectly balanced, higher values = more imbalanced)
"""
function compute_imbalance(balancer::LoadBalancer)
    # Get load distribution
    loads = get_load_distribution(balancer)

    # Compute statistics
    avg_load = mean(loads)
    max_load = maximum(loads)

    # Compute imbalance
    imbalance = (max_load / avg_load) - 1.0

    return imbalance
end

"""
    set_rebalance_threshold(balancer::LoadBalancer, threshold::Float64)

Set the threshold for triggering rebalancing.

# Arguments
- `balancer`: The load balancer
- `threshold`: Threshold for load imbalance (0.0 - 1.0)

# Returns
- `Nothing`
"""
function set_rebalance_threshold(balancer::LoadBalancer, threshold::Float64)
    balancer.rebalance_threshold = threshold
end

"""
    get_rebalance_threshold(balancer::LoadBalancer)

Get the threshold for triggering rebalancing.

# Arguments
- `balancer`: The load balancer

# Returns
- `Float64`: Threshold for load imbalance
"""
function get_rebalance_threshold(balancer::LoadBalancer)
    return balancer.rebalance_threshold
end

"""
    should_rebalance(balancer::LoadBalancer)

Check if rebalancing should be performed.

# Arguments
- `balancer`: The load balancer

# Returns
- `Bool`: Whether rebalancing should be performed
"""
function should_rebalance(balancer::LoadBalancer)
    # Compute current imbalance
    imbalance = compute_imbalance(balancer)

    # Check if imbalance exceeds threshold
    if imbalance > balancer.rebalance_threshold
        # Check if enough time has passed since last rebalance
        current_time = time()
        if current_time - balancer.last_rebalance_time > 10.0  # At least 10 seconds between rebalances
            return true
        end
    end

    return false
end

"""
    rebalance_mesh!(balancer::DiffusionLoadBalancer)

Rebalance the mesh using the diffusion algorithm.

# Arguments
- `balancer`: The diffusion load balancer

# Returns
- `Bool`: Whether the mesh was rebalanced
"""
function rebalance_mesh!(balancer::DiffusionLoadBalancer)
    # Initialize MPI if not already initialized
    if !MPI.Initialized()
        MPI.Init()
    end

    # Get MPI info
    mesh = balancer.mesh
    comm = hasfield(typeof(mesh), :comm) ? mesh.comm : MPI.COMM_WORLD
    rank = MPI.Comm_rank(comm)
    nprocs = MPI.Comm_size(comm)

    # Skip if single process
    if nprocs == 1
        return false
    end

    # Check if rebalancing is needed
    if !should_rebalance(balancer)
        return false
    end

    # Update last rebalance time
    balancer.last_rebalance_time = time()

    # Get current partition
    n_cells = length(mesh.cells)
    partition = zeros(Int, n_cells)

    # Assign cells to processes based on current distribution
    for (proc, cells) in enumerate(mesh.send_maps)
        for cell_idx in cells
            partition[cell_idx] = proc
        end
    end

    # Compute initial load per process
    proc_loads = zeros(Float64, nprocs)
    for i in 1:n_cells
        proc = partition[i]
        proc_loads[proc+1] += balancer.cell_weights[i]
    end

    # Normalize process loads
    avg_load = sum(proc_loads) / nprocs
    proc_loads ./= avg_load

    # Diffusion algorithm
    converged = false
    iteration = 0

    while !converged && iteration < balancer.max_iterations
        iteration += 1

        # Compute flow between processes
        flow = zeros(Float64, nprocs, nprocs)

        for i in 1:n_cells
            proc = partition[i]

            # Check neighbors
            for neighbor_idx in mesh.cell_neighbors[i]
                neighbor_proc = partition[neighbor_idx]

                if proc != neighbor_proc
                    # Compute flow from more loaded to less loaded process
                    if proc_loads[proc+1] > proc_loads[neighbor_proc+1]
                        flow_amount = min(
                            0.5 * (proc_loads[proc+1] - proc_loads[neighbor_proc+1]),
                            balancer.cell_weights[i] / avg_load
                        )
                        flow[proc+1, neighbor_proc+1] += flow_amount
                    end
                end
            end
        end

        # Update process loads
        for i in 1:nprocs
            for j in 1:nprocs
                proc_loads[i] -= flow[i, j]
                proc_loads[j] += flow[i, j]
            end
        end

        # Check convergence
        imbalance = maximum(proc_loads) - minimum(proc_loads)
        if imbalance < balancer.tolerance
            converged = true
        end
    end

    # Compute new partition
    new_partition = zeros(Int, n_cells)

    # Sort cells by weight (heaviest first)
    sorted_indices = sortperm(balancer.cell_weights, rev=true)

    # Assign cells to processes
    for i in sorted_indices
        # Find process with minimum load
        min_load = Inf
        min_proc = 0

        for proc in 0:nprocs-1
            if proc_loads[proc+1] < min_load
                min_load = proc_loads[proc+1]
                min_proc = proc
            end
        end

        # Assign cell to process
        new_partition[i] = min_proc

        # Update process load
        proc_loads[min_proc+1] += balancer.cell_weights[i] / avg_load
    end

    # Migrate cells
    cells_to_migrate = findall(i -> partition[i] != new_partition[i], 1:n_cells)

    if !isempty(cells_to_migrate)
        migrate_cells!(balancer, cells_to_migrate, new_partition)
        return true
    end

    return false
end

"""
    rebalance_mesh!(balancer::GraphPartitionLoadBalancer)

Rebalance the mesh using graph partitioning.

# Arguments
- `balancer`: The graph partition load balancer

# Returns
- `Bool`: Whether the mesh was rebalanced
"""
function rebalance_mesh!(balancer::GraphPartitionLoadBalancer)
    # Initialize MPI if not already initialized
    if !MPI.Initialized()
        MPI.Init()
    end

    # Get MPI info
    mesh = balancer.mesh
    comm = hasfield(typeof(mesh), :comm) ? mesh.comm : MPI.COMM_WORLD
    rank = MPI.Comm_rank(comm)
    nprocs = MPI.Comm_size(comm)

    # Skip if single process
    if nprocs == 1
        return false
    end

    # Check if rebalancing is needed
    if !should_rebalance(balancer)
        return false
    end

    # Update last rebalance time
    balancer.last_rebalance_time = time()

    # Create graph for partitioning
    n_cells = length(mesh.cells)
    adjacency = mesh.cell_cell_connectivity

    # Use METIS for graph partitioning
    new_partition = zeros(Int, n_cells)

    try
        # Convert to METIS format
        xadj = zeros(Int, n_cells + 1)
        adjncy = zeros(Int, nnz(adjacency))

        # Fill xadj and adjncy
        idx = 1
        for i in 1:n_cells
            xadj[i] = idx
            for j in nzrange(adjacency, i)
                adjncy[idx] = adjacency.rowval[j]
                idx += 1
            end
        end
        xadj[n_cells+1] = idx

        # Set weights
        vwgt = round.(Int, balancer.cell_weights * 100)  # Scale to integers
        adjwgt = round.(Int, balancer.edge_weights * 100)  # Scale to integers

        # Set options
        options = zeros(Int, 5)
        options[1] = 1  # Use default values

        # Call METIS
        new_partition = Metis.partition(
            xadj,
            adjncy,
            nprocs,
            vwgt=vwgt,
            adjwgt=adjwgt,
            options=options
        )
    catch e
        # Fallback to simple partitioning
        println("METIS partitioning failed: $e")
        println("Falling back to simple partitioning")

        # Sort cells by weight (heaviest first)
        sorted_indices = sortperm(balancer.cell_weights, rev=true)

        # Distribute cells evenly
        for (i, idx) in enumerate(sorted_indices)
            new_partition[idx] = i % nprocs
        end
    end

    # Get current partition
    current_partition = zeros(Int, n_cells)

    # Assign cells to processes based on current distribution
    for (proc, cells) in enumerate(mesh.send_maps)
        for cell_idx in cells
            current_partition[cell_idx] = proc
        end
    end

    # Migrate cells
    cells_to_migrate = findall(i -> current_partition[i] != new_partition[i], 1:n_cells)

    if !isempty(cells_to_migrate)
        migrate_cells!(balancer, cells_to_migrate, new_partition)
        return true
    end

    return false
end

"""
    rebalance_mesh!(balancer::HierarchicalLoadBalancer)

Rebalance the mesh using hierarchical load balancing.

# Arguments
- `balancer`: The hierarchical load balancer

# Returns
- `Bool`: Whether the mesh was rebalanced
"""
function rebalance_mesh!(balancer::HierarchicalLoadBalancer)
    # Initialize MPI if not already initialized
    if !MPI.Initialized()
        MPI.Init()
    end

    # Get MPI info
    mesh = balancer.mesh
    comm = hasfield(typeof(mesh), :comm) ? mesh.comm : MPI.COMM_WORLD
    rank = MPI.Comm_rank(comm)
    nprocs = MPI.Comm_size(comm)

    # Skip if single process
    if nprocs == 1
        return false
    end

    # Check if rebalancing is needed
    if !should_rebalance(balancer)
        return false
    end

    # Update last rebalance time
    balancer.last_rebalance_time = time()

    # First rebalance between nodes
    node_rebalanced = rebalance_mesh!(balancer.node_balancer)

    # Then rebalance within nodes
    thread_rebalanced = rebalance_mesh!(balancer.thread_balancer)

    return node_rebalanced || thread_rebalanced
end

"""
    rebalance_mesh!(balancer::AdaptiveLoadBalancer)

Rebalance the mesh using adaptive load balancing.

# Arguments
- `balancer`: The adaptive load balancer

# Returns
- `Bool`: Whether the mesh was rebalanced
"""
function rebalance_mesh!(balancer::AdaptiveLoadBalancer)
    # Initialize MPI if not already initialized
    if !MPI.Initialized()
        MPI.Init()
    end

    # Get MPI info
    mesh = balancer.mesh
    comm = hasfield(typeof(mesh), :comm) ? mesh.comm : MPI.COMM_WORLD
    rank = MPI.Comm_rank(comm)
    nprocs = MPI.Comm_size(comm)

    # Skip if single process
    if nprocs == 1
        return false
    end

    # Check if rebalancing is needed
    if !should_rebalance(balancer)
        return false
    end

    # Update last rebalance time
    balancer.last_rebalance_time = time()

    # Try each balancer and measure performance
    best_performance = -Inf
    best_balancer = balancer.active_balancer

    for (i, lb) in enumerate(balancer.balancers)
        # Skip if this is not the active balancer
        if i != balancer.active_balancer
            continue
        end

        # Rebalance using this balancer
        rebalanced = rebalance_mesh!(lb)

        # Measure performance
        if rebalanced
            # Run a simple benchmark
            start_time = time()
            # ... run some computation ...
            end_time = time()

            performance = 1.0 / (end_time - start_time)

            # Update best balancer if performance is better
            if performance > best_performance
                best_performance = performance
                best_balancer = i
            end

            # Store performance in history
            push!(balancer.performance_history, performance)
        end
    end

    # Update active balancer
    balancer.active_balancer = best_balancer

    return true
end

"""
    migrate_cells!(balancer::LoadBalancer, cells::Vector{Int}, new_partition::Vector{Int})

Migrate cells to new processes.

# Arguments
- `balancer`: The load balancer
- `cells`: Indices of cells to migrate
- `new_partition`: New partition (process assignment for each cell)

# Returns
- `Nothing`
"""
function migrate_cells!(balancer::LoadBalancer, cells::Vector{Int}, new_partition::Vector{Int})
    # Initialize MPI if not already initialized
    if !MPI.Initialized()
        MPI.Init()
    end

    # Get MPI info
    mesh = balancer.mesh
    comm = hasfield(typeof(mesh), :comm) ? mesh.comm : MPI.COMM_WORLD
    rank = MPI.Comm_rank(comm)
    nprocs = MPI.Comm_size(comm)

    # Skip if single process
    if nprocs == 1
        return
    end

    # Group cells by destination process
    cells_by_proc = Dict{Int, Vector{Int}}()

    for cell_idx in cells
        dest_proc = new_partition[cell_idx]

        if !haskey(cells_by_proc, dest_proc)
            cells_by_proc[dest_proc] = Int[]
        end

        push!(cells_by_proc[dest_proc], cell_idx)
    end

    # Update send/recv maps
    for (proc, proc_cells) in cells_by_proc
        # Skip if this is the current process
        if proc == rank
            continue
        end

        # Add cells to send map
        if !haskey(mesh.send_maps, proc)
            mesh.send_maps[proc] = Int[]
        end

        append!(mesh.send_maps[proc], proc_cells)

        # Add cells to recv map of destination process
        if !haskey(mesh.recv_maps, proc)
            mesh.recv_maps[proc] = Int[]
        end

        # Send cell indices to destination process
        MPI.Send(proc_cells, proc, 0, comm)
    end

    # Receive cells from other processes
    for proc in 0:nprocs-1
        if proc == rank
            continue
        end

        # Check if there are cells to receive
        if MPI.Iprobe(proc, 0, comm)
            # Receive cell indices
            recv_cells = MPI.Recv(Int, proc, 0, comm)

            # Add cells to recv map
            if !haskey(mesh.recv_maps, proc)
                mesh.recv_maps[proc] = Int[]
            end

            append!(mesh.recv_maps[proc], recv_cells)
        end
    end
end

end # module DynamicLoadBalancing
