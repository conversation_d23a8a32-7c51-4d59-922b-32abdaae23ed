"""
    NonBlockingCommunication.jl

This module provides non-blocking MPI communication utilities for parallel computing.
"""

module NonBlockingCommunication

using MPI
using StaticArrays

# Import types from parent module
import ..JuliaFOAM: Field, Mesh, OptimizedMesh

"""
    exchange_halo_data_nonblocking!(field::Array{T}, mesh::Mesh, comm::MPI.Comm) where T

Exchange halo data between processes using non-blocking MPI communication.
This allows overlapping computation and communication for better performance.
"""
function exchange_halo_data_nonblocking!(field::Array{T}, mesh::Mesh, comm::MPI.Comm) where T
    # Get process rank and number of processes
    rank = MPI.Comm_rank(comm)
    nprocs = MPI.Comm_size(comm)

    if nprocs == 1
        # No need for communication with a single process
        return
    end

    # Get halo information
    send_halos = mesh.send_halos
    recv_halos = mesh.recv_halos

    # Initialize request arrays and buffers
    send_requests = MPI.Request[]
    recv_requests = MPI.Request[]
    send_buffers = Dict{Int, Vector{T}}()
    recv_buffers = Dict{Int, Vector{T}}()

    # Pre-allocate all buffers to avoid allocation during communication
    for (proc, halo_cells) in recv_halos
        # Skip self
        if proc == rank
            continue
        end

        # Create receive buffer
        recv_buffers[proc] = Array{T}(undef, length(halo_cells))
    end

    for (proc, halo_cells) in send_halos
        # Skip self
        if proc == rank
            continue
        end

        # Create send buffer
        send_buffers[proc] = [field[i] for i in halo_cells]
    end

    # Post non-blocking receives first to ensure they're ready
    for (proc, halo_cells) in recv_halos
        # Skip self
        if proc == rank
            continue
        end

        # Post non-blocking receive
        request = MPI.Irecv!(recv_buffers[proc], proc, 0, comm)
        push!(recv_requests, request)
    end

    # Post non-blocking sends
    for (proc, halo_cells) in send_halos
        # Skip self
        if proc == rank
            continue
        end

        # Post non-blocking send
        request = MPI.Isend(send_buffers[proc], proc, 0, comm)
        push!(send_requests, request)
    end

    # Start computation on local data while communication is in progress
    # This is where you would do work on the interior (non-halo) cells
    # For example, if we're computing gradients, we can compute them for interior cells now

    # Wait for all receives to complete
    statuses = MPI.Waitall!(recv_requests)

    # Update field with received data
    for (proc, halo_cells) in recv_halos
        # Skip self
        if proc == rank
            continue
        end

        # Update field with received data
        for (i, cell) in enumerate(halo_cells)
            field[cell] = recv_buffers[proc][i]
        end
    end

    # Wait for all sends to complete (cleanup)
    MPI.Waitall!(send_requests)
end

"""
    exchange_halo_data_nonblocking!(field::Vector{T}, mesh::Any) where T

Exchange halo data for a vector field using non-blocking communication.
Assumes mesh is a distributed mesh structure with halo information.

# Arguments
- `field`: The field data to exchange
- `mesh`: The optimized mesh with parallel decomposition information

# Returns
- `Tuple{Vector{MPI.Request}, Vector{MPI.Request}, Dict{Int, Vector{T}}}`: Send requests, receive requests, and receive buffers
"""
function exchange_halo_data_nonblocking!(field::Vector{T}, mesh::Any) where T
    # Get MPI communicator (use COMM_WORLD if not specified in mesh)
    comm = hasfield(typeof(mesh), :comm) ? mesh.comm : MPI.COMM_WORLD
    rank = MPI.Comm_rank(comm)
    nprocs = MPI.Comm_size(comm)

    if nprocs == 1
        # No communication needed for single process
        return (MPI.Request[], MPI.Request[], Dict{Int, Vector{T}}())
    end

    # Initialize request arrays and buffers
    send_requests = MPI.Request[]
    recv_requests = MPI.Request[]
    recv_buffers = Dict{Int, Vector{T}}()

    # Check if mesh has the necessary parallel decomposition information
    if !hasfield(typeof(mesh), :send_maps) || !hasfield(typeof(mesh), :recv_maps)
        @warn "OptimizedMesh does not have send_maps or recv_maps fields. Halo exchange cannot be performed."
        return (send_requests, recv_requests, recv_buffers)
    end

    # Post non-blocking receives first (to ensure they're ready when sends arrive)
    for (neighbor, halo_indices) in mesh.recv_maps
        if neighbor == rank
            continue  # Skip self
        end

        # Create receive buffer for this neighbor
        recv_buffers[neighbor] = Vector{T}(undef, length(halo_indices))

        # Post non-blocking receive
        request = MPI.Irecv!(recv_buffers[neighbor], neighbor, 0, comm)
        push!(recv_requests, request)
    end

    # Post non-blocking sends
    for (neighbor, send_indices) in mesh.send_maps
        if neighbor == rank
            continue  # Skip self
        end

        # Create send buffer with data from the specified indices
        send_buffer = field[send_indices]

        # Post non-blocking send
        request = MPI.Isend(send_buffer, neighbor, 0, comm)
        push!(send_requests, request)
    end

    return (send_requests, recv_requests, recv_buffers)
end

"""
    exchange_halo_data_nonblocking_vector!(field::Vector{SVector{3, T}}, mesh::Any) where T

Exchange halo data for a vector of SVectors (e.g., velocity field) using non-blocking communication.

# Arguments
- `field`: The vector field data to exchange
- `mesh`: The optimized mesh with parallel decomposition information

# Returns
- `Tuple{Vector{MPI.Request}, Vector{MPI.Request}, Dict{Int, Vector{SVector{3,T}}}}`: Send requests, receive requests, and receive buffers
"""
function exchange_halo_data_nonblocking_vector!(field::Vector{SVector{3, T}}, mesh::Any) where T
    # Get MPI communicator (use COMM_WORLD if not specified in mesh)
    comm = hasfield(typeof(mesh), :comm) ? mesh.comm : MPI.COMM_WORLD
    rank = MPI.Comm_rank(comm)
    nprocs = MPI.Comm_size(comm)

    if nprocs == 1
        # No communication needed for single process
        return (MPI.Request[], MPI.Request[], Dict{Int, Vector{SVector{3,T}}}())
    end

    # Initialize request arrays and buffers
    send_requests = MPI.Request[]
    recv_requests = MPI.Request[]
    recv_buffers = Dict{Int, Vector{SVector{3,T}}}()

    # Check if mesh has the necessary parallel decomposition information
    if !hasfield(typeof(mesh), :send_maps) || !hasfield(typeof(mesh), :recv_maps)
        @warn "OptimizedMesh does not have send_maps or recv_maps fields. Halo exchange cannot be performed."
        return (send_requests, recv_requests, recv_buffers)
    end

    # Post non-blocking receives first (to ensure they're ready when sends arrive)
    for (neighbor, halo_indices) in mesh.recv_maps
        if neighbor == rank
            continue  # Skip self
        end

        # Create receive buffer for this neighbor
        recv_buffers[neighbor] = Vector{SVector{3,T}}(undef, length(halo_indices))

        # Post non-blocking receive
        # We need to flatten the SVector data for MPI transmission
        flat_buffer = Vector{T}(undef, 3 * length(halo_indices))

        # Post non-blocking receive for flattened data
        request = MPI.Irecv!(flat_buffer, neighbor, 0, comm)

        # Store both the request and the flattened buffer for later processing
        push!(recv_requests, request)

        # Store the mapping from neighbor to flattened buffer for post-processing
        if !haskey(recv_buffers, neighbor)
            recv_buffers[neighbor] = (Vector{SVector{3,T}}(undef, length(halo_indices)), flat_buffer)
        end
    end

    # Post non-blocking sends
    for (neighbor, send_indices) in mesh.send_maps
        if neighbor == rank
            continue  # Skip self
        end

        # Create send buffer with data from the specified indices
        # We need to flatten the SVector data for MPI transmission
        flat_buffer = Vector{T}(undef, 3 * length(send_indices))

        # Flatten the data
        for (i, idx) in enumerate(send_indices)
            flat_buffer[3*(i-1) + 1] = field[idx][1]
            flat_buffer[3*(i-1) + 2] = field[idx][2]
            flat_buffer[3*(i-1) + 3] = field[idx][3]
        end

        # Post non-blocking send of flattened data
        request = MPI.Isend(flat_buffer, neighbor, 0, comm)
        push!(send_requests, request)
    end

    return (send_requests, recv_requests, recv_buffers)
end

"""
    wait_for_halo_exchange!(requests::Tuple{Vector{MPI.Request}, Vector{MPI.Request}, Dict{Int, Vector{T}}},
                           field::Vector{T}, mesh::Any) where T

Wait for non-blocking halo exchange to complete and update the field with received data.

# Arguments
- `requests`: Tuple of (send_requests, recv_requests, recv_buffers) from exchange_halo_data_nonblocking!
- `field`: The field to update with received data
- `mesh`: The optimized mesh with parallel decomposition information

# Returns
- `Nothing`
"""
function wait_for_halo_exchange!(requests::Tuple{Vector{MPI.Request}, Vector{MPI.Request}, Dict{Int, Vector{T}}},
                               field::Vector{T}, mesh::Any) where T
    send_requests, recv_requests, recv_buffers = requests

    # Wait for all receives to complete
    if !isempty(recv_requests)
        MPI.Waitall!(recv_requests)
    end

    # Update field with received data
    for (neighbor, recv_buffer) in recv_buffers
        # Check if mesh has the necessary parallel decomposition information
        if hasfield(typeof(mesh), :recv_maps)
            # Update field with received data
            field[mesh.recv_maps[neighbor]] .= recv_buffer
        else
            @warn "OptimizedMesh does not have recv_maps field. Cannot update field with received data."
        end
    end

    # Wait for all sends to complete (cleanup)
    if !isempty(send_requests)
        MPI.Waitall!(send_requests)
    end
end

"""
    all_reduce_nonblocking!(local_value::T, op::MPI.Op, comm::MPI.Comm) where T

Perform a non-blocking all-reduce operation.
"""
function all_reduce_nonblocking!(local_value::T, op::MPI.Op, comm::MPI.Comm) where T
    result = Ref{T}(local_value)
    request = MPI.Iallreduce!(Ref{T}(local_value), result, op, comm)
    return (request, result)
end

"""
    wait_for_all_reduce(request, result)

Wait for a non-blocking all-reduce operation to complete.
"""
function wait_for_all_reduce(request, result)
    MPI.Wait(request)
    return result[]
end

"""
    gather_nonblocking!(local_value::Vector{T}, root::Int, comm::MPI.Comm) where T

Perform a non-blocking gather operation.
"""
function gather_nonblocking!(local_value::Vector{T}, root::Int, comm::MPI.Comm) where T
    size = MPI.Comm_size(comm)
    rank = MPI.Comm_rank(comm)

    if rank == root
        result = Vector{Vector{T}}(undef, size)
        for i in 1:size
            result[i] = Vector{T}(undef, length(local_value))
        end

        request = MPI.Igatherv!(local_value, result, root, comm)
        return (request, result)
    else
        request = MPI.Igatherv!(local_value, nothing, root, comm)
        return (request, nothing)
    end
end

"""
    wait_for_gather(request, result)

Wait for a non-blocking gather operation to complete.
"""
function wait_for_gather(request, result)
    MPI.Wait(request)
    return result
end

"""
    wait_for_halo_exchange_vector!(requests::Tuple{Vector{MPI.Request}, Vector{MPI.Request}, Dict{Int, Tuple{Vector{SVector{3,T}}, Vector{T}}}},
                                 field::Vector{SVector{3,T}}, mesh::Any) where T

Wait for non-blocking vector field halo exchange to complete and update the field with received data.

# Arguments
- `requests`: Tuple of (send_requests, recv_requests, recv_buffers) from exchange_halo_data_nonblocking_vector!
- `field`: The vector field to update with received data
- `mesh`: The optimized mesh with parallel decomposition information

# Returns
- `Nothing`
"""
function wait_for_halo_exchange_vector!(requests::Tuple{Vector{MPI.Request}, Vector{MPI.Request}, Dict{Int, Tuple{Vector{SVector{3,T}}, Vector{T}}}},
                                      field::Vector{SVector{3,T}}, mesh::Any) where T
    send_requests, recv_requests, recv_buffers = requests

    # Wait for all receives to complete
    if !isempty(recv_requests)
        MPI.Waitall!(recv_requests)
    end

    # Update field with received data
    for (neighbor, (svec_buffer, flat_buffer)) in recv_buffers
        # Check if mesh has the necessary parallel decomposition information
        if hasfield(typeof(mesh), :recv_maps)
            # Reconstruct SVectors from flattened data
            halo_indices = mesh.recv_maps[neighbor]
            for (i, idx) in enumerate(halo_indices)
                field[idx] = SVector{3,T}(
                    flat_buffer[3*(i-1) + 1],
                    flat_buffer[3*(i-1) + 2],
                    flat_buffer[3*(i-1) + 3]
                )
            end
        else
            @warn "OptimizedMesh does not have recv_maps field. Cannot update field with received data."
        end
    end

    # Wait for all sends to complete (cleanup)
    if !isempty(send_requests)
        MPI.Waitall!(send_requests)
    end
end

# Export functions
export exchange_halo_data_nonblocking!, wait_for_halo_exchange!
export exchange_halo_data_nonblocking_vector!, wait_for_halo_exchange_vector!
export all_reduce_nonblocking!, wait_for_all_reduce
export gather_nonblocking!, wait_for_gather

end # module NonBlockingCommunication
