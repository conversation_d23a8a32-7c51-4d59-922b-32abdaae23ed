"""
    ParallelPerformance.jl

This module provides tools for monitoring and profiling the performance of parallel computations.
"""
module ParallelPerformance

using MPI
using StaticArrays
using LinearAlgebra
using Statistics
using Dates

export PerformanceMonitor, start_monitoring!, stop_monitoring!, get_performance_data
export ScalingTest, run_strong_scaling_test, run_weak_scaling_test, generate_scaling_report
export ParallelProfile, profile_region!, get_profile_data, print_profile_summary

"""
    PerformanceMonitor

Structure for monitoring the performance of parallel computations.

# Fields
- `start_time`: Start time of the monitoring period
- `end_time`: End time of the monitoring period
- `computation_time`: Time spent on computation
- `communication_time`: Time spent on communication
- `idle_time`: Time spent idle
- `flop_count`: Number of floating-point operations
- `memory_transfers`: Amount of memory transferred
- `active`: Whether monitoring is active
"""
struct PerformanceMonitor
    start_time::Dict{String, Float64}
    end_time::Dict{String, Float64}
    computation_time::Dict{String, Float64}
    communication_time::Dict{String, Float64}
    idle_time::Dict{String, Float64}
    flop_count::Dict{String, Int}
    memory_transfers::Dict{String, Int}
    active::Dict{String, Bool}
    
    function PerformanceMonitor()
        return new(
            Dict{String, Float64}(),
            Dict{String, Float64}(),
            Dict{String, Float64}(),
            Dict{String, Float64}(),
            Dict{String, Float64}(),
            Dict{String, Int}(),
            Dict{String, Int}(),
            Dict{String, Bool}()
        )
    end
end

"""
    start_monitoring!(monitor::PerformanceMonitor, region::String)

Start monitoring the performance of a region.

# Arguments
- `monitor`: The performance monitor
- `region`: The name of the region to monitor
"""
function start_monitoring!(monitor::PerformanceMonitor, region::String)
    monitor.start_time[region] = time()
    monitor.computation_time[region] = 0.0
    monitor.communication_time[region] = 0.0
    monitor.idle_time[region] = 0.0
    monitor.flop_count[region] = 0
    monitor.memory_transfers[region] = 0
    monitor.active[region] = true
end

"""
    stop_monitoring!(monitor::PerformanceMonitor, region::String)

Stop monitoring the performance of a region.

# Arguments
- `monitor`: The performance monitor
- `region`: The name of the region to monitor
"""
function stop_monitoring!(monitor::PerformanceMonitor, region::String)
    if !haskey(monitor.active, region) || !monitor.active[region]
        return
    end
    
    monitor.end_time[region] = time()
    monitor.active[region] = false
end

"""
    get_performance_data(monitor::PerformanceMonitor, region::String)

Get the performance data for a region.

# Arguments
- `monitor`: The performance monitor
- `region`: The name of the region

# Returns
- `Dict`: Performance data for the region
"""
function get_performance_data(monitor::PerformanceMonitor, region::String)
    if !haskey(monitor.start_time, region)
        return Dict{String, Any}()
    end
    
    total_time = monitor.end_time[region] - monitor.start_time[region]
    
    return Dict{String, Any}(
        "total_time" => total_time,
        "computation_time" => monitor.computation_time[region],
        "communication_time" => monitor.communication_time[region],
        "idle_time" => monitor.idle_time[region],
        "flop_count" => monitor.flop_count[region],
        "memory_transfers" => monitor.memory_transfers[region],
        "flops" => monitor.flop_count[region] / total_time,
        "bandwidth" => monitor.memory_transfers[region] / total_time,
        "computation_ratio" => monitor.computation_time[region] / total_time,
        "communication_ratio" => monitor.communication_time[region] / total_time,
        "idle_ratio" => monitor.idle_time[region] / total_time
    )
end

"""
    ScalingTest

Structure for running scaling tests.

# Fields
- `case_name`: Name of the test case
- `mesh_sizes`: Mesh sizes to test
- `num_processes`: Number of processes to test
- `num_threads`: Number of threads to test
- `results`: Test results
"""
struct ScalingTest
    case_name::String
    mesh_sizes::Vector{Tuple{Int, Int, Int}}
    num_processes::Vector{Int}
    num_threads::Vector{Int}
    results::Dict{String, Any}
    
    function ScalingTest(case_name::String, mesh_sizes::Vector{Tuple{Int, Int, Int}}, num_processes::Vector{Int}, num_threads::Vector{Int})
        return new(
            case_name,
            mesh_sizes,
            num_processes,
            num_threads,
            Dict{String, Any}()
        )
    end
end

"""
    run_strong_scaling_test(test::ScalingTest, solver_func::Function, args...)

Run a strong scaling test.

# Arguments
- `test`: The scaling test
- `solver_func`: The solver function to test
- `args...`: Additional arguments to pass to the solver function

# Returns
- `Dict{String, Any}`: Test results
"""
function run_strong_scaling_test(test::ScalingTest, solver_func::Function, args...)
    # Initialize MPI if not already initialized
    if !MPI.Initialized()
        MPI.Init()
    end
    
    # Get MPI info
    comm = MPI.COMM_WORLD
    rank = MPI.Comm_rank(comm)
    
    # Initialize results
    test.results["strong_scaling"] = Dict{String, Any}()
    
    # Run tests for each mesh size
    for mesh_size in test.mesh_sizes
        nx, ny, nz = mesh_size
        
        # Run tests for each process count
        for np in test.num_processes
            # Skip if number of processes doesn't match current run
            if np != MPI.Comm_size(comm)
                continue
            end
            
            # Run tests for each thread count
            for nt in test.num_threads
                # Set number of threads
                if Threads.nthreads() != nt
                    if rank == 0
                        println("Warning: Cannot change number of threads at runtime. Using $(Threads.nthreads()) threads instead of $nt.")
                    end
                end
                
                # Create mesh
                mesh = create_box_mesh(nx, ny, nz, 1.0, 1.0, 1.0)
                opt_mesh = convert_to_optimized_mesh(mesh)
                
                # Create simple partition
                n_cells = length(opt_mesh.cells)
                cell_partition = zeros(Int32, n_cells)
                for i in 1:n_cells
                    cell_partition[i] = (i % np)
                end
                
                # Add send/recv maps to the mesh
                if !hasfield(typeof(opt_mesh), :send_maps)
                    opt_mesh.send_maps = Dict{Int, Vector{Int}}()
                    opt_mesh.recv_maps = Dict{Int, Vector{Int}}()
                    
                    # Create simple send/recv maps
                    for proc in 0:np-1
                        if proc == rank
                            continue
                        end
                        
                        # Cells to send to this process
                        send_cells = findall(i -> cell_partition[i] == proc && i % 2 == rank % 2, 1:n_cells)
                        if !isempty(send_cells)
                            opt_mesh.send_maps[proc] = send_cells
                        end
                        
                        # Cells to receive from this process
                        recv_cells = findall(i -> cell_partition[i] == rank && i % 2 == proc % 2, 1:n_cells)
                        if !isempty(recv_cells)
                            opt_mesh.recv_maps[proc] = recv_cells
                        end
                    end
                end
                
                # Add comm field to the mesh
                opt_mesh.comm = comm
                
                # Create performance monitor
                monitor = PerformanceMonitor()
                
                # Run solver with timing
                start_monitoring!(monitor, "solver")
                solver_func(opt_mesh, args...)
                stop_monitoring!(monitor, "solver")
                
                # Get performance data
                perf_data = get_performance_data(monitor, "solver")
                
                # Store results
                key = "mesh_$(nx)x$(ny)x$(nz)_np$(np)_nt$(nt)"
                test.results["strong_scaling"][key] = perf_data
                
                # Print results
                if rank == 0
                    println("Strong scaling test: $key")
                    println("  Total time: $(perf_data["total_time"]) seconds")
                    println("  Computation time: $(perf_data["computation_time"]) seconds")
                    println("  Communication time: $(perf_data["communication_time"]) seconds")
                    println("  Idle time: $(perf_data["idle_time"]) seconds")
                    println("  FLOPS: $(perf_data["flops"])")
                    println("  Bandwidth: $(perf_data["bandwidth"]) bytes/second")
                end
            end
        end
    end
    
    return test.results
end

"""
    run_weak_scaling_test(test::ScalingTest, solver_func::Function, args...)

Run a weak scaling test.

# Arguments
- `test`: The scaling test
- `solver_func`: The solver function to test
- `args...`: Additional arguments to pass to the solver function

# Returns
- `Dict{String, Any}`: Test results
"""
function run_weak_scaling_test(test::ScalingTest, solver_func::Function, args...)
    # Initialize MPI if not already initialized
    if !MPI.Initialized()
        MPI.Init()
    end
    
    # Get MPI info
    comm = MPI.COMM_WORLD
    rank = MPI.Comm_rank(comm)
    
    # Initialize results
    test.results["weak_scaling"] = Dict{String, Any}()
    
    # Run tests for each process count
    for np in test.num_processes
        # Skip if number of processes doesn't match current run
        if np != MPI.Comm_size(comm)
            continue
        end
        
        # Run tests for each thread count
        for nt in test.num_threads
            # Set number of threads
            if Threads.nthreads() != nt
                if rank == 0
                    println("Warning: Cannot change number of threads at runtime. Using $(Threads.nthreads()) threads instead of $nt.")
                end
            end
            
            # Calculate mesh size based on number of processes
            # For weak scaling, we want to keep the problem size per process constant
            base_nx, base_ny, base_nz = test.mesh_sizes[1]
            nx = base_nx * np^(1/3)
            ny = base_ny * np^(1/3)
            nz = base_nz * np^(1/3)
            
            # Create mesh
            mesh = create_box_mesh(Int(ceil(nx)), Int(ceil(ny)), Int(ceil(nz)), 1.0, 1.0, 1.0)
            opt_mesh = convert_to_optimized_mesh(mesh)
            
            # Create simple partition
            n_cells = length(opt_mesh.cells)
            cell_partition = zeros(Int32, n_cells)
            for i in 1:n_cells
                cell_partition[i] = (i % np)
            end
            
            # Add send/recv maps to the mesh
            if !hasfield(typeof(opt_mesh), :send_maps)
                opt_mesh.send_maps = Dict{Int, Vector{Int}}()
                opt_mesh.recv_maps = Dict{Int, Vector{Int}}()
                
                # Create simple send/recv maps
                for proc in 0:np-1
                    if proc == rank
                        continue
                    end
                    
                    # Cells to send to this process
                    send_cells = findall(i -> cell_partition[i] == proc && i % 2 == rank % 2, 1:n_cells)
                    if !isempty(send_cells)
                        opt_mesh.send_maps[proc] = send_cells
                    end
                    
                    # Cells to receive from this process
                    recv_cells = findall(i -> cell_partition[i] == rank && i % 2 == proc % 2, 1:n_cells)
                    if !isempty(recv_cells)
                        opt_mesh.recv_maps[proc] = recv_cells
                    end
                end
            end
            
            # Add comm field to the mesh
            opt_mesh.comm = comm
            
            # Create performance monitor
            monitor = PerformanceMonitor()
            
            # Run solver with timing
            start_monitoring!(monitor, "solver")
            solver_func(opt_mesh, args...)
            stop_monitoring!(monitor, "solver")
            
            # Get performance data
            perf_data = get_performance_data(monitor, "solver")
            
            # Store results
            key = "mesh_$(Int(ceil(nx)))x$(Int(ceil(ny)))x$(Int(ceil(nz)))_np$(np)_nt$(nt)"
            test.results["weak_scaling"][key] = perf_data
            
            # Print results
            if rank == 0
                println("Weak scaling test: $key")
                println("  Total time: $(perf_data["total_time"]) seconds")
                println("  Computation time: $(perf_data["computation_time"]) seconds")
                println("  Communication time: $(perf_data["communication_time"]) seconds")
                println("  Idle time: $(perf_data["idle_time"]) seconds")
                println("  FLOPS: $(perf_data["flops"])")
                println("  Bandwidth: $(perf_data["bandwidth"]) bytes/second")
            end
        end
    end
    
    return test.results
end

"""
    generate_scaling_report(test::ScalingTest, output_file::String)

Generate a report of scaling test results.

# Arguments
- `test`: The scaling test
- `output_file`: The output file path

# Returns
- `String`: Path to the generated report
"""
function generate_scaling_report(test::ScalingTest, output_file::String)
    # Initialize MPI if not already initialized
    if !MPI.Initialized()
        MPI.Init()
    end
    
    # Get MPI info
    comm = MPI.COMM_WORLD
    rank = MPI.Comm_rank(comm)
    
    # Only rank 0 generates the report
    if rank != 0
        return output_file
    end
    
    # Open output file
    open(output_file, "w") do f
        # Write header
        write(f, "# Scaling Test Report\n\n")
        write(f, "- Case: $(test.case_name)\n")
        write(f, "- Date: $(Dates.format(now(), "yyyy-mm-dd HH:MM:SS"))\n\n")
        
        # Write strong scaling results
        if haskey(test.results, "strong_scaling")
            write(f, "## Strong Scaling Results\n\n")
            
            # Create table header
            write(f, "| Mesh Size | Processes | Threads | Total Time (s) | Computation Time (s) | Communication Time (s) | Idle Time (s) | FLOPS | Bandwidth (B/s) |\n")
            write(f, "|-----------|-----------|---------|---------------|----------------------|------------------------|---------------|-------|----------------|\n")
            
            # Write table rows
            for (key, data) in test.results["strong_scaling"]
                # Parse key
                m = match(r"mesh_(\d+)x(\d+)x(\d+)_np(\d+)_nt(\d+)", key)
                if m !== nothing
                    nx, ny, nz, np, nt = parse.(Int, m.captures)
                    
                    # Write row
                    write(f, "| $(nx)x$(ny)x$(nz) | $np | $nt | $(round(data["total_time"], digits=4)) | $(round(data["computation_time"], digits=4)) | $(round(data["communication_time"], digits=4)) | $(round(data["idle_time"], digits=4)) | $(round(data["flops"], digits=2)) | $(round(data["bandwidth"], digits=2)) |\n")
                end
            end
            
            write(f, "\n")
        end
        
        # Write weak scaling results
        if haskey(test.results, "weak_scaling")
            write(f, "## Weak Scaling Results\n\n")
            
            # Create table header
            write(f, "| Mesh Size | Processes | Threads | Total Time (s) | Computation Time (s) | Communication Time (s) | Idle Time (s) | FLOPS | Bandwidth (B/s) |\n")
            write(f, "|-----------|-----------|---------|---------------|----------------------|------------------------|---------------|-------|----------------|\n")
            
            # Write table rows
            for (key, data) in test.results["weak_scaling"]
                # Parse key
                m = match(r"mesh_(\d+)x(\d+)x(\d+)_np(\d+)_nt(\d+)", key)
                if m !== nothing
                    nx, ny, nz, np, nt = parse.(Int, m.captures)
                    
                    # Write row
                    write(f, "| $(nx)x$(ny)x$(nz) | $np | $nt | $(round(data["total_time"], digits=4)) | $(round(data["computation_time"], digits=4)) | $(round(data["communication_time"], digits=4)) | $(round(data["idle_time"], digits=4)) | $(round(data["flops"], digits=2)) | $(round(data["bandwidth"], digits=2)) |\n")
                end
            end
            
            write(f, "\n")
        end
    end
    
    return output_file
end

"""
    ParallelProfile

Structure for profiling parallel code regions.

# Fields
- `regions`: Dictionary of profiled regions
- `active_regions`: Stack of active regions
- `start_times`: Dictionary of region start times
"""
mutable struct ParallelProfile
    regions::Dict{String, Dict{String, Any}}
    active_regions::Vector{String}
    start_times::Dict{String, Float64}
    
    function ParallelProfile()
        return new(
            Dict{String, Dict{String, Any}}(),
            String[],
            Dict{String, Float64}()
        )
    end
end

"""
    profile_region!(profile::ParallelProfile, region::String, type::Symbol = :computation)

Profile a region of code.

# Arguments
- `profile`: The parallel profile
- `region`: The name of the region
- `type`: The type of region (:computation, :communication, or :idle)

# Returns
- `Nothing`
"""
function profile_region!(profile::ParallelProfile, region::String, type::Symbol = :computation)
    # Check if region is already being profiled
    if region in profile.active_regions
        # End profiling for this region
        idx = findfirst(==(region), profile.active_regions)
        if idx !== nothing
            deleteat!(profile.active_regions, idx)
        end
        
        # Calculate elapsed time
        elapsed = time() - profile.start_times[region]
        
        # Update region data
        if !haskey(profile.regions, region)
            profile.regions[region] = Dict{String, Any}(
                "count" => 0,
                "total_time" => 0.0,
                "computation_time" => 0.0,
                "communication_time" => 0.0,
                "idle_time" => 0.0
            )
        end
        
        profile.regions[region]["count"] += 1
        profile.regions[region]["total_time"] += elapsed
        
        if type == :computation
            profile.regions[region]["computation_time"] += elapsed
        elseif type == :communication
            profile.regions[region]["communication_time"] += elapsed
        elseif type == :idle
            profile.regions[region]["idle_time"] += elapsed
        end
    else
        # Start profiling this region
        push!(profile.active_regions, region)
        profile.start_times[region] = time()
    end
    
    return nothing
end

"""
    get_profile_data(profile::ParallelProfile, region::String)

Get profile data for a region.

# Arguments
- `profile`: The parallel profile
- `region`: The name of the region

# Returns
- `Dict{String, Any}`: Profile data for the region
"""
function get_profile_data(profile::ParallelProfile, region::String)
    if !haskey(profile.regions, region)
        return Dict{String, Any}()
    end
    
    return profile.regions[region]
end

"""
    print_profile_summary(profile::ParallelProfile)

Print a summary of the profile data.

# Arguments
- `profile`: The parallel profile

# Returns
- `Nothing`
"""
function print_profile_summary(profile::ParallelProfile)
    println("Parallel Profile Summary:")
    println("-------------------------")
    
    for (region, data) in profile.regions
        println("Region: $region")
        println("  Count: $(data["count"])")
        println("  Total time: $(data["total_time"]) seconds")
        println("  Computation time: $(data["computation_time"]) seconds ($(round(100 * data["computation_time"] / data["total_time"], digits=2))%)")
        println("  Communication time: $(data["communication_time"]) seconds ($(round(100 * data["communication_time"] / data["total_time"], digits=2))%)")
        println("  Idle time: $(data["idle_time"]) seconds ($(round(100 * data["idle_time"] / data["total_time"], digits=2))%)")
        println()
    end
    
    return nothing
end

end # module ParallelPerformance
