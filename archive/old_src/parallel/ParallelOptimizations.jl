"""
    ParallelOptimizations.jl

This module provides parallel implementations of optimized operations for JuliaFOAM.
It implements hierarchical parallelism combining MPI for distributed memory and
threading for shared memory parallelism.
"""

using MPI
using StaticArrays
using SparseArrays
using LinearAlgebra
using LoopVectorization
using Base.Threads

# Import non-blocking communication module
import ..NonBlockingCommunication

# Import types from parent module
import ..JuliaFOAM: OptimizedMesh, Field, SolverSettings

"""
    hierarchical_solve_simple!(U, p, mesh, properties, under_relaxation_U, under_relaxation_p, max_iterations, tolerance)

Solve the SIMPLE algorithm using hierarchical parallelism (MPI + threading).

# Arguments
- `U`: Velocity field
- `p`: Pressure field
- `mesh`: The optimized mesh
- `properties`: Fluid properties
- `under_relaxation_U`: Under-relaxation factor for velocity
- `under_relaxation_p`: Under-relaxation factor for pressure
- `max_iterations`: Maximum number of iterations
- `tolerance`: Convergence tolerance

# Returns
- `Tuple{Field,Field,Int,Float64,Float64}`: Updated velocity and pressure fields, iterations, U residual, p residual
"""
function hierarchical_solve_simple!(U, p, mesh, properties, under_relaxation_U=0.7, under_relaxation_p=0.3, max_iterations=1000, tolerance=1e-6)
    # Get MPI info
    comm = hasfield(typeof(mesh), :comm) ? mesh.comm : MPI.COMM_WORLD
    rank = MPI.Comm_rank(comm)
    nprocs = MPI.Comm_size(comm)

    # Get thread info
    num_threads = Threads.nthreads()

    # Determine optimal thread/MPI balance based on hardware topology
    threads_per_node = min(num_threads, Sys.CPU_THREADS ÷ nprocs)

    # Create thread partitioning for local cells
    n_local_cells = hasfield(typeof(mesh), :local_indices) ? length(mesh.local_indices) : length(mesh.cells)
    cells_per_thread = cld(n_local_cells, threads_per_node)
    thread_cell_ranges = [((i-1)*cells_per_thread + 1):min(i*cells_per_thread, n_local_cells) for i in 1:threads_per_node]

    # Initialize residuals
    U_residual = 1.0
    p_residual = 1.0

    # Main SIMPLE loop
    for iter in 1:max_iterations
        # Store old fields
        U_old = copy(U.internal_field)
        p_old = copy(p.internal_field)

        # 1. Momentum predictor with threading for local computations
        momentum_predictor_parallel!(U, p, mesh, properties, under_relaxation_U, thread_cell_ranges)

        # 2. Synchronize velocity field across processes
        if nprocs > 1
            requests = NonBlockingCommunication.exchange_halo_data_nonblocking_vector!(U.internal_field, mesh)
        end

        # 3. Compute fluxes while communication is happening (overlap)
        compute_fluxes_threaded!(mesh, U, thread_cell_ranges)

        # 4. Wait for velocity synchronization to complete
        if nprocs > 1
            NonBlockingCommunication.wait_for_halo_exchange_vector!(requests, U.internal_field, mesh)
        end

        # 5. Pressure equation with threading for local computations
        solve_pressure_equation_parallel!(U, p, mesh, properties, under_relaxation_p, thread_cell_ranges)

        # 6. Synchronize pressure field across processes
        if nprocs > 1
            requests = NonBlockingCommunication.exchange_halo_data_nonblocking!(p.internal_field, mesh)
        end

        # 7. Prepare for velocity correction while communication is happening
        prepare_velocity_correction_threaded!(mesh, thread_cell_ranges)

        # 8. Wait for pressure synchronization to complete
        if nprocs > 1
            NonBlockingCommunication.wait_for_halo_exchange!(requests, p.internal_field, mesh)
        end

        # 9. Velocity correction with threading
        correct_velocity_parallel!(U, p, mesh, under_relaxation_U, thread_cell_ranges)

        # 10. Check convergence (global across all processes)
        local_U_residual = norm(U.internal_field - U_old) / max(norm(U.internal_field), 1e-10)
        local_p_residual = norm(p.internal_field - p_old) / max(norm(p.internal_field), 1e-10)

        if nprocs > 1
            U_residual = MPI.Allreduce(local_U_residual, MPI.MAX, comm)
            p_residual = MPI.Allreduce(local_p_residual, MPI.MAX, comm)
        else
            U_residual = local_U_residual
            p_residual = local_p_residual
        end

        if rank == 0
            println("Iteration $iter: U residual = $U_residual, p residual = $p_residual")
        end

        if max(U_residual, p_residual) < tolerance
            if rank == 0
                println("Converged in $iter iterations")
            end
            return U, p, iter, U_residual, p_residual
        end
    end

    if rank == 0
        println("Maximum iterations reached. Final residuals: U = $U_residual, p = $p_residual")
    end

    return U, p, max_iterations, U_residual, p_residual
end

"""
    momentum_predictor_parallel!(U, p, mesh, properties, under_relaxation_U, thread_cell_ranges)

Momentum predictor step with parallel execution.

# Arguments
- `U`: Velocity field
- `p`: Pressure field
- `mesh`: The optimized mesh
- `properties`: Fluid properties
- `under_relaxation_U`: Under-relaxation factor for velocity
- `thread_cell_ranges`: Cell ranges for each thread
"""
function momentum_predictor_parallel!(U, p, mesh, properties, under_relaxation_U, thread_cell_ranges)
    # 1. Build momentum matrix in parallel
    A, b = build_momentum_matrix_parallel(U, p, mesh, properties, under_relaxation_U)

    # 2. Solve momentum equation with parallel solver
    solver_settings = SolverSettings(:bicgstab, :ilu, 1e-6, 1000)

    # Solve for each velocity component in parallel
    Threads.@threads for i in 1:3
        # Extract component from vector field
        n = length(U.internal_field)
        component = [U.internal_field[j][i] for j in 1:n]

        # Extract RHS for this component
        b_component = [b[(j-1)*3 + i] for j in 1:n]

        # Solve
        solve_parallel!(A, component, b_component, solver_settings)

        # Update velocity field
        for j in 1:n
            U.internal_field[j] = SVector{3,Float64}(
                i == 1 ? component[j] : U.internal_field[j][1],
                i == 2 ? component[j] : U.internal_field[j][2],
                i == 3 ? component[j] : U.internal_field[j][3]
            )
        end
    end
end

"""
    compute_fluxes_threaded!(mesh, U, thread_cell_ranges)

Compute face fluxes in parallel using threading.

# Arguments
- `mesh`: The optimized mesh
- `U`: Velocity field
- `thread_cell_ranges`: Cell ranges for each thread
"""
function compute_fluxes_threaded!(mesh, U, thread_cell_ranges)
    # Allocate face flux array
    n_faces = length(mesh.faces)
    face_fluxes = zeros(n_faces)

    # Compute fluxes in parallel
    Threads.@threads for thread_id in 1:length(thread_cell_ranges)
        cell_range = thread_cell_ranges[thread_id]

        for cell_idx in cell_range
            for face_idx in mesh.cell_faces[cell_idx]
                # Get face owner and neighbor
                owner = mesh.face_owner[face_idx]
                neighbor = mesh.face_neighbour[face_idx]

                # Skip if this cell is not the owner (to avoid double counting)
                if owner != cell_idx
                    continue
                end

                # Compute face velocity
                if neighbor > 0
                    # Internal face - average of owner and neighbor
                    face_velocity = 0.5 * (U.internal_field[owner] + U.internal_field[neighbor])
                else
                    # Boundary face - use owner value
                    face_velocity = U.internal_field[owner]
                end

                # Compute flux
                face_fluxes[face_idx] = dot(face_velocity, mesh.faces[face_idx].area)
            end
        end
    end

    return face_fluxes
end

"""
    solve_pressure_equation_parallel!(U, p, mesh, properties, under_relaxation_p, thread_cell_ranges)

Solve the pressure equation in parallel.

# Arguments
- `U`: Velocity field
- `p`: Pressure field
- `mesh`: The optimized mesh
- `properties`: Fluid properties
- `under_relaxation_p`: Under-relaxation factor for pressure
- `thread_cell_ranges`: Cell ranges for each thread
"""
function solve_pressure_equation_parallel!(U, p, mesh, properties, under_relaxation_p, thread_cell_ranges)
    # 1. Build pressure equation in parallel
    A, b = build_pressure_equation_parallel(U, p, mesh, properties, under_relaxation_p)

    # 2. Solve pressure equation with parallel solver
    solver_settings = SolverSettings(:pcg, :amg, 1e-6, 1000)

    # Solve
    solve_parallel!(A, p.internal_field, b, solver_settings)
end

"""
    prepare_velocity_correction_threaded!(mesh, thread_cell_ranges)

Prepare for velocity correction in parallel.

# Arguments
- `mesh`: The optimized mesh
- `thread_cell_ranges`: Cell ranges for each thread
"""
function prepare_velocity_correction_threaded!(mesh, thread_cell_ranges)
    # This function can be used to prepare any data needed for velocity correction
    # For example, computing face gradients or other intermediate values
    # For now, it's a placeholder
end

"""
    correct_velocity_parallel!(U, p, mesh, under_relaxation_U, thread_cell_ranges)

Correct velocity in parallel.

# Arguments
- `U`: Velocity field
- `p`: Pressure field
- `mesh`: The optimized mesh
- `under_relaxation_U`: Under-relaxation factor for velocity
- `thread_cell_ranges`: Cell ranges for each thread
"""
function correct_velocity_parallel!(U, p, mesh, under_relaxation_U, thread_cell_ranges)
    # Calculate pressure gradient
    grad_p = calculate_gradient_parallel(p.internal_field, mesh)

    # Correct velocity in parallel
    Threads.@threads for thread_id in 1:length(thread_cell_ranges)
        cell_range = thread_cell_ranges[thread_id]

        for cell_idx in cell_range
            # Get diagonal coefficient
            A_p = mesh.cells[cell_idx].volume / under_relaxation_U

            # Correct velocity
            U.internal_field[cell_idx] -= grad_p[cell_idx] / A_p
        end
    end
end

"""
    calculate_gradient_parallel(field, mesh)

Calculate gradient of a scalar field in parallel.

# Arguments
- `field`: Scalar field
- `mesh`: The optimized mesh

# Returns
- `Vector{SVector{3,Float64}}`: Gradient field
"""
function calculate_gradient_parallel(field, mesh)
    n_cells = length(mesh.cells)
    grad = Vector{SVector{3,Float64}}(undef, n_cells)

    # Compute gradient using Gauss theorem
    Threads.@threads for i in 1:n_cells
        grad[i] = SVector{3,Float64}(0.0, 0.0, 0.0)
        cell_volume = mesh.cells[i].volume

        for face_idx in mesh.cell_faces[i]
            face = mesh.faces[face_idx]

            # Determine if this cell is the owner or neighbor
            is_owner = (face.owner == i)

            # Get face value
            if face.neighbour > 0
                # Internal face - interpolate between owner and neighbor
                face_value = 0.5 * (field[face.owner] + field[face.neighbour])
            else
                # Boundary face - use owner value
                face_value = field[face.owner]
            end

            # Add contribution to gradient
            sign = is_owner ? 1.0 : -1.0
            grad[i] += sign * face_value * face.area / cell_volume
        end
    end

    return grad
end

"""
    build_momentum_matrix_parallel(U, p, mesh, properties, under_relaxation_U)

Build the momentum matrix in parallel.

# Arguments
- `U`: Velocity field
- `p`: Pressure field
- `mesh`: The optimized mesh
- `properties`: Fluid properties
- `under_relaxation_U`: Under-relaxation factor for velocity

# Returns
- `Tuple{SparseMatrixCSC,Vector{Float64}}`: Coefficient matrix and right-hand side
"""
function build_momentum_matrix_parallel(U, p, mesh, properties, under_relaxation_U)
    # This is a placeholder implementation
    # In a real implementation, we would build the momentum matrix in parallel

    # For now, return a dummy matrix and right-hand side
    n = length(mesh.cells)
    A = spdiagm(0 => ones(n*3))
    b = zeros(n*3)

    return A, b
end

"""
    build_pressure_equation_parallel(U, p, mesh, properties, under_relaxation_p)

Build the pressure equation in parallel.

# Arguments
- `U`: Velocity field
- `p`: Pressure field
- `mesh`: The optimized mesh
- `properties`: Fluid properties
- `under_relaxation_p`: Under-relaxation factor for pressure

# Returns
- `Tuple{SparseMatrixCSC,Vector{Float64}}`: Coefficient matrix and right-hand side
"""
function build_pressure_equation_parallel(U, p, mesh, properties, under_relaxation_p)
    # This is a placeholder implementation
    # In a real implementation, we would build the pressure equation in parallel

    # For now, return a dummy matrix and right-hand side
    n = length(mesh.cells)
    A = spdiagm(0 => ones(n))
    b = zeros(n)

    return A, b
end

"""
    solve_parallel!(A, x, b, solver_settings)

Solve a linear system in parallel.

# Arguments
- `A`: Coefficient matrix
- `x`: Solution vector (initial guess and result)
- `b`: Right-hand side
- `solver_settings`: Solver settings
"""
function solve_parallel!(A, x, b, solver_settings)
    # This is a placeholder implementation
    # In a real implementation, we would solve the system in parallel

    # For now, just set x to b
    x .= b
end

# Export functions
export parallel_matrix_vector_product!, parallel_gradient_calculation!
export parallel_divergence_calculation!, parallel_solve_linear_system!
export parallel_gradient_operator, parallel_simple_iteration!
export hierarchical_solve_simple!

# Constants for performance tuning
const CACHE_LINE_SIZE = 64  # Typical cache line size in bytes
const L1_CACHE_SIZE = 32 * 1024  # Typical L1 cache size in bytes
const L2_CACHE_SIZE = 256 * 1024  # Typical L2 cache size in bytes
const L3_CACHE_SIZE = 8 * 1024 * 1024  # Typical L3 cache size in bytes

"""
    PerformanceMetrics

Structure to hold performance metrics for load balancing.
"""
struct PerformanceMetrics
    # Computation time per process
    proc_times::Vector{Float64}

    # Communication time per process
    comm_times::Vector{Float64}

    # Cell weights (computational intensity)
    cell_weights::Vector{Float64}

    # Face weights (communication intensity)
    face_weights::Vector{Float64}

    # Timestamp
    timestamp::Float64
end

"""
    create_performance_metrics(mesh::Any, n_procs::Int)

Create initial performance metrics for a mesh.

# Arguments
- `mesh`: The optimized mesh
- `n_procs`: Number of processes

# Returns
- `PerformanceMetrics`: Initial performance metrics
"""
function create_performance_metrics(mesh::Any, n_procs::Int)
    # Initialize with equal weights
    proc_times = ones(n_procs)
    comm_times = ones(n_procs)

    # Initialize cell weights based on volume (larger cells have more work)
    n_cells = length(mesh.cells)
    cell_weights = [mesh.cells[i].volume for i in 1:n_cells]

    # Normalize cell weights
    if !isempty(cell_weights)
        cell_weights ./= mean(cell_weights)
    end

    # Initialize face weights based on area (larger faces have more communication)
    n_faces = length(mesh.faces)
    face_weights = [norm(mesh.faces[i].area) for i in 1:n_faces]

    # Normalize face weights
    if !isempty(face_weights)
        face_weights ./= mean(face_weights)
    end

    return PerformanceMetrics(proc_times, comm_times, cell_weights, face_weights, time())
end

"""
    update_performance_metrics!(metrics::PerformanceMetrics, proc_times::Vector{Float64}, comm_times::Vector{Float64})

Update performance metrics with new timing information.

# Arguments
- `metrics`: Performance metrics to update
- `proc_times`: New computation times per process
- `comm_times`: New communication times per process

# Returns
- `PerformanceMetrics`: Updated performance metrics
"""
function update_performance_metrics!(metrics::PerformanceMetrics, proc_times::Vector{Float64}, comm_times::Vector{Float64})
    metrics.proc_times .= proc_times
    metrics.comm_times .= comm_times
    metrics.timestamp = time()
    return metrics
end

"""
    optimize_load_balance(mesh::Any, cell_partition::Vector{Int32}, metrics::PerformanceMetrics, n_procs::Int)

Optimize load balance based on performance metrics.

# Arguments
- `mesh`: The optimized mesh
- `cell_partition`: Current cell partition
- `metrics`: Performance metrics
- `n_procs`: Number of processes

# Returns
- `Vector{Int32}`: Optimized cell partition
"""
function optimize_load_balance(mesh::Any, cell_partition::Vector{Int32}, metrics::PerformanceMetrics, n_procs::Int)
    # Calculate load imbalance
    avg_time = mean(metrics.proc_times)
    max_time = maximum(metrics.proc_times)
    load_imbalance = max_time / avg_time - 1.0

    # If load imbalance is significant, rebalance
    if load_imbalance > 0.1  # 10% threshold
        # Identify overloaded and underloaded processes
        overloaded = findall(t -> t > 1.05 * avg_time, metrics.proc_times)
        underloaded = findall(t -> t < 0.95 * avg_time, metrics.proc_times)

        # Create a copy of the cell partition to modify
        new_partition = copy(cell_partition)

        # For each overloaded process, move cells to underloaded processes
        for over_proc in overloaded
            for under_proc in underloaded
                # Find boundary cells between these processes
                boundary_cells = find_boundary_cells(mesh, cell_partition, over_proc-1, under_proc-1)

                # Calculate workload to transfer
                workload_diff = metrics.proc_times[over_proc] - metrics.proc_times[under_proc]
                cells_to_move = estimate_cells_to_move(boundary_cells, workload_diff, metrics.cell_weights)

                # Update partition
                for cell in cells_to_move
                    new_partition[cell] = Int32(under_proc-1)
                end

                # Update estimated process times
                cell_time = sum(metrics.cell_weights[cells_to_move])
                metrics.proc_times[over_proc] -= cell_time
                metrics.proc_times[under_proc] += cell_time

                # Check if underloaded process is now balanced
                if metrics.proc_times[under_proc] >= 0.95 * avg_time
                    deleteat!(underloaded, findfirst(==(under_proc), underloaded))
                end

                # Check if overloaded process is now balanced
                if metrics.proc_times[over_proc] <= 1.05 * avg_time
                    break
                end
            end
        end

        return new_partition
    else
        # No rebalancing needed
        return cell_partition
    end
end

"""
    find_boundary_cells(mesh::Any, cell_partition::Vector{Int32}, proc1::Int, proc2::Int)

Find cells at the boundary between two processes.

# Arguments
- `mesh`: The optimized mesh
- `cell_partition`: Cell partition
- `proc1`: First process
- `proc2`: Second process

# Returns
- `Vector{Int}`: Boundary cells belonging to proc1
"""
function find_boundary_cells(mesh::Any, cell_partition::Vector{Int32}, proc1::Int, proc2::Int)
    boundary_cells = Int[]

    # Iterate through all cells belonging to proc1
    for (i, part) in enumerate(cell_partition)
        if part == proc1
            # Check if any neighbor belongs to proc2
            for neighbor in mesh.cell_neighbors[i]
                if cell_partition[neighbor] == proc2
                    push!(boundary_cells, i)
                    break
                end
            end
        end
    end

    return boundary_cells
end

"""
    estimate_cells_to_move(boundary_cells::Vector{Int}, workload_diff::Float64, cell_weights::Vector{Float64})

Estimate which cells to move to balance workload.

# Arguments
- `boundary_cells`: Boundary cells that can be moved
- `workload_diff`: Workload difference to balance
- `cell_weights`: Cell weights (computational intensity)

# Returns
- `Vector{Int}`: Cells to move
"""
function estimate_cells_to_move(boundary_cells::Vector{Int}, workload_diff::Float64, cell_weights::Vector{Float64})
    # Sort boundary cells by weight (move lighter cells first to minimize communication)
    sorted_cells = sort(boundary_cells, by=i -> cell_weights[i])

    # Select cells to move
    cells_to_move = Int[]
    current_workload = 0.0

    for cell in sorted_cells
        if current_workload >= workload_diff
            break
        end

        push!(cells_to_move, cell)
        current_workload += cell_weights[cell]
    end

    return cells_to_move
end

"""
    create_topology_aware_partition(mesh::Any, n_procs::Int, n_nodes::Int)

Create a topology-aware partition that minimizes inter-node communication.

# Arguments
- `mesh`: The optimized mesh
- `n_procs`: Total number of processes
- `n_nodes`: Number of compute nodes

# Returns
- `Vector{Int32}`: Topology-aware cell partition
"""
function create_topology_aware_partition(mesh::Any, n_procs::Int, n_nodes::Int)
    # First, create a node-level partition
    procs_per_node = div(n_procs, n_nodes)
    node_partition = decompose_mesh_metis(mesh, n_nodes)

    # Then, for each node, create a process-level partition
    cell_partition = similar(node_partition)

    for node in 0:n_nodes-1
        # Get cells assigned to this node
        node_cells = findall(p -> p == node, node_partition)

        if isempty(node_cells)
            continue
        end

        # Create a submesh for this node
        submesh = create_submesh(mesh, node_cells)

        # Partition the submesh
        subpartition = decompose_mesh_metis(submesh, procs_per_node)

        # Map back to global cell indices
        for (i, cell) in enumerate(node_cells)
            cell_partition[cell] = node * procs_per_node + subpartition[i]
        end
    end

    return cell_partition
end

"""
    create_submesh(mesh::Any, cell_indices::Vector{Int})

Create a submesh containing only the specified cells.

# Arguments
- `mesh`: The optimized mesh
- `cell_indices`: Indices of cells to include

# Returns
- `OptimizedMesh`: Submesh
"""
function create_submesh(mesh::Any, cell_indices::Vector{Int})
    # This is a placeholder implementation
    # In a real implementation, we would create a proper submesh
    return mesh
end

"""
    decompose_mesh_metis(mesh::Any, n_parts::Int)

Decompose mesh using METIS.

# Arguments
- `mesh`: The optimized mesh
- `n_parts`: Number of partitions

# Returns
- `Vector{Int32}`: Cell partition
"""
function decompose_mesh_metis(mesh::Any, n_parts::Int)
    # This is a placeholder implementation
    # In a real implementation, we would use METIS to partition the mesh
    n_cells = length(mesh.cells)
    return rand(0:n_parts-1, n_cells)
end

# Export load balancing functions
export create_performance_metrics, update_performance_metrics!
export optimize_load_balance, create_topology_aware_partition

"""
    parallel_matrix_vector_product!(y::Vector{Float64}, A::SparseMatrixCSC{Float64, Int},
                                   x::Vector{Float64}, mesh::Any)

Compute y = A*x in parallel using MPI.
"""
function parallel_matrix_vector_product!(y::Vector{Float64}, A::SparseMatrixCSC{Float64, Int},
                                        x::Vector{Float64}, mesh::Any)
    # First, exchange halo data for x
    # TODO: The following halo exchange logic is commented out because
    # NonBlockingCommunication.exchange_halo_data_nonblocking! is currently a placeholder
    # and does not return requests/recv_buffers. This needs to be revisited when halo exchange is fully implemented.
    # requests, recv_buffers = NonBlockingCommunication.exchange_halo_data_nonblocking!(x, mesh)
    NonBlockingCommunication.exchange_halo_data_nonblocking!(x, mesh) # Call the placeholder

    # While communication is happening, compute the local part of the matrix-vector product
    # This overlaps computation and communication
    # local_indices = mesh.local_indices # Assuming OptimizedMesh might have local_indices, or this needs to be passed/derived
    vectorized_matrix_vector_product!(y, A, x) # Assuming this operates on the relevant part of y, A, x

    # Wait for communication to complete and update y with contributions from halo regions
    # wait_for_halo_exchange!(requests, recv_buffers, y, mesh) # This would sum contributions
end

"""
    parallel_gradient_calculation!(gradients::Matrix{Float64}, field::Vector{Float64},
                                 mesh::Any, dist_mesh::Any)

Calculate gradients in parallel using MPI.
"""
function parallel_gradient_calculation!(gradients::Matrix{Float64}, field::Vector{Float64},
                                      mesh::CacheOptimizedMesh, dist_mesh::Any)
    # First, exchange halo data for field
    # TODO: The following halo exchange logic is commented out because
    # NonBlockingCommunication.exchange_halo_data_nonblocking! is currently a placeholder
    # and does not return requests/recv_buffers. This needs to be revisited when halo exchange is fully implemented.
    # requests, recv_buffers = NonBlockingCommunication.exchange_halo_data_nonblocking!(field, dist_mesh)
    NonBlockingCommunication.exchange_halo_data_nonblocking!(field, dist_mesh) # Call the placeholder

    # While communication is happening, compute face values for local faces
    n_faces = length(mesh.face_areas)
    face_values = zeros(n_faces)

    # Only process local faces (not halo faces)
    local_faces = findall(face -> mesh.face_owners[face] in dist_mesh.local_indices, 1:n_faces)

    for face_idx in local_faces
        owner = mesh.face_owners[face_idx]
        neighbor = mesh.face_neighbors[face_idx]

        if neighbor > 0 && neighbor in dist_mesh.local_indices  # Internal face with local neighbor
            face_values[face_idx] = 0.5 * (field[owner] + field[neighbor])
        elseif neighbor > 0  # Internal face with halo neighbor
            # Skip for now, will process after halo exchange
        else  # Boundary face
            face_values[face_idx] = field[owner]
        end
    end

    # Wait for halo exchange to complete
    # wait_for_halo_exchange!(requests, recv_buffers, field, dist_mesh)

    # Now process faces with halo neighbors
    halo_faces = findall(face ->
                        mesh.face_owners[face] in dist_mesh.local_indices &&
                        mesh.face_neighbors[face] > 0 &&
                        !(mesh.face_neighbors[face] in dist_mesh.local_indices),
                        1:n_faces)

    for face_idx in halo_faces
        owner = mesh.face_owners[face_idx]
        neighbor = mesh.face_neighbors[face_idx]
        face_values[face_idx] = 0.5 * (field[owner] + field[neighbor])
    end

    # Calculate gradients using the optimized function
    # Only compute gradients for local cells
    local_cell_faces = [mesh.cell_faces[i] for i in dist_mesh.local_indices]
    local_cell_volumes = [mesh.cell_volumes[i] for i in dist_mesh.local_indices]

    vectorized_gradient_calculation!(gradients, face_values, mesh.face_normals,
                                   mesh.face_owners, mesh.face_neighbors,
                                   local_cell_faces, local_cell_volumes)
end

"""
    parallel_divergence_calculation!(divergence::Vector{Float64}, vector_field::Matrix{Float64},
                                   mesh::CacheOptimizedMesh, dist_mesh::Any)

Calculate divergence in parallel using MPI.
"""
function parallel_divergence_calculation!(divergence::Vector{Float64}, vector_field::Matrix{Float64},
                                        mesh::CacheOptimizedMesh, dist_mesh::Any)
    # First, exchange halo data for vector_field
    # We need to handle each component separately
    n_components = size(vector_field, 1)
    # requests = Vector{Vector{MPI.Request}}(undef, n_components)
    # recv_buffers = Vector{Dict{Int, Vector{Float64}}}(undef, n_components)

    for d in 1:n_components
        # requests[d], recv_buffers[d] = NonBlockingCommunication.exchange_halo_data_nonblocking!(vector_field[d, :], dist_mesh)
        NonBlockingCommunication.exchange_halo_data_nonblocking!(vector_field[d, :], dist_mesh)
    end

    # While communication is happening, compute face fluxes for local faces
    n_faces = length(mesh.face_areas)
    face_fluxes = zeros(n_faces)

    # Only process local faces (not halo faces)
    local_faces = findall(face -> mesh.face_owners[face] in dist_mesh.local_indices, 1:n_faces)

    for face_idx in local_faces
        owner = mesh.face_owners[face_idx]
        neighbor = mesh.face_neighbors[face_idx]

        if neighbor > 0 && neighbor in dist_mesh.local_indices  # Internal face with local neighbor
            # Compute average vector at face
            face_vector = zeros(n_components)
            for d in 1:n_components
                face_vector[d] = 0.5 * (vector_field[d, owner] + vector_field[d, neighbor])
            end

            # Compute flux
            for d in 1:n_components
                face_fluxes[face_idx] += face_vector[d] * mesh.face_normals[d, face_idx]
            end
        elseif neighbor > 0  # Internal face with halo neighbor
            # Skip for now, will process after halo exchange
        else  # Boundary face
            # Use owner value
            for d in 1:n_components
                face_fluxes[face_idx] += vector_field[d, owner] * mesh.face_normals[d, face_idx]
            end
        end
    end

    # Wait for halo exchange to complete for all components
    # for d in 1:n_components
    #     wait_for_halo_exchange!(requests[d], recv_buffers[d], vector_field[d, :], dist_mesh)
    # end

    # Now process faces with halo neighbors
    halo_faces = findall(face ->
                        mesh.face_owners[face] in dist_mesh.local_indices &&
                        mesh.face_neighbors[face] > 0 &&
                        !(mesh.face_neighbors[face] in dist_mesh.local_indices),
                        1:n_faces)

    for face_idx in halo_faces
        owner = mesh.face_owners[face_idx]
        neighbor = mesh.face_neighbors[face_idx]

        # Compute average vector at face
        face_vector = zeros(n_components)
        for d in 1:n_components
            face_vector[d] = 0.5 * (vector_field[d, owner] + vector_field[d, neighbor])
        end

        # Compute flux
        for d in 1:n_components
            face_fluxes[face_idx] += face_vector[d] * mesh.face_normals[d, face_idx]
        end
    end

    # Calculate divergence
    # Only compute divergence for local cells
    for (local_idx, global_idx) in enumerate(dist_mesh.local_indices)
        cell_divergence = 0.0

        for face_idx in mesh.cell_faces[global_idx]
            # Check if cell is owner or neighbor
            sign = (mesh.face_owners[face_idx] == global_idx) ? 1.0 : -1.0

            # Add contribution to divergence
            cell_divergence += sign * face_fluxes[face_idx]
        end

        # Divide by cell volume
        divergence[global_idx] = cell_divergence / mesh.cell_volumes[global_idx]
    end
end

"""
    parallel_solve_linear_system!(x::Vector{Float64}, A::SparseMatrixCSC{Float64, Int},
                                b::Vector{Float64}, precond,
                                mesh::Any; tol::Float64=1e-6, max_iter::Int=1000)

Solve a linear system Ax = b in parallel using the conjugate gradient method with a preconditioner.
"""
function parallel_solve_linear_system!(x::Vector{Float64}, A::SparseMatrixCSC{Float64, Int},
                                     b::Vector{Float64}, precond,
                                     mesh::Any; tol::Float64=1e-6, max_iter::Int=1000)
    n = length(b)

    # Initialize residual and search direction
    r = zeros(n)
    parallel_matrix_vector_product!(r, A, x, mesh)
    @turbo for i in 1:n
        r[i] = b[i] - r[i]
    end

    # Apply preconditioner
    z = zeros(n)
    apply_preconditioner!(z, r, precond)

    # Initialize search direction
    p = copy(z)

    # Initialize variables for CG iteration
    rz_old = vectorized_dot_product(r, z)

    # Perform global reduction to get the correct dot product
    global_rz_old = MPI.Allreduce(rz_old, +, mesh.comm)

    # Initial residual norm
    r_norm = sqrt(global_rz_old)
    initial_r_norm = r_norm

    # CG iteration
    for iter in 1:max_iter
        # Matrix-vector product
        Ap = zeros(n)
        parallel_matrix_vector_product!(Ap, A, p, mesh)

        # Calculate step size
        pAp = vectorized_dot_product(p, Ap)
        global_pAp = MPI.Allreduce(pAp, +, mesh.comm)
        alpha = global_rz_old / global_pAp

        # Update solution and residual
        @turbo for i in 1:n
            x[i] += alpha * p[i]
            r[i] -= alpha * Ap[i]
        end

        # Apply preconditioner
        apply_preconditioner!(z, r, precond)

        # Calculate new search direction
        rz_new = vectorized_dot_product(r, z)
        global_rz_new = MPI.Allreduce(rz_new, +, mesh.comm)

        # Check convergence
        r_norm = sqrt(global_rz_new)
        if r_norm < tol * initial_r_norm
            return iter, r_norm / initial_r_norm
        end

        # Update search direction
        beta = global_rz_new / global_rz_old
        @turbo for i in 1:n
            p[i] = z[i] + beta * p[i]
        end

        # Update for next iteration
        global_rz_old = global_rz_new
    end

    return max_iter, r_norm / initial_r_norm
end

"""
    parallel_gradient_operator(field::Vector{Float64}, mesh::Any)

Compute the gradient of a scalar field in parallel.
"""
function parallel_gradient_operator(field::Vector{Float64}, mesh::Any)
    # Exchange halo data for the field
    # TODO: Placeholder for halo exchange
    # requests, recv_buffers = NonBlockingCommunication.exchange_halo_data_nonblocking!(field, mesh)
    NonBlockingCommunication.exchange_halo_data_nonblocking!(field, mesh)

    # Compute local gradient contributions
    local_gradient = gradient_operator_vectorized(field, mesh.local_mesh_for_gradient) # Assuming mesh has sub-mesh or relevant info

    # Wait for halo exchange to complete
    # wait_for_halo_exchange!(requests, recv_buffers, field, mesh) # Ensure field is updated with halo values

    # Adjust local gradient based on halo values if necessary (e.g. for stencil operations at boundaries)
    # This part is highly dependent on the specific gradient scheme and how halo data is used.

    # The result 'local_gradient' here would be the portion of the global gradient corresponding to local cells.
    # Further assembly or synchronization might be needed depending on how it's used.
    return local_gradient
end

# Export functions
export parallel_matrix_vector_product!, parallel_gradient_calculation!
export parallel_divergence_calculation!, parallel_solve_linear_system!
