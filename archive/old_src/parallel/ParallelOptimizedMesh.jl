"""
    ParallelOptimizedMesh.jl

This module provides a parallel-optimized mesh structure for distributed memory computations.
"""
module ParallelOptimized<PERSON>esh

using MPI
using StaticArrays
using SparseArrays
using LinearAlgebra

# Import types from parent module
import ..JuliaFOAM: OptimizedMesh, Cell, Face

export ParallelMesh, create_parallel_mesh, update_halo_cells!

"""
    ParallelMesh

A mesh structure optimized for parallel access patterns.

# Fields
- `local_cells::Vector{Cell}`: Local cells owned by this process
- `local_faces::Vector{Face}`: Local faces owned by this process
- `halo_cells::Vector{Cell}`: Halo cells from neighboring processes
- `halo_faces::Vector{Face}`: Halo faces from neighboring processes
- `local_indices::Vector{Int}`: Global indices of local cells
- `halo_indices::Vector{Int}`: Global indices of halo cells
- `send_maps::Dict{Int, Vector{Int}}`: Maps process ID to local indices to send
- `recv_maps::Dict{Int, Vector{Int}}`: Maps process ID to local indices to receive
- `comm::MPI.Comm`: MPI communicator
- `rank::Int`: Process rank
- `size::Int`: Number of processes
- `thread_cell_ranges::Vector{UnitRange{Int}}`: Cell ranges for each thread
"""
struct ParallelMesh
    # Local mesh data
    local_cells::Vector{Cell}
    local_faces::Vector{Face}
    local_cell_faces::Vector{Vector{Int}}
    
    # Halo data
    halo_cells::Vector{Cell}
    halo_faces::Vector{Face}
    
    # Parallel decomposition data
    local_indices::Vector{Int}  # Global indices of local cells
    halo_indices::Vector{Int}   # Global indices of halo cells
    send_maps::Dict{Int, Vector{Int}}  # Maps process ID to local indices to send
    recv_maps::Dict{Int, Vector{Int}}  # Maps process ID to local indices to receive
    
    # Communication data
    comm::MPI.Comm
    rank::Int
    size::Int
    
    # Cache optimization
    cell_data_blocks::Vector{Vector{Float64}}  # Blocked cell data for better cache locality
    face_data_blocks::Vector{Vector{Float64}}  # Blocked face data for better cache locality
    
    # Thread partitioning
    thread_cell_ranges::Vector{UnitRange{Int}}  # Cell ranges for each thread
    
    # Boundary data
    boundary_patches::Dict{String, Vector{Int}}
    boundary_conditions::Dict{String, Any}
end

"""
    create_parallel_mesh(mesh::Any, cell_partition::Vector{Int32}, comm::MPI.Comm)

Create a parallel mesh from a sequential mesh and cell partition.

# Arguments
- `mesh`: The sequential optimized mesh
- `cell_partition`: Cell partition array
- `comm`: MPI communicator

# Returns
- `ParallelMesh`: Parallel mesh
"""
function create_parallel_mesh(mesh::Any, cell_partition::Vector{Int32}, comm::MPI.Comm)
    rank = MPI.Comm_rank(comm)
    size = MPI.Comm_size(comm)
    
    # Find local cells
    local_indices = findall(i -> cell_partition[i] == rank, 1:length(mesh.cells))
    
    # Find halo cells
    halo_indices = Int[]
    for cell_idx in local_indices
        for neighbor_idx in mesh.cell_neighbors[cell_idx]
            if cell_partition[neighbor_idx] != rank && !(neighbor_idx in halo_indices)
                push!(halo_indices, neighbor_idx)
            end
        end
    end
    
    # Create send/recv maps
    send_maps = Dict{Int, Vector{Int}}()
    recv_maps = Dict{Int, Vector{Int}}()
    
    for proc in 0:size-1
        if proc == rank
            continue
        end
        
        # Find cells to send to this process
        send_cells = Int[]
        for cell_idx in local_indices
            for neighbor_idx in mesh.cell_neighbors[cell_idx]
                if cell_partition[neighbor_idx] == proc && !(cell_idx in send_cells)
                    push!(send_cells, cell_idx)
                end
            end
        end
        
        if !isempty(send_cells)
            send_maps[proc] = send_cells
        end
        
        # Find cells to receive from this process
        recv_cells = filter(idx -> cell_partition[idx] == proc, halo_indices)
        
        if !isempty(recv_cells)
            recv_maps[proc] = recv_cells
        end
    end
    
    # Create thread partitioning
    num_threads = Threads.nthreads()
    cells_per_thread = cld(length(local_indices), num_threads)
    thread_cell_ranges = [((i-1)*cells_per_thread + 1):min(i*cells_per_thread, length(local_indices)) for i in 1:num_threads]
    
    # Create blocked data structures for better cache locality
    block_size = 64  # Typical cache line size in elements
    num_blocks = cld(length(local_indices), block_size)
    cell_data_blocks = [zeros(Float64, block_size) for _ in 1:num_blocks]
    
    num_face_blocks = cld(length(mesh.faces), block_size)
    face_data_blocks = [zeros(Float64, block_size) for _ in 1:num_face_blocks]
    
    # Extract local cells and faces
    local_cells = mesh.cells[local_indices]
    halo_cells = mesh.cells[halo_indices]
    
    # Extract local faces
    local_face_indices = Int[]
    for cell_idx in local_indices
        append!(local_face_indices, mesh.cell_faces[cell_idx])
    end
    unique!(local_face_indices)
    local_faces = mesh.faces[local_face_indices]
    
    # Extract halo faces
    halo_face_indices = Int[]
    for cell_idx in halo_indices
        append!(halo_face_indices, mesh.cell_faces[cell_idx])
    end
    setdiff!(halo_face_indices, local_face_indices)  # Remove faces already in local_faces
    halo_faces = mesh.faces[halo_face_indices]
    
    # Create local cell_faces array
    local_cell_faces = [Int[] for _ in 1:length(local_indices)]
    for (i, global_idx) in enumerate(local_indices)
        # Map global face indices to local indices
        for global_face_idx in mesh.cell_faces[global_idx]
            local_face_idx = findfirst(==(global_face_idx), local_face_indices)
            if local_face_idx !== nothing
                push!(local_cell_faces[i], local_face_idx)
            end
        end
    end
    
    # Extract boundary patches
    boundary_patches = Dict{String, Vector{Int}}()
    for (name, faces) in mesh.boundary_patches
        # Map global face indices to local indices
        local_patch_faces = Int[]
        for global_face_idx in faces
            local_face_idx = findfirst(==(global_face_idx), local_face_indices)
            if local_face_idx !== nothing
                push!(local_patch_faces, local_face_idx)
            end
        end
        
        if !isempty(local_patch_faces)
            boundary_patches[name] = local_patch_faces
        end
    end
    
    # Create the parallel mesh
    return ParallelMesh(
        local_cells,
        local_faces,
        local_cell_faces,
        halo_cells,
        halo_faces,
        local_indices,
        halo_indices,
        send_maps,
        recv_maps,
        comm,
        rank,
        size,
        cell_data_blocks,
        face_data_blocks,
        thread_cell_ranges,
        boundary_patches,
        mesh.boundary_conditions
    )
end

"""
    update_halo_cells!(mesh::ParallelMesh, field::Vector{T}) where T

Update halo cells with values from neighboring processes.

# Arguments
- `mesh`: The parallel mesh
- `field`: The field to update

# Returns
- `Nothing`
"""
function update_halo_cells!(mesh::ParallelMesh, field::Vector{T}) where T
    # Skip if single process
    if mesh.size == 1
        return
    end
    
    # Initialize request arrays and buffers
    send_requests = MPI.Request[]
    recv_requests = MPI.Request[]
    recv_buffers = Dict{Int, Vector{T}}()
    
    # Post non-blocking receives first
    for (neighbor, halo_indices) in mesh.recv_maps
        # Create receive buffer
        recv_buffers[neighbor] = Vector{T}(undef, length(halo_indices))
        
        # Post non-blocking receive
        request = MPI.Irecv!(recv_buffers[neighbor], neighbor, 0, mesh.comm)
        push!(recv_requests, request)
    end
    
    # Post non-blocking sends
    for (neighbor, send_indices) in mesh.send_maps
        # Create send buffer
        send_buffer = field[send_indices]
        
        # Post non-blocking send
        request = MPI.Isend(send_buffer, neighbor, 0, mesh.comm)
        push!(send_requests, request)
    end
    
    # Wait for all receives to complete
    MPI.Waitall!(recv_requests)
    
    # Update field with received data
    for (neighbor, recv_buffer) in recv_buffers
        for (i, idx) in enumerate(mesh.halo_indices)
            field[idx] = recv_buffer[i]
        end
    end
    
    # Wait for all sends to complete (cleanup)
    MPI.Waitall!(send_requests)
end

end # module ParallelOptimizedMesh
