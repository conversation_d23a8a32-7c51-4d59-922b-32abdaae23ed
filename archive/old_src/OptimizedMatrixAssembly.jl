module OptimizedMatrixAssembly

using SparseArrays
using LinearAlgebra

export assemble_laplacian, assemble_convection_diffusion, assemble_pressure_correction

"""
    assemble_laplacian(mesh, diffusivity)

Assemble the Laplacian operator matrix for a given mesh and diffusivity field.
Uses pre-allocation and optimized assembly for performance.

# Arguments
- `mesh`: Mesh data structure from OptimizedMeshGenerator
- `diffusivity`: Scalar field of diffusivity values for each cell
"""
function assemble_laplacian(mesh, diffusivity)
    n_cells = mesh.nx * mesh.ny * mesh.nz
    
    # Pre-allocate arrays for sparse matrix construction
    # Estimate the number of non-zeros: for a structured grid with a 7-point stencil
    # each internal cell has at most 7 entries (itself + 6 neighbors)
    nnz_estimate = 7 * n_cells
    
    I = Vector{Int}(undef, nnz_estimate)  # Row indices
    J = Vector{Int}(undef, nnz_estimate)  # Column indices
    V = Vector{Float64}(undef, nnz_estimate)  # Values
    
    # Initialize counter for filling arrays
    count = 0
    
    # Diagonal entries (initialize to zero, will accumulate)
    for i in 1:n_cells
        count += 1
        I[count] = i
        J[count] = i
        V[count] = 0.0
    end
    
    # Diagonal start indices for each cell
    diag_indices = Dict(i => i for i in 1:n_cells)
    
    # Process internal faces
    @inbounds for face_idx in 1:length(mesh.face_owner)
        owner = mesh.face_owner[face_idx]
        neighbor = mesh.face_neighbor[face_idx]
        
        # Skip boundary faces
        if neighbor <= 0
            continue
        end
        
        # Face area
        area = mesh.face_area[face_idx]
        
        # Face normal
        nx = mesh.face_normal_x[face_idx]
        ny = mesh.face_normal_y[face_idx]
        nz = mesh.face_normal_z[face_idx]
        
        # Distance between cell centers
        dx = mesh.center_x[neighbor] - mesh.center_x[owner]
        dy = mesh.center_y[neighbor] - mesh.center_y[owner]
        dz = mesh.center_z[neighbor] - mesh.center_z[owner]
        
        # Magnitude of distance vector
        distance = sqrt(dx*dx + dy*dy + dz*dz)
        
        # Diffusivity at the face (harmonic mean)
        diff_owner = diffusivity[owner]
        diff_neighbor = diffusivity[neighbor]
        diff_face = 2.0 * diff_owner * diff_neighbor / (diff_owner + diff_neighbor)
        
        # Coefficient for the face
        coeff = diff_face * area / distance
        
        # Add to off-diagonal entries
        count += 1
        I[count] = owner
        J[count] = neighbor
        V[count] = -coeff
        
        count += 1
        I[count] = neighbor
        J[count] = owner
        V[count] = -coeff
        
        # Add to diagonal entries
        V[diag_indices[owner]] += coeff
        V[diag_indices[neighbor]] += coeff
    end
    
    # Process boundary faces
    for (boundary_name, face_indices) in mesh.boundary_faces
        for face_idx in face_indices
            owner = mesh.face_owner[face_idx]
            
            # Face area
            area = mesh.face_area[face_idx]
            
            # For Dirichlet boundaries, add contribution to diagonal
            # (This is a simplified approach - in practice, boundary conditions would be more complex)
            V[diag_indices[owner]] += diffusivity[owner] * area / (0.5 * (mesh.length/mesh.nx))
        end
    end
    
    # Trim arrays to actual size
    resize!(I, count)
    resize!(J, count)
    resize!(V, count)
    
    # Create sparse matrix
    A = sparse(I, J, V, n_cells, n_cells)
    
    return A
end

"""
    assemble_convection_diffusion(mesh, velocity, diffusivity, scheme="upwind")

Assemble the convection-diffusion operator matrix for a given mesh, velocity field, and diffusivity.
Uses pre-allocation and optimized assembly for performance.

# Arguments
- `mesh`: Mesh data structure from OptimizedMeshGenerator
- `velocity`: Vector field of velocity values for each cell (tuple of x,y,z components)
- `diffusivity`: Scalar field of diffusivity values for each cell
- `scheme`: Discretization scheme for convection term ("upwind", "central", "hybrid")
"""
function assemble_convection_diffusion(mesh, velocity, diffusivity, scheme="upwind")
    n_cells = mesh.nx * mesh.ny * mesh.nz
    
    # Pre-allocate arrays for sparse matrix construction
    nnz_estimate = 7 * n_cells
    
    I = Vector{Int}(undef, nnz_estimate)
    J = Vector{Int}(undef, nnz_estimate)
    V = Vector{Float64}(undef, nnz_estimate)
    
    # Initialize counter for filling arrays
    count = 0
    
    # Diagonal entries (initialize to zero, will accumulate)
    for i in 1:n_cells
        count += 1
        I[count] = i
        J[count] = i
        V[count] = 0.0
    end
    
    # Diagonal start indices for each cell
    diag_indices = Dict(i => i for i in 1:n_cells)
    
    # Process internal faces
    @inbounds for face_idx in 1:length(mesh.face_owner)
        owner = mesh.face_owner[face_idx]
        neighbor = mesh.face_neighbor[face_idx]
        
        # Skip boundary faces
        if neighbor <= 0
            continue
        end
        
        # Face area
        area = mesh.face_area[face_idx]
        
        # Face normal
        nx = mesh.face_normal_x[face_idx]
        ny = mesh.face_normal_y[face_idx]
        nz = mesh.face_normal_z[face_idx]
        
        # Distance between cell centers
        dx = mesh.center_x[neighbor] - mesh.center_x[owner]
        dy = mesh.center_y[neighbor] - mesh.center_y[owner]
        dz = mesh.center_z[neighbor] - mesh.center_z[owner]
        
        # Magnitude of distance vector
        distance = sqrt(dx*dx + dy*dy + dz*dz)
        
        # Diffusivity at the face (harmonic mean)
        diff_owner = diffusivity[owner]
        diff_neighbor = diffusivity[neighbor]
        diff_face = 2.0 * diff_owner * diff_neighbor / (diff_owner + diff_neighbor)
        
        # Diffusion coefficient
        diff_coeff = diff_face * area / distance
        
        # Interpolate velocity to the face (linear interpolation)
        vx_face = 0.5 * (velocity[owner][1] + velocity[neighbor][1])
        vy_face = 0.5 * (velocity[owner][2] + velocity[neighbor][2])
        vz_face = 0.5 * (velocity[owner][3] + velocity[neighbor][3])
        
        # Mass flux through the face
        mass_flux = area * (vx_face * nx + vy_face * ny + vz_face * nz)
        
        # Convection coefficient depends on the scheme
        if scheme == "upwind"
            # Upwind scheme
            if mass_flux > 0
                # Flow from owner to neighbor
                conv_coeff_owner = mass_flux
                conv_coeff_neighbor = 0.0
            else
                # Flow from neighbor to owner
                conv_coeff_owner = 0.0
                conv_coeff_neighbor = -mass_flux
            end
        elseif scheme == "central"
            # Central differencing
            conv_coeff_owner = 0.5 * mass_flux
            conv_coeff_neighbor = 0.5 * mass_flux
        elseif scheme == "hybrid"
            # Hybrid scheme (upwind or central depending on Peclet number)
            peclet = abs(mass_flux) / (diff_coeff + 1e-10)
            if peclet > 2.0
                # Upwind for high Peclet number
                if mass_flux > 0
                    conv_coeff_owner = mass_flux
                    conv_coeff_neighbor = 0.0
                else
                    conv_coeff_owner = 0.0
                    conv_coeff_neighbor = -mass_flux
                end
            else
                # Central for low Peclet number
                conv_coeff_owner = 0.5 * mass_flux
                conv_coeff_neighbor = 0.5 * mass_flux
            end
        else
            error("Unknown scheme: $scheme")
        end
        
        # Add diffusion contribution
        count += 1
        I[count] = owner
        J[count] = neighbor
        V[count] = -diff_coeff
        
        count += 1
        I[count] = neighbor
        J[count] = owner
        V[count] = -diff_coeff
        
        # Add to diagonal entries (diffusion)
        V[diag_indices[owner]] += diff_coeff
        V[diag_indices[neighbor]] += diff_coeff
        
        # Add convection contribution
        count += 1
        I[count] = owner
        J[count] = neighbor
        V[count] -= conv_coeff_owner
        
        count += 1
        I[count] = neighbor
        J[count] = owner
        V[count] -= conv_coeff_neighbor
        
        # Add to diagonal entries (convection)
        V[diag_indices[owner]] += conv_coeff_owner
        V[diag_indices[neighbor]] += conv_coeff_neighbor
    end
    
    # Process boundary faces (simplified approach)
    for (boundary_name, face_indices) in mesh.boundary_faces
        for face_idx in face_indices
            owner = mesh.face_owner[face_idx]
            
            # Face area
            area = mesh.face_area[face_idx]
            
            # Add contribution to diagonal for diffusion
            V[diag_indices[owner]] += diffusivity[owner] * area / (0.5 * (mesh.length/mesh.nx))
            
            # For convection, we would need to handle different boundary conditions
            # This is a simplified approach
        end
    end
    
    # Trim arrays to actual size
    resize!(I, count)
    resize!(J, count)
    resize!(V, count)
    
    # Create sparse matrix
    A = sparse(I, J, V, n_cells, n_cells)
    
    return A
end

"""
    assemble_pressure_correction(mesh, face_fluxes)

Assemble the pressure correction equation matrix for the SIMPLE algorithm.
Uses pre-allocation and optimized assembly for performance.

# Arguments
- `mesh`: Mesh data structure from OptimizedMeshGenerator
- `face_fluxes`: Mass flux through each face
"""
function assemble_pressure_correction(mesh, face_fluxes)
    n_cells = mesh.nx * mesh.ny * mesh.nz
    
    # Pre-allocate arrays for sparse matrix construction
    nnz_estimate = 7 * n_cells
    
    I = Vector{Int}(undef, nnz_estimate)
    J = Vector{Int}(undef, nnz_estimate)
    V = Vector{Float64}(undef, nnz_estimate)
    
    # Initialize counter for filling arrays
    count = 0
    
    # Diagonal entries (initialize to zero, will accumulate)
    for i in 1:n_cells
        count += 1
        I[count] = i
        J[count] = i
        V[count] = 0.0
    end
    
    # Diagonal start indices for each cell
    diag_indices = Dict(i => i for i in 1:n_cells)
    
    # Process internal faces
    @inbounds for face_idx in 1:length(mesh.face_owner)
        owner = mesh.face_owner[face_idx]
        neighbor = mesh.face_neighbor[face_idx]
        
        # Skip boundary faces
        if neighbor <= 0
            continue
        end
        
        # Face area
        area = mesh.face_area[face_idx]
        
        # Distance between cell centers
        dx = mesh.center_x[neighbor] - mesh.center_x[owner]
        dy = mesh.center_y[neighbor] - mesh.center_y[owner]
        dz = mesh.center_z[neighbor] - mesh.center_z[owner]
        
        # Magnitude of distance vector
        distance = sqrt(dx*dx + dy*dy + dz*dz)
        
        # Coefficient for the face
        coeff = area / distance
        
        # Add to off-diagonal entries
        count += 1
        I[count] = owner
        J[count] = neighbor
        V[count] = -coeff
        
        count += 1
        I[count] = neighbor
        J[count] = owner
        V[count] = -coeff
        
        # Add to diagonal entries
        V[diag_indices[owner]] += coeff
        V[diag_indices[neighbor]] += coeff
    end
    
    # Process boundary faces (simplified approach)
    for (boundary_name, face_indices) in mesh.boundary_faces
        for face_idx in face_indices
            owner = mesh.face_owner[face_idx]
            
            # Face area
            area = mesh.face_area[face_idx]
            
            # For Neumann boundaries (zero gradient), no contribution
            # For Dirichlet boundaries, add contribution to diagonal
            if boundary_name == "inlet" || boundary_name == "outlet"
                # Fixed pressure boundary
                V[diag_indices[owner]] += area / (0.5 * (mesh.length/mesh.nx))
            end
        end
    end
    
    # Trim arrays to actual size
    resize!(I, count)
    resize!(J, count)
    resize!(V, count)
    
    # Create sparse matrix
    A = sparse(I, J, V, n_cells, n_cells)
    
    return A
end

end # module
