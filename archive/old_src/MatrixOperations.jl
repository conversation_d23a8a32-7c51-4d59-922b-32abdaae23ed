# =========================================================================
# MatrixOperations Module - Comprehensive matrix operations for JuliaFOAM
# =========================================================================

"""
    MatrixOperations

This module provides comprehensive matrix operations for JuliaFOAM, including:
- Optimized sparse matrix-vector products based on matrix characteristics
- Matrix assembly operations for finite volume discretizations  
- Cache-friendly operations for different sparsity patterns
- OpenFOAM-compatible matrix construction

The module automatically selects the most efficient algorithm based on matrix
size, sparsity pattern, and available hardware capabilities.
"""
module MatrixOperations

using LinearAlgebra
using SparseArrays
using Base.Threads
using StaticArrays

# Optional LoopVectorization support
const HAS_LOOPVECTORIZATION = try
    using LoopVectorization
    true
catch
    false
end

# =========================================================================
# Exports
# =========================================================================

export matrix_type_classifier
export optimized_matrix_vector_product!, standard_csc_matvec!, simd_csc_matvec!
export csr_simd_matvec!, block_diagonal_matvec!, banded_matvec!
export parallel_blocked_csr_matvec!
export assemble_laplacian_optimized, assemble_convection_diffusion_optimized
export cache_friendly_transpose, reorder_matrix_for_cache

# =========================================================================
# Matrix Classification
# =========================================================================

"""
    matrix_type_classifier(A::SparseMatrixCSC{Float64, Int})

Classify a sparse matrix based on its size and sparsity pattern to determine
the most efficient algorithm for operations.

# Returns
- `:tiny`: Very small matrices (< 100 rows)
- `:small`: Small matrices with high sparsity (< 1000 rows)
- `:medium`: Medium matrices (1000-10000 rows)
- `:large`: Large matrices (> 10000 rows)
- `:block_diagonal`: Matrices with block diagonal structure
- `:banded`: Matrices with banded structure
"""
function matrix_type_classifier(A::SparseMatrixCSC{Float64, Int})
    rows, cols = size(A)
    nnz_count = nnz(A)
    density = nnz_count / (rows * cols)
    
    # Size-based classification first
    if rows < 100
        return :tiny
    elseif rows < 1000
        return :small
    elseif rows < 10000
        return :medium
    else
        return :large
    end
    
    # Pattern-based classification (simplified)
    # Check for block diagonal structure
    if density < 0.01 && is_block_diagonal_pattern(A)
        return :block_diagonal
    end
    
    # Check for banded structure
    if is_banded_pattern(A)
        return :banded
    end
    
    # Default to size-based classification
    if rows < 100
        return :tiny
    elseif rows < 1000
        return :small
    elseif rows < 10000
        return :medium
    else
        return :large
    end
end

"""
    is_block_diagonal_pattern(A::SparseMatrixCSC{Float64, Int})

Check if matrix has a block diagonal structure.
"""
function is_block_diagonal_pattern(A::SparseMatrixCSC{Float64, Int})
    # Simplified check - look for non-zeros only near diagonal blocks
    rows, cols = size(A)
    block_size = min(50, rows ÷ 10)  # Estimate block size
    
    # Count non-zeros outside diagonal blocks
    outside_blocks = 0
    total_nonzeros = nnz(A)
    
    if total_nonzeros == 0
        return false
    end
    
    for col in 1:cols
        for j in A.colptr[col]:A.colptr[col+1]-1
            row = A.rowval[j]
            # Check if element is outside diagonal blocks
            if abs(row - col) > block_size
                outside_blocks += 1
            end
        end
    end
    
    return (outside_blocks / total_nonzeros) < 0.1
end

"""
    is_banded_pattern(A::SparseMatrixCSC{Float64, Int})

Check if matrix has a banded structure.
"""
function is_banded_pattern(A::SparseMatrixCSC{Float64, Int})
    rows, cols = size(A)
    max_bandwidth = min(20, rows ÷ 5)  # Maximum bandwidth to consider
    
    outside_band = 0
    total_nonzeros = nnz(A)
    
    if total_nonzeros == 0
        return false
    end
    
    for col in 1:cols
        for j in A.colptr[col]:A.colptr[col+1]-1
            row = A.rowval[j]
            if abs(row - col) > max_bandwidth
                outside_band += 1
            end
        end
    end
    
    return (outside_band / total_nonzeros) < 0.05
end

# =========================================================================
# Optimized Matrix-Vector Products
# =========================================================================

"""
    optimized_matrix_vector_product!(y::Vector{Float64}, A::SparseMatrixCSC{Float64, Int}, 
                                    x::Vector{Float64})

Automatically select the most efficient matrix-vector product based on matrix characteristics.
"""
function optimized_matrix_vector_product!(y::Vector{Float64}, A::SparseMatrixCSC{Float64, Int}, x::Vector{Float64})
    matrix_type = matrix_type_classifier(A)
    
    if matrix_type == :tiny
        standard_csc_matvec!(y, A, x)
    elseif matrix_type == :small
        simd_csc_matvec!(y, A, x)
    elseif matrix_type == :block_diagonal
        block_diagonal_matvec!(y, A, x)
    elseif matrix_type == :banded
        banded_matvec!(y, A, x)
    elseif matrix_type == :large && Threads.nthreads() > 1
        parallel_blocked_csr_matvec!(y, A, x)
    else
        simd_csc_matvec!(y, A, x)
    end
end

"""
    standard_csc_matvec!(y::Vector{Float64}, A::SparseMatrixCSC{Float64, Int}, x::Vector{Float64})

Standard compressed sparse column matrix-vector product.
"""
function standard_csc_matvec!(y::Vector{Float64}, A::SparseMatrixCSC{Float64, Int}, x::Vector{Float64})
    fill!(y, 0.0)
    
    @inbounds for j in 1:size(A, 2)
        xj = x[j]
        if xj != 0.0
            for k in A.colptr[j]:A.colptr[j+1]-1
                i = A.rowval[k]
                y[i] += A.nzval[k] * xj
            end
        end
    end
end

"""
    simd_csc_matvec!(y::Vector{Float64}, A::SparseMatrixCSC{Float64, Int}, x::Vector{Float64})

SIMD-optimized compressed sparse column matrix-vector product.
"""
function simd_csc_matvec!(y::Vector{Float64}, A::SparseMatrixCSC{Float64, Int}, x::Vector{Float64})
    fill!(y, 0.0)
    
    @inbounds @simd for j in 1:size(A, 2)
        xj = x[j]
        if xj != 0.0
            @simd for k in A.colptr[j]:A.colptr[j+1]-1
                i = A.rowval[k]
                y[i] += A.nzval[k] * xj
            end
        end
    end
end

"""
    csr_simd_matvec!(y::Vector{Float64}, A::SparseMatrixCSC{Float64, Int}, x::Vector{Float64})

CSR-style SIMD matrix-vector product (converts CSC to CSR-like access).
"""
function csr_simd_matvec!(y::Vector{Float64}, A::SparseMatrixCSC{Float64, Int}, x::Vector{Float64})
    rows, cols = size(A)
    fill!(y, 0.0)
    
    # Convert to row-wise iteration
    A_csr = sparse(A')  # Transpose to get row-wise storage
    
    @inbounds for i in 1:rows
        sum_val = 0.0
        @simd for k in A_csr.colptr[i]:A_csr.colptr[i+1]-1
            j = A_csr.rowval[k]
            sum_val += A_csr.nzval[k] * x[j]
        end
        y[i] = sum_val
    end
end

"""
    block_diagonal_matvec!(y::Vector{Float64}, A::SparseMatrixCSC{Float64, Int}, x::Vector{Float64})

Optimized matrix-vector product for block diagonal matrices.
"""
function block_diagonal_matvec!(y::Vector{Float64}, A::SparseMatrixCSC{Float64, Int}, x::Vector{Float64})
    fill!(y, 0.0)
    rows = size(A, 1)
    
    # Estimate block size
    block_size = min(50, rows ÷ 10)
    
    # Process each diagonal block
    for block_start in 1:block_size:rows
        block_end = min(block_start + block_size - 1, rows)
        
        # Process block
        for j in block_start:block_end
            xj = x[j]
            if xj != 0.0
                for k in A.colptr[j]:A.colptr[j+1]-1
                    i = A.rowval[k]
                    if block_start <= i <= block_end  # Within current block
                        y[i] += A.nzval[k] * xj
                    end
                end
            end
        end
    end
end

"""
    banded_matvec!(y::Vector{Float64}, A::SparseMatrixCSC{Float64, Int}, x::Vector{Float64})

Optimized matrix-vector product for banded matrices.
"""
function banded_matvec!(y::Vector{Float64}, A::SparseMatrixCSC{Float64, Int}, x::Vector{Float64})
    fill!(y, 0.0)
    
    # Use standard implementation for now
    # Could be optimized for specific band structure
    standard_csc_matvec!(y, A, x)
end

"""
    parallel_blocked_csr_matvec!(y::Vector{Float64}, A::SparseMatrixCSC{Float64, Int}, x::Vector{Float64})

Parallel blocked CSR matrix-vector product for large matrices.
"""
function parallel_blocked_csr_matvec!(y::Vector{Float64}, A::SparseMatrixCSC{Float64, Int}, x::Vector{Float64})
    rows = size(A, 1)
    fill!(y, 0.0)
    
    # Divide work among threads
    nthreads = Threads.nthreads()
    chunk_size = ceil(Int, rows / nthreads)
    
    Threads.@threads for tid in 1:nthreads
        start_row = (tid - 1) * chunk_size + 1
        end_row = min(tid * chunk_size, rows)
        
        if start_row <= end_row
            # Process chunk
            for i in start_row:end_row
                sum_val = 0.0
                for j in 1:size(A, 2)
                    for k in A.colptr[j]:A.colptr[j+1]-1
                        if A.rowval[k] == i
                            sum_val += A.nzval[k] * x[j]
                        end
                    end
                end
                y[i] = sum_val
            end
        end
    end
end

# =========================================================================
# Matrix Assembly Operations
# =========================================================================

"""
    assemble_laplacian_optimized(mesh, mesh_data, connectivity, diffusivity)

Assemble the Laplacian operator matrix using optimized data structures.
This function maintains compatibility with OpenFOAM mesh structures.

# Arguments
- `mesh`: The original JuliaFOAM mesh structure (for reference)
- `mesh_data`: Optimized mesh data 
- `connectivity`: Optimized face connectivity 
- `diffusivity`: Scalar field of diffusivity values for each cell

# Returns
- `A`: Assembled sparse matrix for the Laplacian operator
"""
function assemble_laplacian_optimized(mesh, mesh_data, connectivity, diffusivity)
    n_cells = length(mesh_data.cell_centers)
    
    # Estimate number of non-zeros (each cell connects to ~6 neighbors on average)
    estimated_nnz = n_cells * 7
    
    # Pre-allocate arrays for triplet format
    I = Vector{Int}(undef, estimated_nnz)
    J = Vector{Int}(undef, estimated_nnz)
    V = Vector{Float64}(undef, estimated_nnz)
    
    nnz_count = 0
    
    # Assemble matrix contributions
    for cell_id in 1:n_cells
        diagonal_sum = 0.0
        
        # Get neighbors for this cell
        neighbors = get(connectivity.cell_neighbors, cell_id, Int[])
        
        for neighbor_id in neighbors
            if neighbor_id > 0 && neighbor_id <= n_cells
                # Calculate face area and distance
                # Find the shared face between cells
                shared_face_area = 1.0  # Default fallback
                if hasfield(typeof(mesh_data), :face_areas) && !isempty(mesh_data.face_areas)
                    # Try to find the shared face and use its area
                    for (face_idx, _) in enumerate(mesh_data.face_areas)
                        # This is a simplified approach - in practice would need face-cell connectivity
                        shared_face_area = mesh_data.face_areas[min(face_idx, length(mesh_data.face_areas))]
                        break
                    end
                else
                    # Estimate face area based on cell spacing
                    distance = norm(mesh_data.cell_centers[cell_id] - mesh_data.cell_centers[neighbor_id])
                    shared_face_area = distance^1.5  # Rough 2D/3D hybrid estimate
                end
                face_area = shared_face_area
                distance = norm(mesh_data.cell_centers[cell_id] - mesh_data.cell_centers[neighbor_id])
                
                if distance > 1e-14
                    # Diffusive flux coefficient
                    coeff = diffusivity[cell_id] * face_area / distance
                    
                    # Off-diagonal entry
                    nnz_count += 1
                    I[nnz_count] = cell_id
                    J[nnz_count] = neighbor_id
                    V[nnz_count] = -coeff
                    
                    diagonal_sum += coeff
                end
            end
        end
        
        # Diagonal entry
        nnz_count += 1
        I[nnz_count] = cell_id
        J[nnz_count] = cell_id
        V[nnz_count] = diagonal_sum
    end
    
    # Create sparse matrix
    return sparse(I[1:nnz_count], J[1:nnz_count], V[1:nnz_count], n_cells, n_cells)
end

"""
    assemble_convection_diffusion_optimized(mesh, mesh_data, connectivity, velocity, diffusivity)

Assemble the convection-diffusion operator matrix using optimized data structures.

# Arguments
- `mesh`: The original JuliaFOAM mesh structure
- `mesh_data`: Optimized mesh data
- `connectivity`: Optimized face connectivity
- `velocity`: Vector field of velocity values
- `diffusivity`: Scalar field of diffusivity values

# Returns
- `A`: Assembled sparse matrix for the convection-diffusion operator
"""
function assemble_convection_diffusion_optimized(mesh, mesh_data, connectivity, velocity, diffusivity)
    n_cells = length(mesh_data.cell_centers)
    estimated_nnz = n_cells * 7
    
    I = Vector{Int}(undef, estimated_nnz)
    J = Vector{Int}(undef, estimated_nnz)
    V = Vector{Float64}(undef, estimated_nnz)
    
    nnz_count = 0
    
    for cell_id in 1:n_cells
        diagonal_sum = 0.0
        neighbors = get(connectivity.cell_neighbors, cell_id, Int[])
        
        for neighbor_id in neighbors
            if neighbor_id > 0 && neighbor_id <= n_cells
                # Calculate geometric properties
                distance = norm(mesh_data.cell_centers[cell_id] - mesh_data.cell_centers[neighbor_id])
                face_normal = (mesh_data.cell_centers[neighbor_id] - mesh_data.cell_centers[cell_id]) / distance
                
                # Estimate face area from cell spacing - better than constant 1.0
                if hasfield(typeof(mesh_data), :face_areas) && !isempty(mesh_data.face_areas)
                    face_area = mesh_data.face_areas[min(neighbor_id, length(mesh_data.face_areas))]
                else
                    # For structured meshes, face area scales with distance squared
                    face_area = distance^1.5  # Hybrid 2D/3D scaling
                end
                
                if distance > 1e-14
                    # Diffusive contribution
                    diff_coeff = diffusivity[cell_id] * face_area / distance
                    
                    # Convective contribution (upwind)
                    face_velocity = 0.5 * (velocity[cell_id] + velocity[neighbor_id])
                    conv_flux = dot(face_velocity, face_normal) * face_area
                    
                    # Upwind scheme
                    conv_coeff = max(conv_flux, 0.0)
                    
                    # Total coefficient
                    total_coeff = diff_coeff + conv_coeff
                    
                    # Off-diagonal entry
                    nnz_count += 1
                    I[nnz_count] = cell_id
                    J[nnz_count] = neighbor_id
                    V[nnz_count] = -total_coeff
                    
                    diagonal_sum += total_coeff
                end
            end
        end
        
        # Diagonal entry
        nnz_count += 1
        I[nnz_count] = cell_id
        J[nnz_count] = cell_id
        V[nnz_count] = diagonal_sum
    end
    
    return sparse(I[1:nnz_count], J[1:nnz_count], V[1:nnz_count], n_cells, n_cells)
end

# =========================================================================
# Cache Optimization
# =========================================================================

"""
    cache_friendly_transpose(A::SparseMatrixCSC{Float64, Int})

Create a cache-friendly transposed version of the matrix.
"""
function cache_friendly_transpose(A::SparseMatrixCSC{Float64, Int})
    return sparse(A')
end

"""
    reorder_matrix_for_cache(A::SparseMatrixCSC{Float64, Int})

Reorder matrix to improve cache performance.
"""
function reorder_matrix_for_cache(A::SparseMatrixCSC{Float64, Int})
    # Implement basic reverse Cuthill-McKee reordering for better cache locality
    n = size(A, 1)
    
    # Create adjacency graph (make symmetric)
    adj_matrix = A + A'
    
    # Find node with minimum degree as starting point
    degrees = vec(sum(adj_matrix .!= 0, dims=2))
    start_node = argmin(degrees)
    
    # BFS-based reordering (simplified RCM)
    visited = falses(n)
    perm = Int[]
    queue = [start_node]
    visited[start_node] = true
    
    while !isempty(queue)
        current = popfirst!(queue)
        push!(perm, current)
        
        # Get neighbors and sort by degree (ascending)
        neighbors = findall(adj_matrix[current, :] .!= 0)
        unvisited_neighbors = filter(x -> !visited[x], neighbors)
        sort!(unvisited_neighbors, by=x -> degrees[x])
        
        for neighbor in unvisited_neighbors
            if !visited[neighbor]
                visited[neighbor] = true
                push!(queue, neighbor)
            end
        end
    end
    
    # Add any unvisited nodes
    for i in 1:n
        if !visited[i]
            push!(perm, i)
        end
    end
    
    # Reverse the permutation (RCM = reverse of CM)
    perm = reverse(perm)
    
    # Apply permutation to matrix
    return A[perm, perm]
end

end # module MatrixOperations