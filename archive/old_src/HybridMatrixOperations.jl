"""
    HybridMatrixOperations.jl

This module provides hybrid matrix operations optimized for large-scale simulations,
combining SIMD, multi-threading, and cache-friendly algorithms.
"""

using SparseArrays
using LinearAlgebra
using StaticArrays
using LoopVectorization

"""
    hybrid_matrix_vector_product!(y::Vector{Float64}, A::SparseMatrixCSC{Float64, Int}, x::Vector{Float64})

Compute y = A*x using a hybrid approach that selects the best algorithm based on matrix size and sparsity.
This function automatically chooses between:
1. SIMD-optimized CSR format for small to medium matrices
2. Multi-threaded blocked CSR format for large matrices
3. Standard CSC multiplication for very sparse matrices

# Arguments
- `y`: Output vector (pre-allocated)
- `A`: Input matrix in CSC format
- `x`: Input vector
"""
function hybrid_matrix_vector_product!(y::Vector{Float64}, A::SparseMatrixCSC{Float64, Int}, x::Vector{Float64})
    n_rows, n_cols = size(A)
    nnz = nnz(A)
    sparsity = nnz / (n_rows * n_cols)
    
    # Clear output vector
    fill!(y, 0.0)
    
    # Choose algorithm based on matrix size and sparsity
    if n_rows < 1000 || sparsity < 0.0001
        # For very small matrices or extremely sparse matrices, use standard CSC multiplication
        standard_csc_matvec!(y, A, x)
    elseif n_rows < 5000
        # For small matrices, use optimized CSC with SIMD
        optimized_csc_matvec!(y, A, x)
    elseif n_rows < 50000
        # For medium-sized matrices, use SIMD-optimized CSR
        csr_simd_matvec!(y, A, x)
    else
        # For large matrices, use multi-threaded blocked CSR
        blocked_csr_parallel_matvec!(y, A, x)
    end
end

"""
    standard_csc_matvec!(y::Vector{Float64}, A::SparseMatrixCSC{Float64, Int}, x::Vector{Float64})

Compute y = A*x using standard CSC format. This is efficient for very sparse matrices.
"""
function standard_csc_matvec!(y::Vector{Float64}, A::SparseMatrixCSC{Float64, Int}, x::Vector{Float64})
    n_rows, n_cols = size(A)
    
    # Standard CSC matrix-vector product
    for col in 1:n_cols
        x_val = x[col]
        for j in A.colptr[col]:A.colptr[col+1]-1
            row = A.rowval[j]
            y[row] += A.nzval[j] * x_val
        end
    end
end

"""
    optimized_csc_matvec!(y::Vector{Float64}, A::SparseMatrixCSC{Float64, Int}, x::Vector{Float64})

Compute y = A*x using optimized CSC format with SIMD and cache-friendly access patterns.
"""
function optimized_csc_matvec!(y::Vector{Float64}, A::SparseMatrixCSC{Float64, Int}, x::Vector{Float64})
    n_rows, n_cols = size(A)
    
    # Process columns in blocks for better cache utilization
    block_size = 16  # Typical L1 cache line size in doubles
    
    # Process blocks of columns
    for block_start in 1:block_size:n_cols
        block_end = min(block_start + block_size - 1, n_cols)
        
        # Pre-fetch x values for this block into cache
        x_block = Vector{Float64}(undef, block_end - block_start + 1)
        for i in 1:length(x_block)
            x_block[i] = x[block_start + i - 1]
        end
        
        # Process each column in the block
        for col_offset in 1:length(x_block)
            col = block_start + col_offset - 1
            x_val = x_block[col_offset]
            
            # Skip if x value is zero (common in sparse problems)
            if x_val == 0.0
                continue
            end
            
            # Process non-zeros in chunks of 4 for SIMD
            j = A.colptr[col]
            end_j = A.colptr[col+1] - 1
            
            # Process 4 elements at a time if possible
            while j + 3 <= end_j
                # Load 4 rows and values
                row1 = A.rowval[j]
                row2 = A.rowval[j+1]
                row3 = A.rowval[j+2]
                row4 = A.rowval[j+3]
                
                val1 = A.nzval[j] * x_val
                val2 = A.nzval[j+1] * x_val
                val3 = A.nzval[j+2] * x_val
                val4 = A.nzval[j+3] * x_val
                
                # Update y values
                y[row1] += val1
                y[row2] += val2
                y[row3] += val3
                y[row4] += val4
                
                j += 4
            end
            
            # Process remaining elements
            while j <= end_j
                row = A.rowval[j]
                y[row] += A.nzval[j] * x_val
                j += 1
            end
        end
    end
end

"""
    csr_simd_matvec!(y::Vector{Float64}, A::SparseMatrixCSC{Float64, Int}, x::Vector{Float64})

Compute y = A*x using SIMD-optimized CSR format. This is efficient for medium-sized matrices.
"""
function csr_simd_matvec!(y::Vector{Float64}, A::SparseMatrixCSC{Float64, Int}, x::Vector{Float64})
    n_rows, n_cols = size(A)
    
    # Convert CSC to CSR for row-wise access
    row_ptr, col_idx, values = csc_to_csr(A)
    
    # Analyze row lengths for better vectorization strategy
    avg_row_length = nnz(A) / n_rows
    
    if avg_row_length < 5
        # For very sparse rows, use specialized implementation
        sparse_row_csr_matvec!(y, row_ptr, col_idx, values, x, n_rows)
    else
        # For denser rows, use SIMD-optimized implementation
        # Pre-fetch x values into cache-friendly layout if beneficial
        if n_cols <= 10000 && avg_row_length > 10
            # Create a cache-aligned copy of x for better memory access
            x_aligned = similar(x)
            copyto!(x_aligned, x)
            
            # SIMD-optimized CSR matrix-vector product with aligned data
            @turbo for row in 1:n_rows
                sum_val = 0.0
                for j in row_ptr[row]:row_ptr[row+1]-1
                    col = col_idx[j]
                    sum_val += values[j] * x_aligned[col]
                end
                y[row] = sum_val
            end
        else
            # Standard SIMD-optimized CSR matrix-vector product
            @turbo for row in 1:n_rows
                sum_val = 0.0
                for j in row_ptr[row]:row_ptr[row+1]-1
                    col = col_idx[j]
                    sum_val += values[j] * x[col]
                end
                y[row] = sum_val
            end
        end
    end
end

"""    sparse_row_csr_matvec!(y::Vector{Float64}, row_ptr::Vector{Int}, col_idx::Vector{Int}, 
                           values::Vector{Float64}, x::Vector{Float64}, n_rows::Int)

Specialized implementation for matrices with very sparse rows.
"""
function sparse_row_csr_matvec!(y::Vector{Float64}, row_ptr::Vector{Int}, col_idx::Vector{Int}, 
                           values::Vector{Float64}, x::Vector{Float64}, n_rows::Int)
    for row in 1:n_rows
        start_idx = row_ptr[row]
        end_idx = row_ptr[row+1] - 1
        n_elements = end_idx - start_idx + 1
        
        if n_elements == 0
            y[row] = 0.0
        elseif n_elements == 1
            # Optimize for single-element rows (common in CFD matrices)
            j = start_idx
            y[row] = values[j] * x[col_idx[j]]
        elseif n_elements == 2
            # Optimize for two-element rows
            j1 = start_idx
            j2 = start_idx + 1
            y[row] = values[j1] * x[col_idx[j1]] + values[j2] * x[col_idx[j2]]
        elseif n_elements == 3
            # Optimize for three-element rows
            j1 = start_idx
            j2 = start_idx + 1
            j3 = start_idx + 2
            y[row] = values[j1] * x[col_idx[j1]] + 
                     values[j2] * x[col_idx[j2]] + 
                     values[j3] * x[col_idx[j3]]
        else
            # Fall back to loop for rows with more elements
            sum_val = 0.0
            for j in start_idx:end_idx
                sum_val += values[j] * x[col_idx[j]]
            end
            y[row] = sum_val
        end
    end
end

"""
    blocked_csr_parallel_matvec!(y::Vector{Float64}, A::SparseMatrixCSC{Float64, Int}, x::Vector{Float64})

Compute y = A*x using multi-threaded blocked CSR format. This is efficient for large matrices.
"""
function blocked_csr_parallel_matvec!(y::Vector{Float64}, A::SparseMatrixCSC{Float64, Int}, x::Vector{Float64})
    n_rows, n_cols = size(A)
    
    # Convert CSC to CSR for row-wise access
    row_ptr, col_idx, values = csc_to_csr(A)
    
    # Determine block size and number of blocks
    n_threads = Threads.nthreads()
    block_size = cld(n_rows, n_threads)
    n_blocks = cld(n_rows, block_size)
    
    # Multi-threaded blocked CSR matrix-vector product
    Threads.@threads for block in 1:n_blocks
        start_row = (block - 1) * block_size + 1
        end_row = min(block * block_size, n_rows)
        
        for row in start_row:end_row
            sum_val = 0.0
            
            # Process row in chunks for better cache utilization
            j = row_ptr[row]
            chunk_end = row_ptr[row+1] - 1
            
            # Process 4 elements at a time if possible
            while j + 3 <= chunk_end
                sum_val += values[j] * x[col_idx[j]] +
                           values[j+1] * x[col_idx[j+1]] +
                           values[j+2] * x[col_idx[j+2]] +
                           values[j+3] * x[col_idx[j+3]]
                j += 4
            end
            
            # Process remaining elements
            while j <= chunk_end
                sum_val += values[j] * x[col_idx[j]]
                j += 1
            end
            
            y[row] = sum_val
        end
    end
end

"""
    csc_to_csr(A::SparseMatrixCSC{Float64, Int})

Convert a matrix from CSC format to CSR format for efficient row-wise access.

# Returns
- `row_ptr`: Row pointers
- `col_idx`: Column indices
- `values`: Non-zero values
"""
function csc_to_csr(A::SparseMatrixCSC{Float64, Int})
    n_rows, n_cols = size(A)
    nnz = nnz(A)
    
    # Count non-zeros per row
    row_counts = zeros(Int, n_rows)
    for j in 1:nnz
        row = A.rowval[j]
        row_counts[row] += 1
    end
    
    # Compute row pointers
    row_ptr = zeros(Int, n_rows + 1)
    row_ptr[1] = 1
    for i in 1:n_rows
        row_ptr[i+1] = row_ptr[i] + row_counts[i]
    end
    
    # Fill CSR arrays
    col_idx = zeros(Int, nnz)
    values = zeros(Float64, nnz)
    next_pos = copy(row_ptr)
    
    for col in 1:n_cols
        for j in A.colptr[col]:A.colptr[col+1]-1
            row = A.rowval[j]
            pos = next_pos[row]
            col_idx[pos] = col
            values[pos] = A.nzval[j]
            next_pos[row] += 1
        end
    end
    
    return row_ptr, col_idx, values
end

"""
    hybrid_matrix_matrix_product(A::SparseMatrixCSC{Float64, Int}, B::SparseMatrixCSC{Float64, Int})

Compute C = A*B using a hybrid approach that selects the best algorithm based on matrix sizes and sparsity.

# Returns
- `C`: Result matrix in CSC format
"""
function hybrid_matrix_matrix_product(A::SparseMatrixCSC{Float64, Int}, B::SparseMatrixCSC{Float64, Int})
    m, k = size(A)
    k2, n = size(B)
    @assert k == k2 "Matrix dimensions must match for multiplication"
    
    nnz_A = nnz(A)
    nnz_B = nnz(B)
    sparsity_A = nnz_A / (m * k)
    sparsity_B = nnz_B / (k * n)
    
    # Choose algorithm based on matrix sizes and sparsity
    if m < 1000 || n < 1000 || (sparsity_A < 0.001 && sparsity_B < 0.001)
        # For small matrices or very sparse matrices, use standard sparse multiplication
        return A * B
    else
        # For larger matrices, use a blocked approach
        return blocked_matrix_matrix_product(A, B)
    end
end

"""
    blocked_matrix_matrix_product(A::SparseMatrixCSC{Float64, Int}, B::SparseMatrixCSC{Float64, Int})

Compute C = A*B using a blocked approach for better cache utilization.

# Returns
- `C`: Result matrix in CSC format
"""
function blocked_matrix_matrix_product(A::SparseMatrixCSC{Float64, Int}, B::SparseMatrixCSC{Float64, Int})
    m, k = size(A)
    k, n = size(B)
    
    # Convert B to CSR for efficient row access
    B_row_ptr, B_col_idx, B_values = csc_to_csr(B)
    
    # Determine block sizes
    block_size = 128  # Adjust based on cache size
    m_blocks = cld(m, block_size)
    n_blocks = cld(n, block_size)
    k_blocks = cld(k, block_size)
    
    # Initialize result matrix
    C = spzeros(m, n)
    
    # Process blocks
    for mb in 1:m_blocks
        m_start = (mb - 1) * block_size + 1
        m_end = min(mb * block_size, m)
        
        for nb in 1:n_blocks
            n_start = (nb - 1) * block_size + 1
            n_end = min(nb * block_size, n)
            
            # Initialize result block
            C_block = zeros(m_end - m_start + 1, n_end - n_start + 1)
            
            for kb in 1:k_blocks
                k_start = (kb - 1) * block_size + 1
                k_end = min(kb * block_size, k)
                
                # Process sub-matrices
                for i in m_start:m_end
                    i_local = i - m_start + 1
                    
                    for j in A.colptr[i]:A.colptr[i+1]-1
                        row_A = A.rowval[j]
                        if row_A >= k_start && row_A <= k_end
                            val_A = A.nzval[j]
                            row_A_local = row_A - k_start + 1
                            
                            for l in B_row_ptr[row_A]:B_row_ptr[row_A+1]-1
                                col_B = B_col_idx[l]
                                if col_B >= n_start && col_B <= n_end
                                    val_B = B_values[l]
                                    col_B_local = col_B - n_start + 1
                                    
                                    C_block[i_local, col_B_local] += val_A * val_B
                                end
                            end
                        end
                    end
                end
            end
            
            # Add block to result matrix
            for i_local in 1:size(C_block, 1)
                i = m_start + i_local - 1
                for j_local in 1:size(C_block, 2)
                    j = n_start + j_local - 1
                    if C_block[i_local, j_local] != 0
                        C[i, j] = C_block[i_local, j_local]
                    end
                end
            end
        end
    end
    
    return C
end

# Export functions
export hybrid_matrix_vector_product!, hybrid_matrix_matrix_product
