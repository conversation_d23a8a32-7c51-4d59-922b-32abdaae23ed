"""
    EnhancedPreconditioners.jl

This module provides enhanced preconditioners for challenging CFD problems in JuliaFOAM.
It implements:

1. Incomplete LU factorization with threshold dropping (ILUT)
2. Sparse Approximate Inverse with adaptive pattern (SPAI-A)
3. Block ILU for coupled systems
4. Schur complement preconditioner for saddle-point problems
"""
module EnhancedPreconditioners

using LinearAlgebra
using SparseArrays
using Base.Threads

export ilut_preconditioner, spai_adaptive_preconditioner, block_ilu_preconditioner, schur_complement_preconditioner
export apply_preconditioner!

"""
    ilut_preconditioner(A::SparseMatrixCSC{Float64, Int}, droptol::Float64=1e-4, lfil::Int=10)

Create an Incomplete LU factorization with threshold dropping (ILUT) preconditioner.

# Arguments
- `A`: Sparse matrix
- `droptol`: Drop tolerance for small entries
- `lfil`: Maximum fill-in per row

# Returns
- `Tuple{SparseMatrixCSC{Float64, Int}, SparseMatrixCSC{Float64, Int}}`: L and U factors
"""
function ilut_preconditioner(A::SparseMatrixCSC{Float64, Int}, droptol::Float64=1e-4, lfil::Int=10)
    n = size(A, 1)

    # Initialize L and U factors
    L_rows = Int[]
    L_cols = Int[]
    L_vals = Float64[]

    U_rows = Int[]
    U_cols = Int[]
    U_vals = Float64[]

    # Add diagonal entries to L
    for i in 1:n
        push!(L_rows, i)
        push!(L_cols, i)
        push!(L_vals, 1.0)
    end

    # Working arrays
    w = zeros(Float64, n)  # Working vector
    indr = zeros(Int, n)   # Row indices
    indc = zeros(Int, n)   # Column indices

    # Process each row
    for i in 1:n
        # Initialize working vector with row i of A
        fill!(w, 0.0)
        for j in nzrange(A, i)
            row = A.rowval[j]
            w[row] = A.nzval[j]
        end

        # Eliminate previous rows
        for j in 1:i-1
            # If w[j] is nonzero, eliminate j-th row from i-th row
            if abs(w[j]) > droptol
                # Get the multiplier
                idx_u = findfirst(x -> x == j && U_cols[x] == j, U_rows)
                if idx_u !== nothing
                    w[j] /= U_vals[idx_u]
                else
                    w[j] = 0.0
                end

                # Apply the elimination
                for k in nzrange(A, j)
                    row = A.rowval[k]
                    if row >= j
                        w[row] -= w[j] * A.nzval[k]
                    end
                end
            end
        end

        # Store the L and U factors
        nnzL = 0
        nnzU = 0

        # Count nonzeros in L and U parts
        for j in 1:i-1
            if abs(w[j]) > droptol
                nnzL += 1
                indr[nnzL] = j
            end
        end

        for j in i:n
            if abs(w[j]) > droptol
                nnzU += 1
                indc[nnzU] = j
            end
        end

        # Sort by magnitude and keep only the largest lfil entries
        if nnzL > lfil
            # Sort L part
            perm = sortperm([abs(w[indr[j]]) for j in 1:nnzL], rev=true)
            indr[1:min(lfil, nnzL)] = indr[perm[1:min(lfil, nnzL)]]
            nnzL = min(lfil, nnzL)
        end

        if nnzU > lfil
            # Sort U part
            perm = sortperm([abs(w[indc[j]]) for j in 1:nnzU], rev=true)
            indc[1:min(lfil, nnzU)] = indc[perm[1:min(lfil, nnzU)]]
            nnzU = min(lfil, nnzU)
        end

        # Make sure diagonal is included in U
        if i <= n && all(indc[1:nnzU] .!= i)
            nnzU += 1
            indc[nnzU] = i
        end

        # Add entries to L
        for j in 1:nnzL
            push!(L_rows, i)
            push!(L_cols, indr[j])
            push!(L_vals, w[indr[j]])
        end

        # Add entries to U
        for j in 1:nnzU
            push!(U_rows, i)
            push!(U_cols, indc[j])
            push!(U_vals, w[indc[j]])
        end
    end

    # Create sparse matrices
    L = sparse(L_rows, L_cols, L_vals, n, n)
    U = sparse(U_rows, U_cols, U_vals, n, n)

    return L, U
end

"""
    apply_ilut_preconditioner!(z::Vector{Float64}, r::Vector{Float64}, L::SparseMatrixCSC{Float64, Int}, U::SparseMatrixCSC{Float64, Int})

Apply ILUT preconditioner to solve Mz = r.

# Arguments
- `z`: Solution vector (will be overwritten)
- `r`: Right-hand side vector
- `L`: Lower triangular factor
- `U`: Upper triangular factor
"""
function apply_ilut_preconditioner!(z::Vector{Float64}, r::Vector{Float64}, L::SparseMatrixCSC{Float64, Int}, U::SparseMatrixCSC{Float64, Int})
    n = length(r)

    # Solve Ly = r
    y = similar(r)
    for i in 1:n
        y[i] = r[i]
        for j in nzrange(L, i)
            row = L.rowval[j]
            if row < i
                y[i] -= L.nzval[j] * y[row]
            end
        end
    end

    # Solve Uz = y
    for i in n:-1:1
        z[i] = y[i]
        for j in nzrange(U, i)
            row = U.rowval[j]
            if row > i
                z[i] -= U.nzval[j] * z[row]
            end
        end

        # Divide by diagonal
        for j in nzrange(U, i)
            row = U.rowval[j]
            if row == i
                z[i] /= U.nzval[j]
                break
            end
        end
    end
end

"""
    spai_adaptive_preconditioner(A::SparseMatrixCSC{Float64, Int}, tol::Float64=1e-4, max_pattern_size::Int=20)

Create a Sparse Approximate Inverse preconditioner with adaptive pattern.

# Arguments
- `A`: Sparse matrix
- `tol`: Tolerance for pattern selection
- `max_pattern_size`: Maximum pattern size per row

# Returns
- `SparseMatrixCSC{Float64, Int}`: Approximate inverse
"""
function spai_adaptive_preconditioner(A::SparseMatrixCSC{Float64, Int}, tol::Float64=1e-4, max_pattern_size::Int=20)
    n = size(A, 1)

    # Initialize approximate inverse
    M_rows = Int[]
    M_cols = Int[]
    M_vals = Float64[]

    # Process each row
    for i in 1:n
        # Initialize pattern with diagonal
        pattern = [i]

        # Initialize unit vector
        e_i = zeros(Float64, n)
        e_i[i] = 1.0

        # Initial approximation
        m_i = zeros(Float64, n)

        # Iteratively expand pattern
        for iter in 1:max_pattern_size
            # Extract submatrix
            A_pattern = A[pattern, :]

            # Solve least squares problem
            m_pattern = A_pattern \ e_i[pattern]

            # Update approximation
            fill!(m_i, 0.0)
            for (j, idx) in enumerate(pattern)
                m_i[idx] = m_pattern[j]
            end

            # Compute residual
            r = e_i - A * m_i
            r_norm = norm(r)

            # Check convergence
            if r_norm < tol
                break
            end

            # Find index of maximum residual
            max_idx = argmax(abs.(r))

            # Add to pattern if not already included
            if !(max_idx in pattern) && length(pattern) < max_pattern_size
                push!(pattern, max_idx)
            else
                # Pattern is full or no new indices to add
                break
            end
        end

        # Add nonzero entries to M
        for j in 1:n
            if abs(m_i[j]) > tol
                push!(M_rows, i)
                push!(M_cols, j)
                push!(M_vals, m_i[j])
            end
        end
    end

    # Create sparse matrix
    M = sparse(M_rows, M_cols, M_vals, n, n)

    return M
end

"""
    apply_spai_adaptive_preconditioner!(z::Vector{Float64}, r::Vector{Float64}, M::SparseMatrixCSC{Float64, Int})

Apply SPAI-A preconditioner to solve Mz = r.

# Arguments
- `z`: Solution vector (will be overwritten)
- `r`: Right-hand side vector
- `M`: Approximate inverse
"""
function apply_spai_adaptive_preconditioner!(z::Vector{Float64}, r::Vector{Float64}, M::SparseMatrixCSC{Float64, Int})
    mul!(z, M, r)
end

"""
    block_ilu_preconditioner(A::SparseMatrixCSC{Float64, Int}, block_size::Int=3, droptol::Float64=1e-4)

Create a Block ILU preconditioner for coupled systems.

# Arguments
- `A`: Sparse matrix
- `block_size`: Size of blocks (typically 3 for 3D velocity)
- `droptol`: Drop tolerance for small entries

# Returns
- `Tuple{SparseMatrixCSC{Float64, Int}, SparseMatrixCSC{Float64, Int}}`: Block L and U factors
"""
function block_ilu_preconditioner(A::SparseMatrixCSC{Float64, Int}, block_size::Int=3, droptol::Float64=1e-4)
    n = size(A, 1)
    n_blocks = div(n, block_size)

    # Check if matrix size is divisible by block_size
    if n % block_size != 0
        error("Matrix size ($n) is not divisible by block_size ($block_size)")
    end

    # Initialize block L and U factors
    L_rows = Int[]
    L_cols = Int[]
    L_vals = Float64[]

    U_rows = Int[]
    U_cols = Int[]
    U_vals = Float64[]

    # Add diagonal blocks to L
    for i in 1:n
        push!(L_rows, i)
        push!(L_cols, i)
        push!(L_vals, 1.0)
    end

    # Process each block row
    for i in 1:n_blocks
        # Block row range
        i_range = ((i-1)*block_size+1):(i*block_size)

        # Initialize working blocks
        W = zeros(Float64, n, block_size)

        # Copy block row i of A to W
        for j in 1:n_blocks
            j_range = ((j-1)*block_size+1):(j*block_size)
            W[j_range, :] = A[j_range, i_range]
        end

        # Eliminate previous block rows
        for j in 1:i-1
            j_range = ((j-1)*block_size+1):(j*block_size)

            # Extract L_ij block
            L_ij = zeros(Float64, block_size, block_size)
            for r in 1:block_size
                for c in 1:block_size
                    row = j_range[r]
                    col = i_range[c]
                    idx = findfirst(x -> x == row && L_cols[x] == col, L_rows)
                    if idx !== nothing
                        L_ij[r, c] = L_vals[idx]
                    end
                end
            end

            # Extract U_jj block
            U_jj = zeros(Float64, block_size, block_size)
            for r in 1:block_size
                for c in 1:block_size
                    row = j_range[r]
                    col = j_range[c]
                    idx = findfirst(x -> x == row && U_cols[x] == col, U_rows)
                    if idx !== nothing
                        U_jj[r, c] = U_vals[idx]
                    end
                end
            end

            # Skip if L_ij is zero
            if norm(L_ij) < droptol
                continue
            end

            # Compute L_ij * U_jj
            LU = L_ij * inv(U_jj)

            # Update W
            for k in j:n_blocks
                k_range = ((k-1)*block_size+1):(k*block_size)

                # Extract U_jk block
                U_jk = zeros(Float64, block_size, block_size)
                for r in 1:block_size
                    for c in 1:block_size
                        row = j_range[r]
                        col = k_range[c]
                        idx = findfirst(x -> x == row && U_cols[x] == col, U_rows)
                        if idx !== nothing
                            U_jk[r, c] = U_vals[idx]
                        end
                    end
                end

                # Update W_ik
                W[k_range, :] -= LU * U_jk
            end
        end

        # Store the L and U factors
        for j in 1:i-1
            j_range = ((j-1)*block_size+1):(j*block_size)

            # Extract and store L_ij block
            L_ij = W[j_range, :]

            # Apply drop tolerance
            for r in 1:block_size
                for c in 1:block_size
                    if abs(L_ij[r, c]) > droptol
                        push!(L_rows, j_range[r])
                        push!(L_cols, i_range[c])
                        push!(L_vals, L_ij[r, c])
                    end
                end
            end
        end

        # Extract and store U_ii block
        U_ii = W[i_range, :]

        # Apply drop tolerance
        for r in 1:block_size
            for c in 1:block_size
                if abs(U_ii[r, c]) > droptol
                    push!(U_rows, i_range[r])
                    push!(U_cols, i_range[c])
                    push!(U_vals, U_ii[r, c])
                end
            end
        end
    end

    # Create sparse matrices
    L = sparse(L_rows, L_cols, L_vals, n, n)
    U = sparse(U_rows, U_cols, U_vals, n, n)

    return L, U, block_size
end

"""
    apply_block_ilu_preconditioner!(z::Vector{Float64}, r::Vector{Float64}, L::SparseMatrixCSC{Float64, Int}, U::SparseMatrixCSC{Float64, Int}, block_size::Int=3)

Apply Block ILU preconditioner to solve Mz = r.

# Arguments
- `z`: Solution vector (will be overwritten)
- `r`: Right-hand side vector
- `L`: Lower triangular factor
- `U`: Upper triangular factor
- `block_size`: Size of blocks
"""
function apply_block_ilu_preconditioner!(z::Vector{Float64}, r::Vector{Float64}, L::SparseMatrixCSC{Float64, Int}, U::SparseMatrixCSC{Float64, Int}, block_size::Int=3)
    n = length(r)
    n_blocks = div(n, block_size)

    # Solve Ly = r
    y = similar(r)
    for i in 1:n_blocks
        i_range = ((i-1)*block_size+1):(i*block_size)

        # Initialize y_i
        y[i_range] = r[i_range]

        # Subtract L_ij * y_j for j < i
        for j in 1:i-1
            j_range = ((j-1)*block_size+1):(j*block_size)

            # Extract L_ij block
            L_ij = zeros(Float64, block_size, block_size)
            for r in 1:block_size
                for c in 1:block_size
                    row = i_range[r]
                    col = j_range[c]
                    for j in nzrange(L, row)
                        if L.rowval[j] == col
                            L_ij[r, c] = L.nzval[j]
                            break
                        end
                    end
                end
            end

            # Subtract L_ij * y_j
            y[i_range] -= L_ij * y[j_range]
        end
    end

    # Solve Uz = y
    for i in n_blocks:-1:1
        i_range = ((i-1)*block_size+1):(i*block_size)

        # Initialize z_i
        z[i_range] = y[i_range]

        # Subtract U_ij * z_j for j > i
        for j in i+1:n_blocks
            j_range = ((j-1)*block_size+1):(j*block_size)

            # Extract U_ij block
            U_ij = zeros(Float64, block_size, block_size)
            for r in 1:block_size
                for c in 1:block_size
                    row = i_range[r]
                    col = j_range[c]
                    for j in nzrange(U, row)
                        if U.rowval[j] == col
                            U_ij[r, c] = U.nzval[j]
                            break
                        end
                    end
                end
            end

            # Subtract U_ij * z_j
            z[i_range] -= U_ij * z[j_range]
        end

        # Extract U_ii block
        U_ii = zeros(Float64, block_size, block_size)
        for r in 1:block_size
            for c in 1:block_size
                row = i_range[r]
                col = i_range[c]
                for j in nzrange(U, row)
                    if U.rowval[j] == col
                        U_ii[r, c] = U.nzval[j]
                        break
                    end
                end
            end
        end

        # Solve U_ii * z_i = y_i
        z[i_range] = U_ii \ z[i_range]
    end
end

"""
    schur_complement_preconditioner(A::SparseMatrixCSC{Float64, Int}, n_velocity::Int)

Create a Schur complement preconditioner for saddle-point problems.

# Arguments
- `A`: Sparse matrix
- `n_velocity`: Number of velocity unknowns

# Returns
- `Dict`: Preconditioner data
"""
function schur_complement_preconditioner(A::SparseMatrixCSC{Float64, Int}, n_velocity::Int)
    n = size(A, 1)
    n_pressure = n - n_velocity

    # Extract blocks
    A_uu = A[1:n_velocity, 1:n_velocity]
    A_up = A[1:n_velocity, n_velocity+1:n]
    A_pu = A[n_velocity+1:n, 1:n_velocity]
    A_pp = A[n_velocity+1:n, n_velocity+1:n]

    # Create approximate inverse of A_uu
    L_u, U_u = ilut_preconditioner(A_uu)

    # Create approximate Schur complement
    S_approx = sparse(A_pp - A_pu * (U_u \ (L_u \ A_up)))

    # Create preconditioner for Schur complement
    L_s, U_s = ilut_preconditioner(S_approx)

    # Return preconditioner data
    return Dict(
        :n_velocity => n_velocity,
        :n_pressure => n_pressure,
        :L_u => L_u,
        :U_u => U_u,
        :L_s => L_s,
        :U_s => U_s,
        :A_uu => A_uu,
        :A_up => A_up,
        :A_pu => A_pu,
        :A_pp => A_pp
    )
end

"""
    apply_schur_complement_preconditioner!(z::Vector{Float64}, r::Vector{Float64}, precond_data::Dict)

Apply Schur complement preconditioner to solve Mz = r.

# Arguments
- `z`: Solution vector (will be overwritten)
- `r`: Right-hand side vector
- `precond_data`: Preconditioner data
"""
function apply_schur_complement_preconditioner!(z::Vector{Float64}, r::Vector{Float64}, precond_data::Dict)
    n_velocity = precond_data[:n_velocity]
    n_pressure = precond_data[:n_pressure]

    # Extract blocks
    r_u = r[1:n_velocity]
    r_p = r[n_velocity+1:n_velocity+n_pressure]

    # Solve for pressure
    y_p = similar(r_p)
    apply_ilut_preconditioner!(y_p, r_p, precond_data[:L_s], precond_data[:U_s])

    # Compute residual for velocity
    r_u_mod = r_u - precond_data[:A_up] * y_p

    # Solve for velocity
    y_u = similar(r_u)
    apply_ilut_preconditioner!(y_u, r_u_mod, precond_data[:L_u], precond_data[:U_u])

    # Combine results
    z[1:n_velocity] = y_u
    z[n_velocity+1:n_velocity+n_pressure] = y_p
end

"""
    apply_preconditioner!(z::Vector{Float64}, r::Vector{Float64}, preconditioner::Symbol, precond_data::Any)

Apply a preconditioner to solve Mz = r.

# Arguments
- `z`: Solution vector (will be overwritten)
- `r`: Right-hand side vector
- `preconditioner`: Type of preconditioner (:ilut, :spai_adaptive, :block_ilu, :schur_complement)
- `precond_data`: Preconditioner data
"""
function apply_preconditioner!(z::Vector{Float64}, r::Vector{Float64}, preconditioner::Symbol, precond_data::Any)
    if preconditioner == :ilut
        L, U = precond_data
        apply_ilut_preconditioner!(z, r, L, U)
    elseif preconditioner == :spai_adaptive
        M = precond_data
        apply_spai_adaptive_preconditioner!(z, r, M)
    elseif preconditioner == :block_ilu
        L, U, block_size = precond_data
        apply_block_ilu_preconditioner!(z, r, L, U, block_size)
    elseif preconditioner == :schur_complement
        apply_schur_complement_preconditioner!(z, r, precond_data)
    else
        error("Unknown preconditioner type: $preconditioner")
    end
end

end # module EnhancedPreconditioners
