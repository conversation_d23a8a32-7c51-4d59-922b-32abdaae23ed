"""
    MeshObjectPools.jl

This module provides memory pools for frequently allocated mesh objects to reduce
garbage collection overhead and improve performance.
"""
module MeshObjectPools

using StaticArrays
using ..MemoryManager
using ..JuliaFOAM: Cell, Face

export CellPool, FacePool, get_cell!, get_face!, return_cell!, return_face!
export with_cell, with_face

"""
    initialize_cell_pool(initial_size::Int=1000)

Initialize a memory pool for Cell objects.

# Arguments
- `initial_size`: Initial number of cells in the pool

# Returns
- `MemoryPool{Cell}`: Memory pool for Cell objects
"""
function initialize_cell_pool(initial_size::Int=1000)
    return MemoryPool{Cell}(initial_size, () -> Cell(
        Int32[],  # faces
        SVector{3, Float64}(0.0, 0.0, 0.0),  # center
        0.0  # volume
    ))
end

"""
    initialize_face_pool(initial_size::Int=1000)

Initialize a memory pool for Face objects.

# Arguments
- `initial_size`: Initial number of faces in the pool

# Returns
- `MemoryPool{Face}`: Memory pool for Face objects
"""
function initialize_face_pool(initial_size::Int=1000)
    return MemoryPool{Face}(initial_size, () -> <PERSON>(
        Int32(0),  # owner
        Int32(-1),  # neighbour
        SVector{3, Float64}(0.0, 0.0, 0.0),  # area
        SVector{3, Float64}(0.0, 0.0, 0.0)   # center
    ))
end

# Global pools
const CELL_POOL = initialize_cell_pool()
const FACE_POOL = initialize_face_pool()

"""
    get_cell!(center::SVector{3, Float64}, volume::Float64, faces::Vector{Int32}=Int32[])

Get a Cell object from the pool and initialize it.

# Arguments
- `center`: Cell center
- `volume`: Cell volume
- `faces`: Cell faces

# Returns
- `Cell`: Initialized Cell object
"""
function get_cell!(center::SVector{3, Float64}, volume::Float64, faces::Vector{Int32}=Int32[])
    cell = get_from_pool!(CELL_POOL, () -> Cell(
        Int32[],
        SVector{3, Float64}(0.0, 0.0, 0.0),
        0.0
    ))

    # Initialize cell
    cell.faces = faces
    cell.center = center
    cell.volume = volume

    return cell
end

"""
    get_face!(center::SVector{3, Float64}, area::SVector{3, Float64}, owner::Int, neighbour::Int)

Get a Face object from the pool and initialize it.

# Arguments
- `center`: Face center
- `area`: Face area vector
- `owner`: Owner cell index
- `neighbour`: Neighbour cell index

# Returns
- `Face`: Initialized Face object
"""
function get_face!(center::SVector{3, Float64}, area::SVector{3, Float64}, owner::Int, neighbour::Int)
    face = get_from_pool!(FACE_POOL, () -> Face(
        Int32(0),
        Int32(-1),
        SVector{3, Float64}(0.0, 0.0, 0.0),
        SVector{3, Float64}(0.0, 0.0, 0.0)
    ))

    # Initialize face
    face.center = center
    face.area = area
    face.owner = Int32(owner)
    face.neighbour = Int32(neighbour)

    return face
end

"""
    return_cell!(cell::Cell)

Return a Cell object to the pool.

# Arguments
- `cell`: Cell object to return
"""
function return_cell!(cell::Cell)
    return_to_pool!(CELL_POOL, cell)
end

"""
    return_face!(face::Face)

Return a Face object to the pool.

# Arguments
- `face`: Face object to return
"""
function return_face!(face::Face)
    return_to_pool!(FACE_POOL, face)
end

"""
    with_cell(f::Function, center::SVector{3, Float64}, volume::Float64, faces::Vector{Int32}=Int32[])

Execute a function with a Cell object from the pool, then return it.

# Arguments
- `f`: Function to execute with the Cell object
- `center`: Cell center
- `volume`: Cell volume
- `faces`: Cell faces

# Returns
- Result of the function call
"""
function with_cell(f::Function, center::SVector{3, Float64}, volume::Float64, faces::Vector{Int32}=Int32[])
    cell = get_cell!(center, volume, faces)
    result = f(cell)
    return_cell!(cell)
    return result
end

"""
    with_face(f::Function, center::SVector{3, Float64}, area::SVector{3, Float64}, owner::Int, neighbour::Int)

Execute a function with a Face object from the pool, then return it.

# Arguments
- `f`: Function to execute with the Face object
- `center`: Face center
- `area`: Face area vector
- `owner`: Owner cell index
- `neighbour`: Neighbour cell index

# Returns
- Result of the function call
"""
function with_face(f::Function, center::SVector{3, Float64}, area::SVector{3, Float64}, owner::Int, neighbour::Int)
    face = get_face!(center, area, owner, neighbour)
    result = f(face)
    return_face!(face)
    return result
end

"""
    reset_pools!()

Reset all object pools, returning all objects to the pools.
"""
function reset_pools!()
    # This would need to be implemented if we track all allocated objects
    # For now, we just clear the in-use sets
    empty!(CELL_POOL.in_use)
    empty!(FACE_POOL.in_use)
end

end # module MeshObjectPools
