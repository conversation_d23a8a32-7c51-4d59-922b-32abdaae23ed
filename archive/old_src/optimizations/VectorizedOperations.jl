"""
    VectorizedOperations.jl

This module provides SIMD-vectorized implementations of core operations for JuliaFOAM.
It uses <PERSON>'s built-in SIMD capabilities to accelerate computations.
"""
module VectorizedOperations

using StaticArrays
using LinearAlgebra
using SparseArrays
using Base.Threads
using SIMD

export vectorized_dot, vectorized_axpy!, vectorized_scale!
export vectorized_gradient!, vectorized_divergence!, vectorized_laplacian!
export vectorized_matrix_vector_product!

"""
    vectorized_dot(x::Vector{Float64}, y::Vector{Float64})

Compute the dot product of two vectors using SIMD instructions.

# Arguments
- `x`: First vector
- `y`: Second vector

# Returns
- `Float64`: Dot product
"""
function vectorized_dot(x::Vector{Float64}, y::Vector{Float64})
    n = length(x)
    @assert length(y) == n "Vectors must have the same length"

    # Handle non-multiple-of-4 lengths
    remainder = n % 4
    limit = n - remainder

    # Initialize sum with SIMD vector
    sum_vec = Vec{4, Float64}(0.0)

    # Process 4 elements at a time
    @inbounds for i in 1:4:limit
        x_vec = vload(Vec{4, Float64}, x, i)
        y_vec = vload(Vec{4, Float64}, y, i)
        sum_vec += x_vec * y_vec
    end

    # Sum the elements of the SIMD vector
    result = sum(sum_vec)

    # Handle remaining elements
    @inbounds for i in (limit+1):n
        result += x[i] * y[i]
    end

    return result
end

"""
    vectorized_axpy!(y::Vector{Float64}, a::Float64, x::Vector{Float64})

Compute y = y + a*x using SIMD instructions.

# Arguments
- `y`: Target vector (will be modified)
- `a`: Scalar multiplier
- `x`: Source vector
"""
function vectorized_axpy!(y::Vector{Float64}, a::Float64, x::Vector{Float64})
    n = length(x)
    @assert length(y) == n "Vectors must have the same length"

    # Handle non-multiple-of-4 lengths
    remainder = n % 4
    limit = n - remainder

    # Create SIMD scalar
    a_vec = Vec{4, Float64}(a)

    # Process 4 elements at a time
    @inbounds for i in 1:4:limit
        x_vec = vload(Vec{4, Float64}, x, i)
        y_vec = vload(Vec{4, Float64}, y, i)
        vstore(y_vec + a_vec * x_vec, y, i)
    end

    # Handle remaining elements
    @inbounds for i in (limit+1):n
        y[i] += a * x[i]
    end
end

"""
    vectorized_scale!(x::Vector{Float64}, a::Float64)

Scale a vector by a scalar using SIMD instructions.

# Arguments
- `x`: Vector to scale (will be modified)
- `a`: Scalar multiplier
"""
function vectorized_scale!(x::Vector{Float64}, a::Float64)
    n = length(x)

    # Handle non-multiple-of-4 lengths
    remainder = n % 4
    limit = n - remainder

    # Create SIMD scalar
    a_vec = Vec{4, Float64}(a)

    # Process 4 elements at a time
    @inbounds for i in 1:4:limit
        x_vec = vload(Vec{4, Float64}, x, i)
        vstore(a_vec * x_vec, x, i)
    end

    # Handle remaining elements
    @inbounds for i in (limit+1):n
        x[i] *= a
    end
end

"""
    vectorized_gradient!(grad::Matrix{Float64}, field::Vector{Float64}, mesh)

Calculate the gradient of a scalar field using SIMD-vectorized operations.

# Arguments
- `grad`: Gradient field (output, 3 x n_cells)
- `field`: Scalar field (n_cells)
- `mesh`: Computational mesh
"""
function vectorized_gradient!(grad::Matrix{Float64}, field::Vector{Float64}, mesh)
    n_cells = length(mesh.cells)
    n_faces = length(mesh.faces)

    # Initialize gradient to zero
    fill!(grad, 0.0)

    # Process faces
    for f in 1:n_faces
        face = mesh.faces[f]
        owner = face.owner
        neighbour = face.neighbour

        # Face normal and area
        normal = face.area

        if neighbour > 0  # Internal face
            # Owner and neighbor cell values
            phi_owner = field[owner]
            phi_neighbor = field[neighbour]

            # Face value (linear interpolation)
            phi_face = 0.5 * (phi_owner + phi_neighbor)

            # Accumulate contribution to gradient
            @inbounds for d in 1:3
                grad[d, owner] += phi_face * normal[d]
                grad[d, neighbour] -= phi_face * normal[d]
            end
        else  # Boundary face
            # Owner cell value
            phi_owner = field[owner]

            # Use owner value for boundary (simplified)
            phi_face = phi_owner

            # Accumulate contribution to gradient
            @inbounds for d in 1:3
                grad[d, owner] += phi_face * normal[d]
            end
        end
    end

    # Normalize by cell volumes
    @inbounds for c in 1:n_cells
        vol_inv = 1.0 / mesh.cells[c].volume
        @inbounds for d in 1:3
            grad[d, c] *= vol_inv
        end
    end
end

"""
    vectorized_divergence!(div::Vector{Float64}, field::Matrix{Float64}, mesh)

Calculate the divergence of a vector field using SIMD-vectorized operations.

# Arguments
- `div`: Divergence field (output, n_cells)
- `field`: Vector field (3 x n_cells)
- `mesh`: Computational mesh
"""
function vectorized_divergence!(div::Vector{Float64}, field::Matrix{Float64}, mesh)
    n_cells = length(mesh.cells)
    n_faces = length(mesh.faces)

    # Initialize divergence to zero
    fill!(div, 0.0)

    # Process faces
    for f in 1:n_faces
        face = mesh.faces[f]
        owner = face.owner
        neighbour = face.neighbour

        # Face normal and area
        normal = face.area

        if neighbour > 0  # Internal face
            # Face value (linear interpolation)
            u_face = Vec{3, Float64}(0.0)
            @inbounds for d in 1:3
                u_face = vinsert(u_face, 0.5 * (field[d, owner] + field[d, neighbour]), d-1)
            end

            # Flux through face
            n_vec = Vec{3, Float64}(normal[1], normal[2], normal[3])
            flux = sum(u_face * n_vec)

            # Accumulate contribution to divergence
            div[owner] += flux
            div[neighbour] -= flux
        else  # Boundary face
            # Use owner value for boundary (simplified)
            u_face = Vec{3, Float64}(0.0)
            @inbounds for d in 1:3
                u_face = vinsert(u_face, field[d, owner], d-1)
            end

            # Flux through face
            n_vec = Vec{3, Float64}(normal[1], normal[2], normal[3])
            flux = sum(u_face * n_vec)

            # Accumulate contribution to divergence
            div[owner] += flux
        end
    end

    # Normalize by cell volumes
    @inbounds for c in 1:n_cells
        div[c] /= mesh.cells[c].volume
    end
end

"""
    vectorized_laplacian!(lap::Vector{Float64}, field::Vector{Float64}, mesh)

Calculate the Laplacian of a scalar field using SIMD-vectorized operations.

# Arguments
- `lap`: Laplacian field (output, n_cells)
- `field`: Scalar field (n_cells)
- `mesh`: Computational mesh
"""
function vectorized_laplacian!(lap::Vector{Float64}, field::Vector{Float64}, mesh)
    n_cells = length(mesh.cells)
    n_faces = length(mesh.faces)

    # Initialize Laplacian to zero
    fill!(lap, 0.0)

    # Process faces
    for f in 1:n_faces
        face = mesh.faces[f]
        owner = face.owner
        neighbour = face.neighbour

        # Face normal and area magnitude
        normal = face.area
        area_mag = norm(normal)

        if neighbour > 0  # Internal face
            # Owner and neighbor cell values
            phi_owner = field[owner]
            phi_neighbor = field[neighbour]

            # Owner and neighbor cell centers
            c_owner = mesh.cells[owner].center
            c_neighbor = mesh.cells[neighbour].center

            # Distance between cell centers
            dx = c_neighbor - c_owner
            dist = norm(dx)

            # Gradient at face
            grad_face = (phi_neighbor - phi_owner) / dist

            # Accumulate contribution to Laplacian
            lap[owner] += grad_face * area_mag
            lap[neighbour] -= grad_face * area_mag
        else  # Boundary face
            # For simplicity, assume zero gradient at boundaries
            # In a real implementation, this would use the boundary condition
        end
    end

    # Normalize by cell volumes
    @inbounds for c in 1:n_cells
        lap[c] /= mesh.cells[c].volume
    end
end

"""
    vectorized_matrix_vector_product!(y::Vector{Float64}, A::SparseMatrixCSC{Float64, Int}, x::Vector{Float64})

Perform sparse matrix-vector multiplication y = A*x using SIMD-vectorized operations.

# Arguments
- `y`: Result vector (will be overwritten)
- `x`: Input vector
- `A`: Sparse matrix
"""
function vectorized_matrix_vector_product!(y::Vector{Float64}, A::SparseMatrixCSC{Float64, Int}, x::Vector{Float64})
    m, n = size(A)
    fill!(y, 0.0)

    # Process rows
    for i in 1:m
        # Initialize sum with SIMD vector
        sum_val = 0.0

        # Get range of non-zero elements in this row
        row_range = nzrange(A, i)

        # Process non-zero elements
        for j in row_range
            col = A.rowval[j]
            val = A.nzval[j]
            sum_val += val * x[col]
        end

        # Store result
        y[i] = sum_val
    end
end

end # module VectorizedOperations
