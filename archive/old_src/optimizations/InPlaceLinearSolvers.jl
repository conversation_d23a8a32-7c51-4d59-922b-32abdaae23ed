"""
    InPlaceLinearSolvers.jl

This module provides in-place implementations of linear solvers to reduce allocations
and improve performance. It includes:

1. In-place Conjugate Gradient (CG)
2. In-place BiCGSTAB
3. In-place GMRES
"""
module InPlaceLinearSolvers

using LinearAlgebra
using SparseArrays
using Base.Threads
using ..MemoryManager

export cg_solve_inplace!, bicgstab_solve_inplace!, gmres_solve_inplace!

"""
    cg_solve_inplace!(A, x, b, precond, tol, max_iter, residuals)

Solve Ax = b using the Conjugate Gradient method with in-place operations.

# Arguments
- `A`: Matrix or linear operator
- `x`: Solution vector (will be overwritten)
- `b`: Right-hand side vector
- `precond`: Preconditioner function
- `tol`: Convergence tolerance
- `max_iter`: Maximum number of iterations
- `residuals`: Vector to store residual history (optional)

# Returns
- `Int`: Number of iterations
- `Float64`: Final residual norm
"""
function cg_solve_inplace!(A, x, b, precond, tol, max_iter, residuals=nothing)
    n = length(b)
    
    # Get temporary vectors from memory manager
    r = get_temp_vector(n)
    p = get_temp_vector(n)
    Ap = get_temp_vector(n)
    z = get_temp_vector(n)
    
    # r = b - A*x
    mul!(r, A, x)
    r .= b .- r
    
    # Apply preconditioner: z = M⁻¹r
    precond(z, r)
    
    # p = z
    copyto!(p, z)
    
    # Initial residual norm
    r_norm = norm(r)
    init_r_norm = r_norm
    
    # Store initial residual if requested
    if residuals !== nothing
        push!(residuals, r_norm)
    end
    
    # Check if already converged
    if r_norm < tol
        return 0, r_norm
    end
    
    # Main CG loop
    iter = 0
    rz_old = dot(r, z)
    
    for iter in 1:max_iter
        # Ap = A*p
        mul!(Ap, A, p)
        
        # α = (r'*z) / (p'*Ap)
        alpha = rz_old / dot(p, Ap)
        
        # x = x + α*p
        axpy!(alpha, p, x)
        
        # r = r - α*Ap
        axpy!(-alpha, Ap, r)
        
        # Check convergence
        r_norm = norm(r)
        if residuals !== nothing
            push!(residuals, r_norm)
        end
        
        if r_norm < tol * init_r_norm
            break
        end
        
        # Apply preconditioner: z = M⁻¹r
        precond(z, r)
        
        # β = (r'*z) / (r_old'*z_old)
        rz_new = dot(r, z)
        beta = rz_new / rz_old
        rz_old = rz_new
        
        # p = z + β*p
        p .= z .+ beta .* p
    end
    
    return iter, r_norm
end

"""
    bicgstab_solve_inplace!(A, x, b, precond, tol, max_iter, residuals)

Solve Ax = b using the BiCGSTAB method with in-place operations.

# Arguments
- `A`: Matrix or linear operator
- `x`: Solution vector (will be overwritten)
- `b`: Right-hand side vector
- `precond`: Preconditioner function
- `tol`: Convergence tolerance
- `max_iter`: Maximum number of iterations
- `residuals`: Vector to store residual history (optional)

# Returns
- `Int`: Number of iterations
- `Float64`: Final residual norm
"""
function bicgstab_solve_inplace!(A, x, b, precond, tol, max_iter, residuals=nothing)
    n = length(b)
    
    # Get temporary vectors from memory manager
    r = get_temp_vector(n)
    r_hat = get_temp_vector(n)
    p = get_temp_vector(n)
    v = get_temp_vector(n)
    s = get_temp_vector(n)
    t = get_temp_vector(n)
    z = get_temp_vector(n)
    z2 = get_temp_vector(n)
    
    # r = b - A*x
    mul!(r, A, x)
    r .= b .- r
    
    # r_hat = r
    copyto!(r_hat, r)
    
    # Initial residual norm
    r_norm = norm(r)
    init_r_norm = r_norm
    
    # Store initial residual if requested
    if residuals !== nothing
        push!(residuals, r_norm)
    end
    
    # Check if already converged
    if r_norm < tol
        return 0, r_norm
    end
    
    # p = r
    copyto!(p, r)
    
    # Main BiCGSTAB loop
    rho_prev = 1.0
    alpha = 1.0
    omega = 1.0
    
    for iter in 1:max_iter
        rho = dot(r_hat, r)
        
        # Check for breakdown
        if abs(rho) < 1e-15 || abs(omega) < 1e-15
            break
        end
        
        # First time through, p = r
        if iter > 1
            beta = (rho / rho_prev) * (alpha / omega)
            p .= r .+ beta .* (p .- omega .* v)
        end
        
        # Apply preconditioner: z = M⁻¹p
        precond(z, p)
        
        # v = A*z
        mul!(v, A, z)
        
        # α = ρ / (r̂ᵀv)
        alpha = rho / dot(r_hat, v)
        
        # s = r - α*v
        copyto!(s, r)
        axpy!(-alpha, v, s)
        
        # Check if converged
        s_norm = norm(s)
        if s_norm < tol * init_r_norm
            # x = x + α*z
            axpy!(alpha, z, x)
            
            r_norm = s_norm
            if residuals !== nothing
                push!(residuals, r_norm)
            end
            
            break
        end
        
        # Apply preconditioner: z2 = M⁻¹s
        precond(z2, s)
        
        # t = A*z2
        mul!(t, A, z2)
        
        # ω = (tᵀs) / (tᵀt)
        omega = dot(t, s) / dot(t, t)
        
        # x = x + α*z + ω*z2
        axpy!(alpha, z, x)
        axpy!(omega, z2, x)
        
        # r = s - ω*t
        copyto!(r, s)
        axpy!(-omega, t, r)
        
        # Check convergence
        r_norm = norm(r)
        if residuals !== nothing
            push!(residuals, r_norm)
        end
        
        if r_norm < tol * init_r_norm
            break
        end
        
        rho_prev = rho
    end
    
    return iter, r_norm
end

"""
    gmres_solve_inplace!(A, x, b, precond, tol, max_iter, restart, residuals)

Solve Ax = b using the GMRES method with in-place operations.

# Arguments
- `A`: Matrix or linear operator
- `x`: Solution vector (will be overwritten)
- `b`: Right-hand side vector
- `precond`: Preconditioner function
- `tol`: Convergence tolerance
- `max_iter`: Maximum number of iterations
- `restart`: Restart parameter
- `residuals`: Vector to store residual history (optional)

# Returns
- `Int`: Number of iterations
- `Float64`: Final residual norm
"""
function gmres_solve_inplace!(A, x, b, precond, tol, max_iter, restart=30, residuals=nothing)
    n = length(b)
    
    # Get temporary vectors and matrices from memory manager
    r = get_temp_vector(n)
    z = get_temp_vector(n)
    w = get_temp_vector(n)
    y = get_temp_vector(restart+1)
    
    # Allocate Krylov basis vectors and Hessenberg matrix
    V = [get_temp_vector(n) for _ in 1:restart+1]
    H = get_temp_matrix(restart+1, restart)
    
    # Initialize
    total_iter = 0
    r_norm = 0.0
    
    for outer in 1:div(max_iter, restart)+1
        # r = b - A*x
        mul!(r, A, x)
        r .= b .- r
        
        # Apply preconditioner: z = M⁻¹r
        precond(z, r)
        
        # β = ‖z‖₂
        beta = norm(z)
        
        # Initialize residual norm
        r_norm = beta
        init_r_norm = r_norm
        
        # Store initial residual if requested
        if residuals !== nothing && total_iter == 0
            push!(residuals, r_norm)
        end
        
        # Check if already converged
        if r_norm < tol
            return total_iter, r_norm
        end
        
        # V₁ = z/β
        V[1] .= z ./ beta
        
        # Initialize Hessenberg matrix
        fill!(H, 0.0)
        
        # Initialize RHS of the least squares problem
        fill!(y, 0.0)
        y[1] = beta
        
        # Main GMRES loop
        for j in 1:restart
            total_iter += 1
            
            # Apply preconditioned operator: w = M⁻¹(A*Vⱼ)
            mul!(r, A, V[j])
            precond(w, r)
            
            # Modified Gram-Schmidt
            for i in 1:j
                H[i, j] = dot(V[i], w)
                w .= w .- H[i, j] .* V[i]
            end
            
            H[j+1, j] = norm(w)
            
            # Check for breakdown
            if abs(H[j+1, j]) < 1e-14
                # Reduce j to account for breakdown
                j -= 1
                break
            end
            
            # V_{j+1} = w / H[j+1,j]
            V[j+1] .= w ./ H[j+1, j]
            
            # Apply Givens rotations to H and y
            for i in 1:j-1
                temp = H[i, j]
                H[i, j] = H[i, j] * c[i] + H[i+1, j] * s[i]
                H[i+1, j] = -H[i, j] * s[i] + H[i+1, j] * c[i]
            end
            
            # Compute new Givens rotation
            if H[j+1, j] == 0.0
                c[j] = 1.0
                s[j] = 0.0
            else
                temp = sqrt(H[j, j]^2 + H[j+1, j]^2)
                c[j] = H[j, j] / temp
                s[j] = H[j+1, j] / temp
            end
            
            # Apply new Givens rotation to H and y
            H[j, j] = H[j, j] * c[j] + H[j+1, j] * s[j]
            H[j+1, j] = 0.0
            
            y[j+1] = -y[j] * s[j]
            y[j] = y[j] * c[j]
            
            # Update residual norm
            r_norm = abs(y[j+1])
            
            if residuals !== nothing
                push!(residuals, r_norm)
            end
            
            # Check convergence
            if r_norm < tol * init_r_norm || total_iter >= max_iter
                break
            end
        end
        
        # Solve upper triangular system
        for i in j:-1:1
            y[i] = y[i] / H[i, i]
            for k in 1:i-1
                y[k] -= H[k, i] * y[i]
            end
        end
        
        # Update solution
        for i in 1:j
            axpy!(y[i], V[i], x)
        end
        
        # Check if converged or max iterations reached
        if r_norm < tol * init_r_norm || total_iter >= max_iter
            break
        end
    end
    
    return total_iter, r_norm
end

end # module InPlaceLinearSolvers
