"""
    CacheOptimizedOperations.jl

This module provides cache-optimized implementations of key operations in JuliaFOAM.
It focuses on improving cache locality and reducing cache misses through:

1. Structure-of-Arrays (SoA) data layouts
2. Cache-friendly traversal patterns
3. Blocked operations for large arrays
"""
module CacheOptimizedOperations

using StaticArrays
using LinearAlgebra
using SparseArrays
using Base.Threads
using ..JuliaFOAM: Field, Mesh, CacheOptimizedMesh

# Try to use LoopVectorization if available
const HAS_LOOPVECTORIZATION = try
    using LoopVectorization
    true
catch
    false
end

export CacheOptimizedField, convert_to_cache_optimized
export gradient_soa!, divergence_soa!, laplacian_soa!
export blocked_matrix_vector_mul!, blocked_sparse_matrix_vector_mul!
export cache_optimized_face_interpolation!

"""
    CacheOptimizedField{T}

A cache-optimized field representation using Structure-of-Arrays (SoA) layout.

# Fields
- `name::String`: Field name
- `values::Matrix{Float64}`: Field values (components as columns)
- `boundary_values::Dict{String, Matrix{Float64}}`: Boundary values
"""
struct CacheOptimizedField{T}
    name::String
    values::Matrix{Float64}  # For vector fields, components are columns
    boundary_values::Dict{String, Matrix{Float64}}

    """
        CacheOptimizedField{T}(name::String, n_cells::Int) where T

    Create a cache-optimized field.

    # Arguments
    - `name`: Field name
    - `n_cells`: Number of cells

    # Returns
    - `CacheOptimizedField{T}`: Cache-optimized field
    """
    function CacheOptimizedField{T}(name::String, n_cells::Int) where T
        if T <: Number
            # Scalar field
            values = zeros(Float64, n_cells, 1)
        elseif T <: SVector{3, Float64}
            # Vector field
            values = zeros(Float64, n_cells, 3)
        elseif T <: SMatrix{3, 3, Float64, 9}
            # Tensor field
            values = zeros(Float64, n_cells, 9)
        else
            error("Unsupported field type: $T")
        end

        return new{T}(name, values, Dict{String, Matrix{Float64}}())
    end
end

"""
    convert_to_cache_optimized(field::Field{T}, mesh::Mesh) where T

Convert a standard field to a cache-optimized field.

# Arguments
- `field`: Standard field
- `mesh`: Mesh

# Returns
- `CacheOptimizedField{T}`: Cache-optimized field
"""
function convert_to_cache_optimized(field::Field{T}, mesh::Mesh) where T
    n_cells = length(mesh.cells)
    opt_field = CacheOptimizedField{T}(field.name, n_cells)

    if T <: Number
        # Scalar field
        for i in 1:n_cells
            opt_field.values[i, 1] = field.values[i]
        end

        # Boundary values
        for (patch_name, patch_values) in field.boundary_values
            n_faces = length(patch_values)
            opt_field.boundary_values[patch_name] = zeros(Float64, n_faces, 1)
            for i in 1:n_faces
                opt_field.boundary_values[patch_name][i, 1] = patch_values[i]
            end
        end
    elseif T <: SVector{3, Float64}
        # Vector field
        for i in 1:n_cells
            opt_field.values[i, 1] = field.values[i][1]
            opt_field.values[i, 2] = field.values[i][2]
            opt_field.values[i, 3] = field.values[i][3]
        end

        # Boundary values
        for (patch_name, patch_values) in field.boundary_values
            n_faces = length(patch_values)
            opt_field.boundary_values[patch_name] = zeros(Float64, n_faces, 3)
            for i in 1:n_faces
                opt_field.boundary_values[patch_name][i, 1] = patch_values[i][1]
                opt_field.boundary_values[patch_name][i, 2] = patch_values[i][2]
                opt_field.boundary_values[patch_name][i, 3] = patch_values[i][3]
            end
        end
    elseif T <: SMatrix{3, 3, Float64, 9}
        # Tensor field
        for i in 1:n_cells
            for j in 1:3, k in 1:3
                opt_field.values[i, (j-1)*3 + k] = field.values[i][j, k]
            end
        end

        # Boundary values
        for (patch_name, patch_values) in field.boundary_values
            n_faces = length(patch_values)
            opt_field.boundary_values[patch_name] = zeros(Float64, n_faces, 9)
            for i in 1:n_faces
                for j in 1:3, k in 1:3
                    opt_field.boundary_values[patch_name][i, (j-1)*3 + k] = patch_values[i][j, k]
                end
            end
        end
    end

    return opt_field
end

"""
    gradient_soa!(grad::CacheOptimizedField{SVector{3, Float64}}, field::CacheOptimizedField{Float64}, mesh::CacheOptimizedMesh)

Calculate the gradient of a scalar field using cache-optimized operations.

# Arguments
- `grad`: Gradient field (output)
- `field`: Scalar field
- `mesh`: Cache-optimized mesh
"""
function gradient_soa!(grad::CacheOptimizedField{SVector{3, Float64}}, field::CacheOptimizedField{Float64}, mesh::CacheOptimizedMesh)
    n_cells = size(field.values, 1)
    n_faces = size(mesh.face_centers, 2)

    # Initialize gradient to zero
    fill!(grad.values, 0.0)

    # Loop over faces with cache-friendly access pattern
    for f in 1:n_faces
        owner = mesh.face_owners[f]
        neighbor = mesh.face_neighbors[f]

        # Face normal and area
        nx = mesh.face_normals[1, f]
        ny = mesh.face_normals[2, f]
        nz = mesh.face_normals[3, f]
        area = mesh.face_areas[f]

        if neighbor > 0  # Internal face
            # Owner and neighbor cell values
            phi_owner = field.values[owner, 1]
            phi_neighbor = field.values[neighbor, 1]

            # Face value (linear interpolation)
            phi_face = 0.5 * (phi_owner + phi_neighbor)

            # Accumulate contribution to gradient
            grad.values[owner, 1] += phi_face * nx * area
            grad.values[owner, 2] += phi_face * ny * area
            grad.values[owner, 3] += phi_face * nz * area

            grad.values[neighbor, 1] -= phi_face * nx * area
            grad.values[neighbor, 2] -= phi_face * ny * area
            grad.values[neighbor, 3] -= phi_face * nz * area
        else  # Boundary face
            # Owner cell value
            phi_owner = field.values[owner, 1]

            # Get boundary patch
            patch_idx = -neighbor  # Convert to positive index
            patch_name = mesh.boundary_patch_names[patch_idx]
            local_face_idx = mesh.boundary_face_indices[f]

            # Boundary value
            phi_boundary = field.boundary_values[patch_name][local_face_idx, 1]

            # Face value (boundary value)
            phi_face = phi_boundary

            # Accumulate contribution to gradient
            grad.values[owner, 1] += phi_face * nx * area
            grad.values[owner, 2] += phi_face * ny * area
            grad.values[owner, 3] += phi_face * nz * area
        end
    end

    # Normalize by cell volumes
    for c in 1:n_cells
        vol_inv = 1.0 / mesh.cell_volumes[c]
        grad.values[c, 1] *= vol_inv
        grad.values[c, 2] *= vol_inv
        grad.values[c, 3] *= vol_inv
    end
end

"""
    divergence_soa!(div::CacheOptimizedField{Float64}, field::CacheOptimizedField{SVector{3, Float64}}, mesh::CacheOptimizedMesh)

Calculate the divergence of a vector field using cache-optimized operations.

# Arguments
- `div`: Divergence field (output)
- `field`: Vector field
- `mesh`: Cache-optimized mesh
"""
function divergence_soa!(div::CacheOptimizedField{Float64}, field::CacheOptimizedField{SVector{3, Float64}}, mesh::CacheOptimizedMesh)
    n_cells = size(field.values, 1)
    n_faces = size(mesh.face_centers, 2)

    # Initialize divergence to zero
    fill!(div.values, 0.0)

    # Loop over faces with cache-friendly access pattern
    for f in 1:n_faces
        owner = mesh.face_owners[f]
        neighbor = mesh.face_neighbors[f]

        # Face normal and area
        nx = mesh.face_normals[1, f]
        ny = mesh.face_normals[2, f]
        nz = mesh.face_normals[3, f]
        area = mesh.face_areas[f]

        if neighbor > 0  # Internal face
            # Owner and neighbor cell values
            ux_owner = field.values[owner, 1]
            uy_owner = field.values[owner, 2]
            uz_owner = field.values[owner, 3]

            ux_neighbor = field.values[neighbor, 1]
            uy_neighbor = field.values[neighbor, 2]
            uz_neighbor = field.values[neighbor, 3]

            # Face value (linear interpolation)
            ux_face = 0.5 * (ux_owner + ux_neighbor)
            uy_face = 0.5 * (uy_owner + uy_neighbor)
            uz_face = 0.5 * (uz_owner + uz_neighbor)

            # Flux through face
            flux = ux_face * nx + uy_face * ny + uz_face * nz

            # Accumulate contribution to divergence
            div.values[owner, 1] += flux * area
            div.values[neighbor, 1] -= flux * area
        else  # Boundary face
            # Owner cell value
            ux_owner = field.values[owner, 1]
            uy_owner = field.values[owner, 2]
            uz_owner = field.values[owner, 3]

            # Get boundary patch
            patch_idx = -neighbor  # Convert to positive index
            patch_name = mesh.boundary_patch_names[patch_idx]
            local_face_idx = mesh.boundary_face_indices[f]

            # Boundary value
            ux_boundary = field.boundary_values[patch_name][local_face_idx, 1]
            uy_boundary = field.boundary_values[patch_name][local_face_idx, 2]
            uz_boundary = field.boundary_values[patch_name][local_face_idx, 3]

            # Flux through face
            flux = ux_boundary * nx + uy_boundary * ny + uz_boundary * nz

            # Accumulate contribution to divergence
            div.values[owner, 1] += flux * area
        end
    end

    # Normalize by cell volumes
    for c in 1:n_cells
        div.values[c, 1] /= mesh.cell_volumes[c]
    end
end

"""
    blocked_matrix_vector_mul!(y::Vector{Float64}, A::Matrix{Float64}, x::Vector{Float64}, block_size::Int=64)

Perform blocked matrix-vector multiplication for better cache utilization.

# Arguments
- `y`: Result vector
- `A`: Matrix
- `x`: Input vector
- `block_size`: Block size for blocking
"""
function blocked_matrix_vector_mul!(y::Vector{Float64}, A::Matrix{Float64}, x::Vector{Float64}, block_size::Int=64)
    m, n = size(A)
    fill!(y, 0.0)

    # Process blocks
    for i_block in 1:block_size:m
        i_end = min(i_block + block_size - 1, m)

        for j_block in 1:block_size:n
            j_end = min(j_block + block_size - 1, n)

            # Process current block
            for i in i_block:i_end
                for j in j_block:j_end
                    y[i] += A[i, j] * x[j]
                end
            end
        end
    end
end

"""
    blocked_sparse_matrix_vector_mul!(y::Vector{Float64}, A::SparseMatrixCSC{Float64, Int}, x::Vector{Float64})

Perform blocked sparse matrix-vector multiplication for better cache utilization.

# Arguments
- `y`: Result vector
- `A`: Sparse matrix
- `x`: Input vector
"""
function blocked_sparse_matrix_vector_mul!(y::Vector{Float64}, A::SparseMatrixCSC{Float64, Int}, x::Vector{Float64})
    m = size(A, 1)
    fill!(y, 0.0)

    # Process by rows with cache-friendly access
    for i in 1:m
        row_start = A.colptr[i]
        row_end = A.colptr[i+1] - 1

        # Process non-zero elements in this row
        for j in row_start:row_end
            col = A.rowval[j]
            val = A.nzval[j]
            y[i] += val * x[col]
        end
    end
end

end # module CacheOptimizedOperations
