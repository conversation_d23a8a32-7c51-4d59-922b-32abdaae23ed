"""
    Optimizations.jl

This module integrates all optimization components for JuliaFOAM.
"""
module Optimizations

# Import JuliaFOAM
import ..JuliaFOAM
import ..JuliaFOAM: Mesh, Field, Cell, Face, OptimizedMesh, CacheOptimizedMesh

# Import standard libraries
using LinearAlgebra
using SparseArrays
using StaticArrays
using Base.Threads
using Printf
using Dates

# Include all optimization modules
include("MemoryManager.jl")
include("InPlaceLinearSolvers.jl")
include("MeshObjectPools.jl")
include("CacheOptimizedOperations.jl")
include("CacheObliviousAlgorithms.jl")
include("HierarchicalParallelism.jl")
include("OptimizedCommunication.jl")
include("DynamicLoadBalancing.jl")
include("ProfileGuidedCompilation.jl")
include("PerformanceProfiler.jl")
include("BenchmarkRegression.jl")
include("CacheOptimizedMeshOps.jl")
# OptimizedMatrixOps.jl merged into numerics/MatrixOperations.jl
include("VectorizedOperations.jl")
include("AdvancedPreconditioners.jl")
include("EnhancedPreconditioners.jl")
include("GPUAcceleration.jl")
# RobustLinearSolvers.jl merged into linear/LinearSolvers.jl

# Import all modules
using .MemoryManager
using .InPlaceLinearSolvers
using .MeshObjectPools
using .CacheOptimizedOperations
using .CacheObliviousAlgorithms
using .HierarchicalParallelism
using .OptimizedCommunication
using .DynamicLoadBalancing
using .ProfileGuidedCompilation
using .PerformanceProfiler
using .BenchmarkRegression
using .CacheOptimizedMeshOps
# OptimizedMatrixOps merged into numerics/MatrixOperations.jl
using .VectorizedOperations
using .AdvancedPreconditioners
using .EnhancedPreconditioners
using .GPUAcceleration
# RobustLinearSolvers merged into linear/LinearSolvers.jl

# Re-export all modules and their exports
for mod in [
    MemoryManager,
    InPlaceLinearSolvers,
    MeshObjectPools,
    CacheOptimizedOperations,
    CacheObliviousAlgorithms,
    HierarchicalParallelism,
    OptimizedCommunication,
    DynamicLoadBalancing,
    ProfileGuidedCompilation,
    PerformanceProfiler,
    BenchmarkRegression,
    CacheOptimizedMeshOps,
    VectorizedOperations,
    AdvancedPreconditioners,
    EnhancedPreconditioners,
    GPUAcceleration,
# RobustLinearSolvers merged into linear/LinearSolvers.jl
]
    for name in names(mod, all=true, imported=true)
        if name ∉ (mod, :eval, :include, Symbol("#eval"), Symbol("#include")) &&
           !startswith(string(name), "#") &&
           name ∉ [:__init__, :__precompile__]
            @eval export $name
        end
    end
end

# Export key functions that might be used frequently
export get_temp_vector, get_temp_matrix, reset_temp_arrays!
export MemoryPool, get_from_pool!, return_to_pool!
export with_temp_array
export cg_solve_inplace!, bicgstab_solve_inplace!, gmres_solve_inplace!

# Export robust solver functions
export robust_cg_solve!, robust_bicgstab_solve!, robust_gmres_solve!
export check_matrix_properties, diagnose_linear_system

export CellPool, FacePool, get_cell!, get_face!, return_cell!, return_face!
export with_cell, with_face

export CacheOptimizedField, convert_to_cache_optimized
export gradient_soa!, divergence_soa!, laplacian_soa!
export blocked_matrix_vector_mul!, blocked_sparse_matrix_vector_mul!

export cache_oblivious_matrix_multiply!, cache_oblivious_transpose!
export space_filling_curve_mesh_traversal, hilbert_curve_mesh_traversal
export cache_oblivious_sparse_matrix_vector_multiply!

export HierarchicalMesh, create_hierarchical_mesh
export hierarchical_gradient!, hierarchical_divergence!, hierarchical_laplacian!
export hierarchical_matrix_vector_mul!, hierarchical_solve!

export MessageAggregator, aggregate_messages!, send_aggregated_messages!
export CommunicationSchedule, create_communication_schedule, execute_communication_schedule!
export compute_with_overlap!, adaptive_communication!

export LoadMonitor, monitor_load!, detect_imbalance
export DynamicPartitioner, repartition_mesh!, migrate_cells!
export AdaptiveLoadBalancer, select_balancing_strategy

export collect_profile_data, analyze_profile_data, precompile_hot_functions
export generate_precompiled_library, load_precompiled_library
export specialize_functions_for_types

export ProfileRegion, profile_region, end_profile_region
export HotspotAnalyzer, analyze_hotspots, get_top_hotspots
export PerformanceReport, generate_performance_report
export PerformanceRegression, detect_regressions

export BenchmarkSuite, add_benchmark!, run_benchmarks
export BenchmarkResult, save_benchmark_results, load_benchmark_results
export compare_benchmarks, detect_regressions
export generate_benchmark_report

"""
    optimize_mesh(mesh::Mesh)

Apply all mesh optimizations to a mesh.

# Arguments
- `mesh`: Mesh to optimize

# Returns
- `OptimizedMesh`: Optimized mesh
"""
function optimize_mesh(mesh::Mesh)
    # Convert to optimized mesh
    opt_mesh = OptimizedMesh(mesh)

    # Apply cache-friendly traversal
    cell_order = space_filling_curve_mesh_traversal(mesh)

    # Reorder cells for better cache locality
    # This is a placeholder; a real implementation would reorder the cells

    return opt_mesh
end

"""
    optimize_solver(solver::Symbol, A::SparseMatrixCSC{Float64, Int}, b::Vector{Float64}, x0::Vector{Float64}, tol::Float64, max_iter::Int)

Apply all solver optimizations to a linear solver.

# Arguments
- `solver`: Solver type (:cg, :bicgstab, :gmres)
- `A`: System matrix
- `b`: Right-hand side vector
- `x0`: Initial guess
- `tol`: Convergence tolerance
- `max_iter`: Maximum number of iterations

# Returns
- `Tuple{Vector{Float64}, Int, Float64}`: Solution vector, number of iterations, and final residual norm
"""
function optimize_solver(solver::Symbol, A::SparseMatrixCSC{Float64, Int}, b::Vector{Float64}, x0::Vector{Float64}, tol::Float64, max_iter::Int)
    # Create preconditioner
    precond = (z, r) -> z .= r  # Identity preconditioner (placeholder)

    # Initialize solution
    x = copy(x0)

    # Solve using in-place solvers
    if solver == :cg
        iter, r_norm = cg_solve_inplace!(A, x, b, precond, tol, max_iter)
    elseif solver == :bicgstab
        iter, r_norm = bicgstab_solve_inplace!(A, x, b, precond, tol, max_iter)
    elseif solver == :gmres
        iter, r_norm = gmres_solve_inplace!(A, x, b, precond, tol, max_iter, 30)
    else
        error("Unknown solver type: $solver")
    end

    return x, iter, r_norm
end

"""
    optimize_gradient_calculation(field::Field{Float64}, mesh::Mesh)

Apply all optimizations to gradient calculation.

# Arguments
- `field`: Scalar field
- `mesh`: Mesh

# Returns
- `Field{SVector{3, Float64}}`: Gradient field
"""
function optimize_gradient_calculation(field::Field{Float64}, mesh::Mesh)
    # Convert to cache-optimized representations
    opt_mesh = CacheOptimizedMesh(mesh)
    opt_field = convert_to_cache_optimized(field, mesh)

    # Create gradient field
    n_cells = length(mesh.cells)
    grad = CacheOptimizedField{SVector{3, Float64}}("grad_$(field.name)", n_cells)

    # Calculate gradient using cache-optimized operations
    gradient_soa!(grad, opt_field, opt_mesh)

    # Convert back to standard field
    # This is a placeholder; a real implementation would convert the field

    return Field{SVector{3, Float64}}("grad_$(field.name)", [SVector{3, Float64}(grad.values[i, 1], grad.values[i, 2], grad.values[i, 3]) for i in 1:n_cells], Dict{String, Vector{SVector{3, Float64}}}())
end

"""
    optimize_parallel_solver(solver::Symbol, A::SparseMatrixCSC{Float64, Int}, b::Vector{Float64}, x0::Vector{Float64}, tol::Float64, max_iter::Int, mesh::Mesh)

Apply all optimizations to a parallel linear solver.

# Arguments
- `solver`: Solver type (:cg, :bicgstab, :gmres)
- `A`: System matrix
- `b`: Right-hand side vector
- `x0`: Initial guess
- `tol`: Convergence tolerance
- `max_iter`: Maximum number of iterations
- `mesh`: Mesh

# Returns
- `Tuple{Vector{Float64}, Int, Float64}`: Solution vector, number of iterations, and final residual norm
"""
function optimize_parallel_solver(solver::Symbol, A::SparseMatrixCSC{Float64, Int}, b::Vector{Float64}, x0::Vector{Float64}, tol::Float64, max_iter::Int, mesh::Mesh)
    # Create hierarchical mesh
    hier_mesh = create_hierarchical_mesh(mesh)

    # Initialize solution
    x = copy(x0)

    # Solve using hierarchical parallelism
    iter, r_norm = hierarchical_solve!(A, x, b, hier_mesh, tol, max_iter)

    return x, iter, r_norm
end

"""
    run_optimized_benchmarks(suite::BenchmarkSuite)

Run benchmarks with all optimizations enabled.

# Arguments
- `suite`: Benchmark suite

# Returns
- `Vector{BenchmarkResult}`: Benchmark results
"""
function run_optimized_benchmarks(suite::BenchmarkSuite)
    # Run benchmarks
    results = run_benchmarks(suite)

    # Save results
    timestamp = Dates.format(now(), "yyyymmdd_HHMMSS")
    filename = "benchmark_results_$(timestamp).json"
    save_benchmark_results(results, filename)

    return results
end

"""
    profile_and_optimize(test_case::Function)

Profile a test case and apply optimizations.

# Arguments
- `test_case`: Test case function

# Returns
- `Dict`: Optimization results
"""
function profile_and_optimize(test_case::Function)
    # Collect profile data
    profile_data = collect_profile_data(test_case)

    # Analyze profile data
    analysis = analyze_profile_data(profile_data)

    # Precompile hot functions
    precompile_hot_functions(analysis)

    # Generate precompiled library
    library_path = generate_precompiled_library(analysis, "precompiled")

    # Load precompiled library
    load_precompiled_library(library_path)

    # Return optimization results
    return Dict{Symbol, Any}(
        :profile_data => profile_data,
        :analysis => analysis,
        :library_path => library_path
    )
end

end # module Optimizations
