"""
    HierarchicalParallelism.jl

This module provides hierarchical parallelism capabilities for JuliaFOAM,
combining MPI for distributed memory parallelism with threading for shared
memory parallelism.
"""
module HierarchicalParallelism

using MPI
using StaticArrays
using LinearAlgebra
using SparseArrays
using Base.Threads
using ..JuliaFOAM: Mesh, Field, Cell, Face, OptimizedMesh

export HierarchicalMesh, create_hierarchical_mesh
export hierarchical_gradient!, hierarchical_divergence!, hierarchical_laplacian!
export hierarchical_matrix_vector_mul!, hierarchical_solve!
export get_thread_partition, get_process_partition

"""
    HierarchicalMesh

A mesh structure optimized for hierarchical parallelism.

# Fields
- `local_cells::Vector{Cell}`: Local cells owned by this process
- `local_faces::Vector{Face}`: Local faces owned by this process
- `halo_cells::Vector{Cell}`: Halo cells from neighboring processes
- `halo_faces::Vector{Face}`: Halo faces from neighboring processes
- `thread_cell_ranges::Vector{UnitRange{Int}}`: Cell ranges for each thread
- `thread_face_ranges::Vector{UnitRange{Int}}`: Face ranges for each thread
- `send_maps::Dict{Int, Vector{Int}}`: Maps process ID to local indices to send
- `recv_maps::Dict{Int, Vector{Int}}`: Maps process ID to local indices to receive
- `comm::MPI.Comm`: MPI communicator
- `rank::Int`: Process rank
- `size::Int`: Number of processes
"""
struct HierarchicalMesh
    local_cells::Vector{Cell}
    local_faces::Vector{Face}
    halo_cells::Vector{Cell}
    halo_faces::Vector{Face}
    thread_cell_ranges::Vector{UnitRange{Int}}
    thread_face_ranges::Vector{UnitRange{Int}}
    send_maps::Dict{Int, Vector{Int}}
    recv_maps::Dict{Int, Vector{Int}}
    comm::MPI.Comm
    rank::Int
    size::Int
end

"""
    create_hierarchical_mesh(mesh::Any)

Create a hierarchical mesh from an optimized mesh.

# Arguments
- `mesh`: Optimized mesh

# Returns
- `HierarchicalMesh`: Hierarchical mesh
"""
function create_hierarchical_mesh(mesh::Any)
    # Initialize MPI if not already initialized
    if !MPI.Initialized()
        MPI.Init()
    end

    # Get MPI info
    comm = hasfield(typeof(mesh), :comm) ? mesh.comm : MPI.COMM_WORLD
    rank = MPI.Comm_rank(comm)
    size = MPI.Comm_size(comm)

    # Use existing send/recv maps if available
    send_maps = hasfield(typeof(mesh), :send_maps) ? mesh.send_maps : Dict{Int, Vector{Int}}()
    recv_maps = hasfield(typeof(mesh), :recv_maps) ? mesh.recv_maps : Dict{Int, Vector{Int}}()

    # Create thread partitioning
    n_cells = length(mesh.cells)
    n_faces = length(mesh.faces)
    n_threads = Threads.nthreads()

    # Partition cells among threads
    cells_per_thread = div(n_cells, n_threads)
    thread_cell_ranges = [
        if i == n_threads
            ((i-1)*cells_per_thread+1):n_cells
        else
            ((i-1)*cells_per_thread+1):(i*cells_per_thread)
        end
        for i in 1:n_threads
    ]

    # Partition faces among threads
    faces_per_thread = div(n_faces, n_threads)
    thread_face_ranges = [
        if i == n_threads
            ((i-1)*faces_per_thread+1):n_faces
        else
            ((i-1)*faces_per_thread+1):(i*faces_per_thread)
        end
        for i in 1:n_threads
    ]

    # For now, we'll use the original mesh cells and faces
    # In a real implementation, we would create proper halo regions
    local_cells = mesh.cells
    local_faces = mesh.faces
    halo_cells = Cell[]
    halo_faces = Face[]

    return HierarchicalMesh(
        local_cells,
        local_faces,
        halo_cells,
        halo_faces,
        thread_cell_ranges,
        thread_face_ranges,
        send_maps,
        recv_maps,
        comm,
        rank,
        size
    )
end

"""
    get_thread_partition(mesh::HierarchicalMesh)

Get the thread partition for a hierarchical mesh.

# Arguments
- `mesh`: Hierarchical mesh

# Returns
- `Vector{UnitRange{Int}}`: Cell ranges for each thread
"""
function get_thread_partition(mesh::HierarchicalMesh)
    return mesh.thread_cell_ranges
end

"""
    get_process_partition(mesh::HierarchicalMesh)

Get the process partition for a hierarchical mesh.

# Arguments
- `mesh`: Hierarchical mesh

# Returns
- `Dict{Int, Vector{Int}}`: Maps process ID to local indices
"""
function get_process_partition(mesh::HierarchicalMesh)
    return mesh.send_maps
end

"""
    hierarchical_gradient!(grad::Field{SVector{3, Float64}}, field::Field{Float64}, mesh::HierarchicalMesh)

Calculate the gradient of a scalar field using hierarchical parallelism.

# Arguments
- `grad`: Gradient field (output)
- `field`: Scalar field
- `mesh`: Hierarchical mesh
"""
function hierarchical_gradient!(grad::Field{SVector{3, Float64}}, field::Field{Float64}, mesh::HierarchicalMesh)
    # Exchange halo data
    exchange_halo_data!(field, mesh)

    # Initialize gradient to zero
    fill!(grad.values, SVector{3, Float64}(0.0, 0.0, 0.0))

    # Parallel gradient calculation
    @threads for tid in 1:length(mesh.thread_cell_ranges)
        cell_range = mesh.thread_cell_ranges[tid]
        face_range = mesh.thread_face_ranges[tid]

        # Calculate gradient for this thread's cells
        for face_idx in face_range
            face = mesh.local_faces[face_idx]
            owner = face.owner

            # Skip if owner is not in this thread's range
            if owner < cell_range.start || owner > cell_range.stop
                continue
            end

            # Face normal and area
            normal = face.area
            area_mag = norm(normal)

            if face.neighbour > 0  # Internal face
                # Owner and neighbor cell values
                phi_owner = field.values[owner]
                phi_neighbor = field.values[face.neighbour]

                # Face value (linear interpolation)
                phi_face = 0.5 * (phi_owner + phi_neighbor)

                # Accumulate contribution to gradient
                grad.values[owner] += phi_face * normal
            else  # Boundary face
                # Get boundary value
                # In a real implementation, we would have proper functions to get patch name and local face index
                # For now, we'll use a placeholder
                patch_name = "boundary"
                local_face_idx = 1

                # Boundary value
                phi_boundary = field.boundary_values[patch_name][local_face_idx]

                # Accumulate contribution to gradient
                grad.values[owner] += phi_boundary * normal
            end
        end
    end

    # Normalize by cell volumes
    @threads for tid in 1:length(mesh.thread_cell_ranges)
        cell_range = mesh.thread_cell_ranges[tid]

        for cell_idx in cell_range
            cell = mesh.local_cells[cell_idx]
            grad.values[cell_idx] /= cell.volume
        end
    end

    # Synchronize gradient field
    exchange_halo_data!(grad, mesh)
end

"""
    hierarchical_divergence!(div::Field{Float64}, field::Field{SVector{3, Float64}}, mesh::HierarchicalMesh)

Calculate the divergence of a vector field using hierarchical parallelism.

# Arguments
- `div`: Divergence field (output)
- `field`: Vector field
- `mesh`: Hierarchical mesh
"""
function hierarchical_divergence!(div::Field{Float64}, field::Field{SVector{3, Float64}}, mesh::HierarchicalMesh)
    # Exchange halo data
    exchange_halo_data!(field, mesh)

    # Initialize divergence to zero
    fill!(div.values, 0.0)

    # Parallel divergence calculation
    @threads for tid in 1:length(mesh.thread_cell_ranges)
        cell_range = mesh.thread_cell_ranges[tid]
        face_range = mesh.thread_face_ranges[tid]

        # Calculate divergence for this thread's cells
        for face_idx in face_range
            face = mesh.local_faces[face_idx]
            owner = face.owner

            # Skip if owner is not in this thread's range
            if owner < cell_range.start || owner > cell_range.stop
                continue
            end

            # Face normal and area
            normal = face.area

            if face.neighbour > 0  # Internal face
                # Owner and neighbor cell values
                u_owner = field.values[owner]
                u_neighbor = field.values[face.neighbour]

                # Face value (linear interpolation)
                u_face = 0.5 * (u_owner + u_neighbor)

                # Flux through face
                flux = dot(u_face, normal)

                # Accumulate contribution to divergence
                div.values[owner] += flux
            else  # Boundary face
                # Get boundary value
                # In a real implementation, we would have proper functions to get patch name and local face index
                # For now, we'll use a placeholder
                patch_name = "boundary"
                local_face_idx = 1

                # Boundary value
                u_boundary = field.boundary_values[patch_name][local_face_idx]

                # Flux through face
                flux = dot(u_boundary, normal)

                # Accumulate contribution to divergence
                div.values[owner] += flux
            end
        end
    end

    # Normalize by cell volumes
    @threads for tid in 1:length(mesh.thread_cell_ranges)
        cell_range = mesh.thread_cell_ranges[tid]

        for cell_idx in cell_range
            cell = mesh.local_cells[cell_idx]
            div.values[cell_idx] /= cell.volume
        end
    end

    # Synchronize divergence field
    exchange_halo_data!(div, mesh)
end

"""
    hierarchical_matrix_vector_mul!(y::Vector{Float64}, A::SparseMatrixCSC{Float64, Int}, x::Vector{Float64}, mesh::HierarchicalMesh)

Perform matrix-vector multiplication using hierarchical parallelism.

# Arguments
- `y`: Result vector
- `A`: Sparse matrix
- `x`: Input vector
- `mesh`: Hierarchical mesh
"""
function hierarchical_matrix_vector_mul!(y::Vector{Float64}, A::SparseMatrixCSC{Float64, Int}, x::Vector{Float64}, mesh::HierarchicalMesh)
    # Exchange halo data for x
    exchange_halo_data!(x, mesh)

    # Initialize y to zero
    fill!(y, 0.0)

    # Parallel matrix-vector multiplication
    @threads for tid in 1:length(mesh.thread_cell_ranges)
        cell_range = mesh.thread_cell_ranges[tid]

        # Calculate y = A*x for this thread's rows
        for i in cell_range
            # Get row i of A
            for j in A.colptr[i]:A.colptr[i+1]-1
                col = A.rowval[j]
                val = A.nzval[j]

                # Accumulate contribution to y[i]
                y[i] += val * x[col]
            end
        end
    end

    # Synchronize y
    exchange_halo_data!(y, mesh)
end

"""
    hierarchical_solve!(A::SparseMatrixCSC{Float64, Int}, x::Vector{Float64}, b::Vector{Float64}, mesh::HierarchicalMesh, tol::Float64=1e-6, max_iter::Int=1000)

Solve Ax = b using hierarchical parallelism.

# Arguments
- `A`: Sparse matrix
- `x`: Solution vector (will be overwritten)
- `b`: Right-hand side vector
- `mesh`: Hierarchical mesh
- `tol`: Convergence tolerance
- `max_iter`: Maximum number of iterations

# Returns
- `Int`: Number of iterations
- `Float64`: Final residual norm
"""
function hierarchical_solve!(A::SparseMatrixCSC{Float64, Int}, x::Vector{Float64}, b::Vector{Float64}, mesh::HierarchicalMesh, tol::Float64=1e-6, max_iter::Int=1000)
    n = length(b)

    # Initialize temporary vectors
    r = zeros(Float64, n)
    p = zeros(Float64, n)
    Ap = zeros(Float64, n)

    # r = b - A*x
    hierarchical_matrix_vector_mul!(Ap, A, x, mesh)
    @threads for tid in 1:length(mesh.thread_cell_ranges)
        cell_range = mesh.thread_cell_ranges[tid]
        for i in cell_range
            r[i] = b[i] - Ap[i]
        end
    end

    # p = r
    @threads for tid in 1:length(mesh.thread_cell_ranges)
        cell_range = mesh.thread_cell_ranges[tid]
        for i in cell_range
            p[i] = r[i]
        end
    end

    # Initial residual norm
    r_norm_sq_ref = Ref(0.0)
    @threads for tid in 1:length(mesh.thread_cell_ranges)
        cell_range = mesh.thread_cell_ranges[tid]
        local_sum = 0.0
        for i in cell_range
            local_sum += r[i]^2
        end
        atomic_add!(r_norm_sq_ref, local_sum)
    end
    r_norm_sq = r_norm_sq_ref[]

    # Global reduction for r_norm_sq
    global_r_norm_sq = MPI.Allreduce(r_norm_sq, MPI.SUM, mesh.comm)
    r_norm = sqrt(global_r_norm_sq)
    init_r_norm = r_norm

    # Check if already converged
    if r_norm < tol
        return 0, r_norm
    end

    # Main CG loop
    iter = 0
    rr_old = global_r_norm_sq

    for iter in 1:max_iter
        # Ap = A*p
        hierarchical_matrix_vector_mul!(Ap, A, p, mesh)

        # α = (r'*r) / (p'*Ap)
        pAp_ref = Ref(0.0)
        @threads for tid in 1:length(mesh.thread_cell_ranges)
            cell_range = mesh.thread_cell_ranges[tid]
            local_sum = 0.0
            for i in cell_range
                local_sum += p[i] * Ap[i]
            end
            atomic_add!(pAp_ref, local_sum)
        end
        pAp = pAp_ref[]

        # Global reduction for pAp
        global_pAp = MPI.Allreduce(pAp, MPI.SUM, mesh.comm)
        alpha = rr_old / global_pAp

        # x = x + α*p
        # r = r - α*Ap
        @threads for tid in 1:length(mesh.thread_cell_ranges)
            cell_range = mesh.thread_cell_ranges[tid]
            for i in cell_range
                x[i] += alpha * p[i]
                r[i] -= alpha * Ap[i]
            end
        end

        # Check convergence
        rr_new_ref = Ref(0.0)
        @threads for tid in 1:length(mesh.thread_cell_ranges)
            cell_range = mesh.thread_cell_ranges[tid]
            local_sum = 0.0
            for i in cell_range
                local_sum += r[i]^2
            end
            atomic_add!(rr_new_ref, local_sum)
        end
        rr_new = rr_new_ref[]

        # Global reduction for rr_new
        global_rr_new = MPI.Allreduce(rr_new, MPI.SUM, mesh.comm)
        r_norm = sqrt(global_rr_new)

        if r_norm < tol * init_r_norm
            break
        end

        # β = (r_new'*r_new) / (r_old'*r_old)
        beta = global_rr_new / rr_old

        # p = r + β*p
        @threads for tid in 1:length(mesh.thread_cell_ranges)
            cell_range = mesh.thread_cell_ranges[tid]
            for i in cell_range
                p[i] = r[i] + beta * p[i]
            end
        end

        rr_old = global_rr_new
    end

    return iter, r_norm
end

"""
    exchange_halo_data!(field::Vector{T}, mesh::HierarchicalMesh) where T

Exchange halo data between processes.

# Arguments
- `field`: Field data
- `mesh`: Hierarchical mesh
"""
function exchange_halo_data!(field::Vector{T}, mesh::HierarchicalMesh) where T
    # Skip if single process
    if mesh.size == 1
        return
    end

    # Prepare send buffers
    send_buffers = Dict{Int, Vector{T}}()
    for (neighbor, send_indices) in mesh.send_maps
        send_buffers[neighbor] = field[send_indices]
    end

    # Prepare receive buffers
    recv_buffers = Dict{Int, Vector{T}}()
    for (neighbor, recv_indices) in mesh.recv_maps
        recv_buffers[neighbor] = Vector{T}(undef, length(recv_indices))
    end

    # Post non-blocking receives
    recv_requests = MPI.Request[]
    for (neighbor, buffer) in recv_buffers
        request = MPI.Irecv!(buffer, neighbor, 0, mesh.comm)
        push!(recv_requests, request)
    end

    # Post non-blocking sends
    send_requests = MPI.Request[]
    for (neighbor, buffer) in send_buffers
        request = MPI.Isend(buffer, neighbor, 0, mesh.comm)
        push!(send_requests, request)
    end

    # Wait for receives to complete
    MPI.Waitall(recv_requests)

    # Update halo cells
    for (neighbor, recv_indices) in mesh.recv_maps
        field[recv_indices] = recv_buffers[neighbor]
    end

    # Wait for sends to complete (cleanup)
    MPI.Waitall(send_requests)
end

"""
    atomic_add!(x::Ref{Float64}, y::Float64)

Atomically add y to x.

# Arguments
- `x`: Reference to value to update
- `y`: Value to add
"""
function atomic_add!(x::Ref{Float64}, y::Float64)
    Threads.atomic_add!(x, y)
end

end # module HierarchicalParallelism
