"""
    MemoryManager.jl

This module provides memory management utilities for JuliaFOAM to reduce allocations
and improve performance. It implements:

1. Thread-local temporary arrays for common operations
2. Memory pools for frequently allocated objects
3. In-place operations for key numerical algorithms
"""
module MemoryManager

using StaticArrays
using LinearAlgebra
using Base.Threads

export get_temp_vector, get_temp_matrix, reset_temp_arrays!
export MemoryPool, get_from_pool!, return_to_pool!
export with_temp_array

# Thread-local storage for temporary arrays
const TEMP_VECTORS = Vector{Dict{Int, Vector{Float64}}}()
const TEMP_MATRICES = Vector{Dict{Tuple{Int, Int}, Matrix{Float64}}}()

"""
    initialize_temp_storage()

Initialize thread-local storage for temporary arrays.
"""
function initialize_temp_storage()
    resize!(TEMP_VECTORS, nthreads())
    resize!(TEMP_MATRICES, nthreads())
    
    for i in 1:nthreads()
        TEMP_VECTORS[i] = Dict{Int, Vector{Float64}}()
        TEMP_MATRICES[i] = Dict{Tuple{Int, Int}, Matrix{Float64}}()
    end
end

# Initialize on module load
initialize_temp_storage()

"""
    get_temp_vector(size::Int)

Get a pre-allocated temporary vector of the specified size.
The vector is thread-local to avoid race conditions.

# Arguments
- `size`: Size of the vector

# Returns
- `Vector{Float64}`: Pre-allocated vector
"""
function get_temp_vector(size::Int)
    tid = threadid()
    if !haskey(TEMP_VECTORS[tid], size)
        TEMP_VECTORS[tid][size] = zeros(Float64, size)
    end
    return TEMP_VECTORS[tid][size]
end

"""
    get_temp_matrix(rows::Int, cols::Int)

Get a pre-allocated temporary matrix of the specified size.
The matrix is thread-local to avoid race conditions.

# Arguments
- `rows`: Number of rows
- `cols`: Number of columns

# Returns
- `Matrix{Float64}`: Pre-allocated matrix
"""
function get_temp_matrix(rows::Int, cols::Int)
    tid = threadid()
    key = (rows, cols)
    if !haskey(TEMP_MATRICES[tid], key)
        TEMP_MATRICES[tid][key] = zeros(Float64, rows, cols)
    end
    return TEMP_MATRICES[tid][key]
end

"""
    reset_temp_arrays!()

Reset all temporary arrays to zeros.
This is useful when reusing arrays to ensure no stale data remains.
"""
function reset_temp_arrays!()
    tid = threadid()
    for (_, vec) in TEMP_VECTORS[tid]
        fill!(vec, 0.0)
    end
    for (_, mat) in TEMP_MATRICES[tid]
        fill!(mat, 0.0)
    end
end

"""
    with_temp_array(f::Function, size::Int)

Execute a function with a temporary array, ensuring proper cleanup.

# Arguments
- `f`: Function to execute with the temporary array
- `size`: Size of the temporary array

# Returns
- Result of the function call
"""
function with_temp_array(f::Function, size::Int)
    temp = get_temp_vector(size)
    result = f(temp)
    return result
end

"""
    MemoryPool{T}

A memory pool for objects of type T to reduce allocations.

# Fields
- `pool`: Vector of available objects
- `in_use`: Set of objects currently in use
"""
mutable struct MemoryPool{T}
    pool::Vector{T}
    in_use::Set{T}
    
    """
        MemoryPool{T}(initial_size::Int, factory::Function)
    
    Create a memory pool with initial objects.
    
    # Arguments
    - `initial_size`: Initial number of objects in the pool
    - `factory`: Function to create new objects
    """
    function MemoryPool{T}(initial_size::Int, factory::Function) where T
        pool = Vector{T}(undef, initial_size)
        for i in 1:initial_size
            pool[i] = factory()
        end
        return new(pool, Set{T}())
    end
end

"""
    get_from_pool!(pool::MemoryPool{T}) where T

Get an object from the memory pool.

# Arguments
- `pool`: The memory pool

# Returns
- `T`: An object from the pool
"""
function get_from_pool!(pool::MemoryPool{T}, factory::Function) where T
    if isempty(pool.pool)
        # Create a new object if pool is empty
        obj = factory()
    else
        # Get an object from the pool
        obj = pop!(pool.pool)
    end
    
    # Mark as in use
    push!(pool.in_use, obj)
    
    return obj
end

"""
    return_to_pool!(pool::MemoryPool{T}, obj::T) where T

Return an object to the memory pool.

# Arguments
- `pool`: The memory pool
- `obj`: The object to return
"""
function return_to_pool!(pool::MemoryPool{T}, obj::T) where T
    # Check if object is in use
    if obj in pool.in_use
        # Remove from in-use set
        delete!(pool.in_use, obj)
        
        # Add to pool
        push!(pool.pool, obj)
    end
end

end # module MemoryManager
