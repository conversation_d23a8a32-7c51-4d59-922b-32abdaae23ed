module OptimizedMeshGenerator

export generate_structured_mesh, MeshData

"""
    MeshData

Optimized data structure for mesh storage with pre-allocated arrays.
"""
struct MeshData
    # Mesh dimensions
    nx::Int
    ny::Int
    nz::Int
    
    # Domain dimensions
    length::Float64
    height::Float64
    depth::Float64
    
    # Cell centers (stored as separate x, y, z arrays for better memory layout)
    center_x::Vector{Float64}
    center_y::Vector{Float64}
    center_z::Vector{Float64}
    
    # Cell volumes
    volumes::Vector{Float64}
    
    # Face information (stored as indices for efficiency)
    # Each face connects two cells (or a cell and a boundary)
    face_owner::Vector{Int}  # Cell that owns the face
    face_neighbor::Vector{Int}  # Neighboring cell (or -1 for boundary)
    
    # Face areas and normals
    face_area::Vector{Float64}
    face_normal_x::Vector{Float64}
    face_normal_y::Vector{Float64}
    face_normal_z::Vector{Float64}
    
    # Boundary information
    boundary_faces::Dict{String, Vector{Int}}  # Maps boundary name to face indices
end

"""
    generate_structured_mesh(nx, ny, nz, length, height, depth)

Generate a structured mesh with optimized memory layout and pre-allocated arrays.
Returns a MeshData struct containing all mesh information.

# Arguments
- `nx::Int`: Number of cells in x-direction
- `ny::Int`: Number of cells in y-direction
- `nz::Int`: Number of cells in z-direction (use 1 for 2D)
- `length::Float64`: Domain length in x-direction
- `height::Float64`: Domain height in y-direction
- `depth::Float64`: Domain depth in z-direction (use 1.0 for 2D)
"""
function generate_structured_mesh(nx::Int, ny::Int, nz::Int, length::Float64, height::Float64, depth::Float64=1.0)
    # Calculate cell dimensions
    dx = length / nx
    dy = height / ny
    dz = depth / nz
    
    # Calculate total number of cells and faces
    n_cells = nx * ny * nz
    n_internal_faces_x = (nx - 1) * ny * nz
    n_internal_faces_y = nx * (ny - 1) * nz
    n_internal_faces_z = nx * ny * (nz - 1)
    n_boundary_faces_x = 2 * ny * nz
    n_boundary_faces_y = 2 * nx * nz
    n_boundary_faces_z = 2 * nx * ny
    n_faces = n_internal_faces_x + n_internal_faces_y + n_internal_faces_z + 
              n_boundary_faces_x + n_boundary_faces_y + n_boundary_faces_z
    
    # Pre-allocate arrays for better performance
    center_x = Vector{Float64}(undef, n_cells)
    center_y = Vector{Float64}(undef, n_cells)
    center_z = Vector{Float64}(undef, n_cells)
    volumes = Vector{Float64}(undef, n_cells)
    
    face_owner = Vector{Int}(undef, n_faces)
    face_neighbor = Vector{Int}(undef, n_faces)
    face_area = Vector{Float64}(undef, n_faces)
    face_normal_x = Vector{Float64}(undef, n_faces)
    face_normal_y = Vector{Float64}(undef, n_faces)
    face_normal_z = Vector{Float64}(undef, n_faces)
    
    # Initialize boundary face collections
    boundary_faces = Dict{String, Vector{Int}}(
        "left" => Int[],
        "right" => Int[],
        "bottom" => Int[],
        "top" => Int[],
        "front" => Int[],
        "back" => Int[]
    )
    
    # Fill cell data with optimized loop ordering for cache efficiency
    # Use @inbounds for performance (skip bounds checking)
    @inbounds for k in 1:nz
        for j in 1:ny
            for i in 1:nx
                idx = get_cell_index(i, j, k, nx, ny)
                
                # Cell center coordinates
                center_x[idx] = (i - 0.5) * dx
                center_y[idx] = (j - 0.5) * dy
                center_z[idx] = (k - 0.5) * dz
                
                # Cell volume
                volumes[idx] = dx * dy * dz
            end
        end
    end
    
    # Create faces with optimized loop ordering
    face_idx = 1
    
    # Internal x-faces
    @inbounds for k in 1:nz
        for j in 1:ny
            for i in 1:(nx-1)
                owner_idx = get_cell_index(i, j, k, nx, ny)
                neighbor_idx = get_cell_index(i+1, j, k, nx, ny)
                
                face_owner[face_idx] = owner_idx
                face_neighbor[face_idx] = neighbor_idx
                face_area[face_idx] = dy * dz
                face_normal_x[face_idx] = 1.0
                face_normal_y[face_idx] = 0.0
                face_normal_z[face_idx] = 0.0
                
                face_idx += 1
            end
        end
    end
    
    # Internal y-faces
    @inbounds for k in 1:nz
        for j in 1:(ny-1)
            for i in 1:nx
                owner_idx = get_cell_index(i, j, k, nx, ny)
                neighbor_idx = get_cell_index(i, j+1, k, nx, ny)
                
                face_owner[face_idx] = owner_idx
                face_neighbor[face_idx] = neighbor_idx
                face_area[face_idx] = dx * dz
                face_normal_x[face_idx] = 0.0
                face_normal_y[face_idx] = 1.0
                face_normal_z[face_idx] = 0.0
                
                face_idx += 1
            end
        end
    end
    
    # Internal z-faces
    @inbounds for k in 1:(nz-1)
        for j in 1:ny
            for i in 1:nx
                owner_idx = get_cell_index(i, j, k, nx, ny)
                neighbor_idx = get_cell_index(i, j, k+1, nx, ny)
                
                face_owner[face_idx] = owner_idx
                face_neighbor[face_idx] = neighbor_idx
                face_area[face_idx] = dx * dy
                face_normal_x[face_idx] = 0.0
                face_normal_y[face_idx] = 0.0
                face_normal_z[face_idx] = 1.0
                
                face_idx += 1
            end
        end
    end
    
    # Boundary faces - left (x-)
    @inbounds for k in 1:nz
        for j in 1:ny
            owner_idx = get_cell_index(1, j, k, nx, ny)
            
            face_owner[face_idx] = owner_idx
            face_neighbor[face_idx] = -1  # Boundary indicator
            face_area[face_idx] = dy * dz
            face_normal_x[face_idx] = -1.0
            face_normal_y[face_idx] = 0.0
            face_normal_z[face_idx] = 0.0
            
            push!(boundary_faces["left"], face_idx)
            face_idx += 1
        end
    end
    
    # Boundary faces - right (x+)
    @inbounds for k in 1:nz
        for j in 1:ny
            owner_idx = get_cell_index(nx, j, k, nx, ny)
            
            face_owner[face_idx] = owner_idx
            face_neighbor[face_idx] = -1  # Boundary indicator
            face_area[face_idx] = dy * dz
            face_normal_x[face_idx] = 1.0
            face_normal_y[face_idx] = 0.0
            face_normal_z[face_idx] = 0.0
            
            push!(boundary_faces["right"], face_idx)
            face_idx += 1
        end
    end
    
    # Boundary faces - bottom (y-)
    @inbounds for k in 1:nz
        for i in 1:nx
            owner_idx = get_cell_index(i, 1, k, nx, ny)
            
            face_owner[face_idx] = owner_idx
            face_neighbor[face_idx] = -1  # Boundary indicator
            face_area[face_idx] = dx * dz
            face_normal_x[face_idx] = 0.0
            face_normal_y[face_idx] = -1.0
            face_normal_z[face_idx] = 0.0
            
            push!(boundary_faces["bottom"], face_idx)
            face_idx += 1
        end
    end
    
    # Boundary faces - top (y+)
    @inbounds for k in 1:nz
        for i in 1:nx
            owner_idx = get_cell_index(i, ny, k, nx, ny)
            
            face_owner[face_idx] = owner_idx
            face_neighbor[face_idx] = -1  # Boundary indicator
            face_area[face_idx] = dx * dz
            face_normal_x[face_idx] = 0.0
            face_normal_y[face_idx] = 1.0
            face_normal_z[face_idx] = 0.0
            
            push!(boundary_faces["top"], face_idx)
            face_idx += 1
        end
    end
    
    # Add front/back faces for 3D meshes
    if nz > 1
        # Boundary faces - front (z-)
        @inbounds for j in 1:ny
            for i in 1:nx
                owner_idx = get_cell_index(i, j, 1, nx, ny)
                
                face_owner[face_idx] = owner_idx
                face_neighbor[face_idx] = -1  # Boundary indicator
                face_area[face_idx] = dx * dy
                face_normal_x[face_idx] = 0.0
                face_normal_y[face_idx] = 0.0
                face_normal_z[face_idx] = -1.0
                
                push!(boundary_faces["front"], face_idx)
                face_idx += 1
            end
        end
        
        # Boundary faces - back (z+)
        @inbounds for j in 1:ny
            for i in 1:nx
                owner_idx = get_cell_index(i, j, nz, nx, ny)
                
                face_owner[face_idx] = owner_idx
                face_neighbor[face_idx] = -1  # Boundary indicator
                face_area[face_idx] = dx * dy
                face_normal_x[face_idx] = 0.0
                face_normal_y[face_idx] = 0.0
                face_normal_z[face_idx] = 1.0
                
                push!(boundary_faces["back"], face_idx)
                face_idx += 1
            end
        end
    end
    
    # Create and return the mesh data structure
    return MeshData(
        nx, ny, nz,
        length, height, depth,
        center_x, center_y, center_z,
        volumes,
        face_owner, face_neighbor,
        face_area, face_normal_x, face_normal_y, face_normal_z,
        boundary_faces
    )
end

"""
    get_cell_index(i, j, k, nx, ny)

Calculate the 1D array index for a cell at position (i,j,k) in a 3D grid.
"""
@inline function get_cell_index(i::Int, j::Int, k::Int, nx::Int, ny::Int)
    return i + (j-1)*nx + (k-1)*nx*ny
end

end # module
