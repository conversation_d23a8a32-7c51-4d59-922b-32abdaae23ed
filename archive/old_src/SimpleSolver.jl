"""
    SimpleSolver.jl - General matrix-based SIMPLE algorithm implementation
"""
module SimpleSolver

using LinearAlgebra, SparseArrays
using Printf  # for @printf in residual logging
using MPI
using Base.Threads
using StaticArrays

import JuliaFOAM: Mesh, FluidProperties, Field, OptimizedMesh, apply_boundary_conditions!, build_momentum_matrix, build_pressure_equation
import JuliaFOAM: hierarchical_solve_simple!

export SimpleSolverConfig, solve_simple, solve_simple_parallel

"""
    SimpleSolverConfig

Configuration for the general SIMPLE solver.
"""
struct SimpleSolverConfig
    max_iterations::Int
    tolerance::Float64
    relaxation_factors::Dict{String,Float64}
    track_residuals::Bool
    residual_output_interval::Int
end

function SimpleSolverConfig(;
    max_iterations::Int=1000,
    tolerance::Float64=1e-6,
    relaxation_factors::Dict{String,Float64}=Dict("U"=>0.7,"p"=>0.3),
    track_residuals::Bool=true,
    residual_output_interval::Int=10
)
    return SimpleSolverConfig(max_iterations, tolerance, relaxation_factors, track_residuals, residual_output_interval)
end

"""
    solve_simple(
        mesh::Mesh,
        fields::Dict{String,Field},
        boundary_conditions::Dict{String,Any},
        config::SimpleSolverConfig
    )
General SIMPLE solver using matrix assembly and linear solves.
"""
function solve_simple(
    mesh::Mesh,
    fields::Dict{String,Field},
    boundary_conditions::Dict{String,Any},
    config::SimpleSolverConfig
)
    # Retrieve fields and properties
    U = fields["U"]
    p = fields["p"]
    props = FluidProperties()

    # Residual storage
    residuals = Dict("U"=>Float64[], "p"=>Float64[])

    for iter in 1:config.max_iterations
        # 1. Momentum predictor: assemble and solve
        A_U, b_U = build_momentum_matrix(U, p, mesh, props, config.relaxation_factors["U"])
        # Flatten U if vector field; for scalar, values is Float64
        U_vec = Float64[]
        if eltype(U.values) <: Number
            U_vec = copy(U.values)
        else
            # take first component for vector fields
            U_vec = [u[1] for u in U.values]
        end
        sol_U = solve_linear_system(A_U, b_U, U_vec, config.tolerance)
        # Assign back
        if eltype(U.values) <: Number
            U.values .= sol_U
        else
            U.values .= [SVector(sol_U[i],0.0,0.0) for i in 1:length(sol_U)]
        end
        apply_boundary_conditions!(U, mesh)

        # 2. Pressure equation: assemble and solve
        A_p, b_p = build_pressure_equation(U, p, mesh, props, config.relaxation_factors["p"])
        sol_p = solve_linear_system(A_p, b_p, copy(p.values), config.tolerance)
        p.values .= sol_p
        apply_boundary_conditions!(p, mesh)

        # 3. Velocity correction
        correct_velocity!(U, p, mesh, props)
        apply_boundary_conditions!(U, mesh)

        # 4. Compute residuals
        rU = norm([u[1] for u in U.values])
        rp = norm(p.values)
        push!(residuals["U"], rU)
        push!(residuals["p"], rp)

        # 5. Track and print
        if config.track_residuals && (iter == 1 || iter % config.residual_output_interval == 0)
            @printf("Iter %d: U_res=%.3e, p_res=%.3e\n", iter, rU, rp)
        end

        # 6. Convergence check
        if max(rU, rp) < config.tolerance
            break
        end
    end

    return fields, residuals
end

"""
    solve_simple_parallel(
        mesh::Any,
        fields::Dict{String,Field},
        boundary_conditions::Dict{String,Any},
        config::SimpleSolverConfig
    )

Parallel SIMPLE solver using hierarchical parallelism (MPI + threading).
"""
function solve_simple_parallel(
    mesh::Any,
    fields::Dict{String,Field},
    boundary_conditions::Dict{String,Any},
    config::SimpleSolverConfig
)
    # Initialize MPI if not already initialized
    if !MPI.Initialized()
        MPI.Init()
    end

    # Get MPI info
    comm = hasfield(typeof(mesh), :comm) ? mesh.comm : MPI.COMM_WORLD
    rank = MPI.Comm_rank(comm)
    nprocs = MPI.Comm_size(comm)

    # Retrieve fields and properties
    U = fields["U"]
    p = fields["p"]
    props = FluidProperties()

    # Ensure mesh has boundary conditions
    if !hasfield(typeof(mesh), :boundary_conditions) || isempty(mesh.boundary_conditions)
        mesh.boundary_conditions = boundary_conditions
    end

    # Residual storage
    residuals = Dict("U"=>Float64[], "p"=>Float64[])

    # Solve using hierarchical SIMPLE algorithm
    U_new, p_new, iterations, U_residual, p_residual = hierarchical_solve_simple!(
        U, p, mesh, props,
        config.relaxation_factors["U"],
        config.relaxation_factors["p"],
        config.max_iterations,
        config.tolerance
    )

    # Update fields
    fields["U"] = U_new
    fields["p"] = p_new

    # Store final residuals
    push!(residuals["U"], U_residual)
    push!(residuals["p"], p_residual)

    # Print final status
    if rank == 0 && config.track_residuals
        @printf("Converged in %d iterations: U_res=%.3e, p_res=%.3e\n",
                iterations, U_residual, p_residual)
    end

    return fields, residuals
end

end # module SimpleSolver
