"""
    Module for optimized matrix operations with OpenFOAM interoperability.
    
    This module provides performance-optimized matrix assembly operations while maintaining
    full compatibility with OpenFOAM data structures and formats.
"""
module InteropOptimizedMatrixOps

using StaticArrays
using SparseArrays
using LinearAlgebra
using Base.Threads

# Import necessary JuliaFOAM modules
import JuliaFOAM: Mesh
using ..InteropOptimizedMesh

export assemble_laplacian_optimized, assemble_convection_diffusion_optimized
export check_matrix_compatibility

"""
    assemble_laplacian_optimized(mesh, mesh_opt, diffusivity; openfoam_compatible=true)

Assemble the Laplacian operator matrix using optimized data structures.
This function maintains compatibility with OpenFOAM matrix formats.

# Arguments
- `mesh`: The original JuliaFOAM mesh structure (for reference)
- `mesh_opt`: Optimized mesh data from InteropOptimizedMesh.optimize_mesh_access
- `diffusivity`: Scalar field of diffusivity values for each cell
- `openfoam_compatible`: Whether to ensure OpenFOAM compatibility

# Returns
- `A`: Assembled sparse matrix for the Laplacian operator
"""
function assemble_laplacian_optimized(mesh::Mesh, mesh_opt::InteropOptimizedMesh.MeshOptimizationData, diffusivity; openfoam_compatible=true)
    # Extract data
    n_cells = mesh_opt.n_cells
    center_x = mesh_opt.center_x
    center_y = mesh_opt.center_y
    center_z = mesh_opt.center_z
    
    face_owner = mesh_opt.face_owner
    face_neighbor = mesh_opt.face_neighbor
    face_area = mesh_opt.face_area
    face_normal_x = mesh_opt.face_normal_x
    face_normal_y = mesh_opt.face_normal_y
    face_normal_z = mesh_opt.face_normal_z
    n_faces = mesh_opt.n_faces
    
    # Pre-allocate arrays for sparse matrix construction
    # Estimate the number of non-zeros: for a structured grid with a 7-point stencil
    # each internal cell has at most 7 entries (itself + 6 neighbors)
    nnz_estimate = min(7 * n_cells, n_cells^2)  # Cap to avoid excessive memory use
    
    I = Vector{Int}(undef, nnz_estimate)  # Row indices
    J = Vector{Int}(undef, nnz_estimate)  # Column indices
    V = Vector{Float64}(undef, nnz_estimate)  # Values
    
    # Initialize counter for filling arrays
    count = 0
    
    # Diagonal entries (initialize to zero, will accumulate)
    for i in 1:n_cells
        count += 1
        I[count] = i
        J[count] = i
        V[count] = 0.0
    end
    
    # Diagonal start indices for each cell
    diag_indices = Dict(i => i for i in 1:n_cells)
    
    # Process internal faces
    @inbounds for face_idx in 1:n_faces
        owner = face_owner[face_idx]
        neighbor = face_neighbor[face_idx]
        
        # Skip boundary faces
        if neighbor <= 0
            continue
        end
        
        # Face area
        area = face_area[face_idx]
        
        # Face normal
        nx = face_normal_x[face_idx]
        ny = face_normal_y[face_idx]
        nz = face_normal_z[face_idx]
        
        # Distance between cell centers
        dx = center_x[neighbor] - center_x[owner]
        dy = center_y[neighbor] - center_y[owner]
        dz = center_z[neighbor] - center_z[owner]
        
        # Magnitude of distance vector
        distance = sqrt(dx*dx + dy*dy + dz*dz)
        
        # Diffusivity at the face (harmonic mean)
        diff_owner = diffusivity[owner]
        diff_neighbor = diffusivity[neighbor]
        diff_face = 2.0 * diff_owner * diff_neighbor / (diff_owner + diff_neighbor + 1e-10)
        
        # Coefficient for the face
        coeff = diff_face * area / distance
        
        # Add to off-diagonal entries
        count += 1
        I[count] = owner
        J[count] = neighbor
        V[count] = -coeff
        
        count += 1
        I[count] = neighbor
        J[count] = owner
        V[count] = -coeff
        
        # Add to diagonal entries
        V[diag_indices[owner]] += coeff
        V[diag_indices[neighbor]] += coeff
    end
    
    # Process boundary faces
    for (boundary_name, face_indices) in mesh_opt.boundary_faces
        # Get boundary condition for this boundary
        boundary = if hasfield(typeof(mesh), :boundaries) && haskey(mesh.boundaries, boundary_name)
            mesh.boundaries[boundary_name]
        else
            continue
        end
        
        for face_idx in face_indices
            owner = face_owner[face_idx]
            
            # Face area
            area = face_area[face_idx]
            
            # Get cell center and face center
            cell_center = SVector(center_x[owner], center_y[owner], center_z[owner])
            face_center = if hasfield(typeof(mesh), :faces) && face_idx <= length(mesh.faces)
                mesh.faces[face_idx].center
            else
                # Estimate face center as cell center + normal * distance
                cell_center + SVector(face_normal_x[face_idx], face_normal_y[face_idx], face_normal_z[face_idx]) * 0.5
            end
            
            # Distance to boundary (approximate)
            distance = norm(face_center - cell_center)
            
            # For Dirichlet boundaries, add contribution to diagonal
            if hasfield(typeof(boundary), :type) && boundary.type == "fixedValue"
                V[diag_indices[owner]] += diffusivity[owner] * area / distance
            end
        end
    end
    
    # Trim arrays to actual size
    resize!(I, count)
    resize!(J, count)
    resize!(V, count)
    
    # Create sparse matrix
    A = sparse(I, J, V, n_cells, n_cells)
    
    # If OpenFOAM compatibility is required, ensure matrix has OpenFOAM format
    if openfoam_compatible
        A = ensure_openfoam_matrix_format(A)
    end
    
    return A
end

"""
    assemble_convection_diffusion_optimized(mesh, mesh_opt, velocity, diffusivity, scheme="upwind"; openfoam_compatible=true)

Assemble the convection-diffusion operator matrix using optimized data structures.
This function maintains compatibility with OpenFOAM matrix formats.

# Arguments
- `mesh`: The original JuliaFOAM mesh structure (for reference)
- `mesh_opt`: Optimized mesh data from InteropOptimizedMesh.optimize_mesh_access
- `velocity`: Vector field of velocity values for each cell
- `diffusivity`: Scalar field of diffusivity values for each cell
- `scheme`: Discretization scheme for convection term ("upwind", "central", "hybrid")
- `openfoam_compatible`: Whether to ensure OpenFOAM compatibility

# Returns
- `A`: Assembled sparse matrix for the convection-diffusion operator
"""
function assemble_convection_diffusion_optimized(mesh::Mesh, mesh_opt::InteropOptimizedMesh.MeshOptimizationData, velocity, diffusivity, scheme="upwind"; openfoam_compatible=true)
    # Extract data
    n_cells = mesh_opt.n_cells
    center_x = mesh_opt.center_x
    center_y = mesh_opt.center_y
    center_z = mesh_opt.center_z
    
    face_owner = mesh_opt.face_owner
    face_neighbor = mesh_opt.face_neighbor
    face_area = mesh_opt.face_area
    face_normal_x = mesh_opt.face_normal_x
    face_normal_y = mesh_opt.face_normal_y
    face_normal_z = mesh_opt.face_normal_z
    n_faces = mesh_opt.n_faces
    
    # Pre-allocate arrays for sparse matrix construction
    nnz_estimate = min(7 * n_cells, n_cells^2)  # Cap to avoid excessive memory use
    
    I = Vector{Int}(undef, nnz_estimate)
    J = Vector{Int}(undef, nnz_estimate)
    V = Vector{Float64}(undef, nnz_estimate)
    
    # Initialize counter for filling arrays
    count = 0
    
    # Diagonal entries (initialize to zero, will accumulate)
    for i in 1:n_cells
        count += 1
        I[count] = i
        J[count] = i
        V[count] = 0.0
    end
    
    # Diagonal start indices for each cell
    diag_indices = Dict(i => i for i in 1:n_cells)
    
    # Process internal faces
    @inbounds for face_idx in 1:n_faces
        owner = face_owner[face_idx]
        neighbor = face_neighbor[face_idx]
        
        # Skip boundary faces
        if neighbor <= 0
            continue
        end
        
        # Face area
        area = face_area[face_idx]
        
        # Face normal
        nx = face_normal_x[face_idx]
        ny = face_normal_y[face_idx]
        nz = face_normal_z[face_idx]
        
        # Distance between cell centers
        dx = center_x[neighbor] - center_x[owner]
        dy = center_y[neighbor] - center_y[owner]
        dz = center_z[neighbor] - center_z[owner]
        
        # Magnitude of distance vector
        distance = sqrt(dx*dx + dy*dy + dz*dz)
        
        # Diffusivity at the face (harmonic mean)
        diff_owner = diffusivity[owner]
        diff_neighbor = diffusivity[neighbor]
        diff_face = 2.0 * diff_owner * diff_neighbor / (diff_owner + diff_neighbor + 1e-10)
        
        # Diffusion coefficient
        diff_coeff = diff_face * area / distance
        
        # Extract velocity components
        vx_owner = velocity[owner][1]
        vy_owner = velocity[owner][2]
        vz_owner = velocity[owner][3]
        
        vx_neighbor = velocity[neighbor][1]
        vy_neighbor = velocity[neighbor][2]
        vz_neighbor = velocity[neighbor][3]
        
        # Interpolate velocity to the face (linear interpolation)
        vx_face = 0.5 * (vx_owner + vx_neighbor)
        vy_face = 0.5 * (vy_owner + vy_neighbor)
        vz_face = 0.5 * (vz_owner + vz_neighbor)
        
        # Mass flux through the face
        mass_flux = area * (vx_face * nx + vy_face * ny + vz_face * nz)
        
        # Convection coefficient depends on the scheme
        if scheme == "upwind"
            # Upwind scheme
            if mass_flux > 0
                # Flow from owner to neighbor
                conv_coeff_owner = mass_flux
                conv_coeff_neighbor = 0.0
            else
                # Flow from neighbor to owner
                conv_coeff_owner = 0.0
                conv_coeff_neighbor = -mass_flux
            end
        elseif scheme == "central"
            # Central differencing
            conv_coeff_owner = 0.5 * mass_flux
            conv_coeff_neighbor = 0.5 * mass_flux
        elseif scheme == "hybrid"
            # Hybrid scheme (upwind or central depending on Peclet number)
            peclet = abs(mass_flux) / (diff_coeff + 1e-10)
            if peclet > 2.0
                # Upwind for high Peclet number
                if mass_flux > 0
                    conv_coeff_owner = mass_flux
                    conv_coeff_neighbor = 0.0
                else
                    conv_coeff_owner = 0.0
                    conv_coeff_neighbor = -mass_flux
                end
            else
                # Central for low Peclet number
                conv_coeff_owner = 0.5 * mass_flux
                conv_coeff_neighbor = 0.5 * mass_flux
            end
        else
            error("Unknown scheme: $scheme")
        end
        
        # Add diffusion contribution
        count += 1
        I[count] = owner
        J[count] = neighbor
        V[count] = -diff_coeff
        
        count += 1
        I[count] = neighbor
        J[count] = owner
        V[count] = -diff_coeff
        
        # Add to diagonal entries (diffusion)
        V[diag_indices[owner]] += diff_coeff
        V[diag_indices[neighbor]] += diff_coeff
        
        # Add convection contribution
        count += 1
        I[count] = owner
        J[count] = neighbor
        V[count] -= conv_coeff_owner
        
        count += 1
        I[count] = neighbor
        J[count] = owner
        V[count] -= conv_coeff_neighbor
        
        # Add to diagonal entries (convection)
        V[diag_indices[owner]] += conv_coeff_owner
        V[diag_indices[neighbor]] += conv_coeff_neighbor
    end
    
    # Process boundary faces
    for (boundary_name, face_indices) in mesh_opt.boundary_faces
        # Get boundary condition for this boundary
        boundary = if hasfield(typeof(mesh), :boundaries) && haskey(mesh.boundaries, boundary_name)
            mesh.boundaries[boundary_name]
        else
            continue
        end
        
        for face_idx in face_indices
            owner = face_owner[face_idx]
            
            # Face area
            area = face_area[face_idx]
            
            # Get cell center and face center
            cell_center = SVector(center_x[owner], center_y[owner], center_z[owner])
            face_center = if hasfield(typeof(mesh), :faces) && face_idx <= length(mesh.faces)
                mesh.faces[face_idx].center
            else
                # Estimate face center as cell center + normal * distance
                cell_center + SVector(face_normal_x[face_idx], face_normal_y[face_idx], face_normal_z[face_idx]) * 0.5
            end
            
            # Distance to boundary (approximate)
            distance = norm(face_center - cell_center)
            
            # Process boundary conditions (simplified approach)
            if hasfield(typeof(boundary), :type)
                if boundary.type == "fixedValue"
                    # Add diffusion contribution to diagonal
                    V[diag_indices[owner]] += diffusivity[owner] * area / distance
                    
                    # For convection, use upwind for inlet/outlet boundaries
                    # This is a simplified approach and should be extended
                    # to handle different boundary condition types
                elseif boundary.type == "zeroGradient"
                    # No special handling needed for diffusion
                    # For convection, use upwind for outlet boundaries
                    # TODO: Implement proper handling based on velocity field
                end
            end
        end
    end
    
    # Trim arrays to actual size
    resize!(I, count)
    resize!(J, count)
    resize!(V, count)
    
    # Create sparse matrix
    A = sparse(I, J, V, n_cells, n_cells)
    
    # If OpenFOAM compatibility is required, ensure matrix has OpenFOAM format
    if openfoam_compatible
        A = ensure_openfoam_matrix_format(A)
    end
    
    return A
end

"""
    ensure_openfoam_matrix_format(A)

Ensure the matrix is in a format compatible with OpenFOAM.
This function is a placeholder and should be implemented based on
the specific format requirements of OpenFOAM.

# Arguments
- `A`: Input matrix

# Returns
- `A_compatible`: Matrix in OpenFOAM-compatible format
"""
function ensure_openfoam_matrix_format(A)
    # In a real implementation, we would convert the matrix to OpenFOAM's
    # specific format if needed. For now, we just return the matrix as is.
    return A
end

"""
    check_matrix_compatibility(A, mesh)

Check if a matrix is compatible with a mesh for OpenFOAM export.

# Arguments
- `A`: Matrix to check
- `mesh`: Mesh to check against

# Returns
- `is_compatible`: Boolean indicating compatibility
- `issues`: List of compatibility issues (empty if compatible)
"""
function check_matrix_compatibility(A, mesh::Mesh)
    issues = String[]
    
    # Check matrix dimensions
    n_cells = length(mesh.cells)
    if size(A, 1) != n_cells || size(A, 2) != n_cells
        push!(issues, "Matrix dimensions ($(size(A))) do not match mesh cell count ($n_cells)")
    end
    
    # Check for NaN or Inf values
    if any(isnan, A) || any(isinf, A)
        push!(issues, "Matrix contains NaN or Inf values")
    end
    
    # Check for symmetry if expected
    if !issymmetric(A) && !ishermitian(A)
        push!(issues, "Matrix is not symmetric (this may be intentional for convection terms)")
    end
    
    # Check diagonal dominance (this is a common expectation for discretized PDEs)
    is_diag_dominant = true
    for i in 1:size(A, 1)
        diag_val = abs(A[i, i])
        row_sum = sum(abs.(A[i, :]))
        
        if diag_val < row_sum - diag_val
            is_diag_dominant = false
            break
        end
    end
    
    if !is_diag_dominant
        push!(issues, "Matrix is not diagonally dominant (this may indicate stability issues)")
    end
    
    return isempty(issues), issues
end

end # module
