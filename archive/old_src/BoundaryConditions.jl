# =========================================================================
# Boundary Conditions Module - Implementation of boundary condition application
# =========================================================================

# External dependencies
using StaticArrays      # For small fixed-size vectors and matrices
using LinearAlgebra     # For vector operations like dot product and normalize

# Import core types from JuliaFOAM main module
import ..JuliaFOAM: Field, Mesh

# This module assumes the following types are available from the parent module:
# - BoundaryCondition: Abstract type for all boundary conditions
# - FixedValueBC, ZeroGradientBC, FixedGradientBC, etc.: Concrete boundary condition types

"""
    apply_boundary_conditions!(field::Field, mesh::Mesh)

Apply boundary conditions to a field based on the boundary conditions defined in the mesh.

# Arguments
- `field::Field{T}`: The field to apply boundary conditions to
- `mesh::Mesh`: The mesh containing boundary condition definitions

# OpenFOAM Compatibility
This function is designed to work similarly to OpenFOAM's boundary condition application,
making it easier to port OpenFOAM cases to JuliaFOAM.
"""
function apply_boundary_conditions_with_dict!(field::Field{T}, mesh::Mesh, bcs::Dict{String,JuliaFOAM.BoundaryCondition}) where T
    # Apply boundary conditions from the provided dictionary
    for (patch_name, bc) in bcs
        # Skip if patch doesn't exist in the mesh
        if !haskey(mesh.boundary_patches, patch_name)
            continue
        end

        face_indices = mesh.boundary_patches[patch_name]

        # Type checking for vector fields
        if T <: SVector{3, Float64} && typeof(bc) <: FixedValueBC{<:Any}
            # Ensure the BC value is compatible with vector field
            if !(typeof(bc.value) <: SVector{3, Float64})
                continue
            end
        end

        # Apply the boundary condition
        apply_boundary_condition!(field, mesh, face_indices, bc, patch_name)
    end
end

# Standard method for applying boundary conditions from mesh
function apply_boundary_conditions!(field::Field{T}, mesh::Mesh) where T
    # Generic handling using the mesh's boundary conditions
    for (patch_name, bc) in mesh.boundary_conditions
        face_indices = mesh.boundary_patches[patch_name]
        apply_boundary_condition!(field, mesh, face_indices, bc, patch_name)
    end
end

# Convenience method for applying velocity boundary conditions
function apply_velocity_boundary_conditions!(velocity_field::Field{SVector{3, Float64}}, mesh::Mesh, velocity_bcs::Dict{String,JuliaFOAM.BoundaryCondition})
    apply_boundary_conditions_with_dict!(velocity_field, mesh, velocity_bcs)
end

# Convenience method for applying pressure boundary conditions
function apply_pressure_boundary_conditions!(pressure_field::Field{Float64}, mesh::Mesh, pressure_bcs::Dict{String,JuliaFOAM.BoundaryCondition})
    apply_boundary_conditions_with_dict!(pressure_field, mesh, pressure_bcs)
end

"""
    apply_boundary_condition!(field::Field, mesh::Mesh, face_indices, bc, patch_name)

Apply a specific boundary condition to a field on a specific patch.
"""
function apply_boundary_condition!(field::Field{T}, mesh::Mesh, face_indices::Vector{Int32},
                                  bc::FixedValueBC{T}, patch_name::String) where T
    # For fixed value BC, directly set the boundary field values
    # Use loop to avoid broadcasting issues with SVector
    for i in eachindex(field.boundary_values[patch_name])
        field.boundary_values[patch_name][i] = bc.value
    end
end

function apply_boundary_condition!(field::Field{T}, mesh::Mesh, face_indices::Vector{Int32},
                                  bc::ZeroGradientBC, patch_name::String) where T
    # For zero gradient BC, set boundary values equal to adjacent cell values
    for (i, face_idx) in enumerate(face_indices)
        face = mesh.faces[face_idx]
        owner_cell = face.owner
        field.boundary_values[patch_name][i] = field.internal_field[owner_cell]
    end
end

function apply_boundary_condition!(field::Field{T}, mesh::Mesh, face_indices::Vector{Int32},
                                  bc::FixedGradientBC{T}, patch_name::String) where T
    # For fixed gradient BC, calculate boundary value based on gradient and distance
    for (i, face_idx) in enumerate(face_indices)
        face = mesh.faces[face_idx]
        owner_cell = face.owner

        # Calculate distance from cell center to face center
        cell_center = mesh.cells[owner_cell].center
        face_center = face.center
        delta = face_center - cell_center

        # Calculate normal component of the distance
        face_normal = normalize(face.area)
        delta_normal = dot(delta, face_normal)

        # Set boundary value: cell_value + gradient * distance
        if T <: SVector{3,Float64}  # Vector field
            field.boundary_values[patch_name][i] = field.internal_field[owner_cell] +
                                                  dot(bc.gradient, face_normal) * delta_normal
        else  # Scalar field
            field.boundary_values[patch_name][i] = field.internal_field[owner_cell] +
                                                  bc.gradient * delta_normal
        end
    end
end

function apply_boundary_condition!(field::Field{T}, mesh::Mesh, face_indices::Vector{Int32},
                                  bc::CyclicBC, patch_name::String) where T
    # For cyclic BC, need to find matching faces and copy values
    matching_patch = bc.matching_patch
    matching_faces = mesh.boundary_patches[matching_patch]

    # Ensure patches have same number of faces
    if length(face_indices) != length(matching_faces)
        error("Cyclic patches $patch_name and $matching_patch have different number of faces")
    end

    # This is simplified - in reality we need to match faces properly
    # Here we assume faces are ordered the same way in both patches
    for (i, face_idx) in enumerate(face_indices)
        matching_face_idx = matching_faces[i]

        # Get owner cells
        face = mesh.faces[face_idx]
        matching_face = mesh.faces[matching_face_idx]

        owner_cell = face.owner
        matching_owner_cell = matching_face.owner

        # Copy values (simplified - should interpolate if non-conformal)
        field.boundary_values[patch_name][i] = field.internal_field[matching_owner_cell]
    end
end

"""
    get_boundary_face_value(field::Field, face_idx::Int32, mesh::Mesh)

Get the value of a field at a boundary face.
"""
function get_boundary_face_value(field::Field{T}, face_idx::Int32, mesh::Mesh) where T
    # Find which patch this face belongs to
    for (patch_name, patch_faces) in mesh.boundary_patches
        local_idx = findfirst(x -> x == face_idx, patch_faces)
        if local_idx !== nothing
            return field.boundary_values[patch_name][local_idx]
        end
    end

    error("Face $face_idx not found in any boundary patch")
end

"""
    get_boundary_face_vector_value(field::Field, face_idx::Int32, mesh::Mesh)

Get the vector value of a field at a boundary face.
"""
function get_boundary_face_vector_value(field::Field{SVector{3,Float64}}, face_idx::Int32, mesh::Mesh)
    return get_boundary_face_value(field, face_idx, mesh)
end
