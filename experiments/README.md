# JuliaFOAM Experiments

This directory contains all experimental code, tests, benchmarks, validations, and verifications for the JuliaFOAM project.

## Directory Structure

- `benchmarks/` - Performance benchmarks and comparisons
  - `results/` - Benchmark results and reports
  - `cases/` - Benchmark case definitions

- `tests/` - Unit and integration tests
  - `unit/` - Unit tests for individual components
  - `integration/` - Integration tests for combined components
  - `results/` - Test results and logs

- `validations/` - Validation test cases
  - `cases/` - Validation case definitions
  - `results/` - Validation results and comparisons
  - `references/` - Reference data for validation

- `verifications/` - Verification test cases
  - `cases/` - Verification case definitions
  - `results/` - Verification results

## Running Experiments

### Running Benchmarks

```bash
julia ../run_benchmarks.jl
```

### Running Validations

```bash
julia ../run_verification_validation_benchmark.jl --validation-only
```

### Running Verifications

```bash
julia ../run_verification_validation_benchmark.jl --verification-only
```

### Running Tests

```bash
# Run all tests
julia -e 'using Pkg; Pkg.test("JuliaFOAM")'

# Run specific test file
julia path/to/test_file.jl
```

## Adding New Experiments

1. **Benchmarks**: Add new benchmark cases in `benchmarks/cases/`
2. **Tests**: Add new test files in `tests/unit/` or `tests/integration/`
3. **Validations**: Add validation cases in `validations/cases/`
4. **Verifications**: Add verification cases in `verifications/cases/`

## Results and Reports

- Benchmark results are stored in `benchmarks/results/`
- Test results are stored in `tests/results/`
- Validation results are stored in `validations/results/`
- Verification results are stored in `verifications/results/`

## Dependencies

All required Julia packages are specified in the main `Project.toml` file. To install them, run:

```bash
julia -e 'using Pkg; Pkg.instantiate()'
```
