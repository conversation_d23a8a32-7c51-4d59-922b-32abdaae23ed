"""
    test_optimizations.jl

This example demonstrates the use of the optimized modules in JuliaFOAM.
It tests the cache-optimized mesh operations and matrix operations.
"""

using JuliaFOAM
using LinearAlgebra
using SparseArrays
using StaticArrays
using Printf
using Statistics

# Import specific types and modules
import JuliaFOAM: Cell, Face, Mesh, Field, BoundaryCondition, FluidProperties
import JuliaFOAM.Optimizations: CacheOptimizedMeshOps, OptimizedMatrixOps

# Test cache-optimized mesh operations
function test_cache_optimized_mesh_ops()
    println("\n================================================================================")
    println("Testing Cache-Optimized Mesh Operations")
    println("================================================================================")
    
    # Create a simple mesh
    n_cells = 1000
    n_faces = 3000
    
    # Create cells
    cells = []
    for i in 1:n_cells
        # Cell center
        center = SVector{3, Float64}(
            rand(),
            rand(),
            rand()
        )
        
        # Cell volume
        volume = 0.001
        
        # Create cell
        push!(cells, Cell(Int32[], center, volume))
    end
    
    # Create faces
    faces = []
    for i in 1:n_faces
        # Face center
        center = SVector{3, Float64}(
            rand(),
            rand(),
            rand()
        )
        
        # Face area
        area = SVector{3, Float64}(
            rand(),
            rand(),
            rand()
        )
        
        # Owner and neighbor cells
        owner = rand(1:n_cells)
        neighbour = rand(0:n_cells)  # 0 means boundary face
        if neighbour == owner
            neighbour = 0
        end
        
        # Create face
        push!(faces, Face(Int32(owner), Int32(neighbour), area, center))
    end
    
    # Create mesh
    boundary_faces = Int32[]
    boundary_patches = Dict{String, Vector{Int32}}()
    boundary_conditions = Dict{String, BoundaryCondition}()
    mesh = Mesh(cells, faces, boundary_faces, boundary_patches, boundary_conditions)
    
    # Convert to SoA mesh
    println("  Converting to SoA mesh...")
    soa_mesh = CacheOptimizedMeshOps.convert_to_soa_mesh(mesh)
    
    # Create a scalar field
    println("  Creating scalar field...")
    scalar_field = [rand() for _ in 1:n_cells]
    
    # Create a vector field
    println("  Creating vector field...")
    vector_field = zeros(Float64, 3, n_cells)
    for i in 1:n_cells
        vector_field[1, i] = rand()
        vector_field[2, i] = rand()
        vector_field[3, i] = rand()
    end
    
    # Allocate result arrays
    grad = zeros(Float64, 3, n_cells)
    div = zeros(Float64, n_cells)
    lap = zeros(Float64, n_cells)
    face_values = zeros(Float64, n_faces)
    
    # Test gradient calculation
    println("  Testing gradient calculation...")
    start_time = time()
    CacheOptimizedMeshOps.gradient_soa!(grad, scalar_field, soa_mesh)
    end_time = time()
    println("    Time: $(end_time - start_time) seconds")
    
    # Test divergence calculation
    println("  Testing divergence calculation...")
    start_time = time()
    CacheOptimizedMeshOps.divergence_soa!(div, vector_field, soa_mesh)
    end_time = time()
    println("    Time: $(end_time - start_time) seconds")
    
    # Test Laplacian calculation
    println("  Testing Laplacian calculation...")
    start_time = time()
    CacheOptimizedMeshOps.laplacian_soa!(lap, scalar_field, soa_mesh)
    end_time = time()
    println("    Time: $(end_time - start_time) seconds")
    
    # Test interpolation to faces
    println("  Testing interpolation to faces...")
    start_time = time()
    CacheOptimizedMeshOps.interpolate_to_faces_soa!(face_values, scalar_field, soa_mesh)
    end_time = time()
    println("    Time: $(end_time - start_time) seconds")
    
    println("  Cache-optimized mesh operations completed successfully!")
end

# Test optimized matrix operations
function test_optimized_matrix_ops()
    println("\n================================================================================")
    println("Testing Optimized Matrix Operations")
    println("================================================================================")
    
    # Create a sparse matrix
    n = 1000
    A = sprand(n, n, 0.01)
    
    # Create vectors
    x = rand(n)
    y1 = zeros(n)
    y2 = zeros(n)
    y3 = zeros(n)
    
    # Test standard matrix-vector multiplication
    println("  Testing standard matrix-vector multiplication...")
    start_time = time()
    mul!(y1, A, x)
    end_time = time()
    standard_time = end_time - start_time
    println("    Time: $(standard_time) seconds")
    
    # Test CSR-like matrix-vector multiplication
    println("  Testing CSR-like matrix-vector multiplication...")
    start_time = time()
    JuliaFOAM.MatrixOperations.csr_simd_matvec!(y2, A, x)
    end_time = time()
    csr_time = end_time - start_time
    println("    Time: $(csr_time) seconds")
    
    # Test blocked matrix-vector multiplication
    println("  Testing blocked matrix-vector multiplication...")
    start_time = time()
    JuliaFOAM.MatrixOperations.parallel_blocked_csr_matvec!(y3, A, x)
    end_time = time()
    blocked_time = end_time - start_time
    println("    Time: $(blocked_time) seconds")
    
    # Check results
    println("  Checking results...")
    println("    Standard vs CSR-like: $(norm(y1 - y2))")
    println("    Standard vs Blocked: $(norm(y1 - y3))")
    
    # Test optimized matrix-vector product
    println("  Testing optimized matrix-vector product...")
    start_time = time()
    JuliaFOAM.MatrixOperations.optimized_matrix_vector_product!(y1, A, x)
    end_time = time()
    optimized_time = end_time - start_time
    println("    Time: $(optimized_time) seconds")
    
    # Calculate speedups
    println("  Speedups:")
    println("    CSR-like vs Standard: $(standard_time / csr_time)x")
    println("    Blocked vs Standard: $(standard_time / blocked_time)x")
    println("    Optimized vs Standard: $(standard_time / optimized_time)x")
    
    println("  Optimized matrix operations completed successfully!")
end

# Run all tests
function run_all_tests()
    println("================================================================================")
    println("JuliaFOAM Optimizations Test")
    println("================================================================================")
    
    # Test cache-optimized mesh operations
    test_cache_optimized_mesh_ops()
    
    # Test optimized matrix operations
    test_optimized_matrix_ops()
    
    println("\n================================================================================")
    println("All tests completed successfully!")
    println("================================================================================")
end

# Run the tests
run_all_tests()
