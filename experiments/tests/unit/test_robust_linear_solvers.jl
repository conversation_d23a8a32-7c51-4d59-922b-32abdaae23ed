"""
    test_robust_linear_solvers.jl

This example tests the RobustLinearSolvers module directly.
"""

using LinearAlgebra
using SparseArrays
using Printf

# RobustLinearSolvers functionality is now part of LinearSolvers
using JuliaFOAM
using JuliaFOAM.LinearSolvers

"""
    create_ill_conditioned_matrix(n::Int, condition_number::Float64=1e10)

Create an ill-conditioned sparse matrix for testing.

# Arguments
- `n`: Matrix size
- `condition_number`: Desired condition number

# Returns
- `SparseMatrixCSC{Float64, Int}`: Ill-conditioned matrix
"""
function create_ill_conditioned_matrix(n::Int, condition_number::Float64=1e10)
    # Create a diagonal matrix with eigenvalues ranging from 1 to condition_number
    d = [1.0 + (condition_number - 1.0) * (i-1) / (n-1) for i in 1:n]
    
    # Create a random orthogonal matrix
    Q = qr(randn(n, n)).Q
    
    # Create the ill-conditioned matrix: Q * D * Q'
    A_dense = Q * Diagonal(d) * Q'
    
    # Convert to sparse
    A = sparse(A_dense)
    
    return A
end

"""
    create_test_linear_system(n::Int, condition_number::Float64=1e10, add_nan::Bool=false)

Create a test linear system with controllable numerical issues.

# Arguments
- `n`: System size
- `condition_number`: Desired condition number
- `add_nan`: Whether to add NaN values to the matrix

# Returns
- `Tuple{SparseMatrixCSC{Float64, Int}, Vector{Float64}}`: Matrix and right-hand side
"""
function create_test_linear_system(n::Int, condition_number::Float64=1e10, add_nan::Bool=false)
    # Create ill-conditioned matrix
    A = create_ill_conditioned_matrix(n, condition_number)
    
    # Create a known solution
    x_exact = ones(n)
    
    # Create right-hand side
    b = A * x_exact
    
    # Add some numerical issues if requested
    if add_nan
        # Add NaN to a few entries
        for i in 1:3
            idx = rand(1:n)
            A[idx, idx] = NaN
        end
        
        # Add NaN to right-hand side
        idx = rand(1:n)
        b[idx] = NaN
    end
    
    return A, b, x_exact
end

"""
    test_standard_solver(A::SparseMatrixCSC{Float64, Int}, b::Vector{Float64}, x_exact::Vector{Float64})

Test the standard solver on a linear system.

# Arguments
- `A`: System matrix
- `b`: Right-hand side
- `x_exact`: Exact solution

# Returns
- `Dict`: Test results
"""
function test_standard_solver(A::SparseMatrixCSC{Float64, Int}, b::Vector{Float64}, x_exact::Vector{Float64})
    println("\n=== Testing Standard Solver ===")
    
    # Create initial guess
    x0 = zeros(length(b))
    
    # Create diagonal preconditioner
    n = length(b)
    d_inv = zeros(Float64, n)
    
    for i in 1:n
        if abs(A[i, i]) > 1e-15
            d_inv[i] = 1.0 / A[i, i]
        else
            d_inv[i] = 1.0
        end
    end
    
    precond = (z, r) -> z .= d_inv .* r
    
    # Solve with standard BiCGSTAB
    println("Solving with standard BiCGSTAB...")
    
    try
        # Time the solution
        start_time = time()
        
        # Create BiCGSTAB solver
        function bicgstab_solve(A, b, x0, precond, tol, maxiter)
            n = length(b)
            x = copy(x0)
            r = b - A * x
            r_hat = copy(r)
            p = zeros(Float64, n)
            v = zeros(Float64, n)
            s = zeros(Float64, n)
            t = zeros(Float64, n)
            
            rho_prev = 1.0
            alpha = 1.0
            omega = 1.0
            
            iter = 0
            r_norm = norm(r)
            b_norm = norm(b)
            
            while iter < maxiter && r_norm > tol * b_norm
                rho = dot(r_hat, r)
                
                if abs(rho) < 1e-15
                    break
                end
                
                if iter > 0
                    beta = (rho / rho_prev) * (alpha / omega)
                    p .= r .+ beta .* (p .- omega .* v)
                else
                    p .= r
                end
                
                p_hat = similar(p)
                precond(p_hat, p)
                
                v .= A * p_hat
                
                alpha = rho / dot(r_hat, v)
                s .= r .- alpha .* v
                
                s_hat = similar(s)
                precond(s_hat, s)
                
                t .= A * s_hat
                
                omega = dot(t, s) / dot(t, t)
                
                x .+= alpha .* p_hat .+ omega .* s_hat
                r .= s .- omega .* t
                
                r_norm = norm(r)
                iter += 1
            end
            
            return x, iter, r_norm / b_norm
        end
        
        x, iter, rel_residual = bicgstab_solve(A, b, x0, precond, 1e-6, 1000)
        
        end_time = time()
        solution_time = end_time - start_time
        
        # Compute error
        error = norm(x - x_exact) / norm(x_exact)
        
        println("  Iterations: $iter")
        println("  Relative Residual: $rel_residual")
        println("  Relative Error: $error")
        println("  Solution Time: $solution_time seconds")
        
        return Dict(
            "success" => true,
            "iterations" => iter,
            "relative_residual" => rel_residual,
            "relative_error" => error,
            "solution_time" => solution_time
        )
    catch e
        println("  ERROR: Standard solver failed with error:")
        println("  $e")
        
        return Dict(
            "success" => false,
            "error" => e
        )
    end
end

"""
    test_robust_solver(A::SparseMatrixCSC{Float64, Int}, b::Vector{Float64}, x_exact::Vector{Float64})

Test the robust solver on a linear system.

# Arguments
- `A`: System matrix
- `b`: Right-hand side
- `x_exact`: Exact solution

# Returns
- `Dict`: Test results
"""
function test_robust_solver(A::SparseMatrixCSC{Float64, Int}, b::Vector{Float64}, x_exact::Vector{Float64})
    println("\n=== Testing Robust Solver ===")
    
    # Create initial guess
    x0 = zeros(length(b))
    
    # Diagnose the linear system
    println("Diagnosing linear system...")
    diagnostics = check_matrix_properties(A)
    
    # Print issues
    if haskey(diagnostics, :weak_rows) && !isempty(diagnostics[:weak_rows])
        println("  Issue detected: Matrix is not diagonally dominant")
        println("  $(length(diagnostics[:weak_rows])) weak rows found")
    end
    
    if haskey(diagnostics, :zero_diagonals) && !isempty(diagnostics[:zero_diagonals])
        println("  Issue detected: Matrix has zero diagonal entries")
        println("  $(length(diagnostics[:zero_diagonals])) zero diagonals found")
    end
    
    if get(diagnostics, :ill_conditioned, false)
        println("  Issue detected: Matrix is ill-conditioned")
        println("  Condition number: $(get(diagnostics, :condition_number, "unknown"))")
    end
    
    # Create diagonal preconditioner
    n = length(b)
    d_inv = zeros(Float64, n)
    
    for i in 1:n
        if abs(A[i, i]) > 1e-15
            d_inv[i] = 1.0 / A[i, i]
        else
            d_inv[i] = 1.0
        end
    end
    
    precond = (z, r) -> z .= d_inv .* r
    
    # Solve with robust BiCGSTAB
    println("Solving with robust BiCGSTAB...")
    
    # Time the solution
    start_time = time()
    
    # Solve with robust BiCGSTAB
    iter, rel_residual = robust_bicgstab_solve!(
        A, b, x0, precond, 1e-6, 1000,
        verbose=true
    )
    
    end_time = time()
    solution_time = end_time - start_time
    
    # Compute error
    error = norm(x0 - x_exact) / norm(x_exact)
    
    println("  Iterations: $iter")
    println("  Relative Residual: $rel_residual")
    println("  Relative Error: $error")
    println("  Solution Time: $solution_time seconds")
    
    return Dict(
        "success" => true,
        "iterations" => iter,
        "relative_residual" => rel_residual,
        "relative_error" => error,
        "solution_time" => solution_time,
        "diagnostics" => diagnostics
    )
end

"""
    run_tests()

Run tests comparing standard and robust solvers.
"""
function run_tests()
    println("=== Robust Linear Solver Test ===")
    
    # Test with well-conditioned matrix
    println("\n--- Test 1: Well-conditioned matrix ---")
    A1, b1, x_exact1 = create_test_linear_system(100, 1e3, false)
    standard_results1 = test_standard_solver(A1, b1, x_exact1)
    robust_results1 = test_robust_solver(A1, b1, x_exact1)
    
    # Test with ill-conditioned matrix
    println("\n--- Test 2: Ill-conditioned matrix ---")
    A2, b2, x_exact2 = create_test_linear_system(100, 1e10, false)
    standard_results2 = test_standard_solver(A2, b2, x_exact2)
    robust_results2 = test_robust_solver(A2, b2, x_exact2)
    
    # Test with NaN values
    println("\n--- Test 3: Matrix with NaN values ---")
    A3, b3, x_exact3 = create_test_linear_system(100, 1e6, true)
    standard_results3 = test_standard_solver(A3, b3, x_exact3)
    robust_results3 = test_robust_solver(A3, b3, x_exact3)
    
    # Print summary
    println("\n=== Summary ===")
    println("Test 1 (Well-conditioned):")
    println("  Standard solver: $(get(standard_results1, "success", false) ? "SUCCESS" : "FAILED")")
    println("  Robust solver: $(get(robust_results1, "success", false) ? "SUCCESS" : "FAILED")")
    
    println("Test 2 (Ill-conditioned):")
    println("  Standard solver: $(get(standard_results2, "success", false) ? "SUCCESS" : "FAILED")")
    println("  Robust solver: $(get(robust_results2, "success", false) ? "SUCCESS" : "FAILED")")
    
    println("Test 3 (NaN values):")
    println("  Standard solver: $(get(standard_results3, "success", false) ? "SUCCESS" : "FAILED")")
    println("  Robust solver: $(get(robust_results3, "success", false) ? "SUCCESS" : "FAILED")")
end

# Run the tests
run_tests()
