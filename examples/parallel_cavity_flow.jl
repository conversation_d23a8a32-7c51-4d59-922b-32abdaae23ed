"""
Parallel Lid-Driven Cavity Flow Example

This example demonstrates transparent parallel execution of the lid-driven cavity
benchmark using JuliaFOAM's new parallel abstractions. The code is nearly identical
to the serial version, but runs efficiently in parallel.

Run with MPI:
```bash
mpiexec -np 4 julia parallel_cavity_flow.jl
```
"""

using JuliaFOAM
using LinearAlgebra
using Printf

# Only use MPI features if available
if JuliaFOAM.HAVE_MPI
    using JuliaFOAM.TransparentParallel
    using MPI
else
    error("This example requires MPI. Please install MPI.jl")
end

function setup_cavity_case(N::Int=50, Re::Float64=1000.0)
    """Setup lid-driven cavity case with given resolution and Reynolds number"""
    
    # Create 2D mesh
    mesh_2d = create_box_mesh(N, N, 1.0, 1.0)
    mesh = create_2d_mesh_as_3d(mesh_2d)
    
    # Set boundary conditions
    U_lid = 1.0  # Lid velocity
    nu = U_lid * 1.0 / Re  # Kinematic viscosity (L=1.0)
    
    # Velocity boundary conditions
    bc_U = Dict(
        "top" => FixedValueBC(SVector(U_lid, 0.0, 0.0)),
        "bottom" => NoSlipBC(),
        "left" => NoSlipBC(),
        "right" => NoSlipBC(),
        "frontAndBack" => EmptyBC()
    )
    
    # Pressure boundary conditions
    bc_p = Dict(
        "top" => ZeroGradientBC(),
        "bottom" => ZeroGradientBC(),
        "left" => ZeroGradientBC(),
        "right" => ZeroGradientBC(),
        "frontAndBack" => EmptyBC()
    )
    
    # Update mesh with boundary conditions
    for (patch, bc) in bc_U
        if haskey(mesh.boundary_conditions, patch)
            mesh.boundary_conditions[patch] = bc
        end
    end
    
    # Fluid properties
    properties = FluidProperties(nu=nu, rho=1.0)
    
    return mesh, bc_U, bc_p, properties
end

function run_parallel_cavity(N::Int=50, Re::Float64=1000.0, end_time::Float64=10.0)
    """Run parallel lid-driven cavity simulation"""
    
    # Initialize parallel environment
    comm, rank, nprocs = init_parallel()
    
    timer = start_timer("Total simulation")
    
    # Print info on rank 0
    if rank == 0
        println("\n" * "="^60)
        println("Parallel Lid-Driven Cavity Flow")
        println("="^60)
        println("Grid size: $(N)×$(N)")
        println("Reynolds number: $Re")
        println("Number of processes: $nprocs")
        println("="^60 * "\n")
    end
    
    # Setup case
    setup_timer = start_timer("Case setup")
    mesh, bc_U, bc_p, properties = setup_cavity_case(N, Re)
    stop_timer(setup_timer)
    
    # Automatic mesh partitioning
    partition_timer = start_timer("Mesh partitioning")
    automatic_partitioning!(mesh, method=:simple, comm=comm)  # Use simple for 2D
    dmesh = DistributedMeshData(mesh, comm)
    stop_timer(partition_timer)
    
    # Display mesh statistics
    if rank == 0
        parallel_mesh_stats(mesh, comm=comm)
    end
    
    # Create distributed fields
    field_timer = start_timer("Field initialization")
    U = create_distributed_field("U", mesh, SVector(0.0, 0.0, 0.0), comm=comm)
    p = create_distributed_field("p", mesh, 0.0, comm=comm)
    stop_timer(field_timer)
    
    # Apply initial boundary conditions
    apply_boundary_conditions!(U, bc_U, mesh)
    apply_boundary_conditions!(p, bc_p, mesh)
    
    # Time stepping parameters
    dt = 0.001
    n_steps = Int(end_time / dt)
    write_interval = max(1, n_steps ÷ 20)
    
    # Solver configuration
    solver_config = SimpleSolverConfig(
        momentum_solver=:BiCGSTAB,
        pressure_solver=:PCG,
        momentum_tol=1e-8,
        pressure_tol=1e-10,
        max_momentum_iter=100,
        max_pressure_iter=200,
        n_pressure_correctors=2,
        momentum_relaxation=0.7,
        pressure_relaxation=0.3
    )
    
    # Main time loop
    if rank == 0
        println("\nStarting time integration...")
    end
    
    solve_timer = start_timer("Time integration")
    
    for step in 1:n_steps
        t = step * dt
        
        # Solve Navier-Stokes with automatic parallel execution
        solve_navier_stokes!(U, p, mesh, dt, properties, solver_config, bc_U, bc_p)
        
        # Synchronize fields (done automatically by operations)
        sync!(U)
        sync!(p)
        
        # Output progress
        if step % write_interval == 0
            # Compute global statistics
            U_max = maximum(norm.(U.local_field.values[1:length(U.local_cells)]))
            U_max_global = MPI.Allreduce(U_max, max, comm)
            
            div_U = div(U, dmesh)
            continuity_error = maximum(abs.(div_U.local_field.values[1:length(div_U.local_cells)]))
            continuity_error_global = MPI.Allreduce(continuity_error, max, comm)
            
            if rank == 0
                @printf("Step %5d, t = %6.3f, max|U| = %.6f, max|div(U)| = %.2e\n",
                       step, t, U_max_global, continuity_error_global)
            end
        end
    end
    
    stop_timer(solve_timer)
    
    # Final statistics
    if rank == 0
        println("\n" * "="^60)
        println("Simulation completed!")
        
        # Compute cavity flow characteristics
        # These would need proper parallel reduction operations
        println("\nFlow characteristics:")
        
        # Get center line velocities (simplified - would need proper gathering)
        # This is just a placeholder
        println("  (Detailed analysis requires gathering distributed fields)")
        
        println("="^60)
    end
    
    stop_timer(timer)
    
    # Write final fields (parallel I/O)
    if rank == 0
        println("\nWriting results...")
    end
    
    # Each process writes its portion
    parallel_write(U, "cavity_results")
    parallel_write(p, "cavity_results")
    
    return U, p
end

# Function to compare with analytical or benchmark data
function validate_results(U::DistributedField, p::DistributedField, N::Int, Re::Float64)
    """Compare results with Ghia et al. benchmark data"""
    
    comm = U.comm
    rank = U.rank
    
    if rank == 0
        println("\nValidation against Ghia et al. (1982) benchmark:")
        println("-" * 50)
        
        # This would require gathering the solution and interpolating
        # to the centerline positions for comparison
        
        println("(Full validation requires field gathering and interpolation)")
    end
end

# Main execution
function main()
    # Problem parameters
    N = 64  # Grid resolution (use smaller for quick test)
    Re = 1000.0  # Reynolds number
    end_time = 5.0  # Simulation time
    
    # Run simulation
    U, p = run_parallel_cavity(N, Re, end_time)
    
    # Validate results
    validate_results(U, p, N, Re)
    
    # Finalize MPI
    finalize_parallel()
end

# Run the simulation
if abspath(PROGRAM_FILE) == @__FILE__
    main()
end