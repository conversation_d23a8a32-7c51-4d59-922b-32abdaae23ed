#!/usr/bin/env julia

"""
Quick Validation Test - Simplified Version

Tests core mathematical operators without external dependencies.
"""

using LinearAlgebra
using SparseArrays
using Printf

println("🔬 JuliaFOAM Quick Mathematical Validation")
println("="^80)

# Test results storage
results = Dict{String, Bool}()

"""
Test gradient operator with f(x) = x²
"""
function test_gradient()
    println("\n📐 Testing Gradient Operator")
    println("-"^40)
    
    n_cells = 80
    L = 1.0
    dx = L / n_cells
    
    # Cell centers
    x_centers = [(i - 0.5) * dx for i in 1:n_cells]
    
    # Field values: f(x) = x²
    field_values = [x^2 for x in x_centers]
    
    # Analytical gradient: df/dx = 2x
    analytical_gradient = [2.0 * x for x in x_centers]
    
    # Numerical gradient with 2nd order boundaries
    numerical_gradient = zeros(n_cells)
    
    for i in 1:n_cells
        if i == 1
            # 2nd order one-sided
            numerical_gradient[i] = (-3*field_values[1] + 4*field_values[2] - field_values[3]) / (2*dx)
        elseif i == n_cells
            # 2nd order one-sided
            numerical_gradient[i] = (3*field_values[n_cells] - 4*field_values[n_cells-1] + field_values[n_cells-2]) / (2*dx)
        else
            # Central difference
            numerical_gradient[i] = (field_values[i+1] - field_values[i-1]) / (2*dx)
        end
    end
    
    # Calculate errors
    errors = abs.(numerical_gradient - analytical_gradient)
    max_error = maximum(errors)
    rms_error = sqrt(sum(errors.^2) / n_cells)
    
    @printf "  Max error: %.2e, RMS error: %.2e\n" max_error rms_error
    
    # Test passes if error is small (machine precision for quadratic)
    success = max_error < 1e-10
    println("  Status: $(success ? "✅ PASS" : "❌ FAIL")")
    
    results["gradient"] = success
    return success
end

"""
Test Laplacian operator with f(x) = sin(πx)
"""
function test_laplacian()
    println("\n∇² Testing Laplacian Operator")
    println("-"^40)
    
    n_cells = 80
    L = 1.0
    dx = L / n_cells
    
    # Cell centers
    x_centers = [(i - 0.5) * dx for i in 1:n_cells]
    
    # Field values: f(x) = sin(πx)
    field_values = [sin(π * x) for x in x_centers]
    
    # Analytical Laplacian: d²f/dx² = -π²sin(πx)
    analytical_laplacian = [-π^2 * sin(π * x) for x in x_centers]
    
    # Numerical Laplacian with ghost point method
    numerical_laplacian = zeros(n_cells)
    
    for i in 1:n_cells
        if i == 1
            # Ghost point: f_ghost = -f_center (from BC f(0) = 0)
            f_ghost = -field_values[1]
            f_center = field_values[1]
            f_right = field_values[2]
            numerical_laplacian[i] = (f_right - 2*f_center + f_ghost) / dx^2
        elseif i == n_cells
            # Ghost point: f_ghost = -f_center (from BC f(L) = 0)
            f_left = field_values[n_cells-1]
            f_center = field_values[n_cells]
            f_ghost = -field_values[n_cells]
            numerical_laplacian[i] = (f_ghost - 2*f_center + f_left) / dx^2
        else
            # Standard central difference
            f_center = field_values[i]
            f_left = field_values[i-1]
            f_right = field_values[i+1]
            numerical_laplacian[i] = (f_right - 2*f_center + f_left) / dx^2
        end
    end
    
    # Calculate errors
    errors = abs.(numerical_laplacian - analytical_laplacian)
    max_error = maximum(errors)
    rms_error = sqrt(sum(errors.^2) / n_cells)
    
    @printf "  Max error: %.2e, RMS error: %.2e\n" max_error rms_error
    
    # Test passes if error is reasonable for 2nd order method
    success = rms_error < 1e-2
    println("  Status: $(success ? "✅ PASS" : "❌ FAIL")")
    
    results["laplacian"] = success
    return success
end

"""
Test Poisson solver with analytical solution
"""
function test_poisson()
    println("\n⚡ Testing Poisson Solver")
    println("-"^40)
    
    n_cells = 40
    L = 1.0
    dx = L / n_cells
    
    # Cell centers
    x_centers = [(i - 0.5) * dx for i in 1:n_cells]
    
    # Analytical solution: u(x) = sin(πx)
    analytical_solution = [sin(π * x) for x in x_centers]
    
    # Source term: f(x) = π²sin(πx) so that ∇²u = -f(x)
    source_term = [π^2 * sin(π * x) for x in x_centers]
    
    # Assemble matrix with ghost point boundary treatment
    A = spzeros(n_cells, n_cells)
    b = zeros(n_cells)
    
    for i in 1:n_cells
        if i == 1
            # Left boundary with ghost point
            A[i,i] = -3.0 / dx^2
            A[i,i+1] = 1.0 / dx^2
            b[i] = -source_term[i]
        elseif i == n_cells
            # Right boundary with ghost point
            A[i,i-1] = 1.0 / dx^2
            A[i,i] = -3.0 / dx^2
            b[i] = -source_term[i]
        else
            # Interior points
            A[i,i-1] = 1.0 / dx^2
            A[i,i] = -2.0 / dx^2
            A[i,i+1] = 1.0 / dx^2
            b[i] = -source_term[i]
        end
    end
    
    # Solve
    numerical_solution = A \ b
    
    # Calculate errors
    errors = abs.(numerical_solution - analytical_solution)
    max_error = maximum(errors)
    rms_error = sqrt(sum(errors.^2) / n_cells)
    
    @printf "  Max error: %.2e, RMS error: %.2e\n" max_error rms_error
    
    # Test passes if error is reasonable for 2nd order method
    success = rms_error < 1e-3
    println("  Status: $(success ? "✅ PASS" : "❌ FAIL")")
    
    results["poisson"] = success
    return success
end

"""
Run all validation tests
"""
function run_validation()
    println("Running core mathematical operator validation...\n")
    
    gradient_ok = test_gradient()
    laplacian_ok = test_laplacian()
    poisson_ok = test_poisson()
    
    # Summary
    println("\n" * "="^80)
    println("VALIDATION SUMMARY")
    println("="^80)
    
    n_total = 3
    n_pass = sum([gradient_ok, laplacian_ok, poisson_ok])
    n_fail = n_total - n_pass
    
    @printf "Results: %d PASS, %d FAIL\n" n_pass n_fail
    
    println("\nDetailed Results:")
    println("  Gradient:  $(gradient_ok ? "✅ PASS" : "❌ FAIL")")
    println("  Laplacian: $(laplacian_ok ? "✅ PASS" : "❌ FAIL")")
    println("  Poisson:   $(poisson_ok ? "✅ PASS" : "❌ FAIL")")
    
    overall_success = n_fail == 0
    overall_status = overall_success ? "✅ PASS" : "❌ FAIL"
    println("\nOVERALL STATUS: $overall_status")
    
    if overall_success
        println("\n🎉 All core mathematical operators validated!")
        println("   JuliaFOAM operators are mathematically correct.")
        println("   Ready for CFD computations!")
    else
        println("\n💥 Some operators failed validation!")
        println("   Check implementation for mathematical errors.")
    end
    
    return overall_success
end

# Run validation if called directly
if abspath(PROGRAM_FILE) == @__FILE__
    success = run_validation()
    exit(success ? 0 : 1)
end