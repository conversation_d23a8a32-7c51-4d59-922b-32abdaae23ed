#!/usr/bin/env julia

"""
CORRECTED 1D Mathematical Validation for JuliaFOAM Core Operators

This implements the mathematically CORRECT finite difference operators
with proper 2nd order boundary treatment.
"""

using LinearAlgebra
using SparseArrays
using Printf

println("🔬 JuliaFOAM 1D CORRECTED Mathematical Validation")
println("="^80)
println("CORRECTED OPERATORS - PROPER 2ND ORDER BOUNDARY TREATMENT")

# Test results storage
test_results = Dict{String, Any}()

"""
CORRECTED Test 1: 2nd Order Accurate Gradient Operator
Uses 2nd order one-sided differences at boundaries
"""
function test_corrected_gradient()
    println("\n📐 CORRECTED Test 1: Gradient Operator (2nd Order)")
    println("-"^60)
    
    test_results["gradient"] = Dict()
    
    for n_cells in [10, 20, 40, 80]
        L = 1.0
        dx = L / n_cells
        
        # Cell centers
        x_centers = [(i - 0.5) * dx for i in 1:n_cells]
        
        # Field values: f(x) = x²
        field_values = [x^2 for x in x_centers]
        
        # Analytical gradient: df/dx = 2x
        analytical_gradient = [2.0 * x for x in x_centers]
        
        # CORRECTED numerical gradient with 2nd order boundaries
        numerical_gradient = zeros(n_cells)
        
        for i in 1:n_cells
            if i == 1
                # 2nd order forward difference: f'[0] ≈ (-3f[0] + 4f[1] - f[2])/(2*dx)
                # But we need this at the cell center, so use modified stencil
                # For cell-centered: use boundary extrapolation + central difference
                f_boundary = 0.0  # f(0) = 0
                f_center = field_values[i]
                f_right = field_values[i+1]
                
                # Use 2nd order forward difference from boundary to cell center
                x_boundary = 0.0
                x_center = x_centers[i]
                x_right = x_centers[i+1]
                
                # 2nd order interpolation for gradient at cell center
                # Using f(0), f(x_center), f(x_right)
                # Lagrange interpolation derivative
                h1 = x_center - x_boundary
                h2 = x_right - x_center
                numerical_gradient[i] = f_boundary * (-2*h1 - h2)/(h1*(h1 + h2)) + 
                                       f_center * (h1 + h2)/(h1*h2) + 
                                       f_right * (-h1)/(h2*(h1 + h2))
                
            elseif i == n_cells
                # 2nd order backward difference at right boundary
                f_left = field_values[i-1]
                f_center = field_values[i]
                f_boundary = L^2  # f(L) = L²
                
                x_left = x_centers[i-1]
                x_center = x_centers[i]
                x_boundary = L
                
                # 2nd order backward difference
                h1 = x_center - x_left
                h2 = x_boundary - x_center
                numerical_gradient[i] = f_left * h2/(h1*(h1 + h2)) + 
                                       f_center * -(h1 + h2)/(h1*h2) + 
                                       f_boundary * (2*h2 + h1)/(h2*(h1 + h2))
                
            else
                # Standard 2nd order central difference
                f_left = field_values[i-1]
                f_right = field_values[i+1]
                numerical_gradient[i] = (f_right - f_left) / (2*dx)
            end
        end
        
        # Calculate errors
        errors = abs.(numerical_gradient - analytical_gradient)
        max_error = maximum(errors)
        rms_error = sqrt(sum(errors.^2) / n_cells)
        
        test_results["gradient"]["n$n_cells"] = Dict(
            "max_error" => max_error,
            "rms_error" => rms_error,
            "h" => dx
        )
        
        @printf "  n=%3d, dx=%.4f: max_error=%.2e, rms_error=%.2e\n" n_cells dx max_error rms_error
    end
    
    # Check convergence order
    mesh_sizes = [10, 20, 40, 80]
    println("\n  Convergence Analysis:")
    for i in 2:length(mesh_sizes)
        n_coarse = mesh_sizes[i-1]
        n_fine = mesh_sizes[i]
        
        h_coarse = test_results["gradient"]["n$n_coarse"]["h"]
        h_fine = test_results["gradient"]["n$n_fine"]["h"]
        error_coarse = test_results["gradient"]["n$n_coarse"]["rms_error"]
        error_fine = test_results["gradient"]["n$n_fine"]["rms_error"]
        
        if error_coarse > 0 && error_fine > 0
            order = log(error_coarse / error_fine) / log(h_coarse / h_fine)
            @printf "    h=%.4f → %.4f: order = %.2f\n" h_coarse h_fine order
        end
    end
end

"""
CORRECTED Test 2: 2nd Order Accurate Laplacian Operator
Uses proper boundary stencils and ghost point method
"""
function test_corrected_laplacian()
    println("\n∇² CORRECTED Test 2: Laplacian Operator (2nd Order)")
    println("-"^60)
    
    test_results["laplacian"] = Dict()
    
    for n_cells in [10, 20, 40, 80]
        L = 1.0
        dx = L / n_cells
        
        # Cell centers
        x_centers = [(i - 0.5) * dx for i in 1:n_cells]
        
        # Field values: f(x) = sin(πx)
        field_values = [sin(π * x) for x in x_centers]
        
        # Analytical second derivative: d²f/dx² = -π²sin(πx)
        analytical_laplacian = [-π^2 * sin(π * x) for x in x_centers]
        
        # CORRECTED numerical Laplacian with proper boundary treatment
        numerical_laplacian = zeros(n_cells)
        
        for i in 1:n_cells
            if i == 1
                # Left boundary: use ghost point method
                # For Dirichlet BC: f(0) = 0
                # Ghost point: f(-dx/2) such that (f(-dx/2) + f(dx/2))/2 = 0
                # So f(-dx/2) = -f(dx/2)
                f_ghost = -field_values[1]
                f_center = field_values[1]
                f_right = field_values[2]
                
                numerical_laplacian[i] = (f_right - 2*f_center + f_ghost) / dx^2
                
            elseif i == n_cells
                # Right boundary: use ghost point method  
                # For Dirichlet BC: f(L) = 0
                # Ghost point: f(L + dx/2) such that (f(L - dx/2) + f(L + dx/2))/2 = 0
                # So f(L + dx/2) = -f(L - dx/2)
                f_left = field_values[n_cells-1]
                f_center = field_values[n_cells]
                f_ghost = -field_values[n_cells]
                
                numerical_laplacian[i] = (f_ghost - 2*f_center + f_left) / dx^2
                
            else
                # Standard central difference
                f_center = field_values[i]
                f_left = field_values[i-1]
                f_right = field_values[i+1]
                numerical_laplacian[i] = (f_right - 2*f_center + f_left) / dx^2
            end
        end
        
        # Calculate errors
        errors = abs.(numerical_laplacian - analytical_laplacian)
        max_error = maximum(errors)
        rms_error = sqrt(sum(errors.^2) / n_cells)
        
        test_results["laplacian"]["n$n_cells"] = Dict(
            "max_error" => max_error,
            "rms_error" => rms_error,
            "h" => dx
        )
        
        @printf "  n=%3d, dx=%.4f: max_error=%.2e, rms_error=%.2e\n" n_cells dx max_error rms_error
    end
    
    # Check convergence order
    mesh_sizes = [10, 20, 40, 80]
    println("\n  Convergence Analysis:")
    for i in 2:length(mesh_sizes)
        n_coarse = mesh_sizes[i-1]
        n_fine = mesh_sizes[i]
        
        h_coarse = test_results["laplacian"]["n$n_coarse"]["h"]
        h_fine = test_results["laplacian"]["n$n_fine"]["h"]
        error_coarse = test_results["laplacian"]["n$n_coarse"]["rms_error"]
        error_fine = test_results["laplacian"]["n$n_fine"]["rms_error"]
        
        if error_coarse > 0 && error_fine > 0
            order = log(error_coarse / error_fine) / log(h_coarse / h_fine)
            @printf "    h=%.4f → %.4f: order = %.2f\n" h_coarse h_fine order
        end
    end
end

"""
CORRECTED Test 3: 2nd Order Poisson Solver
Uses corrected Laplacian operator
"""
function test_corrected_poisson()
    println("\n⚡ CORRECTED Test 3: Poisson Solver (2nd Order)")
    println("-"^60)
    
    test_results["poisson"] = Dict()
    
    for n_cells in [10, 20, 40, 80]
        L = 1.0
        dx = L / n_cells
        
        # Cell centers
        x_centers = [(i - 0.5) * dx for i in 1:n_cells]
        
        # Analytical solution: u(x) = sin(πx)
        analytical_solution = [sin(π * x) for x in x_centers]
        
        # Source term: f(x) = π²sin(πx) so that d²u/dx² = -f(x)
        source_term = [π^2 * sin(π * x) for x in x_centers]
        
        # Assemble CORRECTED Poisson matrix with proper boundary treatment
        A = spzeros(n_cells, n_cells)
        b = zeros(n_cells)
        
        for i in 1:n_cells
            if i == 1
                # Left boundary with ghost point method
                # Standard stencil: (u[i+1] - 2*u[i] + u_ghost)/dx² = -f[i]
                # Ghost point: u_ghost = -u[i] (from BC u(0) = 0)
                A[i,i] = -3.0 / dx^2
                A[i,i+1] = 1.0 / dx^2
                b[i] = -source_term[i]
                
            elseif i == n_cells
                # Right boundary with ghost point method
                # Standard stencil: (u_ghost - 2*u[i] + u[i-1])/dx² = -f[i]
                # Ghost point: u_ghost = -u[i] (from BC u(L) = 0)
                A[i,i-1] = 1.0 / dx^2
                A[i,i] = -3.0 / dx^2
                b[i] = -source_term[i]
                
            else
                # Internal points: standard central difference
                A[i,i-1] = 1.0 / dx^2
                A[i,i] = -2.0 / dx^2
                A[i,i+1] = 1.0 / dx^2
                b[i] = -source_term[i]
            end
        end
        
        # Solve the linear system
        numerical_solution = A \ b
        
        # Calculate errors
        errors = abs.(numerical_solution - analytical_solution)
        max_error = maximum(errors)
        rms_error = sqrt(sum(errors.^2) / n_cells)
        
        test_results["poisson"]["n$n_cells"] = Dict(
            "max_error" => max_error,
            "rms_error" => rms_error,
            "h" => dx
        )
        
        @printf "  n=%3d, dx=%.4f: max_error=%.2e, rms_error=%.2e\n" n_cells dx max_error rms_error
    end
    
    # Check convergence order
    mesh_sizes = [10, 20, 40, 80]
    println("\n  Convergence Analysis:")
    for i in 2:length(mesh_sizes)
        n_coarse = mesh_sizes[i-1]
        n_fine = mesh_sizes[i]
        
        h_coarse = test_results["poisson"]["n$n_coarse"]["h"]
        h_fine = test_results["poisson"]["n$n_fine"]["h"]
        error_coarse = test_results["poisson"]["n$n_coarse"]["rms_error"]
        error_fine = test_results["poisson"]["n$n_fine"]["rms_error"]
        
        if error_coarse > 0 && error_fine > 0
            order = log(error_coarse / error_fine) / log(h_coarse / h_fine)
            @printf "    h=%.4f → %.4f: order = %.2f\n" h_coarse h_fine order
        end
    end
end

"""
CORRECTED Test 4: 2nd Order Divergence Operator
Uses proper face value computation
"""
function test_corrected_divergence()
    println("\n∇· CORRECTED Test 4: Divergence Operator (2nd Order)")
    println("-"^60)
    
    test_results["divergence"] = Dict()
    
    for n_cells in [10, 20, 40, 80]
        L = 1.0
        dx = L / n_cells
        
        # Cell centers
        x_centers = [(i - 0.5) * dx for i in 1:n_cells]
        
        # Vector field: v(x) = [x² 0 0]
        velocity_x = [x^2 for x in x_centers]
        
        # Analytical divergence: div(v) = dv_x/dx = 2x
        analytical_divergence = [2.0 * x for x in x_centers]
        
        # CORRECTED numerical divergence using proper finite volume
        numerical_divergence = zeros(n_cells)
        
        for i in 1:n_cells
            # Face locations
            x_left_face = (i - 1) * dx
            x_right_face = i * dx
            
            # CORRECTED face velocity computation using 2nd order interpolation
            if i == 1
                # Left boundary: v(0) = 0²
                v_left_face = 0.0
                # Right face: 2nd order interpolation
                # Use quadratic fit through points i, i+1, and boundary
                x1, x2, x3 = x_centers[1], x_centers[2], 0.0
                v1, v2, v3 = velocity_x[1], velocity_x[2], 0.0
                
                # Quadratic interpolation to face at x = x_right_face
                # Using Lagrange interpolation
                x = x_right_face
                v_right_face = v1 * (x - x2)*(x - x3)/((x1 - x2)*(x1 - x3)) +
                              v2 * (x - x1)*(x - x3)/((x2 - x1)*(x2 - x3)) +
                              v3 * (x - x1)*(x - x2)/((x3 - x1)*(x3 - x2))
                              
            elseif i == n_cells
                # Left face: 2nd order interpolation
                x1, x2, x3 = x_centers[n_cells-1], x_centers[n_cells], L
                v1, v2, v3 = velocity_x[n_cells-1], velocity_x[n_cells], L^2
                
                x = x_left_face
                v_left_face = v1 * (x - x2)*(x - x3)/((x1 - x2)*(x1 - x3)) +
                             v2 * (x - x1)*(x - x3)/((x2 - x1)*(x2 - x3)) +
                             v3 * (x - x1)*(x - x2)/((x3 - x1)*(x3 - x2))
                             
                # Right boundary: v(L) = L²
                v_right_face = L^2
                
            else
                # Internal cell: linear interpolation at both faces
                # Left face
                weight = 0.5
                v_left_face = weight * velocity_x[i-1] + (1-weight) * velocity_x[i]
                # Right face
                v_right_face = weight * velocity_x[i] + (1-weight) * velocity_x[i+1]
            end
            
            # Divergence using finite volume
            numerical_divergence[i] = (v_right_face - v_left_face) / dx
        end
        
        # Calculate errors
        errors = abs.(numerical_divergence - analytical_divergence)
        max_error = maximum(errors)
        rms_error = sqrt(sum(errors.^2) / n_cells)
        
        test_results["divergence"]["n$n_cells"] = Dict(
            "max_error" => max_error,
            "rms_error" => rms_error,
            "h" => dx
        )
        
        @printf "  n=%3d, dx=%.4f: max_error=%.2e, rms_error=%.2e\n" n_cells dx max_error rms_error
    end
    
    # Check convergence order
    mesh_sizes = [10, 20, 40, 80]
    println("\n  Convergence Analysis:")
    for i in 2:length(mesh_sizes)
        n_coarse = mesh_sizes[i-1]
        n_fine = mesh_sizes[i]
        
        h_coarse = test_results["divergence"]["n$n_coarse"]["h"]
        h_fine = test_results["divergence"]["n$n_fine"]["h"]
        error_coarse = test_results["divergence"]["n$n_coarse"]["rms_error"]
        error_fine = test_results["divergence"]["n$n_fine"]["rms_error"]
        
        if error_coarse > 0 && error_fine > 0
            order = log(error_coarse / error_fine) / log(h_coarse / h_fine)
            @printf "    h=%.4f → %.4f: order = %.2f\n" h_coarse h_fine order
        end
    end
end

"""
Generate CORRECTED validation report
"""
function generate_corrected_validation_report()
    println("\n📊 CORRECTED MATHEMATICAL VALIDATION SUMMARY")
    println("="^80)
    
    # Expected theoretical orders of accuracy (should now be achieved!)
    expected_orders = Dict(
        "gradient" => 2.0,      # 2nd order with proper boundaries
        "laplacian" => 2.0,     # 2nd order with ghost points
        "poisson" => 2.0,       # 2nd order discretization
        "divergence" => 2.0,    # 2nd order finite volume
    )
    
    # Improved accuracy thresholds
    accuracy_thresholds = Dict(
        "gradient" => 1e-10,    # Should be much more accurate now
        "laplacian" => 1e-6,    # Should converge properly
        "poisson" => 1e-6,      # Should be 2nd order
        "divergence" => 1e-6,   # Should be 2nd order
    )
    
    all_passed = true
    
    for (test_name, expected_order) in expected_orders
        if haskey(test_results, test_name)
            println("\n$test_name:")
            
            # Get finest mesh results
            finest_mesh = maximum([parse(Int, k[2:end]) for k in keys(test_results[test_name])])
            finest_result = test_results[test_name]["n$finest_mesh"]
            finest_error = finest_result["rms_error"]
            
            # Check accuracy
            threshold = accuracy_thresholds[test_name]
            accuracy_ok = finest_error < threshold
            accuracy_status = accuracy_ok ? "✅ PASS" : "❌ FAIL"
            
            @printf "  Accuracy: %s (error: %.2e, threshold: %.2e)\n" accuracy_status finest_error threshold
            
            # Compute average convergence order
            mesh_sizes = sort([parse(Int, k[2:end]) for k in keys(test_results[test_name])])
            orders = Float64[]
            
            for i in 2:length(mesh_sizes)
                n_coarse = mesh_sizes[i-1]
                n_fine = mesh_sizes[i]
                
                h_coarse = test_results[test_name]["n$n_coarse"]["h"]
                h_fine = test_results[test_name]["n$n_fine"]["h"]
                error_coarse = test_results[test_name]["n$n_coarse"]["rms_error"]
                error_fine = test_results[test_name]["n$n_fine"]["rms_error"]
                
                if error_coarse > 0 && error_fine > 0
                    order = log(error_coarse / error_fine) / log(h_coarse / h_fine)
                    push!(orders, order)
                end
            end
            
            if !isempty(orders)
                avg_order = sum(orders) / length(orders)
                order_ok = avg_order > expected_order * 0.9  # Allow 10% tolerance
                order_status = order_ok ? "✅ PASS" : "❌ FAIL"
                
                @printf "  Convergence: %s (avg order: %.2f, expected: %.1f)\n" order_status avg_order expected_order
                
                if !accuracy_ok || !order_ok
                    all_passed = false
                end
            else
                println("  Convergence: ❌ FAIL (could not compute order)")
                all_passed = false
            end
        else
            println("\n$test_name: ❌ MISSING")
            all_passed = false
        end
    end
    
    println("\n" * "="^80)
    if all_passed
        println("🎉 ALL CORRECTED MATHEMATICAL OPERATORS VALIDATED!")
        println("   ✓ Gradient operator: 2nd order accurate")
        println("   ✓ Laplacian operator: 2nd order accurate")
        println("   ✓ Divergence operator: 2nd order accurate")
        println("   ✓ Poisson solver: 2nd order accurate")
        println("\n   🔥 JuliaFOAM core math is NOW CORRECT!")
        println("   Ready for production CFD computations!")
    else
        println("❌ SOME OPERATORS STILL NEED WORK!")
        println("   Check boundary treatment implementation.")
    end
    
    return all_passed
end

# Run all CORRECTED validation tests
function run_all_corrected_tests()
    test_corrected_gradient()
    test_corrected_laplacian()
    test_corrected_poisson()
    test_corrected_divergence()
    
    return generate_corrected_validation_report()
end

# Execute if run directly
if abspath(PROGRAM_FILE) == @__FILE__
    success = run_all_corrected_tests()
    exit(success ? 0 : 1)
end