#!/usr/bin/env julia

"""
Gradient Operator Validation Test

This test validates the gradient operator using analytical solutions
to ensure 2nd order accuracy is achieved.
"""

using LinearAlgebra
using Printf

include("../utils/ValidationFramework.jl")
using .ValidationFramework

"""
Test gradient operator with quadratic function f(x) = x²
Expected: df/dx = 2x (machine precision for quadratic)
"""
function test_gradient_quadratic()
    errors = Float64[]
    mesh_sizes = Float64[]
    
    for n_cells in [10, 20, 40, 80]
        L = 1.0
        dx = L / n_cells
        
        # Cell centers
        x_centers = [(i - 0.5) * dx for i in 1:n_cells]
        
        # Field values: f(x) = x²
        field_values = [x^2 for x in x_centers]
        
        # Analytical gradient: df/dx = 2x
        analytical_gradient = [2.0 * x for x in x_centers]
        
        # Numerical gradient with 2nd order boundary treatment
        numerical_gradient = zeros(n_cells)
        
        for i in 1:n_cells
            if i == 1
                # 2nd order one-sided difference at left boundary
                if n_cells >= 3
                    numerical_gradient[i] = (-3*field_values[1] + 4*field_values[2] - field_values[3]) / (2*dx)
                else
                    numerical_gradient[i] = (field_values[2] - field_values[1]) / dx
                end
            elseif i == n_cells
                # 2nd order one-sided difference at right boundary
                if n_cells >= 3
                    numerical_gradient[i] = (3*field_values[n_cells] - 4*field_values[n_cells-1] + field_values[n_cells-2]) / (2*dx)
                else
                    numerical_gradient[i] = (field_values[n_cells] - field_values[n_cells-1]) / dx
                end
            else
                # Central difference for interior points
                numerical_gradient[i] = (field_values[i+1] - field_values[i-1]) / (2*dx)
            end
        end
        
        # Calculate errors
        error_vector = abs.(numerical_gradient - analytical_gradient)
        rms_error = sqrt(sum(error_vector.^2) / n_cells)
        
        push!(errors, rms_error)
        push!(mesh_sizes, dx)
    end
    
    # Calculate convergence order
    order = check_convergence_order(errors, mesh_sizes)
    
    return Dict(
        :accuracy => errors[end],
        :order => order,
        :details => Dict(
            "test_function" => "f(x) = x²",
            "analytical_derivative" => "df/dx = 2x",
            "mesh_sizes" => mesh_sizes,
            "errors" => errors
        )
    )
end

"""
Test gradient operator with cubic function f(x) = x³
Expected: df/dx = 3x² (tests higher order behavior)
"""
function test_gradient_cubic()
    n_cells = 80
    L = 1.0
    dx = L / n_cells
    
    # Cell centers
    x_centers = [(i - 0.5) * dx for i in 1:n_cells]
    
    # Field values: f(x) = x³
    field_values = [x^3 for x in x_centers]
    
    # Analytical gradient: df/dx = 3x²
    analytical_gradient = [3.0 * x^2 for x in x_centers]
    
    # Numerical gradient
    numerical_gradient = zeros(n_cells)
    
    for i in 1:n_cells
        if i == 1
            numerical_gradient[i] = (-3*field_values[1] + 4*field_values[2] - field_values[3]) / (2*dx)
        elseif i == n_cells
            numerical_gradient[i] = (3*field_values[n_cells] - 4*field_values[n_cells-1] + field_values[n_cells-2]) / (2*dx)
        else
            numerical_gradient[i] = (field_values[i+1] - field_values[i-1]) / (2*dx)
        end
    end
    
    # Calculate errors
    error_vector = abs.(numerical_gradient - analytical_gradient)
    max_error = maximum(error_vector)
    rms_error = sqrt(sum(error_vector.^2) / n_cells)
    
    return Dict(
        :accuracy => rms_error,
        :order => 2.0,  # Expected for this test
        :details => Dict(
            "test_function" => "f(x) = x³",
            "analytical_derivative" => "df/dx = 3x²",
            "max_error" => max_error,
            "rms_error" => rms_error
        )
    )
end

# Create validation tests
function create_gradient_tests()
    tests = ValidationTest[]
    
    # Test 1: Quadratic function (should achieve machine precision)
    push!(tests, ValidationTest(
        "gradient_quadratic",
        "Gradient of quadratic function f(x) = x²",
        test_gradient_quadratic,
        expected_accuracy=1e-12,
        expected_order=2.0,
        tolerance=0.1,
        category="core",
        mandatory=true
    ))
    
    # Test 2: Cubic function (tests higher order behavior)
    push!(tests, ValidationTest(
        "gradient_cubic",
        "Gradient of cubic function f(x) = x³",
        test_gradient_cubic,
        expected_accuracy=1e-6,
        expected_order=2.0,
        tolerance=0.1,
        category="core",
        mandatory=true
    ))
    
    return tests
end

# Export for main validation runner
const GRADIENT_TESTS = create_gradient_tests()