#!/usr/bin/env julia

"""
Laplacian Operator Validation Test

This test validates the Laplacian operator using analytical solutions
with proper ghost point boundary treatment for 2nd order accuracy.
"""

using LinearAlgebra
using Printf

include("../utils/ValidationFramework.jl")
using .ValidationFramework

"""
Test Laplacian operator with sinusoidal function f(x) = sin(πx)
Expected: d²f/dx² = -π²sin(πx)
Boundary conditions: f(0) = f(L) = 0 (homogeneous Dirichlet)
"""
function test_laplacian_sinusoidal()
    errors = Float64[]
    mesh_sizes = Float64[]
    
    for n_cells in [10, 20, 40, 80]
        L = 1.0
        dx = L / n_cells
        
        # Cell centers
        x_centers = [(i - 0.5) * dx for i in 1:n_cells]
        
        # Field values: f(x) = sin(πx)
        field_values = [sin(π * x) for x in x_centers]
        
        # Analytical second derivative: d²f/dx² = -π²sin(πx)
        analytical_laplacian = [-π^2 * sin(π * x) for x in x_centers]
        
        # Numerical Laplacian with ghost point method
        numerical_laplacian = zeros(n_cells)
        
        for i in 1:n_cells
            if i == 1
                # Left boundary: ghost point method
                # From BC f(0) = 0: f_ghost = -f_center
                f_ghost = -field_values[1]
                f_center = field_values[1]
                f_right = field_values[2]
                numerical_laplacian[i] = (f_right - 2*f_center + f_ghost) / dx^2
                
            elseif i == n_cells
                # Right boundary: ghost point method
                # From BC f(L) = 0: f_ghost = -f_center
                f_left = field_values[n_cells-1]
                f_center = field_values[n_cells]
                f_ghost = -field_values[n_cells]
                numerical_laplacian[i] = (f_ghost - 2*f_center + f_left) / dx^2
                
            else
                # Interior: standard central difference
                f_center = field_values[i]
                f_left = field_values[i-1]
                f_right = field_values[i+1]
                numerical_laplacian[i] = (f_right - 2*f_center + f_left) / dx^2
            end
        end
        
        # Calculate errors
        error_vector = abs.(numerical_laplacian - analytical_laplacian)
        rms_error = sqrt(sum(error_vector.^2) / n_cells)
        
        push!(errors, rms_error)
        push!(mesh_sizes, dx)
    end
    
    # Calculate convergence order
    order = check_convergence_order(errors, mesh_sizes)
    
    return Dict(
        :accuracy => errors[end],
        :order => order,
        :details => Dict(
            "test_function" => "f(x) = sin(πx)",
            "analytical_laplacian" => "d²f/dx² = -π²sin(πx)",
            "boundary_conditions" => "f(0) = f(L) = 0",
            "method" => "Ghost point method",
            "mesh_sizes" => mesh_sizes,
            "errors" => errors
        )
    )
end

"""
Test Laplacian operator with quadratic function f(x) = x(1-x)
Expected: d²f/dx² = -2 (constant)
Boundary conditions: f(0) = f(1) = 0
"""
function test_laplacian_quadratic()
    n_cells = 40
    L = 1.0
    dx = L / n_cells
    
    # Cell centers
    x_centers = [(i - 0.5) * dx for i in 1:n_cells]
    
    # Field values: f(x) = x(1-x) = x - x²
    field_values = [x * (1 - x) for x in x_centers]
    
    # Analytical second derivative: d²f/dx² = -2
    analytical_laplacian = fill(-2.0, n_cells)
    
    # Numerical Laplacian
    numerical_laplacian = zeros(n_cells)
    
    for i in 1:n_cells
        if i == 1
            # Ghost point: f_ghost = -f_center (from BC f(0) = 0)
            f_ghost = -field_values[1]
            f_center = field_values[1]
            f_right = field_values[2]
            numerical_laplacian[i] = (f_right - 2*f_center + f_ghost) / dx^2
            
        elseif i == n_cells
            # Ghost point: f_ghost = -f_center (from BC f(L) = 0)
            f_left = field_values[n_cells-1]
            f_center = field_values[n_cells]
            f_ghost = -field_values[n_cells]
            numerical_laplacian[i] = (f_ghost - 2*f_center + f_left) / dx^2
            
        else
            # Interior points
            f_center = field_values[i]
            f_left = field_values[i-1]
            f_right = field_values[i+1]
            numerical_laplacian[i] = (f_right - 2*f_center + f_left) / dx^2
        end
    end
    
    # Calculate errors
    error_vector = abs.(numerical_laplacian - analytical_laplacian)
    max_error = maximum(error_vector)
    rms_error = sqrt(sum(error_vector.^2) / n_cells)
    
    return Dict(
        :accuracy => rms_error,
        :order => 2.0,  # Expected for this test
        :details => Dict(
            "test_function" => "f(x) = x(1-x)",
            "analytical_laplacian" => "d²f/dx² = -2",
            "max_error" => max_error,
            "rms_error" => rms_error
        )
    )
end

# Create validation tests
function create_laplacian_tests()
    tests = ValidationTest[]
    
    # Test 1: Sinusoidal function (convergence study)
    push!(tests, ValidationTest(
        "laplacian_sinusoidal",
        "Laplacian of sinusoidal function f(x) = sin(πx)",
        test_laplacian_sinusoidal,
        expected_accuracy=1e-3,
        expected_order=2.0,
        tolerance=0.1,
        category="core",
        mandatory=true
    ))
    
    # Test 2: Quadratic function (should be exact for constant result)
    push!(tests, ValidationTest(
        "laplacian_quadratic",
        "Laplacian of quadratic function f(x) = x(1-x)",
        test_laplacian_quadratic,
        expected_accuracy=1e-10,
        expected_order=2.0,
        tolerance=0.1,
        category="core",
        mandatory=true
    ))
    
    return tests
end

# Export for main validation runner
const LAPLACIAN_TESTS = create_laplacian_tests()