#!/usr/bin/env julia

"""
Divergence Operator Validation Test

This test validates the divergence operator using analytical solutions
with proper finite volume face interpolation.
"""

using LinearAlgebra
using Printf

include("../utils/ValidationFramework.jl")
using .ValidationFramework

"""
Test divergence operator with quadratic velocity field v(x) = [x², 0, 0]
Expected: ∇·v = dv_x/dx = 2x
"""
function test_divergence_quadratic()
    errors = Float64[]
    mesh_sizes = Float64[]
    
    for n_cells in [10, 20, 40, 80]
        L = 1.0
        dx = L / n_cells
        
        # Cell centers
        x_centers = [(i - 0.5) * dx for i in 1:n_cells]
        
        # Vector field: v(x) = [x², 0, 0]
        velocity_x = [x^2 for x in x_centers]
        
        # Analytical divergence: div(v) = dv_x/dx = 2x
        analytical_divergence = [2.0 * x for x in x_centers]
        
        # Numerical divergence using finite volume
        numerical_divergence = zeros(n_cells)
        
        for i in 1:n_cells
            # Face locations
            x_left_face = (i - 1) * dx
            x_right_face = i * dx
            
            # Face velocity computation with 2nd order interpolation
            if i == 1
                # Left boundary: v(0) = 0²
                v_left_face = 0.0
                # Right face: quadratic interpolation using boundary + two cells
                x1, x2, x3 = 0.0, x_centers[1], x_centers[2]
                v1, v2, v3 = 0.0, velocity_x[1], velocity_x[2]
                
                # Lagrange interpolation to right face
                x = x_right_face
                v_right_face = v1 * (x - x2)*(x - x3)/((x1 - x2)*(x1 - x3)) +
                              v2 * (x - x1)*(x - x3)/((x2 - x1)*(x2 - x3)) +
                              v3 * (x - x1)*(x - x2)/((x3 - x1)*(x3 - x2))
                              
            elseif i == n_cells
                # Left face: quadratic interpolation
                x1, x2, x3 = x_centers[n_cells-1], x_centers[n_cells], L
                v1, v2, v3 = velocity_x[n_cells-1], velocity_x[n_cells], L^2
                
                x = x_left_face
                v_left_face = v1 * (x - x2)*(x - x3)/((x1 - x2)*(x1 - x3)) +
                             v2 * (x - x1)*(x - x3)/((x2 - x1)*(x2 - x3)) +
                             v3 * (x - x1)*(x - x2)/((x3 - x1)*(x3 - x2))
                             
                # Right boundary: v(L) = L²
                v_right_face = L^2
                
            else
                # Internal cell: linear interpolation at faces
                # Left face
                weight = 0.5
                v_left_face = weight * velocity_x[i-1] + (1-weight) * velocity_x[i]
                # Right face
                v_right_face = weight * velocity_x[i] + (1-weight) * velocity_x[i+1]
            end
            
            # Finite volume divergence
            numerical_divergence[i] = (v_right_face - v_left_face) / dx
        end
        
        # Calculate errors
        error_vector = abs.(numerical_divergence - analytical_divergence)
        rms_error = sqrt(sum(error_vector.^2) / n_cells)
        
        push!(errors, rms_error)
        push!(mesh_sizes, dx)
    end
    
    # Calculate convergence order
    order = check_convergence_order(errors, mesh_sizes)
    
    return Dict(
        :accuracy => errors[end],
        :order => order,
        :details => Dict(
            "test_function" => "v(x) = [x², 0, 0]",
            "analytical_divergence" => "∇·v = 2x",
            "method" => "Finite volume with face interpolation",
            "mesh_sizes" => mesh_sizes,
            "errors" => errors
        )
    )
end

"""
Test divergence operator with linear velocity field v(x) = [x, 0, 0]
Expected: ∇·v = dv_x/dx = 1 (constant)
This should be exact to machine precision for linear interpolation.
"""
function test_divergence_linear()
    n_cells = 40
    L = 1.0
    dx = L / n_cells
    
    # Cell centers
    x_centers = [(i - 0.5) * dx for i in 1:n_cells]
    
    # Vector field: v(x) = [x, 0, 0]
    velocity_x = [x for x in x_centers]
    
    # Analytical divergence: div(v) = dv_x/dx = 1
    analytical_divergence = fill(1.0, n_cells)
    
    # Numerical divergence
    numerical_divergence = zeros(n_cells)
    
    for i in 1:n_cells
        # Face locations
        x_left_face = (i - 1) * dx
        x_right_face = i * dx
        
        # For linear function, simple linear interpolation should be exact
        if i == 1
            v_left_face = 0.0  # v(0) = 0
            v_right_face = x_right_face  # v(x) = x
        elseif i == n_cells
            v_left_face = x_left_face
            v_right_face = L  # v(L) = L
        else
            # Linear interpolation at faces
            v_left_face = x_left_face
            v_right_face = x_right_face
        end
        
        # Finite volume divergence
        numerical_divergence[i] = (v_right_face - v_left_face) / dx
    end
    
    # Calculate errors
    error_vector = abs.(numerical_divergence - analytical_divergence)
    max_error = maximum(error_vector)
    rms_error = sqrt(sum(error_vector.^2) / n_cells)
    
    return Dict(
        :accuracy => rms_error,
        :order => 2.0,  # Should be machine precision
        :details => Dict(
            "test_function" => "v(x) = [x, 0, 0]",
            "analytical_divergence" => "∇·v = 1",
            "max_error" => max_error,
            "rms_error" => rms_error,
            "note" => "Should be exact to machine precision"
        )
    )
end

# Create validation tests
function create_divergence_tests()
    tests = ValidationTest[]
    
    # Test 1: Quadratic velocity field (convergence study)
    push!(tests, ValidationTest(
        "divergence_quadratic",
        "Divergence of quadratic velocity field v(x) = [x², 0, 0]",
        test_divergence_quadratic,
        expected_accuracy=1e-6,
        expected_order=2.0,
        tolerance=0.1,
        category="core",
        mandatory=true
    ))
    
    # Test 2: Linear velocity field (should be exact)
    push!(tests, ValidationTest(
        "divergence_linear",
        "Divergence of linear velocity field v(x) = [x, 0, 0]",
        test_divergence_linear,
        expected_accuracy=1e-12,
        expected_order=2.0,
        tolerance=0.1,
        category="core",
        mandatory=true
    ))
    
    return tests
end

# Export for main validation runner
const DIVERGENCE_TESTS = create_divergence_tests()