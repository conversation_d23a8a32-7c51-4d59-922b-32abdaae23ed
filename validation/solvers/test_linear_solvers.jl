#!/usr/bin/env julia

"""
Test Linear Solver Implementations

Tests the linear solver fixes to ensure they work correctly.
"""

using LinearAlgebra
using SparseArrays
using Printf

println("🔢 Testing Linear Solver Implementations")
println("="^80)

# Test preconditioner application
function test_preconditioner_application()
    println("\n🔧 Testing Preconditioner Application")
    println("-"^50)
    
    try
        n = 5
        
        # Test vector
        r = rand(n)
        z = zeros(n)
        
        # Test different preconditioner types
        println("Testing different preconditioner types:")
        
        # Test 1: No preconditioner (identity)
        function apply_preconditioner_simple!(z, r, precond)
            if precond === nothing || precond === I
                z .= r
            elseif isa(precond, Diagonal)
                z .= precond \ r
            elseif isa(precond, AbstractMatrix)
                z .= precond \ r
            else
                @warn "Unknown preconditioner type"
                z .= r
            end
        end
        
        apply_preconditioner_simple!(z, r, nothing)
        @printf "✓ Identity preconditioner: norm(z-r) = %.2e\n" norm(z - r)
        
        # Test 2: Diagonal preconditioner
        D = Diagonal(1.0 ./ (1:n))
        apply_preconditioner_simple!(z, r, D)
        @printf "✓ Diagonal preconditioner applied\n"
        
        # Test 3: Matrix preconditioner
        A = spdiagm(0 => 2*ones(n), 1 => -ones(n-1), -1 => -ones(n-1))
        P = Diagonal(1.0 ./ diag(A))
        apply_preconditioner_simple!(z, r, P)
        @printf "✓ Matrix preconditioner applied\n"
        
        println("✅ PASS: Preconditioner application works")
        return true
        
    catch e
        println("❌ FAIL: Error in preconditioner test: $e")
        return false
    end
end

# Test DILU preconditioner construction
function test_dilu_preconditioner()
    println("\n🔧 Testing DILU Preconditioner Construction")
    println("-"^50)
    
    try
        # Create test matrix (simple tridiagonal)
        n = 10
        A = spdiagm(-1 => -ones(n-1), 0 => 4*ones(n), 1 => -ones(n-1))
        
        function build_dilu_simple(A)
            n = size(A, 1)
            dilu_diag = zeros(n)
            
            # For each row, sum absolute values of all elements
            for i in 1:n
                row_sum = 0.0
                for j in 1:n
                    if A[i, j] != 0.0
                        row_sum += abs(A[i, j])
                    end
                end
                dilu_diag[i] = max(row_sum, 1e-15)
            end
            
            return Diagonal(1.0 ./ dilu_diag)
        end
        
        P_dilu = build_dilu_simple(A)
        
        @printf "✓ DILU preconditioner built: %dx%d\n" size(P_dilu)...
        @printf "  Condition number estimate: %.2e\n" cond(Matrix(P_dilu))
        
        # Test application
        x = ones(n)
        y = P_dilu * x
        
        if all(isfinite.(y)) && norm(y) > 0
            println("✅ PASS: DILU preconditioner works")
            return true
        else
            println("❌ FAIL: DILU preconditioner produces invalid results")
            return false
        end
        
    catch e
        println("❌ FAIL: Error in DILU test: $e")
        return false
    end
end

# Test DIC preconditioner construction
function test_dic_preconditioner()
    println("\n🔧 Testing DIC Preconditioner Construction")
    println("-"^50)
    
    try
        # Create symmetric test matrix
        n = 8
        A = spdiagm(-1 => -ones(n-1), 0 => 3*ones(n), 1 => -ones(n-1))
        
        function build_dic_simple(A)
            n = size(A, 1)
            dic_diag = zeros(n)
            
            for i in 1:n
                # Start with diagonal element
                aii = A[i, i]
                sum_off_diag = 0.0
                
                # Sum absolute values of off-diagonal elements in row i
                for j in 1:n
                    if i != j && A[i, j] != 0.0
                        sum_off_diag += abs(A[i, j])
                    end
                end
                
                # DIC diagonal
                dic_diag[i] = max(aii + sum_off_diag, abs(aii), 1.0)
            end
            
            return Diagonal(1.0 ./ dic_diag)
        end
        
        P_dic = build_dic_simple(A)
        
        @printf "✓ DIC preconditioner built: %dx%d\n" size(P_dic)...
        @printf "  Diagonal range: [%.2e, %.2e]\n" minimum(diag(P_dic)) maximum(diag(P_dic))
        
        # Test application
        x = ones(n)
        y = P_dic * x
        
        if all(isfinite.(y)) && all(y .> 0)
            println("✅ PASS: DIC preconditioner works")
            return true
        else
            println("❌ FAIL: DIC preconditioner produces invalid results")
            return false
        end
        
    catch e
        println("❌ FAIL: Error in DIC test: $e")
        return false
    end
end

# Test simple iterative solver
function test_simple_cg_solver()
    println("\n🔧 Testing Simple CG Solver")
    println("-"^50)
    
    try
        # Create SPD test system
        n = 20
        A = spdiagm(-1 => -ones(n-1), 0 => 3*ones(n), 1 => -ones(n-1))
        x_exact = ones(n)
        b = A * x_exact
        
        function simple_cg_solve(A, b, x0, tol=1e-6, max_iter=100)
            x = copy(x0)
            r = b - A * x
            p = copy(r)
            rsold = dot(r, r)
            
            for iter in 1:max_iter
                Ap = A * p
                alpha = rsold / dot(p, Ap)
                
                x += alpha * p
                r -= alpha * Ap
                
                rsnew = dot(r, r)
                
                if sqrt(rsnew) < tol
                    return x, iter, sqrt(rsnew)
                end
                
                beta = rsnew / rsold
                p = r + beta * p
                rsold = rsnew
            end
            
            return x, max_iter, sqrt(rsold)
        end
        
        x0 = zeros(n)
        x_sol, iterations, residual = simple_cg_solve(A, b, x0)
        
        error = norm(x_sol - x_exact)
        
        @printf "✓ CG solver completed in %d iterations\n" iterations
        @printf "  Final residual: %.2e\n" residual
        @printf "  Solution error: %.2e\n" error
        
        if error < 1e-6 && residual < 1e-6
            println("✅ PASS: CG solver works correctly")
            return true
        else
            println("❌ FAIL: CG solver has poor convergence")
            return false
        end
        
    catch e
        println("❌ FAIL: Error in CG solver test: $e")
        return false
    end
end

# Test preconditioned solver
function test_preconditioned_cg()
    println("\n🔧 Testing Preconditioned CG Solver")
    println("-"^50)
    
    try
        # Create test system
        n = 15
        A = spdiagm(-2 => ones(n-2), -1 => -2*ones(n-1), 0 => 5*ones(n), 1 => -2*ones(n-1), 2 => ones(n-2))
        x_exact = rand(n)
        b = A * x_exact
        
        # Diagonal preconditioner
        P = Diagonal(1.0 ./ diag(A))
        
        function preconditioned_cg_solve(A, b, x0, P, tol=1e-8, max_iter=100)
            x = copy(x0)
            r = b - A * x
            z = P * r
            p = copy(z)
            rzold = dot(r, z)
            
            for iter in 1:max_iter
                Ap = A * p
                alpha = rzold / dot(p, Ap)
                
                x += alpha * p
                r -= alpha * Ap
                
                z = P * r
                rznew = dot(r, z)
                
                if sqrt(dot(r, r)) < tol
                    return x, iter, sqrt(dot(r, r))
                end
                
                beta = rznew / rzold
                p = z + beta * p
                rzold = rznew
            end
            
            return x, max_iter, sqrt(dot(r, r))
        end
        
        x0 = zeros(n)
        x_sol, iterations, residual = preconditioned_cg_solve(A, b, x0, P)
        
        error = norm(x_sol - x_exact)
        
        @printf "✓ Preconditioned CG completed in %d iterations\n" iterations
        @printf "  Final residual: %.2e\n" residual
        @printf "  Solution error: %.2e\n" error
        
        if error < 1e-6 && residual < 1e-6
            println("✅ PASS: Preconditioned CG works correctly")
            return true
        else
            println("❌ FAIL: Preconditioned CG has poor convergence")
            return false
        end
        
    catch e
        println("❌ FAIL: Error in preconditioned CG test: $e")
        return false
    end
end

# Test solver robustness with ill-conditioned matrix
function test_solver_robustness()
    println("\n🔧 Testing Solver Robustness")
    println("-"^50)
    
    try
        # Create ill-conditioned matrix
        n = 10
        A = spdiagm(0 => [1000.0; ones(n-1)])  # Very different diagonal elements
        x_exact = ones(n)
        b = A * x_exact
        
        # Test with different preconditioners
        println("Testing solver robustness:")
        
        # No preconditioning
        function simple_solver(A, b, max_iter=1000)
            n = length(b)
            x = zeros(n)
            for iter in 1:max_iter
                for i in 1:n
                    if A[i, i] != 0
                        x[i] = (b[i] - sum(A[i, j] * x[j] for j in 1:n if j != i)) / A[i, i]
                    end
                end
                
                if iter % 100 == 0
                    residual = norm(A * x - b)
                    if residual < 1e-6
                        return x, iter, residual
                    end
                end
            end
            return x, max_iter, norm(A * x - b)
        end
        
        x_sol, iterations, residual = simple_solver(A, b)
        error = norm(x_sol - x_exact)
        
        @printf "✓ Simple iterative solver: %d iterations\n" iterations
        @printf "  Residual: %.2e, Error: %.2e\n" residual error
        
        # With diagonal preconditioning
        P = Diagonal(1.0 ./ abs.(diag(A)))
        A_precond = P * A
        b_precond = P * b
        
        x_sol_p, iterations_p, residual_p = simple_solver(A_precond, b_precond)
        error_p = norm(x_sol_p - x_exact)
        
        @printf "✓ With diagonal preconditioning: %d iterations\n" iterations_p
        @printf "  Residual: %.2e, Error: %.2e\n" residual_p error_p
        
        if iterations_p < iterations || error_p < error
            println("✅ PASS: Preconditioning improves convergence")
            return true
        else
            println("⚠️  WARNING: Preconditioning doesn't improve this case (may be expected)")
            return true
        end
        
    catch e
        println("❌ FAIL: Error in robustness test: $e")
        return false
    end
end

# Run all linear solver tests
function run_linear_solver_tests()
    println("\n🏁 Running Linear Solver Tests")
    println("="^80)
    
    tests = [
        ("Preconditioner Application", test_preconditioner_application),
        ("DILU Preconditioner", test_dilu_preconditioner),
        ("DIC Preconditioner", test_dic_preconditioner),
        ("Simple CG Solver", test_simple_cg_solver),
        ("Preconditioned CG", test_preconditioned_cg),
        ("Solver Robustness", test_solver_robustness)
    ]
    
    passed = 0
    total = length(tests)
    
    for (name, test_func) in tests
        if test_func()
            passed += 1
        end
    end
    
    println("\n" * "="^80)
    println("LINEAR SOLVER TEST SUMMARY")
    println("="^80)
    @printf "Tests passed: %d/%d\n" passed total
    
    if passed == total
        println("🎉 ALL LINEAR SOLVER TESTS PASSED!")
        return true
    else
        println("💥 SOME LINEAR SOLVER TESTS FAILED!")
        return false
    end
end

# Run the tests
if abspath(PROGRAM_FILE) == @__FILE__
    success = run_linear_solver_tests()
    exit(success ? 0 : 1)
end