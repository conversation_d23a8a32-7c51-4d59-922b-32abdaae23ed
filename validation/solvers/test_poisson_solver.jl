#!/usr/bin/env julia

"""
Poisson Solver Validation Test

This test validates the Poisson equation solver using analytical solutions
with proper matrix assembly and boundary treatment.
"""

using LinearAlgebra
using SparseArrays
using Printf

include("../utils/ValidationFramework.jl")
using .ValidationFramework

"""
Test Poisson solver with sinusoidal source: ∇²u = -π²sin(πx)
Analytical solution: u(x) = sin(πx)
Boundary conditions: u(0) = u(L) = 0
"""
function test_poisson_sinusoidal()
    errors = Float64[]
    mesh_sizes = Float64[]
    
    for n_cells in [10, 20, 40, 80]
        L = 1.0
        dx = L / n_cells
        
        # Cell centers
        x_centers = [(i - 0.5) * dx for i in 1:n_cells]
        
        # Analytical solution: u(x) = sin(πx)
        analytical_solution = [sin(π * x) for x in x_centers]
        
        # Source term: f(x) = π²sin(πx) so that ∇²u = -f(x)
        source_term = [π^2 * sin(π * x) for x in x_centers]
        
        # Assemble Poisson matrix with ghost point boundary treatment
        A = spzeros(n_cells, n_cells)
        b = zeros(n_cells)
        
        for i in 1:n_cells
            if i == 1
                # Left boundary with ghost point method
                # Ghost point: u_ghost = -u[i] (from BC u(0) = 0)
                # Stencil: (u[i+1] - 2*u[i] + u_ghost)/dx² = -f[i]
                A[i,i] = -3.0 / dx^2
                A[i,i+1] = 1.0 / dx^2
                b[i] = -source_term[i]
                
            elseif i == n_cells
                # Right boundary with ghost point method
                # Ghost point: u_ghost = -u[i] (from BC u(L) = 0)
                A[i,i-1] = 1.0 / dx^2
                A[i,i] = -3.0 / dx^2
                b[i] = -source_term[i]
                
            else
                # Interior points: standard central difference
                A[i,i-1] = 1.0 / dx^2
                A[i,i] = -2.0 / dx^2
                A[i,i+1] = 1.0 / dx^2
                b[i] = -source_term[i]
            end
        end
        
        # Solve the linear system
        numerical_solution = A \ b
        
        # Calculate errors
        error_vector = abs.(numerical_solution - analytical_solution)
        rms_error = sqrt(sum(error_vector.^2) / n_cells)
        
        push!(errors, rms_error)
        push!(mesh_sizes, dx)
    end
    
    # Calculate convergence order
    order = check_convergence_order(errors, mesh_sizes)
    
    return Dict(
        :accuracy => errors[end],
        :order => order,
        :details => Dict(
            "equation" => "∇²u = -π²sin(πx)",
            "analytical_solution" => "u(x) = sin(πx)",
            "boundary_conditions" => "u(0) = u(L) = 0",
            "method" => "Ghost point method + sparse direct solve",
            "mesh_sizes" => mesh_sizes,
            "errors" => errors
        )
    )
end

"""
Test Poisson solver with quadratic source: ∇²u = -2
Analytical solution: u(x) = x(1-x) = x - x²
Boundary conditions: u(0) = u(1) = 0
"""
function test_poisson_quadratic()
    n_cells = 40
    L = 1.0
    dx = L / n_cells
    
    # Cell centers
    x_centers = [(i - 0.5) * dx for i in 1:n_cells]
    
    # Analytical solution: u(x) = x(1-x)
    analytical_solution = [x * (1 - x) for x in x_centers]
    
    # Source term: f(x) = 2 (constant) so that ∇²u = -f(x) = -2
    source_term = fill(2.0, n_cells)
    
    # Assemble matrix
    A = spzeros(n_cells, n_cells)
    b = zeros(n_cells)
    
    for i in 1:n_cells
        if i == 1
            # Left boundary
            A[i,i] = -3.0 / dx^2
            A[i,i+1] = 1.0 / dx^2
            b[i] = -source_term[i]
        elseif i == n_cells
            # Right boundary
            A[i,i-1] = 1.0 / dx^2
            A[i,i] = -3.0 / dx^2
            b[i] = -source_term[i]
        else
            # Interior
            A[i,i-1] = 1.0 / dx^2
            A[i,i] = -2.0 / dx^2
            A[i,i+1] = 1.0 / dx^2
            b[i] = -source_term[i]
        end
    end
    
    # Solve
    numerical_solution = A \ b
    
    # Calculate errors
    error_vector = abs.(numerical_solution - analytical_solution)
    max_error = maximum(error_vector)
    rms_error = sqrt(sum(error_vector.^2) / n_cells)
    
    return Dict(
        :accuracy => rms_error,
        :order => 2.0,  # Expected for this test
        :details => Dict(
            "equation" => "∇²u = -2",
            "analytical_solution" => "u(x) = x(1-x)",
            "max_error" => max_error,
            "rms_error" => rms_error
        )
    )
end

"""
Test Poisson solver condition number and matrix properties
"""
function test_poisson_matrix_properties()
    n_cells = 50
    L = 1.0
    dx = L / n_cells
    
    # Assemble standard Poisson matrix
    A = spzeros(n_cells, n_cells)
    
    for i in 1:n_cells
        if i == 1
            A[i,i] = -3.0 / dx^2
            A[i,i+1] = 1.0 / dx^2
        elseif i == n_cells
            A[i,i-1] = 1.0 / dx^2
            A[i,i] = -3.0 / dx^2
        else
            A[i,i-1] = 1.0 / dx^2
            A[i,i] = -2.0 / dx^2
            A[i,i+1] = 1.0 / dx^2
        end
    end
    
    # Check matrix properties
    eigenvals = eigvals(Matrix(A))
    condition_number = cond(Matrix(A))
    is_symmetric = issymmetric(A)
    is_positive_definite = all(eigenvals .< 0)  # Should be negative definite
    
    return Dict(
        :accuracy => 1.0 / condition_number,  # Inverse condition number as accuracy metric
        :order => 2.0,
        :details => Dict(
            "condition_number" => condition_number,
            "is_symmetric" => is_symmetric,
            "is_negative_definite" => is_positive_definite,
            "min_eigenvalue" => minimum(eigenvals),
            "max_eigenvalue" => maximum(eigenvals),
            "matrix_size" => n_cells
        )
    )
end

# Create validation tests
function create_poisson_solver_tests()
    tests = ValidationTest[]
    
    # Test 1: Sinusoidal solution (convergence study)
    push!(tests, ValidationTest(
        "poisson_sinusoidal",
        "Poisson solver with sinusoidal solution",
        test_poisson_sinusoidal,
        expected_accuracy=1e-4,
        expected_order=2.0,
        tolerance=0.1,
        category="solvers",
        mandatory=true
    ))
    
    # Test 2: Quadratic solution
    push!(tests, ValidationTest(
        "poisson_quadratic",
        "Poisson solver with quadratic solution",
        test_poisson_quadratic,
        expected_accuracy=1e-6,
        expected_order=2.0,
        tolerance=0.1,
        category="solvers",
        mandatory=true
    ))
    
    # Test 3: Matrix properties
    push!(tests, ValidationTest(
        "poisson_matrix_properties",
        "Poisson matrix condition and properties",
        test_poisson_matrix_properties,
        expected_accuracy=1e-10,
        expected_order=2.0,
        tolerance=0.1,
        category="solvers",
        mandatory=false
    ))
    
    return tests
end

# Export for main validation runner
const POISSON_SOLVER_TESTS = create_poisson_solver_tests()