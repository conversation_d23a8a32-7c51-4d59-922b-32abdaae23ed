# JuliaFOAM Validation Configuration

[general]
# Global validation settings
timeout_seconds = 300
parallel_execution = false
generate_plots = true
verbose_output = true

[thresholds]
# Accuracy thresholds for different test categories
core_operators_accuracy = 1e-12    # Core mathematical operators should be very accurate
solver_accuracy = 1e-6            # Solvers have some discretization error
convergence_order_tolerance = 0.1  # Allow 10% deviation from expected order

[core_operators]
# Settings specific to core mathematical operators
expected_order = 2.0
test_mesh_sizes = [10, 20, 40, 80]
boundary_treatment = "ghost_point"

[core_operators.gradient]
# Gradient operator specific settings
expected_accuracy = 1e-12  # Should achieve machine precision for quadratic
test_functions = ["quadratic", "cubic"]

[core_operators.laplacian] 
# Laplacian operator specific settings
expected_accuracy = 1e-3   # Limited by discretization
test_functions = ["sinusoidal", "quadratic"]
boundary_conditions = "homogeneous_dirichlet"

[core_operators.divergence]
# Divergence operator specific settings  
expected_accuracy = 1e-6   # Finite volume discretization
test_functions = ["quadratic", "linear"]
interpolation_method = "lagrange"

[solvers]
# Settings for solver validation
max_iterations = 1000
convergence_tolerance = 1e-12

[solvers.poisson]
# Poisson solver specific settings
expected_accuracy = 1e-4
matrix_solver = "direct"
test_cases = ["sinusoidal", "quadratic"]
boundary_conditions = "dirichlet"

[reporting]
# Report generation settings
generate_convergence_plots = true
generate_error_tables = true
include_matrix_analysis = true
output_formats = ["txt", "markdown", "json"]

[reporting.plots]
# Plot-specific settings (if plotting available)
figure_format = "png"
dpi = 150
show_theoretical_lines = true

[ci]
# Continuous integration settings
fail_on_warnings = false
generate_benchmark_comparison = true
upload_results = false

[reference]
# Reference result settings
update_reference_on_improvement = false
reference_tolerance_factor = 1.1  # Allow 10% degradation from reference

# Historical reference values (will be updated by successful runs)
[reference.gradient_quadratic]
last_accuracy = 1e-15
last_order = 2.0
last_updated = "2025-01-01"

[reference.laplacian_sinusoidal]
last_accuracy = 8.97e-04  
last_order = 2.00
last_updated = "2025-01-01"

[reference.poisson_sinusoidal]
last_accuracy = 9.09e-05
last_order = 2.00
last_updated = "2025-01-01"