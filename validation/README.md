# 🔬 JuliaFOAM Validation Framework

## Overview

This validation framework ensures mathematical correctness and maintains code quality for JuliaFOAM. **All developers must pass these validation tests** before merging code changes.

## Directory Structure

```
validation/
├── README.md                   # This file
├── run_validation.jl          # Framework-based validation runner
├── run_all_validation.jl      # Master validation runner (ALL TESTS)
├── quick_validation_test.jl   # Quick mathematical validation
├── validation_config.toml     # Configuration settings
├── core/                      # Core mathematical operator tests
│   ├── test_gradient.jl       # Gradient operator validation
│   ├── test_laplacian.jl      # Laplacian operator validation
│   ├── test_divergence.jl     # Divergence operator validation
│   ├── test_1d_*.jl          # 1D analytical validation tests
│   └── test_interpolation.jl  # Face interpolation validation
├── solvers/                   # Solver validation tests
│   ├── test_linear_solvers.jl # Linear solver accuracy
│   ├── test_poisson_solver.jl # Poisson equation solver
│   └── test_simple_solver.jl  # SIMPLE algorithm validation
├── fixes/                     # Bug fix verification tests
│   ├── test_fixes_verification.jl # All fix verification
│   ├── test_all_fixes.jl         # Comprehensive fix tests
│   └── test_*_fixes.jl          # Specific fix categories
├── integration/               # Integration tests
│   ├── test_integration.jl    # Component integration tests
│   └── test_comprehensive.jl  # Full system integration
├── turbulence/               # Turbulence model tests
│   └── test_simple_turbulence.jl # Turbulence validation
├── io/                       # I/O validation tests
│   └── test_openfoam_io.jl   # OpenFOAM format compatibility
├── utils/                     # Validation utilities
│   ├── ValidationFramework.jl # Core validation framework
│   ├── AnalyticalSolutions.jl # Analytical solution library
│   └── ValidationReports.jl   # Report generation
├── reference/                 # Reference results
│   └── core_operators_ref.json # Reference values for core operators
└── results/                   # Validation results (generated)
    ├── latest/                # Latest validation run
    └── history/               # Historical results
```

## Usage

### For Developers

1. **Quick validation (recommended for commits):**
   ```bash
   julia validation/quick_validation_test.jl
   ```

2. **Comprehensive validation (before releases):**
   ```bash
   julia validation/run_all_validation.jl
   ```

3. **Framework-based validation:**
   ```bash
   julia validation/run_validation.jl --core-only
   julia validation/run_validation.jl --solvers-only
   ```

### For CI/CD

```bash
# In your CI pipeline
julia validation/run_validation.jl --ci --no-plots
```

## Validation Levels

### Level 1: Core Mathematical Operators ⚡ **MANDATORY**
- **Gradient operator**: 2nd order convergence
- **Laplacian operator**: 2nd order convergence  
- **Divergence operator**: 2nd order convergence
- **Face interpolation**: Expected order convergence
- **Boundary conditions**: Proper implementation

**Acceptance Criteria:**
- All operators achieve expected convergence order (±10%)
- RMS errors below specified thresholds
- No regression in accuracy

### Level 2: Linear Solvers 🔧 **MANDATORY**
- **Iterative solvers**: CG, BiCGSTAB, GMRES convergence
- **Preconditioners**: Effectiveness validation
- **Matrix assembly**: Accuracy verification
- **Boundary condition application**: Correctness

**Acceptance Criteria:**
- Solvers converge within expected iterations
- Solution accuracy meets thresholds
- Preconditioners improve convergence rates

### Level 3: CFD Solvers 🌊 **REQUIRED**
- **SIMPLE algorithm**: Convergence properties
- **PISO algorithm**: Accuracy validation
- **Pressure-velocity coupling**: Proper implementation
- **Time integration**: Stability and accuracy

**Acceptance Criteria:**
- Algorithms converge for standard test cases
- Solution accuracy within bounds
- Stability properties maintained

### Level 4: Complete Cases 🏁 **RECOMMENDED**
- **Lid-driven cavity**: Benchmark comparison
- **Channel flow**: Analytical solution comparison
- **Complex geometries**: Mesh independence studies

**Acceptance Criteria:**
- Solutions match reference data
- Mesh independence achieved
- Physical correctness verified

## Quick Start for New Developers

1. **Clone and setup:**
   ```bash
   git clone <repository>
   cd JuliaFOAM
   julia --project=. -e "using Pkg; Pkg.instantiate()"
   ```

2. **Run basic validation:**
   ```bash
   julia validation/run_validation.jl --core-only
   ```

3. **Check results:**
   ```bash
   cat validation/results/latest/summary.txt
   ```

4. **Make your changes and re-validate:**
   ```bash
   # Make your code changes...
   julia validation/run_validation.jl --quick
   ```

## Configuration

Edit `validation/validation_config.toml` to adjust:
- Convergence thresholds
- Test case parameters  
- Solver tolerances
- Output settings

## Reporting Issues

If validation tests fail:

1. **Check the detailed report:**
   ```bash
   cat validation/results/latest/detailed_report.md
   ```

2. **Compare with reference:**
   ```bash
   julia validation/utils/compare_with_reference.jl
   ```

3. **File an issue** with the validation report attached

## Contributing New Validation Tests

1. **Follow the template** in `validation/utils/test_template.jl`
2. **Add analytical solutions** to `validation/utils/AnalyticalSolutions.jl`
3. **Update reference results** in `validation/reference/`
4. **Document the test** in this README

## Mathematical Standards

All validation tests must:
- ✅ Use **exact analytical solutions** (no fake results)
- ✅ Perform **convergence studies** with multiple mesh sizes
- ✅ Test **boundary condition implementation**
- ✅ Verify **expected order of accuracy**
- ✅ Include **error analysis** (max, RMS, L∞ norms)

## Validation History

- **v1.0** (2025-01): Initial core operator validation
  - Fixed Laplacian divergence issue
  - Achieved 2nd order convergence for all operators
  - Established baseline reference results

---

**Remember: Code that doesn't pass validation cannot be merged!** 🚫➡️✅