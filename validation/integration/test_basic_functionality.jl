#!/usr/bin/env julia

"""
Test basic JuliaFOAM functionality after cleanup
"""

using Test
using JuliaFOAM
using LinearAlgebra
using SparseArrays

@testset "JuliaFOAM Basic Functionality Tests" begin
    
    @testset "Module Loading" begin
        @test JuliaFOAM isa Module
        println("✓ JuliaFOAM module loads successfully")
    end
    
    @testset "LinearSolvers Module" begin
        @test JuliaFOAM.LinearSolvers isa Module
        println("✓ LinearSolvers module accessible")
        
        # Test SolverSettings struct
        settings = JuliaFOAM.LinearSolvers.SolverSettings()
        @test settings.tolerance > 0
        @test settings.max_iterations > 0
        println("✓ SolverSettings created successfully")
    end
    
    @testset "Basic Matrix Operations" begin
        # Create a simple matrix
        A = sparse([1, 2, 3], [1, 2, 3], [2.0, 2.0, 2.0])
        x = [1.0, 1.0, 1.0]
        y = A * x
        
        @test y ≈ [2.0, 2.0, 2.0]
        println("✓ Basic sparse matrix operations work")
    end
    
    @testset "CacheOptimizedMesh Access" begin
        try
            # Test that CacheOptimizedMesh is accessible
            @test JuliaFOAM.OptimizedMesh.CacheOptimizedMesh isa DataType
            println("✓ CacheOptimizedMesh accessible through OptimizedMesh module")
        catch e
            println("! CacheOptimizedMesh access failed: ", e)
            @test false
        end
    end
    
    @testset "Tools Module" begin
        @test JuliaFOAM.Tools isa Module
        println("✓ Tools module accessible")
    end

end

println("\n🎉 All basic functionality tests completed!")