#!/usr/bin/env julia

"""
Comprehensive test suite for JuliaFOAM after cleanup
"""

using Test
using JuliaFOAM
using LinearAlgebra
using SparseArrays

println("🚀 Running Comprehensive JuliaFOAM Tests")
println("=" ^ 60)

@testset "JuliaFOAM Comprehensive Tests" begin
    
    @testset "Core Module Loading" begin
        @test JuliaFOAM isa Module
        @test JuliaFOAM.LinearSolvers isa Module  
        @test JuliaFOAM.OptimizedMesh isa Module
        @test JuliaFOAM.Tools isa Module
        @test JuliaFOAM.MatrixOperations isa Module
        println("✓ All core modules load successfully")
    end
    
    @testset "LinearSolvers Functionality" begin
        # Create test matrix
        n = 50
        A = spdiagm(0 => 2.0 * ones(n), 1 => -1.0 * ones(n-1), -1 => -1.0 * ones(n-1))
        x_exact = ones(n)
        b = A * x_exact
        
        # Test with identity preconditioner
        precond = JuliaFOAM.LinearSolvers.IdentityPreconditioner()
        settings = JuliaFOAM.LinearSolvers.SolverSettings(tolerance=1e-8, max_iterations=200, robust=true)
        
        # Test CG solver
        x_cg = zeros(n)
        iter_cg, res_cg = JuliaFOAM.LinearSolvers.cg_solve!(A, b, x_cg, precond, settings)
        error_cg = norm(x_cg - x_exact) / norm(x_exact)
        @test error_cg < 1e-6
        @test res_cg < 1e-8
        
        # Test BiCGSTAB solver
        x_bicg = zeros(n)
        iter_bicg, res_bicg = JuliaFOAM.LinearSolvers.bicgstab_solve!(A, b, x_bicg, precond, settings)
        error_bicg = norm(x_bicg - x_exact) / norm(x_exact)
        @test error_bicg < 1e-3
        
        # Test GMRES solver
        x_gmres = zeros(n)
        iter_gmres, res_gmres = JuliaFOAM.LinearSolvers.gmres_solve!(A, b, x_gmres, precond, settings)
        error_gmres = norm(x_gmres - x_exact) / norm(x_exact)
        @test error_gmres < 1e-3
        
        println("✓ LinearSolvers: CG($(iter_cg) iter), BiCGSTAB($(iter_bicg) iter), GMRES($(iter_gmres) iter)")
    end
    
    @testset "Matrix Operations" begin
        # Create test sparse matrix
        n = 100
        A = sprand(n, n, 0.1) + 10I
        x = rand(n)
        y1 = zeros(n)
        y2 = zeros(n)
        
        # Standard multiplication
        y1 = A * x
        
        # Optimized multiplication
        JuliaFOAM.MatrixOperations.optimized_matrix_vector_product!(y2, A, x)
        
        # Check results are close
        @test norm(y1 - y2) < 1e-12
        println("✓ Matrix operations: Standard vs Optimized multiplication consistent")
    end
    
    @testset "Mesh Operations" begin
        # Test CacheOptimizedMesh access
        @test JuliaFOAM.OptimizedMesh.CacheOptimizedMesh isa DataType
        @test JuliaFOAM.OptimizedMesh.Mesh isa DataType
        println("✓ Mesh: CacheOptimizedMesh and Mesh types accessible")
    end
    
    @testset "Tools Module" begin
        # Test MemoryProfiler access
        @test JuliaFOAM.Tools.profile_memory_usage isa Function
        println("✓ Tools: MemoryProfiler functions accessible")
    end
    
    @testset "Preconditioners" begin
        # Test different preconditioner types
        n = 20
        A = spdiagm(0 => 2.0 * ones(n), 1 => -1.0 * ones(n-1), -1 => -1.0 * ones(n-1))
        
        # Identity preconditioner
        identity_precond = JuliaFOAM.LinearSolvers.IdentityPreconditioner()
        @test identity_precond isa JuliaFOAM.LinearSolvers.Preconditioner
        
        # Diagonal preconditioner  
        diag_precond = JuliaFOAM.LinearSolvers.DiagonalPreconditioner(diag(A))
        @test diag_precond isa JuliaFOAM.LinearSolvers.Preconditioner
        
        println("✓ Preconditioners: Identity and Diagonal types work")
    end

end

println("\n🎉 Comprehensive tests completed successfully!")
println("📊 Summary:")
println("   - Core modules loading correctly")
println("   - Linear solvers (CG, BiCGSTAB, GMRES) working")
println("   - Matrix operations optimized")
println("   - Mesh data structures accessible")
println("   - Tools and preconditioners functional")