#!/usr/bin/env julia

"""
JuliaFOAM Validation Runner

Main entry point for running validation tests. This ensures mathematical
correctness and maintains code quality for JuliaFOAM.

Usage:
    julia validation/run_validation.jl [options]

Options:
    --quick         Run only mandatory tests
    --core-only     Run only core mathematical operator tests
    --solvers-only  Run only solver tests
    --full          Run all tests (default)
    --ci            CI mode: no interactive output, JSON reports
    --no-plots      Skip plot generation
    --config FILE   Use custom configuration file
"""

using Pkg
using Dates
using Printf
using LinearAlgebra
using SparseArrays

# Try to use JSON3 and TOML, install if needed
try
    using JSON3, TOML
catch
    println("Installing required packages...")
    Pkg.add(["JSON3", "TOML"])
    using JSON3, TOML
end

# Include validation framework
include("utils/ValidationFramework.jl")
using .ValidationFramework

# Include test modules
include("core/test_gradient.jl")
include("core/test_laplacian.jl")
include("core/test_divergence.jl")
include("solvers/test_poisson_solver.jl")

function parse_commandline()
    # Simple argument parsing without ArgParse dependency
    args = Dict{String, Any}()
    
    # Default values
    args["quick"] = false
    args["core-only"] = false  
    args["solvers-only"] = false
    args["full"] = true
    args["ci"] = false
    args["no-plots"] = true
    args["config"] = "validation_config.toml"
    args["output-dir"] = "results/latest"
    
    # Parse command line arguments
    for arg in ARGS
        if arg == "--quick"
            args["quick"] = true
        elseif arg == "--core-only"
            args["core-only"] = true
            args["full"] = false
        elseif arg == "--solvers-only"
            args["solvers-only"] = true
            args["full"] = false
        elseif arg == "--ci"
            args["ci"] = true
        elseif arg == "--no-plots"
            args["no-plots"] = true
        elseif arg == "--help" || arg == "-h"
            println("""
JuliaFOAM Validation Test Runner

Usage: julia validation/run_validation.jl [options]

Options:
    --quick         Run only mandatory tests
    --core-only     Run only core mathematical operator tests  
    --solvers-only  Run only solver tests
    --ci            CI mode: minimal output
    --no-plots      Skip plot generation
    --help, -h      Show this help
            """)
            exit(0)
        end
    end
    
    return args
end

function create_validation_suite(args)
    # Load configuration
    config_path = joinpath(@__DIR__, args["config"])
    suite = ValidationSuite("JuliaFOAM Core Mathematical Validation", config_path)
    
    # Add core tests
    if !args["solvers-only"]
        for test in GRADIENT_TESTS
            add_test!(suite, test)
        end
        for test in LAPLACIAN_TESTS
            add_test!(suite, test)
        end
        for test in DIVERGENCE_TESTS
            add_test!(suite, test)
        end
    end
    
    # Add solver tests
    if !args["core-only"]
        for test in POISSON_SOLVER_TESTS
            add_test!(suite, test)
        end
    end
    
    return suite
end

function main()
    args = parse_commandline()
    
    println("🔬 JuliaFOAM Validation Test Runner")
    println("="^80)
    println("Started at: $(now())")
    
    if args["ci"]
        println("Running in CI mode...")
    end
    
    # Create validation suite
    suite = create_validation_suite(args)
    
    println("\\nTotal tests to run: $(length(suite.tests))")
    
    # Determine test categories to run
    categories = String[]
    if args["core-only"]
        push!(categories, "core")
    elseif args["solvers-only"]
        push!(categories, "solvers")
    else
        # Run all categories
        categories = []
    end
    
    # Run validation suite
    start_time = time()
    results = run_suite(suite, categories=categories, quick=args["quick"])
    total_time = time() - start_time
    
    # Print summary
    println("\\n" * "="^80)
    println("VALIDATION SUMMARY")
    println("="^80)
    
    n_pass = count(r -> r.status == PASS, results)
    n_fail = count(r -> r.status == FAIL, results)
    n_warn = count(r -> r.status == WARNING, results)
    n_total = length(results)
    
    @printf "Results: %d PASS, %d FAIL, %d WARNING (%.1f seconds)\\n" n_pass n_fail n_warn total_time
    
    # Detailed status
    if n_fail > 0
        println("\\nFAILED TESTS:")
        for result in results
            if result.status == FAIL
                println("  ❌ $(result.test_name): $(result.error_message)")
            end
        end
    end
    
    if n_warn > 0
        println("\\nWARNING TESTS:")
        for result in results
            if result.status == WARNING
                println("  ⚠️  $(result.test_name)")
            end
        end
    end
    
    # Overall status
    overall_success = n_fail == 0
    overall_status = overall_success ? "✅ PASS" : "❌ FAIL"
    println("\\nOVERALL STATUS: $overall_status")
    
    # Generate reports
    output_dir = joinpath(@__DIR__, args["output-dir"])
    mkpath(output_dir)
    
    if !args["ci"]
        println("\\n📊 Generating validation reports...")
    end
    
    try
        generate_report(suite, output_dir)
        
        if !args["ci"]
            println("\\nReports generated in: $output_dir")
            println("  📄 summary.txt - Quick summary")
            println("  📋 detailed_report.md - Detailed results")
            println("  📊 results.json - Machine-readable results")
        end
        
    catch e
        println("Warning: Could not generate all reports: $e")
    end
    
    # CI-specific output
    if args["ci"]
        # Output JSON summary for CI consumption
        ci_summary = Dict(
            "timestamp" => string(now()),
            "total_tests" => n_total,
            "passed" => n_pass,
            "failed" => n_fail,
            "warnings" => n_warn,
            "overall_success" => overall_success,
            "execution_time" => total_time
        )
        
        println("\\nCI_SUMMARY_JSON:")
        println(JSON3.write(ci_summary))
    else
        # Interactive output
        if overall_success
            println("\\n🎉 All validation tests passed!")
            println("   JuliaFOAM mathematical operators are verified correct.")
            println("   Code is ready for production CFD simulations!")
        else
            println("\\n💥 Validation failed!")
            println("   Mathematical operators need fixes before production use.")
            println("   Check the detailed report for debugging information.")
        end
        
        println("\\nFor detailed results, see:")
        println("  cat $(joinpath(output_dir, "summary.txt"))")
    end
    
    # Exit with appropriate code
    exit(overall_success ? 0 : 1)
end

if abspath(PROGRAM_FILE) == @__FILE__
    main()
end