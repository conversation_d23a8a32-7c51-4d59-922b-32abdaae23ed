"""
ValidationFramework.jl - Core framework for JuliaFOAM validation

This module provides the infrastructure for running, tracking, and reporting
validation tests to ensure mathematical correctness and code quality.
"""

module ValidationFramework

using LinearAlgebra
using SparseArrays
using Printf
using Dates
using JSON3
using TOML

export ValidationTest, ValidationSuite, ValidationResult
export run_test, run_suite, generate_report
export check_convergence_order, compare_with_reference
export PASS, FAIL, WARNING

# Test result status
@enum TestStatus PASS FAIL WARNING

"""
    ValidationTest

Represents a single validation test with expected results.
"""
mutable struct ValidationTest
    name::String
    description::String
    test_function::Function
    expected_accuracy::Float64
    expected_order::Float64
    tolerance::Float64
    category::String
    mandatory::Bool
    
    function ValidationTest(name, description, test_func; 
                          expected_accuracy=1e-6, expected_order=2.0, 
                          tolerance=0.1, category="core", mandatory=true)
        new(name, description, test_func, expected_accuracy, expected_order, 
            tolerance, category, mandatory)
    end
end

"""
    ValidationResult

Contains the results of a validation test execution.
"""
struct ValidationResult
    test_name::String
    status::TestStatus
    accuracy_achieved::Float64
    order_achieved::Float64
    execution_time::Float64
    error_message::String
    details::Dict{String, Any}
    timestamp::DateTime
    
    function ValidationResult(test_name, status, accuracy, order, exec_time, 
                            error_msg="", details=Dict{String, Any}())
        new(test_name, status, accuracy, order, exec_time, error_msg, details, now())
    end
end

"""
    ValidationSuite

Collection of validation tests with execution and reporting capabilities.
"""
mutable struct ValidationSuite
    name::String
    tests::Vector{ValidationTest}
    results::Vector{ValidationResult}
    config::Dict{String, Any}
    
    function ValidationSuite(name::String, config_path::String="")
        config = isempty(config_path) ? Dict{String, Any}() : TOML.parsefile(config_path)
        new(name, ValidationTest[], ValidationResult[], config)
    end
end

"""
    add_test!(suite::ValidationSuite, test::ValidationTest)

Add a validation test to the suite.
"""
function add_test!(suite::ValidationSuite, test::ValidationTest)
    push!(suite.tests, test)
end

"""
    run_test(test::ValidationTest) -> ValidationResult

Execute a single validation test and return the result.
"""
function run_test(test::ValidationTest)
    println("🔬 Running $(test.name)...")
    
    start_time = time()
    
    try
        # Execute the test function
        result = test.test_function()
        
        execution_time = time() - start_time
        
        # Extract results
        if haskey(result, :accuracy) && haskey(result, :order)
            accuracy = result[:accuracy]
            order = result[:order]
            details = get(result, :details, Dict{String, Any}())
            
            # Determine status
            accuracy_ok = accuracy <= test.expected_accuracy
            order_ok = abs(order - test.expected_order) <= test.tolerance
            
            status = if accuracy_ok && order_ok
                PASS
            elseif test.mandatory
                FAIL
            else
                WARNING
            end
            
            return ValidationResult(test.name, status, accuracy, order, 
                                  execution_time, "", details)
        else
            return ValidationResult(test.name, FAIL, Inf, NaN, execution_time, 
                                  "Test function must return Dict with :accuracy and :order keys")
        end
        
    catch e
        execution_time = time() - start_time
        error_msg = string(e)
        println("  ❌ ERROR: $error_msg")
        
        return ValidationResult(test.name, FAIL, Inf, NaN, execution_time, error_msg)
    end
end

"""
    run_suite(suite::ValidationSuite; categories=String[], quick=false) -> Vector{ValidationResult}

Run all tests in a validation suite.
"""
function run_suite(suite::ValidationSuite; categories=String[], quick=false)
    println("🚀 Running validation suite: $(suite.name)")
    println("="^80)
    
    # Filter tests by category if specified
    tests_to_run = if isempty(categories)
        suite.tests
    else
        filter(t -> t.category in categories, suite.tests)
    end
    
    # Filter by quick mode if specified
    if quick
        tests_to_run = filter(t -> t.mandatory, tests_to_run)
    end
    
    println("Running $(length(tests_to_run)) tests...")
    
    # Clear previous results
    empty!(suite.results)
    
    # Run tests
    for test in tests_to_run
        result = run_test(test)
        push!(suite.results, result)
        
        # Print immediate feedback
        status_symbol = result.status == PASS ? "✅" : 
                       result.status == WARNING ? "⚠️" : "❌"
        @printf "  %s %s (%.2f s)\\n" status_symbol test.name result.execution_time
        
        if result.status == FAIL && test.mandatory
            println("  💀 MANDATORY TEST FAILED - STOPPING")
            break
        end
    end
    
    return suite.results
end

"""
    check_convergence_order(errors::Vector{Float64}, mesh_sizes::Vector{Float64}) -> Float64

Calculate the convergence order from error data and mesh sizes.
"""
function check_convergence_order(errors::Vector{Float64}, mesh_sizes::Vector{Float64})
    if length(errors) != length(mesh_sizes) || length(errors) < 2
        return NaN
    end
    
    # Calculate convergence order using least squares fit
    log_h = log.(mesh_sizes)
    log_e = log.(errors)
    
    # Remove any infinite or NaN values
    valid_indices = findall(isfinite.(log_h) .&& isfinite.(log_e))
    
    if length(valid_indices) < 2
        return NaN
    end
    
    log_h_valid = log_h[valid_indices]
    log_e_valid = log_e[valid_indices]
    
    # Linear regression: log(e) = order * log(h) + constant
    n = length(log_h_valid)
    sum_x = sum(log_h_valid)
    sum_y = sum(log_e_valid)
    sum_xy = sum(log_h_valid .* log_e_valid)
    sum_x2 = sum(log_h_valid.^2)
    
    # Calculate slope (convergence order)
    order = (n * sum_xy - sum_x * sum_y) / (n * sum_x2 - sum_x^2)
    
    return order
end

"""
    generate_report(suite::ValidationSuite, output_dir::String)

Generate comprehensive validation report.
"""
function generate_report(suite::ValidationSuite, output_dir::String)
    mkpath(output_dir)
    
    # Generate summary
    generate_summary_report(suite, joinpath(output_dir, "summary.txt"))
    
    # Generate detailed report
    generate_detailed_report(suite, joinpath(output_dir, "detailed_report.md"))
    
    # Generate JSON results for programmatic access
    generate_json_report(suite, joinpath(output_dir, "results.json"))
    
    # Generate plots if possible
    try
        generate_plots(suite, output_dir)
    catch e
        println("Warning: Could not generate plots: $e")
    end
    
    println("📊 Reports generated in: $output_dir")
end

"""
    generate_summary_report(suite::ValidationSuite, filename::String)

Generate a concise summary report.
"""
function generate_summary_report(suite::ValidationSuite, filename::String)
    open(filename, "w") do f
        write(f, "JuliaFOAM Validation Summary\\n")
        write(f, "="^50 * "\\n")
        write(f, "Suite: $(suite.name)\\n")
        write(f, "Date: $(now())\\n")
        write(f, "\\n")
        
        # Count results by status
        n_pass = count(r -> r.status == PASS, suite.results)
        n_fail = count(r -> r.status == FAIL, suite.results)
        n_warn = count(r -> r.status == WARNING, suite.results)
        n_total = length(suite.results)
        
        write(f, "RESULTS SUMMARY:\\n")
        write(f, "  Passed:  $n_pass/$n_total\\n")
        write(f, "  Failed:  $n_fail/$n_total\\n")
        write(f, "  Warning: $n_warn/$n_total\\n")
        write(f, "\\n")
        
        # Overall status
        overall_status = n_fail == 0 ? "PASS" : "FAIL"
        write(f, "OVERALL STATUS: $overall_status\\n")
        write(f, "\\n")
        
        # List failed tests
        if n_fail > 0
            write(f, "FAILED TESTS:\\n")
            for result in suite.results
                if result.status == FAIL
                    write(f, "  ❌ $(result.test_name): $(result.error_message)\\n")
                end
            end
        end
    end
end

"""
    generate_detailed_report(suite::ValidationSuite, filename::String)

Generate a detailed markdown report.
"""
function generate_detailed_report(suite::ValidationSuite, filename::String)
    open(filename, "w") do f
        write(f, "# JuliaFOAM Validation Detailed Report\\n\\n")
        write(f, "**Suite:** $(suite.name)\\n")
        write(f, "**Date:** $(now())\\n\\n")
        
        # Summary table
        write(f, "## Summary\\n\\n")
        write(f, "| Test | Status | Accuracy | Order | Time (s) |\\n")
        write(f, "|------|--------|----------|-------|----------|\\n")
        
        for result in suite.results
            status_symbol = result.status == PASS ? "✅ PASS" : 
                           result.status == WARNING ? "⚠️ WARN" : "❌ FAIL"
            acc_str = isfinite(result.accuracy_achieved) ? 
                     @sprintf("%.2e", result.accuracy_achieved) : "N/A"
            ord_str = isfinite(result.order_achieved) ? 
                     @sprintf("%.2f", result.order_achieved) : "N/A"
            
            write(f, "| $(result.test_name) | $status_symbol | $acc_str | $ord_str | $(result.execution_time:.2f) |\\n")
        end
        
        write(f, "\\n")
        
        # Detailed results
        write(f, "## Detailed Results\\n\\n")
        for result in suite.results
            write(f, "### $(result.test_name)\\n\\n")
            write(f, "- **Status:** $(result.status)\\n")
            write(f, "- **Accuracy:** $(result.accuracy_achieved)\\n")
            write(f, "- **Convergence Order:** $(result.order_achieved)\\n")
            write(f, "- **Execution Time:** $(result.execution_time:.2f) seconds\\n")
            
            if !isempty(result.error_message)
                write(f, "- **Error:** $(result.error_message)\\n")
            end
            
            if !isempty(result.details)
                write(f, "- **Details:**\\n")
                for (key, value) in result.details
                    write(f, "  - $key: $value\\n")
                end
            end
            
            write(f, "\\n")
        end
    end
end

"""
    generate_json_report(suite::ValidationSuite, filename::String)

Generate JSON report for programmatic access.
"""
function generate_json_report(suite::ValidationSuite, filename::String)
    # Convert results to JSON-serializable format
    json_results = []
    
    for result in suite.results
        push!(json_results, Dict(
            "test_name" => result.test_name,
            "status" => string(result.status),
            "accuracy_achieved" => result.accuracy_achieved,
            "order_achieved" => result.order_achieved,
            "execution_time" => result.execution_time,
            "error_message" => result.error_message,
            "details" => result.details,
            "timestamp" => string(result.timestamp)
        ))
    end
    
    json_data = Dict(
        "suite_name" => suite.name,
        "timestamp" => string(now()),
        "results" => json_results
    )
    
    open(filename, "w") do f
        JSON3.pretty(f, json_data)
    end
end

"""
    generate_plots(suite::ValidationSuite, output_dir::String)

Generate convergence plots if plotting capability is available.
"""
function generate_plots(suite::ValidationSuite, output_dir::String)
    # This would generate convergence plots if a plotting package is available
    # For now, just create a placeholder
    println("📈 Plots would be generated here if plotting package is available")
end

"""
    compare_with_reference(results_file::String, reference_file::String) -> Bool

Compare current results with reference results.
"""
function compare_with_reference(results_file::String, reference_file::String)
    if !isfile(reference_file)
        println("⚠️  No reference file found: $reference_file")
        return false
    end
    
    current = JSON3.read(read(results_file, String))
    reference = JSON3.read(read(reference_file, String))
    
    # Compare key metrics
    differences = []
    
    for curr_result in current["results"]
        test_name = curr_result["test_name"]
        
        # Find corresponding reference result
        ref_result = nothing
        for ref in reference["results"]
            if ref["test_name"] == test_name
                ref_result = ref
                break
            end
        end
        
        if ref_result === nothing
            push!(differences, "New test: $test_name")
            continue
        end
        
        # Compare accuracy
        curr_acc = curr_result["accuracy_achieved"]
        ref_acc = ref_result["accuracy_achieved"]
        
        if isfinite(curr_acc) && isfinite(ref_acc)
            if curr_acc > ref_acc * 1.1  # Allow 10% degradation
                push!(differences, "$test_name: Accuracy degraded from $(ref_acc) to $(curr_acc)")
            end
        end
        
        # Compare convergence order
        curr_ord = curr_result["order_achieved"]
        ref_ord = ref_result["order_achieved"]
        
        if isfinite(curr_ord) && isfinite(ref_ord)
            if abs(curr_ord - ref_ord) > 0.2  # Allow 0.2 difference in order
                push!(differences, "$test_name: Order changed from $(ref_ord) to $(curr_ord)")
            end
        end
    end
    
    if isempty(differences)
        println("✅ Results match reference within tolerance")
        return true
    else
        println("⚠️  Differences found:")
        for diff in differences
            println("  - $diff")
        end
        return false
    end
end

end # module ValidationFramework