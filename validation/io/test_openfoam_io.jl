#!/usr/bin/env julia

"""
Test OpenFOAM I/O Functions

Tests the I/O functions we fixed to ensure they properly parse OpenFOAM files.
"""

using Printf

println("📁 Testing OpenFOAM I/O Implementation")
println("="^80)

# Create test OpenFOAM files
function create_test_openfoam_files()
    # Create test directory structure
    test_dir = "test_case"
    mkpath("$test_dir/0")
    mkpath("$test_dir/constant")
    mkpath("$test_dir/system")
    
    # Create test U field file
    u_content = """
/*--------------------------------*- C++ -*----------------------------------*\\
| =========                 |                                                 |
| \\\\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\\\    /   O peration     | Website:  https://openfoam.org                 |
|   \\\\  /    A nd           | Version:  8                                     |
|    \\\\/     M anipulation  |                                                 |
\\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    class       volVectorField;
    object      U;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

dimensions      [0 1 -1 0 0 0 0];

internalField   uniform (1 0 0);

boundaryField
{
    movingWall
    {
        type            fixedValue;
        value           uniform (1 0 0);
    }
    fixedWalls
    {
        type            noSlip;
        value           uniform (0 0 0);
    }
    frontAndBack
    {
        type            empty;
    }
}

// ************************************************************************* //
"""
    
    # Create test p field file
    p_content = """
/*--------------------------------*- C++ -*----------------------------------*\\
| =========                 |                                                 |
| \\\\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\\\    /   O peration     | Website:  https://openfoam.org                 |
|   \\\\  /    A nd           | Version:  8                                     |
|    \\\\/     M anipulation  |                                                 |
\\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    class       volScalarField;
    object      p;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

dimensions      [0 2 -2 0 0 0 0];

internalField   uniform 0;

boundaryField
{
    movingWall
    {
        type            zeroGradient;
    }
    fixedWalls
    {
        type            zeroGradient;
    }
    frontAndBack
    {
        type            empty;
    }
}

// ************************************************************************* //
"""
    
    # Create transportProperties
    transport_content = """
/*--------------------------------*- C++ -*----------------------------------*\\
| =========                 |                                                 |
| \\\\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\\\    /   O peration     | Website:  https://openfoam.org                 |
|   \\\\  /    A nd           | Version:  8                                     |
|    \\\\/     M anipulation  |                                                 |
\\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    class       dictionary;
    object      transportProperties;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

transportModel  Newtonian;

nu              [0 2 -1 0 0 0 0] 1e-05;

// ************************************************************************* //
"""
    
    # Create turbulenceProperties
    turbulence_content = """
/*--------------------------------*- C++ -*----------------------------------*\\
| =========                 |                                                 |
| \\\\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\\\    /   O peration     | Website:  https://openfoam.org                 |
|   \\\\  /    A nd           | Version:  8                                     |
|    \\\\/     M anipulation  |                                                 |
\\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    class       dictionary;
    object      turbulenceProperties;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

simulationType laminar;

// ************************************************************************* //
"""
    
    # Write test files
    write("$test_dir/0/U", u_content)
    write("$test_dir/0/p", p_content)
    write("$test_dir/constant/transportProperties", transport_content)
    write("$test_dir/constant/turbulenceProperties", turbulence_content)
    
    return test_dir
end

# Test field parsing
function test_field_parsing()
    println("\n📄 Testing Field File Parsing")
    println("-"^50)
    
    try
        test_dir = create_test_openfoam_files()
        
        # Simple field parsing function
        function parse_field_simple(file_path)
            if !isfile(file_path)
                return nothing, "File not found"
            end
            
            lines = readlines(file_path)
            internal_field = nothing
            boundary_conditions = Dict{String, Tuple{String, Any}}()
            
            in_boundary_field = false
            current_patch = ""
            
            for line in lines
                line = strip(line)
                
                # Skip comments and empty lines
                if startswith(line, "//") || startswith(line, "/*") || isempty(line)
                    continue
                end
                
                # Parse internalField
                if contains(line, "internalField")
                    if contains(line, "uniform")
                        value_str = split(line, "uniform")[2]
                        value_str = strip(replace(value_str, ";" => ""))
                        
                        if startswith(value_str, "(") && endswith(value_str, ")")
                            # Vector field
                            value_str = replace(value_str, "(" => "")
                            value_str = replace(value_str, ")" => "")
                            components = parse.(Float64, split(value_str))
                            internal_field = (components[1], components[2], components[3])
                        else
                            # Scalar field
                            internal_field = parse(Float64, value_str)
                        end
                    end
                end
                
                # Parse boundaryField
                if contains(line, "boundaryField")
                    in_boundary_field = true
                    continue
                end
                
                if in_boundary_field
                    # Look for patch definitions
                    if contains(line, "{") && !contains(line, "boundaryField")
                        patch_name = split(line, "{")[1]
                        current_patch = strip(patch_name)
                    elseif !isempty(current_patch) && contains(line, "type")
                        bc_type = strip(split(line, "type")[2])
                        bc_type = strip(replace(bc_type, ";" => ""))
                        boundary_conditions[current_patch] = (bc_type, nothing)
                    elseif contains(line, "}")
                        if !isempty(current_patch)
                            current_patch = ""
                        else
                            break  # End of boundaryField
                        end
                    end
                end
            end
            
            return (internal_field, boundary_conditions), "Success"
        end
        
        # Test U field parsing
        u_result, u_msg = parse_field_simple("$test_dir/0/U")
        if u_result !== nothing
            internal_u, bc_u = u_result
            @printf "✓ U field parsed - Internal: %s\n" string(internal_u)
            @printf "  Boundary conditions: %d patches\n" length(bc_u)
            for (patch, (bc_type, value)) in bc_u
                @printf "    %s: %s\n" patch bc_type
            end
        else
            println("❌ Failed to parse U field: $u_msg")
            return false
        end
        
        # Test p field parsing
        p_result, p_msg = parse_field_simple("$test_dir/0/p")
        if p_result !== nothing
            internal_p, bc_p = p_result
            @printf "✓ p field parsed - Internal: %s\n" string(internal_p)
            @printf "  Boundary conditions: %d patches\n" length(bc_p)
        else
            println("❌ Failed to parse p field: $p_msg")
            return false
        end
        
        # Cleanup
        rm(test_dir, recursive=true)
        
        println("✅ PASS: Field parsing works correctly")
        return true
        
    catch e
        println("❌ FAIL: Error in field parsing test: $e")
        return false
    end
end

# Test transport properties parsing
function test_transport_properties()
    println("\n🔧 Testing Transport Properties Parsing")
    println("-"^50)
    
    try
        test_dir = create_test_openfoam_files()
        
        function parse_transport_properties_simple(file_path)
            if !isfile(file_path)
                return Dict{String, Any}()
            end
            
            props = Dict{String, Any}()
            lines = readlines(file_path)
            
            for line in lines
                line = strip(line)
                
                if startswith(line, "//") || startswith(line, "/*") || isempty(line)
                    continue
                end
                
                if contains(line, "transportModel")
                    value_str = split(line, "transportModel")[2]
                    value_str = strip(replace(value_str, ";" => ""))
                    props["transportModel"] = strip(value_str)
                end
                
                if contains(line, "nu") && contains(line, "[")
                    # Parse kinematic viscosity
                    parts = split(line)
                    for (i, part) in enumerate(parts)
                        if part == "nu" && i < length(parts)
                            for j in (i+1):length(parts)
                                cleaned = replace(parts[j], ";" => "")
                                if !contains(cleaned, "[") && !contains(cleaned, "]")
                                    try
                                        props["nu"] = parse(Float64, cleaned)
                                        break
                                    catch
                                        continue
                                    end
                                end
                            end
                            break
                        end
                    end
                end
            end
            
            return props
        end
        
        props = parse_transport_properties_simple("$test_dir/constant/transportProperties")
        
        @printf "✓ Transport properties parsed:\n"
        for (key, value) in props
            @printf "  %s: %s\n" key string(value)
        end
        
        # Check expected values
        if haskey(props, "transportModel") && props["transportModel"] == "Newtonian"
            println("✓ Transport model correctly identified")
        else
            println("❌ Transport model not found or incorrect")
            return false
        end
        
        if haskey(props, "nu") && props["nu"] == 1e-5
            println("✓ Kinematic viscosity correctly parsed")
        else
            println("❌ Kinematic viscosity not found or incorrect")
            return false
        end
        
        # Cleanup
        rm(test_dir, recursive=true)
        
        println("✅ PASS: Transport properties parsing works")
        return true
        
    catch e
        println("❌ FAIL: Error in transport properties test: $e")
        return false
    end
end

# Test turbulence properties parsing
function test_turbulence_properties()
    println("\n🌪️  Testing Turbulence Properties Parsing")
    println("-"^50)
    
    try
        test_dir = create_test_openfoam_files()
        
        function parse_turbulence_properties_simple(file_path)
            if !isfile(file_path)
                return Dict{String, Any}()
            end
            
            props = Dict{String, Any}()
            lines = readlines(file_path)
            
            for line in lines
                line = strip(line)
                
                if startswith(line, "//") || startswith(line, "/*") || isempty(line)
                    continue
                end
                
                if contains(line, "simulationType")
                    value_str = split(line, "simulationType")[2]
                    value_str = strip(replace(value_str, ";" => ""))
                    props["simulationType"] = strip(value_str)
                end
            end
            
            return props
        end
        
        props = parse_turbulence_properties_simple("$test_dir/constant/turbulenceProperties")
        
        @printf "✓ Turbulence properties parsed:\n"
        for (key, value) in props
            @printf "  %s: %s\n" key string(value)
        end
        
        # Check expected values
        if haskey(props, "simulationType") && props["simulationType"] == "laminar"
            println("✓ Simulation type correctly identified")
        else
            println("❌ Simulation type not found or incorrect")
            return false
        end
        
        # Cleanup
        rm(test_dir, recursive=true)
        
        println("✅ PASS: Turbulence properties parsing works")
        return true
        
    catch e
        println("❌ FAIL: Error in turbulence properties test: $e")
        return false
    end
end

# Test with a real OpenFOAM case if available
function test_real_openfoam_case()
    println("\n🏠 Testing Real OpenFOAM Case (if available)")
    println("-"^50)
    
    # Check if there's a real case available
    cavity_case = "cases/cavity"
    if isdir(cavity_case)
        println("✓ Found cavity case directory")
        
        # Check for typical OpenFOAM files
        files_to_check = [
            "$cavity_case/0/U",
            "$cavity_case/0/p",
            "$cavity_case/constant/transportProperties"
        ]
        
        found_files = 0
        for file_path in files_to_check
            if isfile(file_path)
                found_files += 1
                @printf "✓ Found: %s\n" file_path
            else
                @printf "❌ Missing: %s\n" file_path
            end
        end
        
        if found_files >= 2
            println("✅ PASS: Real OpenFOAM case structure looks good")
            return true
        else
            println("⚠️  WARNING: Real case incomplete but test directory exists")
            return true
        end
    else
        println("ℹ️  INFO: No real OpenFOAM case found (optional)")
        return true
    end
end

# Run all I/O tests
function run_io_tests()
    println("\n🏁 Running OpenFOAM I/O Tests")
    println("="^80)
    
    tests = [
        ("Field Parsing", test_field_parsing),
        ("Transport Properties", test_transport_properties),
        ("Turbulence Properties", test_turbulence_properties),
        ("Real Case Check", test_real_openfoam_case)
    ]
    
    passed = 0
    total = length(tests)
    
    for (name, test_func) in tests
        if test_func()
            passed += 1
        end
    end
    
    println("\n" * "="^80)
    println("I/O TEST SUMMARY")
    println("="^80)
    @printf "Tests passed: %d/%d\n" passed total
    
    if passed == total
        println("🎉 ALL I/O TESTS PASSED!")
        return true
    else
        println("💥 SOME I/O TESTS FAILED!")
        return false
    end
end

# Run the tests
if abspath(PROGRAM_FILE) == @__FILE__
    success = run_io_tests()
    exit(success ? 0 : 1)
end