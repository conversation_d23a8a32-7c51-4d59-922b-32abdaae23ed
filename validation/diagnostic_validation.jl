#!/usr/bin/env julia

"""
Diagnostic-Validation Integration for JuliaFOAM

This script integrates the comprehensive diagnostic tools with the validation
framework to provide unified health assessment and validation checking.

Features:
- Unified diagnostic and validation runner
- Cross-validation of diagnostic results
- Automated issue correlation
- Comprehensive reporting
- CI/CD integration support
"""

# Add parent directories to path for importing JuliaFOAM
push!(LOAD_PATH, joinpath(@__DIR__, "../src"))
push!(LOAD_PATH, joinpath(@__DIR__, ".."))

include("../src/tools/JuliaFOAMDoctor.jl")
include("../src/tools/MeshQualityChecks.jl")
include("../src/core/Types.jl")

using .JuliaFOAMDoctor
using .MeshQualityChecks
using Printf
using Dates

"""
Unified validation and diagnostic results
"""
struct UnifiedValidationResults
    # Diagnostic results
    health_results::JuliaFOAMHealthResults
    quality_results::MeshQualityResults
    
    # Validation results
    mathematical_validation_passed::Bool
    fix_verification_passed::Bool
    integration_tests_passed::Bool
    
    # Cross-validation analysis
    diagnostic_validation_consistency::Dict{String, Bool}
    conflicting_assessments::Vector{String}
    correlated_issues::Vector{Tuple{String, String}}  # (diagnostic_issue, validation_failure)
    
    # Unified assessment
    overall_system_health::String  # "EXCELLENT", "GOOD", "NEEDS_ATTENTION", "CRITICAL"
    deployment_readiness::String   # "PRODUCTION", "DEVELOPMENT", "TESTING_ONLY", "NOT_READY"
    confidence_score::Float64      # 0-100, how confident we are in the assessment
    
    # Recommendations
    prioritized_actions::Vector{Tuple{String, String, String}}  # (action, reason, urgency)
    validation_gaps::Vector{String}
    diagnostic_limitations::Vector{String}
end

"""
Run unified validation and diagnostics
"""
function run_unified_validation_diagnostics(mesh::Mesh; 
                                           verbose::Bool=true, 
                                           run_validation_tests::Bool=true,
                                           cfd_application::String="general")::UnifiedValidationResults
    
    if verbose
        println("🔬 JuliaFOAM Unified Validation & Diagnostics")
        println("="^70)
        println("Started at: $(now())")
        println()
    end
    
    # 1. Run Comprehensive Health Diagnostics
    if verbose
        println("🩺 Running Comprehensive Health Diagnostics...")
    end
    
    health_check = JuliaFOAMHealthCheck(mesh, verbose=false)  # Suppress individual diagnostic output
    health_results = run_full_diagnostics(health_check)
    
    # 2. Run Detailed Mesh Quality Analysis
    if verbose
        println("📐 Running Detailed Mesh Quality Analysis...")
    end
    
    quality_analyzer = MeshQualityAnalyzer(mesh, cfd_application=cfd_application, verbose=false)
    quality_results = analyze_mesh_quality(quality_analyzer)
    
    # 3. Run Validation Tests
    mathematical_validation_passed = true
    fix_verification_passed = true
    integration_tests_passed = true
    
    if run_validation_tests
        if verbose
            println("✅ Running Validation Test Suite...")
        end
        
        # Run key validation tests
        mathematical_validation_passed = run_mathematical_validation()
        fix_verification_passed = run_fix_verification()
        integration_tests_passed = run_integration_tests()
    end
    
    # 4. Cross-Validation Analysis
    if verbose
        println("🔍 Performing Cross-Validation Analysis...")
    end
    
    diagnostic_validation_consistency = analyze_consistency(health_results, quality_results, 
                                                          mathematical_validation_passed,
                                                          fix_verification_passed, 
                                                          integration_tests_passed)
    
    conflicting_assessments = identify_conflicts(health_results, quality_results,
                                                mathematical_validation_passed,
                                                fix_verification_passed,
                                                integration_tests_passed)
    
    correlated_issues = correlate_issues(health_results, quality_results,
                                        mathematical_validation_passed,
                                        fix_verification_passed,
                                        integration_tests_passed)
    
    # 5. Unified Assessment
    overall_system_health = assess_overall_system_health(health_results, quality_results,
                                                        mathematical_validation_passed,
                                                        fix_verification_passed,
                                                        integration_tests_passed)
    
    deployment_readiness = assess_deployment_readiness(overall_system_health, health_results, quality_results)
    
    confidence_score = calculate_confidence_score(health_results, quality_results,
                                                 diagnostic_validation_consistency,
                                                 conflicting_assessments)
    
    # 6. Generate Recommendations
    prioritized_actions = generate_prioritized_actions(health_results, quality_results,
                                                     mathematical_validation_passed,
                                                     fix_verification_passed,
                                                     integration_tests_passed)
    
    validation_gaps = identify_validation_gaps(run_validation_tests, mathematical_validation_passed,
                                              fix_verification_passed, integration_tests_passed)
    
    diagnostic_limitations = identify_diagnostic_limitations(health_results, quality_results)
    
    if verbose
        println("\n📊 Unified Assessment Complete!")
        println("Overall System Health: $overall_system_health")
        println("Deployment Readiness: $deployment_readiness")
        println("Confidence Score: $(round(confidence_score, digits=1))/100")
        
        if !isempty(conflicting_assessments)
            println("\n⚠️  Warning: $(length(conflicting_assessments)) conflicting assessments detected")
        end
    end
    
    return UnifiedValidationResults(
        health_results, quality_results,
        mathematical_validation_passed, fix_verification_passed, integration_tests_passed,
        diagnostic_validation_consistency, conflicting_assessments, correlated_issues,
        overall_system_health, deployment_readiness, confidence_score,
        prioritized_actions, validation_gaps, diagnostic_limitations
    )
end

"""
Run mathematical validation tests
"""
function run_mathematical_validation()::Bool
    try
        # Run the quick validation test
        result = read(`julia $(joinpath(@__DIR__, "quick_validation_test.jl"))`, String)
        return occursin("✅ PASS", result) && occursin("OVERALL STATUS: ✅ PASS", result)
    catch e
        return false
    end
end

"""
Run fix verification tests
"""
function run_fix_verification()::Bool
    try
        # Run the fix verification test
        result = read(`julia $(joinpath(@__DIR__, "fixes/test_fixes_verification.jl"))`, String)
        return occursin("ALL FIXES VERIFIED SUCCESSFULLY", result)
    catch e
        return false
    end
end

"""
Run integration tests
"""
function run_integration_tests()::Bool
    try
        # Run the integration test
        result = read(`julia $(joinpath(@__DIR__, "integration/test_integration.jl"))`, String)
        return occursin("ALL INTEGRATION TESTS PASSED", result)
    catch e
        return false
    end
end

"""
Analyze consistency between diagnostic and validation results
"""
function analyze_consistency(health_results::JuliaFOAMHealthResults,
                           quality_results::MeshQualityResults,
                           math_val::Bool, fix_val::Bool, int_val::Bool)::Dict{String, Bool}
    
    consistency = Dict{String, Bool}()
    
    # Check if health score aligns with validation results
    health_score = health_results.overall_health_score
    all_validations_pass = math_val && fix_val && int_val
    
    consistency["health_validation_alignment"] = (health_score > 80) == all_validations_pass
    
    # Check if mesh quality aligns with mathematical validation
    mesh_quality_good = quality_results.overall_mesh_grade in ["A+", "A", "A-", "B+", "B"]
    consistency["mesh_math_alignment"] = mesh_quality_good == math_val
    
    # Check if FVC/FVM diagnostics align with fix verification
    fvc_fvm_good = health_results.fvc_results.overall_fvc_quality in ["GOOD", "EXCELLENT"] &&
                   health_results.fvm_results.overall_fvm_quality in ["GOOD", "EXCELLENT"]
    consistency["operators_fixes_alignment"] = fvc_fvm_good == fix_val
    
    # Check if integration results align with integration tests
    system_ready = health_results.system_readiness in ["PRODUCTION_READY", "DEVELOPMENT_READY"]
    consistency["system_integration_alignment"] = system_ready == int_val
    
    return consistency
end

"""
Identify conflicting assessments
"""
function identify_conflicts(health_results::JuliaFOAMHealthResults,
                          quality_results::MeshQualityResults,
                          math_val::Bool, fix_val::Bool, int_val::Bool)::Vector{String}
    
    conflicts = String[]
    
    # Conflict 1: High health score but validation failures
    if health_results.overall_health_score > 85 && (!math_val || !fix_val || !int_val)
        push!(conflicts, "High diagnostic health score conflicts with validation test failures")
    end
    
    # Conflict 2: Low health score but validation passes
    if health_results.overall_health_score < 60 && math_val && fix_val && int_val
        push!(conflicts, "Low diagnostic health score conflicts with passing validation tests")
    end
    
    # Conflict 3: Excellent mesh quality but poor mathematical validation
    if quality_results.overall_mesh_grade in ["A+", "A"] && !math_val
        push!(conflicts, "Excellent mesh quality conflicts with mathematical validation failures")
    end
    
    # Conflict 4: Poor mesh quality but good CFD readiness
    if quality_results.cfd_readiness_score > 80 && quality_results.overall_mesh_grade in ["D", "F"]
        push!(conflicts, "High CFD readiness conflicts with poor mesh quality grade")
    end
    
    # Conflict 5: System ready but integration tests fail
    if health_results.system_readiness == "PRODUCTION_READY" && !int_val
        push!(conflicts, "Production readiness assessment conflicts with integration test failures")
    end
    
    return conflicts
end

"""
Correlate diagnostic issues with validation failures
"""
function correlate_issues(health_results::JuliaFOAMHealthResults,
                         quality_results::MeshQualityResults,
                         math_val::Bool, fix_val::Bool, int_val::Bool)::Vector{Tuple{String, String}}
    
    correlations = Tuple{String, String}[]
    
    # Mathematical validation correlations
    if !math_val
        if !isempty(health_results.fvc_results.critical_fvc_issues)
            push!(correlations, ("FVC operator issues detected", "Mathematical validation failed"))
        end
        
        if quality_results.overall_mesh_grade in ["D", "F"]
            push!(correlations, ("Poor mesh quality detected", "Mathematical validation failed"))
        end
    end
    
    # Fix verification correlations
    if !fix_val
        if !isempty(health_results.fvm_results.matrix_issues)
            push!(correlations, ("Matrix assembly issues detected", "Fix verification failed"))
        end
        
        if !isempty(health_results.fvm_results.solver_issues)
            push!(correlations, ("Solver convergence issues detected", "Fix verification failed"))
        end
    end
    
    # Integration test correlations
    if !int_val
        if health_results.system_readiness == "CRITICAL_ISSUES"
            push!(correlations, ("Critical system issues detected", "Integration tests failed"))
        end
        
        if !isempty(health_results.fvm_results.conservation_issues)
            push!(correlations, ("Conservation law violations detected", "Integration tests failed"))
        end
    end
    
    return correlations
end

"""
Assess overall system health considering all factors
"""
function assess_overall_system_health(health_results::JuliaFOAMHealthResults,
                                     quality_results::MeshQualityResults,
                                     math_val::Bool, fix_val::Bool, int_val::Bool)::String
    
    # Calculate weighted score
    diagnostic_score = health_results.overall_health_score * 0.4
    quality_score = quality_results.cfd_readiness_score * 0.3
    validation_score = (math_val + fix_val + int_val) / 3 * 100 * 0.3
    
    overall_score = diagnostic_score + quality_score + validation_score
    
    # Penalty for critical issues
    if !isempty(health_results.critical_issues) || !isempty(quality_results.critical_quality_issues)
        overall_score -= 20
    end
    
    # Categorize
    if overall_score >= 90
        return "EXCELLENT"
    elseif overall_score >= 75
        return "GOOD"
    elseif overall_score >= 60
        return "NEEDS_ATTENTION"
    else
        return "CRITICAL"
    end
end

"""
Assess deployment readiness
"""
function assess_deployment_readiness(overall_health::String,
                                    health_results::JuliaFOAMHealthResults,
                                    quality_results::MeshQualityResults)::String
    
    # Check for blockers
    has_critical_issues = !isempty(health_results.critical_issues) || 
                         !isempty(quality_results.critical_quality_issues)
    
    if has_critical_issues
        return "NOT_READY"
    end
    
    if overall_health == "EXCELLENT"
        return "PRODUCTION"
    elseif overall_health == "GOOD"
        return "DEVELOPMENT"
    elseif overall_health == "NEEDS_ATTENTION"
        return "TESTING_ONLY"
    else
        return "NOT_READY"
    end
end

"""
Calculate confidence score in the assessment
"""
function calculate_confidence_score(health_results::JuliaFOAMHealthResults,
                                   quality_results::MeshQualityResults,
                                   consistency::Dict{String, Bool},
                                   conflicts::Vector{String})::Float64
    
    base_confidence = 100.0
    
    # Reduce confidence for inconsistencies
    inconsistent_assessments = count(values(consistency)) - sum(values(consistency))
    base_confidence -= inconsistent_assessments * 15
    
    # Reduce confidence for conflicts
    base_confidence -= length(conflicts) * 10
    
    # Reduce confidence for low diagnostic time (rushed assessment)
    if health_results.total_diagnostic_time < 1.0
        base_confidence -= 10
    end
    
    # Increase confidence for comprehensive coverage
    if health_results.validation_coverage > 90
        base_confidence += 5
    end
    
    return max(min(base_confidence, 100.0), 0.0)
end

"""
Generate prioritized actions
"""
function generate_prioritized_actions(health_results::JuliaFOAMHealthResults,
                                     quality_results::MeshQualityResults,
                                     math_val::Bool, fix_val::Bool, int_val::Bool)::Vector{Tuple{String, String, String}}
    
    actions = Tuple{String, String, String}[]
    
    # Critical priority actions
    if !isempty(health_results.critical_issues)
        push!(actions, ("Fix critical system issues", "System has critical health issues", "CRITICAL"))
    end
    
    if !isempty(quality_results.critical_quality_issues)
        push!(actions, ("Fix critical mesh quality issues", "Mesh quality is below CFD standards", "CRITICAL"))
    end
    
    # High priority actions
    if !math_val
        push!(actions, ("Fix mathematical operator implementations", "Mathematical validation failed", "HIGH"))
    end
    
    if !fix_val
        push!(actions, ("Verify all bug fixes are properly implemented", "Fix verification failed", "HIGH"))
    end
    
    # Medium priority actions
    if !int_val
        push!(actions, ("Fix component integration issues", "Integration tests failed", "MEDIUM"))
    end
    
    if quality_results.cfd_readiness_score < 70
        push!(actions, ("Improve mesh quality for CFD applications", "CFD readiness score low", "MEDIUM"))
    end
    
    # Low priority actions
    if health_results.validation_coverage < 80
        push!(actions, ("Increase validation test coverage", "Some components lack validation", "LOW"))
    end
    
    return actions
end

"""
Identify validation gaps
"""
function identify_validation_gaps(ran_tests::Bool, math_val::Bool, fix_val::Bool, int_val::Bool)::Vector{String}
    gaps = String[]
    
    if !ran_tests
        push!(gaps, "Validation tests were not executed")
        return gaps
    end
    
    if !math_val
        push!(gaps, "Mathematical operator validation incomplete")
    end
    
    if !fix_val
        push!(gaps, "Bug fix verification incomplete")
    end
    
    if !int_val
        push!(gaps, "System integration validation incomplete")
    end
    
    # Additional gaps that could be detected
    push!(gaps, "Performance validation not implemented")
    push!(gaps, "Memory usage validation not implemented")
    push!(gaps, "Parallel scaling validation not implemented")
    
    return gaps
end

"""
Identify diagnostic limitations
"""
function identify_diagnostic_limitations(health_results::JuliaFOAMHealthResults,
                                       quality_results::MeshQualityResults)::Vector{String}
    limitations = String[]
    
    # Time-based limitations
    if health_results.total_diagnostic_time < 2.0
        push!(limitations, "Diagnostic time too short for comprehensive analysis")
    end
    
    # Coverage limitations
    if health_results.validation_coverage < 90
        push!(limitations, "Incomplete diagnostic coverage of all components")
    end
    
    # Mesh size limitations
    if health_results.total_cells < 100
        push!(limitations, "Mesh too small for realistic quality assessment")
    end
    
    # Application-specific limitations
    push!(limitations, "Turbulence model validation not comprehensive")
    push!(limitations, "Boundary condition validation simplified")
    push!(limitations, "Real-world case validation missing")
    
    return limitations
end

"""
Generate unified diagnostic-validation report
"""
function generate_unified_report(results::UnifiedValidationResults; 
                                output_file::Union{String, Nothing}=nothing)::String
    
    report_lines = String[]
    
    push!(report_lines, "")
    push!(report_lines, "🔬 JuliaFOAM Unified Validation & Diagnostics Report")
    push!(report_lines, "="^80)
    push!(report_lines, "Generated: $(now())")
    push!(report_lines, "")
    
    # Executive Summary
    push!(report_lines, "📋 Executive Summary")
    push!(report_lines, "-"^30)
    push!(report_lines, "Overall System Health: $(results.overall_system_health)")
    push!(report_lines, "Deployment Readiness: $(results.deployment_readiness)")
    push!(report_lines, "Assessment Confidence: $(round(results.confidence_score, digits=1))/100")
    push!(report_lines, "")
    
    # Health & Quality Overview
    push!(report_lines, "📊 Health & Quality Overview")
    push!(report_lines, "-"^40)
    push!(report_lines, "Health Score: $(round(results.health_results.overall_health_score, digits=1))/100 ($(results.health_results.health_grade))")
    push!(report_lines, "Mesh Quality Grade: $(results.quality_results.overall_mesh_grade)")
    push!(report_lines, "CFD Readiness: $(round(results.quality_results.cfd_readiness_score, digits=1))/100")
    push!(report_lines, "")
    
    # Validation Test Results
    push!(report_lines, "✅ Validation Test Results")
    push!(report_lines, "-"^35)
    math_status = results.mathematical_validation_passed ? "✅ PASS" : "❌ FAIL"
    fix_status = results.fix_verification_passed ? "✅ PASS" : "❌ FAIL"
    int_status = results.integration_tests_passed ? "✅ PASS" : "❌ FAIL"
    
    push!(report_lines, "Mathematical Validation: $math_status")
    push!(report_lines, "Fix Verification: $fix_status")
    push!(report_lines, "Integration Tests: $int_status")
    push!(report_lines, "")
    
    # Cross-Validation Analysis
    push!(report_lines, "🔍 Cross-Validation Analysis")
    push!(report_lines, "-"^40)
    
    consistent_count = sum(values(results.diagnostic_validation_consistency))
    total_count = length(results.diagnostic_validation_consistency)
    consistency_pct = total_count > 0 ? (consistent_count / total_count) * 100 : 100
    
    push!(report_lines, "Consistency Score: $(round(consistency_pct, digits=1))% ($(consistent_count)/$(total_count) checks)")
    
    if !isempty(results.conflicting_assessments)
        push!(report_lines, "")
        push!(report_lines, "⚠️  Conflicting Assessments:")
        for conflict in results.conflicting_assessments
            push!(report_lines, "  • $conflict")
        end
    end
    
    if !isempty(results.correlated_issues)
        push!(report_lines, "")
        push!(report_lines, "🔗 Issue Correlations:")
        for (diagnostic, validation) in results.correlated_issues
            push!(report_lines, "  • $diagnostic → $validation")
        end
    end
    push!(report_lines, "")
    
    # Prioritized Actions
    if !isempty(results.prioritized_actions)
        push!(report_lines, "🎯 Prioritized Actions")
        push!(report_lines, "-"^30)
        
        critical_actions = filter(a -> a[3] == "CRITICAL", results.prioritized_actions)
        high_actions = filter(a -> a[3] == "HIGH", results.prioritized_actions)
        medium_actions = filter(a -> a[3] == "MEDIUM", results.prioritized_actions)
        low_actions = filter(a -> a[3] == "LOW", results.prioritized_actions)
        
        if !isempty(critical_actions)
            push!(report_lines, "🚨 CRITICAL:")
            for (action, reason, _) in critical_actions
                push!(report_lines, "  • $action - $reason")
            end
        end
        
        if !isempty(high_actions)
            push!(report_lines, "🔴 HIGH:")
            for (action, reason, _) in high_actions
                push!(report_lines, "  • $action - $reason")
            end
        end
        
        if !isempty(medium_actions)
            push!(report_lines, "🟡 MEDIUM:")
            for (action, reason, _) in medium_actions
                push!(report_lines, "  • $action - $reason")
            end
        end
        
        if !isempty(low_actions)
            push!(report_lines, "🟢 LOW:")
            for (action, reason, _) in low_actions
                push!(report_lines, "  • $action - $reason")
            end
        end
        push!(report_lines, "")
    end
    
    # Gaps and Limitations
    push!(report_lines, "⚠️  Assessment Gaps & Limitations")
    push!(report_lines, "-"^45)
    
    if !isempty(results.validation_gaps)
        push!(report_lines, "Validation Gaps:")
        for gap in results.validation_gaps
            push!(report_lines, "  • $gap")
        end
    end
    
    if !isempty(results.diagnostic_limitations)
        push!(report_lines, "Diagnostic Limitations:")
        for limitation in results.diagnostic_limitations
            push!(report_lines, "  • $limitation")
        end
    end
    push!(report_lines, "")
    
    # Deployment Guidance
    push!(report_lines, "🚀 Deployment Guidance")
    push!(report_lines, "-"^30)
    
    if results.deployment_readiness == "PRODUCTION"
        push!(report_lines, "✅ System is ready for production CFD simulations")
        push!(report_lines, "• All critical validations passed")
        push!(report_lines, "• Mesh quality suitable for CFD")
        push!(report_lines, "• Proceed with confidence")
    elseif results.deployment_readiness == "DEVELOPMENT"
        push!(report_lines, "⚠️  System is suitable for development and testing")
        push!(report_lines, "• Address remaining issues before production")
        push!(report_lines, "• Continue validation testing")
    elseif results.deployment_readiness == "TESTING_ONLY"
        push!(report_lines, "🔍 System requires further validation")
        push!(report_lines, "• Use only for testing and development")
        push!(report_lines, "• Address quality issues")
    else  # NOT_READY
        push!(report_lines, "🚨 System is NOT ready for CFD use")
        push!(report_lines, "• Critical issues must be resolved")
        push!(report_lines, "• Do not proceed with simulations")
    end
    push!(report_lines, "")
    
    # Footer
    push!(report_lines, "="^80)
    push!(report_lines, "Unified report combining diagnostics and validation")
    push!(report_lines, "Confidence: $(round(results.confidence_score, digits=1))% | System Health: $(results.overall_system_health)")
    push!(report_lines, "")
    
    report = join(report_lines, "\n")
    
    if output_file !== nothing
        open(output_file, "w") do f
            write(f, report)
        end
        println("Unified validation-diagnostic report saved to: $output_file")
    else
        println(report)
    end
    
    return report
end

"""
Main entry point for command-line usage
"""
function main()
    if length(ARGS) == 0
        println("Usage: julia validation/diagnostic_validation.jl [--quick] [--no-validation] [--output FILE]")
        return
    end
    
    quick_mode = "--quick" in ARGS
    run_validation = !("--no-validation" in ARGS)
    
    output_file = nothing
    if "--output" in ARGS
        idx = findfirst(x -> x == "--output", ARGS)
        if idx !== nothing && idx < length(ARGS)
            output_file = ARGS[idx + 1]
        end
    end
    
    # Create test mesh for demonstration
    println("📐 Creating test mesh for validation...")
    
    # Simple test mesh creation (placeholder)
    cells = [Cell(SVector{3,Float64}(0.5, 0.5, 0.5), 1.0)]
    faces = [Face(1, -1, SVector{3,Float64}(1.0, 0.0, 0.0), SVector{3,Float64}(1.0, 0.5, 0.5))]
    test_mesh = Mesh(cells, faces)
    
    # Run unified validation and diagnostics
    results = run_unified_validation_diagnostics(
        test_mesh, 
        verbose=true,
        run_validation_tests=run_validation,
        cfd_application="general"
    )
    
    # Generate report
    generate_unified_report(results, output_file=output_file)
    
    # Exit with appropriate code
    if results.deployment_readiness == "NOT_READY"
        exit(2)
    elseif results.deployment_readiness in ["TESTING_ONLY", "DEVELOPMENT"]
        exit(1)
    else
        exit(0)
    end
end

if abspath(PROGRAM_FILE) == @__FILE__
    main()
end