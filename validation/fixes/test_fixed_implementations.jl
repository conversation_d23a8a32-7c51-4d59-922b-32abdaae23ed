#!/usr/bin/env julia

"""
Test the fixed implementations after addressing test failures
"""

using Test
using JuliaFOAM
using LinearAlgebra
using SparseArrays

println("🔧 Testing Fixed Implementations")
println("=" ^ 40)

@testset "Fixed Implementation Tests" begin
    
    @testset "GMRES Solver with Fixed Preconditioners" begin
        println("🔧 Testing Fixed GMRES with Preconditioners...")
        
        # Create a test linear system
        n = 30
        A = spdiagm(0 => 4.0 * ones(n), 1 => -1.0 * ones(n-1), -1 => -1.0 * ones(n-1))
        x_exact = ones(n)
        b = A * x_exact
        x = zeros(n)
        
        # Test with IdentityPreconditioner (should work now)
        precond = JuliaFOAM.LinearSolvers.IdentityPreconditioner()
        settings = JuliaFOAM.LinearSolvers.SolverSettings(tolerance=1e-8, max_iterations=100)
        
        iter, residual = JuliaFOAM.LinearSolvers.gmres_solve!(A, b, x, precond, settings)
        
        @test iter > 0
        @test residual < 1e-8
        @test norm(x - x_exact) / norm(x_exact) < 1e-6
        
        println("✅ GMRES with IdentityPreconditioner working ($(iter) iterations)")
        
        # Test with DiagonalPreconditioner
        x .= 0.0
        diag_precond = JuliaFOAM.LinearSolvers.DiagonalPreconditioner(diag(A))
        iter_diag, residual_diag = JuliaFOAM.LinearSolvers.gmres_solve!(A, b, x, diag_precond, settings)
        
        @test iter_diag > 0
        @test residual_diag < 1e-6
        @test norm(x - x_exact) / norm(x_exact) < 1e-4
        
        println("✅ GMRES with DiagonalPreconditioner working ($(iter_diag) iterations)")
    end
    
    @testset "Preconditioner Apply Methods" begin
        println("⚙️ Testing Preconditioner Apply Methods...")
        
        n = 10
        x = rand(n)
        y = zeros(n)
        
        # Test IdentityPreconditioner
        identity_precond = JuliaFOAM.LinearSolvers.IdentityPreconditioner()
        JuliaFOAM.LinearSolvers.apply_preconditioner!(y, x, identity_precond)
        @test y ≈ x
        
        # Test DiagonalPreconditioner  
        A = spdiagm(0 => 2.0 * ones(n))
        diag_precond = JuliaFOAM.LinearSolvers.build_diagonal_preconditioner(A)
        JuliaFOAM.LinearSolvers.apply_preconditioner!(y, x, diag_precond)
        @test y ≈ 0.5 * x  # Since preconditioner contains 1/diagonal = 1/2.0 = 0.5
        
        println("✅ All preconditioner apply methods working")
    end
    
    @testset "Linear Solver Suite" begin
        println("🧮 Testing Complete Linear Solver Suite...")
        
        n = 25
        A = spdiagm(0 => 3.0 * ones(n), 1 => -1.0 * ones(n-1), -1 => -1.0 * ones(n-1))
        x_exact = rand(n)
        b = A * x_exact
        precond = JuliaFOAM.LinearSolvers.IdentityPreconditioner()
        settings = JuliaFOAM.LinearSolvers.SolverSettings(tolerance=1e-10, max_iterations=200)
        
        # Test CG
        x_cg = zeros(n)
        iter_cg, res_cg = JuliaFOAM.LinearSolvers.cg_solve!(A, b, x_cg, precond, settings)
        error_cg = norm(x_cg - x_exact) / norm(x_exact)
        
        # Test BiCGSTAB
        x_bicg = zeros(n)
        iter_bicg, res_bicg = JuliaFOAM.LinearSolvers.bicgstab_solve!(A, b, x_bicg, precond, settings)
        error_bicg = norm(x_bicg - x_exact) / norm(x_exact)
        
        # Test GMRES
        x_gmres = zeros(n)
        iter_gmres, res_gmres = JuliaFOAM.LinearSolvers.gmres_solve!(A, b, x_gmres, precond, settings)
        error_gmres = norm(x_gmres - x_exact) / norm(x_exact)
        
        @test error_cg < 1e-8
        @test error_bicg < 1e-6
        @test error_gmres < 1e-8
        
        println("✅ CG: $(iter_cg) iter, BiCGSTAB: $(iter_bicg) iter, GMRES: $(iter_gmres) iter")
    end
    
    @testset "Matrix Cache Reordering" begin
        println("🔄 Testing Matrix Cache Reordering...")
        
        n = 15
        A = sprand(n, n, 0.3) + 5I
        
        A_reordered = JuliaFOAM.MatrixOperations.reorder_matrix_for_cache(A)
        
        @test size(A_reordered) == size(A)
        @test nnz(A_reordered) == nnz(A)
        
        # Check that matrix properties are preserved (looser tolerance for reordering)
        @test abs(det(Matrix(A)) - det(Matrix(A_reordered))) < 1e-3
        
        println("✅ Matrix reordering preserves properties")
    end
    
    @testset "Function Exports and Accessibility" begin
        println("📤 Testing Function Exports...")
        
        # Test DiscretizationSchemes exports
        @test isdefined(JuliaFOAM.DiscretizationSchemes, :compute_cell_gradient_least_squares)
        println("✅ Gradient function exported from DiscretizationSchemes")
        
        # Test KOmegaSSTModel exports
        @test isdefined(JuliaFOAM.KOmegaSSTModel, :compute_strain_rate_magnitude)
        @test isdefined(JuliaFOAM.KOmegaSSTModel, :compute_F2_blending_function)
        @test isdefined(JuliaFOAM.KOmegaSSTModel, :compute_wall_distance)
        println("✅ Turbulence model functions exported")
        
        # Test PostProcessing exports
        @test isdefined(JuliaFOAM.PostProcessing, :find_cell_containing_point)
        @test isdefined(JuliaFOAM.PostProcessing, :find_closest_cell)
        println("✅ PostProcessing functions exported")
        
        # Test MatrixOperations exports
        @test isdefined(JuliaFOAM.MatrixOperations, :reorder_matrix_for_cache)
        @test isdefined(JuliaFOAM.MatrixOperations, :csr_simd_matvec!)
        println("✅ Matrix operations functions exported")
    end
    
    @testset "Integration Test" begin
        println("🔗 Testing Integration of Fixed Components...")
        
        # Create a small test that uses multiple fixed components
        n = 20
        A = spdiagm(0 => 2.0 * ones(n), 1 => -1.0 * ones(n-1), -1 => -1.0 * ones(n-1))
        
        # 1. Reorder matrix for cache optimization
        A_opt = JuliaFOAM.MatrixOperations.reorder_matrix_for_cache(A)
        
        # 2. Set up linear system
        x_exact = ones(n)
        b = A_opt * x_exact
        
        # 3. Solve with GMRES and diagonal preconditioning
        precond = JuliaFOAM.LinearSolvers.DiagonalPreconditioner(diag(A_opt))
        settings = JuliaFOAM.LinearSolvers.SolverSettings(tolerance=1e-10, max_iterations=100)
        
        x = zeros(n)
        iter, residual = JuliaFOAM.LinearSolvers.gmres_solve!(A_opt, b, x, precond, settings)
        
        error = norm(x - x_exact) / norm(x_exact)
        
        @test error < 1e-4
        @test iter < 100
        
        println("✅ Integration test: Cache optimization + GMRES + Preconditioning")
        println("   Matrix reordered ✓ GMRES converged in $(iter) iterations ✓")
    end

end

println("\n🎉 All Fixed Implementation Tests Passed!")
println("📊 Summary of Fixes:")
println("   ✅ GMRES solver preconditioner integration fixed")
println("   ✅ Preconditioner apply_preconditioner! signatures corrected")
println("   ✅ Function exports added to all modules")
println("   ✅ Matrix operations accessible and working")
println("   ✅ Integration between components verified")
println("\n🚀 JuliaFOAM test suite failures resolved!")