#!/usr/bin/env julia

"""
Master Validation Runner for JuliaFOAM

Runs all validation tests in the proper order and generates a comprehensive report.
This is the main entry point for validating JuliaFOAM correctness.
"""

using Printf
using Dates

println("🚀 JuliaFOAM Master Validation Runner")
println("="^80)
println("Started at: $(now())")

# Validation test categories and files
validation_tests = [
    ("Core Mathematics (1D)", "quick_validation_test.jl"),
    ("Core Mathematics (Final)", "core/test_1d_final_math_validation.jl"),
    ("Fix Verification", "fixes/test_fixes_verification.jl"),
    ("Integration Tests", "integration/test_integration.jl"),
    ("Solver Tests", "solvers/test_linear_solvers.jl"),
    ("Turbulence Tests", "turbulence/test_simple_turbulence.jl"),
    ("I/O Tests", "io/test_openfoam_io.jl")
]

# Test results
results = Dict{String, Bool}()
total_tests = 0
passed_tests = 0

function run_validation_test(name, filepath)
    global total_tests, passed_tests
    total_tests += 1
    
    println("\n🧪 Running: $name")
    println("-"^60)
    
    full_path = joinpath(@__DIR__, filepath)
    
    if !isfile(full_path)
        println("⚠️  Test file not found: $filepath")
        results[name] = false
        return false
    end
    
    try
        # Run the test in a separate Julia process to isolate issues
        output = read(`julia $full_path`, String)
        
        # Simple heuristic: if output contains "PASS" or success indicators
        success = occursin("✅ PASS", output) || 
                 occursin("All", output) && occursin("pass", lowercase(output)) ||
                 occursin("SUCCESS", output)
        
        if success
            println("✅ PASSED: $name")
            passed_tests += 1
        else
            println("❌ FAILED: $name")
            println("Output:")
            println(output)
        end
        
        results[name] = success
        return success
        
    catch e
        println("❌ ERROR running $name: $e")
        results[name] = false
        return false
    end
end

function generate_summary_report()
    println("\n" * "="^80)
    println("VALIDATION SUMMARY REPORT")
    println("="^80)
    println("Generated at: $(now())")
    
    @printf "Total Tests: %d\n" total_tests
    @printf "Passed: %d\n" passed_tests
    @printf "Failed: %d\n" (total_tests - passed_tests)
    @printf "Success Rate: %.1f%%\n" (passed_tests / total_tests * 100)
    
    println("\nDetailed Results:")
    for (name, passed) in results
        status = passed ? "✅ PASS" : "❌ FAIL"
        println("  $status  $name")
    end
    
    overall_success = passed_tests == total_tests
    overall_status = overall_success ? "✅ ALL TESTS PASSED" : "❌ SOME TESTS FAILED"
    
    println("\nOVERALL STATUS: $overall_status")
    
    if overall_success
        println("\n🎉 JuliaFOAM Validation Complete!")
        println("   All mathematical operators and components are verified.")
        println("   The system is ready for production CFD simulations!")
    else
        println("\n⚠️  Validation Issues Detected!")
        println("   Some tests failed. Review the detailed output above.")
        println("   Fix issues before using in production.")
    end
    
    return overall_success
end

# Main validation run
println("\nRunning $(length(validation_tests)) validation test suites...")

for (name, filepath) in validation_tests
    run_validation_test(name, filepath)
end

# Generate final report
success = generate_summary_report()

# Exit with appropriate code
exit(success ? 0 : 1)