#!/usr/bin/env julia

"""
Test Script for Turbulence Model Fixes

This script thoroughly tests the turbulence model implementations we fixed
to ensure they work correctly and don't have runtime errors.
"""

using Pkg
Pkg.activate(".")

using LinearAlgebra
using SparseArrays
using StaticArrays
using Printf

# Include the modules we need to test
include("src/JuliaFOAM.jl")
using .JuliaFOAM

include("src/core/Types.jl")
include("src/mesh/Mesh.jl")
include("src/turbulence/KOmegaSSTModel.jl")

println("🧪 Testing Turbulence Model Implementations")
println("="^80)

# Create a simple test mesh
function create_test_mesh()
    # Simple 2D cavity mesh for testing
    n_cells = 10
    L = 1.0
    dx = L / n_cells
    
    cells = Cell[]
    faces = Face[]
    points = Point[]
    
    # Create points
    for j in 0:n_cells
        for i in 0:n_cells
            x = i * dx
            y = j * dx
            push!(points, Point(x, y, 0.0))
        end
    end
    
    # Create cells (simplified)
    for j in 1:n_cells
        for i in 1:n_cells
            center = Point((i-0.5)*dx, (j-0.5)*dx, 0.0)
            volume = dx^2
            # Simplified cell with face indices
            face_indices = [1, 2, 3, 4]  # Placeholder
            push!(cells, Cell(face_indices, center, volume))
        end
    end
    
    # Create faces (simplified)
    for i in 1:20  # Some test faces
        center = Point(0.5, 0.5, 0.0)
        area = SVector{3,Float64}(0.0, dx, 0.0)
        owner = 1
        neighbour = min(i+1, length(cells))
        push!(faces, Face([1,2,3,4], center, area, owner, neighbour))
    end
    
    # Create boundaries
    boundaries = [
        Boundary("walls", "wall", [1, 2, 3, 4]),
        Boundary("inlet", "inlet", [5, 6]),
        Boundary("outlet", "outlet", [7, 8])
    ]
    
    boundary_patches = Dict(
        "walls" => [1, 2, 3, 4],
        "inlet" => [5, 6], 
        "outlet" => [7, 8]
    )
    
    return Mesh(points, faces, cells, boundaries, boundary_patches)
end

function test_wall_distance_computation()
    println("\n🔧 Testing Wall Distance Computation")
    println("-"^50)
    
    try
        mesh = create_test_mesh()
        
        # Test individual cell wall distance
        wall_dist = KOmegaSSTModel.compute_wall_distance(mesh, 1)
        @printf "✓ Wall distance for cell 1: %.6f\n" wall_dist
        
        # Test field computation
        wall_dist_field = KOmegaSSTModel.compute_wall_distance_field(mesh)
        @printf "✓ Wall distance field computed for %d cells\n" length(wall_dist_field)
        @printf "  Min distance: %.6f\n" minimum(wall_dist_field)
        @printf "  Max distance: %.6f\n" maximum(wall_dist_field)
        
        # Verify distances are positive
        if all(wall_dist_field .> 0)
            println("✅ PASS: All wall distances are positive")
        else
            println("❌ FAIL: Some wall distances are non-positive")
            return false
        end
        
        return true
        
    catch e
        println("❌ FAIL: Wall distance computation error: $e")
        return false
    end
end

function test_cross_diffusion_term()
    println("\n🔧 Testing Cross-Diffusion Term Computation")
    println("-"^50)
    
    try
        mesh = create_test_mesh()
        n_cells = length(mesh.cells)
        
        # Create test k and omega fields
        k = rand(n_cells) * 0.01  # Typical k values
        omega = rand(n_cells) * 10.0 + 1.0  # Typical omega values
        
        # Test cross-diffusion computation
        CDkw = KOmegaSSTModel.compute_cross_diffusion_term(k, omega, mesh)
        
        @printf "✓ Cross-diffusion term computed for %d cells\n" length(CDkw)
        @printf "  Min CDkw: %.6e\n" minimum(CDkw)
        @printf "  Max CDkw: %.6e\n" maximum(CDkw)
        
        # Verify CDkw values are reasonable
        if all(CDkw .>= 1e-20) && all(isfinite.(CDkw))
            println("✅ PASS: Cross-diffusion terms are physical")
        else
            println("❌ FAIL: Cross-diffusion terms have non-physical values")
            return false
        end
        
        return true
        
    catch e
        println("❌ FAIL: Cross-diffusion computation error: $e")
        return false
    end
end

function test_scalar_gradient()
    println("\n🔧 Testing Scalar Gradient Computation")
    println("-"^50)
    
    try
        mesh = create_test_mesh()
        n_cells = length(mesh.cells)
        
        # Create a linear test field: f(x,y) = x + 2*y
        field = zeros(n_cells)
        for i in 1:n_cells
            center = mesh.cells[i].center
            field[i] = center[1] + 2.0 * center[2]
        end
        
        # Compute gradient
        grad = KOmegaSSTModel.compute_scalar_gradient(field, mesh, 1)
        
        @printf "✓ Gradient computed: [%.3f, %.3f, %.3f]\n" grad[1] grad[2] grad[3]
        
        # For linear field f = x + 2y, gradient should be approximately [1, 2, 0]
        expected_grad = SVector{3,Float64}(1.0, 2.0, 0.0)
        error = norm(grad - expected_grad)
        @printf "  Expected: [1.0, 2.0, 0.0], Error: %.3f\n" error
        
        if error < 0.5  # Allow some discretization error
            println("✅ PASS: Gradient computation is approximately correct")
        else
            println("❌ FAIL: Gradient computation has large error")
            return false
        end
        
        return true
        
    catch e
        println("❌ FAIL: Scalar gradient computation error: $e")
        return false
    end
end

function test_diffusion_term()
    println("\n🔧 Testing Diffusion Term Computation")
    println("-"^50)
    
    try
        mesh = create_test_mesh()
        n_cells = length(mesh.cells)
        
        # Create test field
        field = rand(n_cells)
        
        # Test parameters
        sigma = 1.0
        nu = 1e-5
        nu_t = 1e-3
        
        # Compute diffusion term
        diffusion = KOmegaSSTModel.compute_diffusion_term(field, mesh, 1, sigma, nu, nu_t)
        
        @printf "✓ Diffusion term computed: %.6e\n" diffusion
        
        # Verify it's finite
        if isfinite(diffusion)
            println("✅ PASS: Diffusion term is finite")
        else
            println("❌ FAIL: Diffusion term is not finite")
            return false
        end
        
        return true
        
    catch e
        println("❌ FAIL: Diffusion term computation error: $e")
        return false
    end
end

function test_komega_sst_update()
    println("\n🔧 Testing K-Omega SST Model Update")
    println("-"^50)
    
    try
        mesh = create_test_mesh()
        n_cells = length(mesh.cells)
        
        # Create turbulence fields
        k = Field{Float64}(ones(n_cells) * 0.01, Dict{String, Vector{Float64}}())
        omega = Field{Float64}(ones(n_cells) * 10.0, Dict{String, Vector{Float64}}())
        nu_t = ones(n_cells) * 1e-3
        
        # Create velocity field
        U = Field{SVector{3,Float64}}(
            [SVector{3,Float64}(1.0, 0.0, 0.0) for _ in 1:n_cells],
            Dict{String, Vector{SVector{3,Float64}}}()
        )
        
        # Fluid properties
        properties = (kinematic_viscosity = 1e-5, density = 1.0)
        
        dt = 0.001
        
        # Store initial values
        k_initial = copy(k.internal_field)
        omega_initial = copy(omega.internal_field)
        
        # Call the update function
        KOmegaSSTModel.update_komega_sst_fields!(k, omega, nu_t, U, mesh, dt, properties)
        
        @printf "✓ K-Omega SST update completed\n"
        @printf "  K range: [%.6e, %.6e]\n" minimum(k.internal_field) maximum(k.internal_field)
        @printf "  Omega range: [%.6e, %.6e]\n" minimum(omega.internal_field) maximum(omega.internal_field)
        
        # Check that values remained positive
        if all(k.internal_field .> 0) && all(omega.internal_field .> 0)
            println("✅ PASS: Turbulence fields remain positive")
        else
            println("❌ FAIL: Turbulence fields became negative")
            return false
        end
        
        # Check that values are finite
        if all(isfinite.(k.internal_field)) && all(isfinite.(omega.internal_field))
            println("✅ PASS: Turbulence fields are finite")
        else
            println("❌ FAIL: Turbulence fields contain non-finite values")
            return false
        end
        
        return true
        
    catch e
        println("❌ FAIL: K-Omega SST update error: $e")
        println("  Stack trace:")
        for line in split(sprint(Base.showerror, e, catch_backtrace()), '\n')
            println("    $line")
        end
        return false
    end
end

function run_all_turbulence_tests()
    println("\n🏁 Running All Turbulence Tests")
    println("="^80)
    
    tests = [
        ("Wall Distance", test_wall_distance_computation),
        ("Cross-Diffusion", test_cross_diffusion_term),
        ("Scalar Gradient", test_scalar_gradient),
        ("Diffusion Term", test_diffusion_term),
        ("K-Omega SST Update", test_komega_sst_update)
    ]
    
    passed = 0
    total = length(tests)
    
    for (name, test_func) in tests
        if test_func()
            passed += 1
        end
    end
    
    println("\n" * "="^80)
    println("TURBULENCE TEST SUMMARY")
    println("="^80)
    @printf "Tests passed: %d/%d\n" passed total
    
    if passed == total
        println("🎉 ALL TURBULENCE TESTS PASSED!")
        return true
    else
        println("💥 SOME TURBULENCE TESTS FAILED!")
        return false
    end
end

# Run the tests
if abspath(PROGRAM_FILE) == @__FILE__
    success = run_all_turbulence_tests()
    exit(success ? 0 : 1)
end