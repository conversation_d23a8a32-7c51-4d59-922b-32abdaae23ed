#!/usr/bin/env julia

"""
Standalone test for the memory profiler that doesn't require JuliaFOAM compilation.
"""

using Test
using Profile
using BenchmarkTools
using Printf
using Statistics
using Dates

# Include the memory profiler directly
include("../src/tools/MemoryProfiler.jl")
using .MemoryProfiler

@testset "Standalone Memory Profiler Tests" begin
    
    @testset "Basic Memory Profiling" begin
        # Test simple function profiling
        function test_allocation_function(n::Int)
            # Deliberately allocate memory
            arrays = [rand(100) for _ in 1:n]
            return sum(sum(arr) for arr in arrays)
        end
        
        # Profile the function
        profile, result = profile_memory_usage(test_allocation_function, 10; 
                                             profile_name="test_allocation")
        
        @test profile.name == "test_allocation"
        @test profile.end_time !== nothing
        @test profile.peak_memory >= profile.baseline_memory
        @test haskey(profile.gc_stats, "total_allocations")
        @test haskey(profile.gc_stats, "allocd")
        
        # Test that result is returned correctly
        @test result isa Float64
        @test result > 0
        
        println("✓ Basic profiling test passed")
        println("  Peak memory: $(round((profile.peak_memory - profile.baseline_memory) / 1024^2, digits=2)) MB")
        println("  Total allocations: $(profile.gc_stats["total_allocations"])")
    end
    
    @testset "Allocation Analysis" begin
        # Create a test function with known allocation pattern
        function allocation_heavy_function()
            # Multiple allocation patterns
            a = rand(1000)  # Large allocation
            b = [rand(10) for _ in 1:100]  # Many small allocations
            c = zeros(500)  # Zero allocation
            return sum(a) + sum(sum(arr) for arr in b) + sum(c)
        end
        
        # Profile and analyze
        profile, _ = profile_memory_usage(allocation_heavy_function; 
                                        profile_name="allocation_analysis")
        analysis = analyze_allocations(profile)
        
        @test analysis isa ProfileResult
        @test analysis.profile_name == "allocation_analysis"
        @test analysis.total_allocations >= 0
        @test analysis.peak_memory_mb >= 0
        @test analysis.gc_time_percentage >= 0
        @test length(analysis.allocation_hotspots) >= 0
        @test length(analysis.recommendations) >= 0
        
        # Test that hotspots are sorted by allocation count
        if length(analysis.allocation_hotspots) > 1
            for i in 1:(length(analysis.allocation_hotspots)-1)
                @test analysis.allocation_hotspots[i][2] >= analysis.allocation_hotspots[i+1][2]
            end
        end
        
        println("✓ Allocation analysis test passed")
        println("  Peak memory: $(round(analysis.peak_memory_mb, digits=2)) MB")
        println("  GC time: $(round(analysis.gc_time_percentage, digits=2))%")
        println("  Recommendations: $(length(analysis.recommendations))")
    end
    
    @testset "Memory Report Generation" begin
        # Create a simple test case
        function simple_test_function()
            return sum(rand(100))
        end
        
        profile, _ = profile_memory_usage(simple_test_function; 
                                        profile_name="report_test")
        analysis = analyze_allocations(profile)
        
        # Generate report
        report_file = "test_memory_report.md"
        create_memory_report(analysis, report_file)
        
        # Verify report file exists and has content
        @test isfile(report_file)
        content = read(report_file, String)
        @test contains(content, "Memory Analysis Report")
        @test contains(content, "report_test")
        @test contains(content, "Summary")
        @test contains(content, "Allocation Hotspots")
        @test contains(content, "Optimization Recommendations")
        
        println("✓ Memory report generation test passed")
        println("  Report saved to: $report_file")
        
        # Clean up
        rm(report_file, force=true)
    end
    
    @testset "Benchmark Memory Performance" begin
        # Test benchmarking functionality
        function benchmark_test_function(n::Int)
            return sum(rand(n))
        end
        
        # Run benchmark with small sample size for testing
        println("Running benchmark analysis...")
        results = benchmark_memory_performance(benchmark_test_function, 100; 
                                             samples=3, profile_name="benchmark_test")
        
        @test length(results) == 3
        @test all(r -> r isa ProfileResult, results)
        @test all(r -> contains(r.profile_name, "benchmark_test"), results)
        
        println("✓ Benchmark memory performance test passed")
    end
    
    @testset "Allocation Hotspot Tracking" begin
        # Create multiple test functions with different allocation patterns
        function low_allocation_function()
            return sum(1:100)  # No allocations
        end
        
        function medium_allocation_function()
            return sum(rand(100))  # Small allocation
        end
        
        function high_allocation_function()
            return sum(rand(1000))  # Large allocation
        end
        
        functions = [low_allocation_function, medium_allocation_function, high_allocation_function]
        args_list = [(), (), ()]  # No arguments for any function
        
        println("Tracking allocation hotspots...")
        results = track_allocation_hotspots(functions, args_list; 
                                          profile_name="hotspot_test")
        
        @test length(results) == 3
        @test all(r -> r isa ProfileResult, results)
        
        println("✓ Allocation hotspot tracking test passed")
    end
    
    @testset "Error Handling" begin
        # Test error handling for incomplete profiles
        incomplete_profile = MemoryProfile("incomplete")
        @test_throws ErrorException analyze_allocations(incomplete_profile)
        
        # Test error handling for mismatched function/args lists
        functions = [x -> x^2, x -> x^3]
        args_list = [(2,)]  # Only one argument list for two functions
        
        @test_throws ErrorException track_allocation_hotspots(functions, args_list)
        
        println("✓ Error handling test passed")
    end
    
    @testset "Performance Thresholds" begin
        # Test recommendation generation based on performance thresholds
        
        # Create a high-allocation function to trigger recommendations
        function high_gc_function()
            arrays = [rand(1000) for _ in 1:100]  # Force many allocations
            GC.gc()  # Force garbage collection
            return sum(sum(arr) for arr in arrays)
        end
        
        profile, _ = profile_memory_usage(high_gc_function; 
                                        profile_name="high_gc_test")
        analysis = analyze_allocations(profile)
        
        # Should have some recommendations due to allocations
        @test length(analysis.recommendations) >= 0
        
        println("✓ Performance thresholds test passed")
        println("  Recommendations generated: $(length(analysis.recommendations))")
        for (i, rec) in enumerate(analysis.recommendations)
            println("    $i. $rec")
        end
    end
end

println("\n" * "="^60)
println("Memory Profiler Test Summary")
println("="^60)
println("All tests passed successfully!")
println("The memory profiler is ready for use in JuliaFOAM development.")
println("="^60)
