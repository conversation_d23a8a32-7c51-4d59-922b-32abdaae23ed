"""
Comprehensive unit tests for all parallel components in JuliaFOAM
"""

using Test
using LinearAlgebra
using Statistics

# Add the source directory to the path
push!(LOAD_PATH, joinpath(@__DIR__, "..", "..", "src"))

# Import all parallel modules
include("../../src/parallel/MeshPartitioning.jl")
include("../../src/parallel/TransparentParallel.jl")
include("../../src/parallel/ParallelIO.jl")
include("../../src/IO/OpenFOAMImporter.jl")
include("../../src/mesh/UnstructuredMesh.jl")

using .MeshPartitioning
using .TransparentParallel
using .ParallelIO
using .OpenFOAMImporter
using .UnstructuredMesh

@testset "Comprehensive Parallel Component Tests" begin
    
    @testset "METIS Integration Tests" begin
        println("🧪 Testing METIS Integration...")
        
        # Test METIS availability check
        @test hasmethod(is_metis_available, ())
        metis_available = is_metis_available()
        @test isa(metis_available, Bool)
        println("   METIS available: $metis_available")
        
        # Test graph construction
        n_vertices = 10
        adjacency = [
            [2, 3], [1, 4], [1, 5], [2, 6], [3, 7],
            [4, 8], [5, 9], [6, 10], [7], [8]
        ]
        
        graph = construct_metis_graph(n_vertices, adjacency)
        @test isa(graph, Dict)
        @test haskey(graph, "n_vertices")
        @test haskey(graph, "adjacency")
        @test graph["n_vertices"] == n_vertices
        
        # Test partitioning
        n_parts = 3
        partition = partition_graph_metis(graph, n_parts)
        @test isa(partition, Vector{Int})
        @test length(partition) == n_vertices
        @test all(1 .<= partition .<= n_parts)
        
        # Test partition quality
        quality = assess_partition_quality(partition, adjacency)
        @test isa(quality, Dict)
        @test haskey(quality, "edge_cut")
        @test haskey(quality, "imbalance")
        @test quality["edge_cut"] >= 0
        @test quality["imbalance"] >= 0.0
        
        println("   ✅ METIS integration tests passed")
    end
    
    @testset "Mesh Partitioning Tests" begin
        println("🧪 Testing Mesh Partitioning...")
        
        # Create a simple test mesh
        points = [
            Point3D(0.0, 0.0, 0.0), Point3D(1.0, 0.0, 0.0),
            Point3D(2.0, 0.0, 0.0), Point3D(0.0, 1.0, 0.0),
            Point3D(1.0, 1.0, 0.0), Point3D(2.0, 1.0, 0.0)
        ]
        
        face_point_lists = [
            [1, 2, 5, 4], [2, 3, 6, 5]  # Two adjacent cells
        ]
        
        face_owners = [1, 2]
        face_neighbors = [2, 1]
        boundary_patches = [UnstructuredBoundaryPatch("walls", :wall, [1, 2], 1, 2)]
        
        test_mesh = Mesh(points, face_point_lists, face_owners, face_neighbors, boundary_patches)
        
        # Test mesh partitioning
        n_partitions = 2
        partitioned_mesh = partition_mesh(test_mesh, n_partitions)
        
        @test isa(partitioned_mesh, Dict)
        @test haskey(partitioned_mesh, "partitions")
        @test haskey(partitioned_mesh, "halo_info")
        @test haskey(partitioned_mesh, "partition_quality")
        
        partitions = partitioned_mesh["partitions"]
        @test length(partitions) == n_partitions
        
        # Test that all cells are assigned
        all_cells = Set{Int}()
        for partition in partitions
            union!(all_cells, partition["cells"])
        end
        @test length(all_cells) == length(test_mesh.cells)
        
        println("   ✅ Mesh partitioning tests passed")
    end
    
    @testset "MPI Infrastructure Tests" begin
        println("🧪 Testing MPI Infrastructure...")
        
        # Test MPI initialization
        with_parallel() do comm, rank, nprocs
            @test rank == 0  # Single process test
            @test nprocs == 1
            
            # Test distributed field creation
            n_cells = 50
            mock_mesh = Dict(
                :cells => [Dict(:id => i) for i in 1:n_cells],
                :cell_partition => zeros(Int, n_cells),
                :halo_cells => Int[]
            )
            
            # Test scalar field
            scalar_field = create_distributed_field("temperature", mock_mesh, 300.0, comm=comm)
            @test length(local_values(scalar_field)) == n_cells
            @test all(local_values(scalar_field) .== 300.0)
            
            # Test field operations
            sync!(scalar_field)
            @test !scalar_field.needs_sync
            
            # Test global operations
            field_sum = parallel_sum(scalar_field)
            @test field_sum ≈ n_cells * 300.0
            
            field_max = parallel_max(scalar_field)
            @test field_max ≈ 300.0
            
            field_norm = parallel_norm(scalar_field)
            @test field_norm ≈ 300.0 * sqrt(n_cells)
            
            # Test asynchronous communication
            sync_async!(scalar_field)
            wait_sync!(scalar_field)
            @test !scalar_field.needs_sync
        end
        
        println("   ✅ MPI infrastructure tests passed")
    end
    
    @testset "Field I/O Tests" begin
        println("🧪 Testing Field I/O...")
        
        # Create temporary directory for I/O tests
        test_dir = mktempdir()
        
        # Test scalar field I/O
        scalar_data = Dict{String, Any}(
            "class" => "volScalarField",
            "object" => "T",
            "dimensions" => "[0 0 0 1 0 0 0]",
            "internal_field" => Dict("type" => "uniform", "value" => 300.0),
            "boundary_field" => Dict{String, Any}(
                "inlet" => Dict("type" => "fixedValue", "value" => 350.0),
                "outlet" => Dict("type" => "zeroGradient")
            )
        )
        
        scalar_file = joinpath(test_dir, "T")
        write_openfoam_field_file(scalar_file, scalar_data)
        @test isfile(scalar_file)
        
        # Read back and verify
        read_scalar = read_openfoam_field_file(scalar_file)
        @test read_scalar["class"] == "volScalarField"
        @test read_scalar["internal_field"]["value"] == 300.0
        
        # Test vector field I/O
        vector_data = Dict{String, Any}(
            "class" => "volVectorField",
            "object" => "U",
            "dimensions" => "[0 1 -1 0 0 0 0]",
            "internal_field" => Dict("type" => "uniform", "value" => [1.0, 0.0, 0.0]),
            "boundary_field" => Dict{String, Any}(
                "inlet" => Dict("type" => "fixedValue", "value" => [2.0, 0.0, 0.0]),
                "walls" => Dict("type" => "fixedValue", "value" => [0.0, 0.0, 0.0])
            )
        )
        
        vector_file = joinpath(test_dir, "U")
        write_openfoam_field_file(vector_file, vector_data)
        @test isfile(vector_file)
        
        read_vector = read_openfoam_field_file(vector_file)
        @test read_vector["class"] == "volVectorField"
        @test read_vector["internal_field"]["value"] == [1.0, 0.0, 0.0]
        
        # Test parallel field I/O
        test_parallel_field_io(test_dir, 2)  # Test with 2 processors
        
        # Cleanup
        rm(test_dir, recursive=true)
        
        println("   ✅ Field I/O tests passed")
    end
    
    @testset "OpenFOAM File Reading Tests" begin
        println("🧪 Testing OpenFOAM File Reading...")
        
        test_dir = mktempdir()
        
        # Test case type detection
        serial_case = joinpath(test_dir, "serial_case")
        mkpath(joinpath(serial_case, "constant", "polyMesh"))
        
        case_type, n_procs = detect_case_type(serial_case)
        @test case_type == :serial
        @test n_procs == 1
        
        # Test decomposed case detection
        decomposed_case = joinpath(test_dir, "decomposed_case")
        mkpath(decomposed_case)
        
        for i in 0:2
            proc_dir = joinpath(decomposed_case, "processor$i")
            mkpath(joinpath(proc_dir, "constant", "polyMesh"))
        end
        
        case_type, n_procs = detect_case_type(decomposed_case)
        @test case_type == :decomposed
        @test n_procs == 3
        
        # Test processor directory listing
        proc_dirs = list_processor_directories(decomposed_case)
        @test length(proc_dirs) == 3
        @test "processor0" in proc_dirs
        
        # Test addressing file reading
        addr_file = joinpath(test_dir, "cellProcAddressing")
        addr_content = """
FoamFile
{
    version     2.0;
    format      ascii;
    class       labelList;
    object      cellProcAddressing;
}

3
(
0
1
2
)
"""
        open(addr_file, "w") do f
            write(f, addr_content)
        end
        
        addresses = read_addressing_file(addr_file)
        @test length(addresses) == 3
        @test addresses == [1, 2, 3]  # Converted from 0-based
        
        rm(test_dir, recursive=true)
        
        println("   ✅ OpenFOAM file reading tests passed")
    end
    
    @testset "Complex Geometry Support Tests" begin
        println("🧪 Testing Complex Geometry Support...")
        
        # Create a test mesh with potential issues
        points = [
            Point3D(0.0, 0.0, 0.0), Point3D(1.0, 0.0, 0.0),
            Point3D(0.5, 1.0, 0.0), Point3D(0.5, 0.5, 1.0)
        ]
        
        face_point_lists = [
            [1, 2, 3], [1, 4, 2], [2, 4, 3], [3, 4, 1]
        ]
        
        face_owners = [1, 1, 1, 1]
        face_neighbors = [-1, -1, -1, -1]
        boundary_patches = [UnstructuredBoundaryPatch("walls", :wall, [1, 2, 3, 4], 1, 4)]
        
        test_mesh = Mesh(points, face_point_lists, face_owners, face_neighbors, boundary_patches)
        
        # Test complex geometry validation
        issues = validate_complex_geometry(test_mesh)
        @test isa(issues, Vector{String})
        
        # Test mesh repair
        repaired_mesh = repair_mesh_connectivity(test_mesh)
        @test isa(repaired_mesh, Mesh)
        @test length(repaired_mesh.points) == length(test_mesh.points)
        
        # Test mesh optimization
        optimized_mesh = optimize_mesh_topology(test_mesh)
        @test isa(optimized_mesh, Mesh)
        
        println("   ✅ Complex geometry support tests passed")
    end
end

"""
Helper function to test parallel field I/O
"""
function test_parallel_field_io(test_dir::String, n_procs::Int)
    # Create mock processor directories
    for i in 0:(n_procs-1)
        proc_dir = joinpath(test_dir, "processor$i")
        mkpath(joinpath(proc_dir, "0"))
        
        # Create mock field file
        field_data = Dict{String, Any}(
            "class" => "volScalarField",
            "object" => "p",
            "dimensions" => "[0 2 -2 0 0 0 0]",
            "internal_field" => Dict("type" => "uniform", "value" => Float64(i)),
            "boundary_field" => Dict{String, Any}()
        )
        
        field_file = joinpath(proc_dir, "0", "p")
        write_openfoam_field_file(field_file, field_data)
    end
    
    # Test that files were created
    for i in 0:(n_procs-1)
        field_file = joinpath(test_dir, "processor$i", "0", "p")
        @test isfile(field_file)
    end
end

println("Comprehensive parallel component tests completed!")
