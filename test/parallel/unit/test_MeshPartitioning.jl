"""
Unit tests for MeshPartitioning.jl

Tests all partitioning methods and quality metrics.
"""

using Test
using LinearAlgebra
using Statistics

# Include the module path
push!(LOAD_PATH, joinpath(@__DIR__, "../../../src/parallel"))
push!(LOAD_PATH, joinpath(@__DIR__, ".."))

using MeshPartitioning
using TestUtilities

@testset "MeshPartitioning.jl Tests" begin
    
    # ========================================================================
    # SIMPLE PARTITION TESTS
    # ========================================================================
    
    @testset "SimplePartition" begin
        
        @testset "2D Structured Mesh" begin
            # Create test mesh
            mesh = create_test_mesh_2d(10, 10)
            
            @testset "X-direction partitioning" begin
                method = SimplePartition(4, :x)
                partition_info = partition_mesh(mesh, method)
                
                # Verify basic properties
                @test partition_info.partition.n_subdomains == 4
                @test partition_info.partition.n_cells == mesh.n_cells
                @test length(partition_info.partition.cell_processor) == mesh.n_cells
                
                # Check load balance
                cells_per_proc = [length(cells) for cells in partition_info.partition.processor_cells]
                @test all(c -> c == 25, cells_per_proc)  # 100 cells / 4 procs = 25
                @test partition_info.partition.load_imbalance ≈ 1.0
                
                # Verify partitioning pattern
                # In X-direction, cells 1-25 → proc 0, 26-50 → proc 1, etc.
                for i in 1:100
                    expected_proc = min(div(mod(i-1, 10), 3), 3)  # 10 cells in X, ~3 per proc
                    # This is simplified - actual mapping depends on implementation
                end
            end
            
            @testset "Y-direction partitioning" begin
                method = SimplePartition(5, :y)
                partition_info = partition_mesh(mesh, method)
                
                @test partition_info.partition.n_subdomains == 5
                cells_per_proc = [length(cells) for cells in partition_info.partition.processor_cells]
                @test all(c -> c == 20, cells_per_proc)  # 100 cells / 5 procs = 20
            end
            
            @testset "XY-direction partitioning" begin
                method = SimplePartition(4, :xy)
                partition_info = partition_mesh(mesh, method)
                
                @test partition_info.partition.n_subdomains == 4
                @test partition_info.partition.load_imbalance ≈ 1.0
                
                # Check connectivity pattern
                @test length(partition_info.processor_neighbors) == 4
                # Each processor should have 2 neighbors in 2x2 layout (except corners)
            end
        end
        
        @testset "3D Structured Mesh" begin
            mesh = create_test_mesh_3d(6, 6, 6)
            
            @testset "XYZ partitioning" begin
                method = SimplePartition(8, :xyz)
                partition_info = partition_mesh(mesh, method)
                
                @test partition_info.partition.n_subdomains == 8
                @test partition_info.partition.n_cells == 216  # 6^3
                
                # Perfect load balance for 216 cells / 8 procs = 27 cells each
                cells_per_proc = [length(cells) for cells in partition_info.partition.processor_cells]
                @test all(c -> c == 27, cells_per_proc)
                @test partition_info.partition.load_imbalance ≈ 1.0
            end
        end
        
        @testset "Edge cases" begin
            mesh = create_test_mesh_2d(10, 10)
            
            # Single processor
            method = SimplePartition(1, :x)
            partition_info = partition_mesh(mesh, method)
            @test partition_info.partition.n_subdomains == 1
            @test all(p -> p == 0, partition_info.partition.cell_processor)
            @test partition_info.partition.n_interface_faces == 0
            
            # More processors than cells (should still work)
            small_mesh = create_test_mesh_2d(2, 2)  # 4 cells
            method = SimplePartition(8, :xy)
            partition_info = partition_mesh(small_mesh, method)
            @test partition_info.partition.n_subdomains == 8
            # Some processors will be empty
            cells_per_proc = [length(cells) for cells in partition_info.partition.processor_cells]
            @test sum(cells_per_proc) == 4
            @test count(c -> c == 0, cells_per_proc) == 4  # 4 empty processors
        end
    end
    
    # ========================================================================
    # METIS PARTITION TESTS
    # ========================================================================
    
    @testset "MetisPartition" begin
        
        @testset "Basic METIS partitioning" begin
            mesh = create_test_mesh_2d(10, 10)
            
            # K-way partitioning
            method = MetisPartition(4, method=:kway)
            partition_info = partition_mesh(mesh, method)
            
            @test partition_info.partition.n_subdomains == 4
            @test partition_info.partition.n_cells == 100
            
            # Check load balance (METIS should give good balance)
            @test partition_info.partition.load_imbalance < 1.1  # Within 10%
            
            # Check that edge cut is reasonable
            @test partition_info.partition.edge_cut > 0  # Some cuts needed
            @test partition_info.partition.edge_cut < 50  # Not too many
        end
        
        @testset "Recursive bisection" begin
            mesh = create_test_mesh_2d(8, 8)
            
            method = MetisPartition(4, method=:recursive)
            partition_info = partition_mesh(mesh, method)
            
            @test partition_info.partition.n_subdomains == 4
            @test partition_info.partition.load_imbalance < 1.1
        end
        
        @testset "Face weights" begin
            mesh = create_test_mesh_2d(10, 10)
            
            # Without face weights
            method1 = MetisPartition(4, face_weights=false)
            partition_info1 = partition_mesh(mesh, method1)
            
            # With face weights
            method2 = MetisPartition(4, face_weights=true)
            partition_info2 = partition_mesh(mesh, method2)
            
            # Both should work, might give different results
            @test partition_info1.partition.n_subdomains == 4
            @test partition_info2.partition.n_subdomains == 4
            
            # Face weights might give different edge cut
            # (Can't guarantee which is better without specific mesh)
        end
        
        @testset "Imbalance tolerance" begin
            mesh = create_test_mesh_2d(10, 10)
            
            # Tight balance
            method1 = MetisPartition(4, imbalance=1.01)  # 1% imbalance
            partition_info1 = partition_mesh(mesh, method1)
            @test partition_info1.partition.load_imbalance <= 1.01
            
            # Loose balance
            method2 = MetisPartition(4, imbalance=1.20)  # 20% imbalance
            partition_info2 = partition_mesh(mesh, method2)
            @test partition_info2.partition.load_imbalance <= 1.20
            
            # Looser balance might give better edge cut
            @test partition_info2.partition.edge_cut <= partition_info1.partition.edge_cut
        end
        
        @testset "Unstructured mesh" begin
            mesh = create_unstructured_test_mesh(200)
            
            method = MetisPartition(8)
            partition_info = partition_mesh(mesh, method)
            
            @test partition_info.partition.n_subdomains == 8
            @test partition_info.partition.n_cells == 200
            @test partition_info.partition.load_imbalance < 1.2  # Reasonable balance
        end
    end
    
    # ========================================================================
    # HIERARCHICAL PARTITION TESTS
    # ========================================================================
    
    @testset "HierarchicalPartition" begin
        
        @testset "Two-level hierarchy" begin
            mesh = create_test_mesh_3d(8, 8, 8)  # 512 cells
            
            method = HierarchicalPartition(16, 2)  # 16 procs, 2 levels
            partition_info = partition_mesh(mesh, method)
            
            @test partition_info.partition.n_subdomains == 16
            @test partition_info.partition.n_cells == 512
            
            # Check that all cells are assigned
            @test length(unique(partition_info.partition.cell_processor)) == 16
            
            # Load balance should be reasonable
            @test partition_info.partition.load_imbalance < 1.3
        end
        
        @testset "Three-level hierarchy" begin
            mesh = create_test_mesh_3d(8, 8, 8)
            
            # 8 processors = 2×2×2
            method = HierarchicalPartition(8, 3)
            partition_info = partition_mesh(mesh, method)
            
            @test partition_info.partition.n_subdomains == 8
            cells_per_proc = [length(cells) for cells in partition_info.partition.processor_cells]
            @test all(c -> c == 64, cells_per_proc)  # Perfect balance: 512/8 = 64
        end
    end
    
    # ========================================================================
    # MANUAL PARTITION TESTS
    # ========================================================================
    
    @testset "ManualPartition" begin
        mesh = create_test_mesh_2d(4, 4)  # 16 cells
        
        # Create manual mapping
        cell_processor_map = zeros(Int, 16)
        # Assign cells in checkerboard pattern
        for i in 1:16
            row = div(i-1, 4)
            col = mod(i-1, 4)
            cell_processor_map[i] = mod(row + col, 2)
        end
        
        method = ManualPartition(cell_processor_map)
        partition_info = partition_mesh(mesh, method)
        
        @test partition_info.partition.n_subdomains == 2
        @test partition_info.partition.cell_processor == cell_processor_map
        
        # Check that we have 8 cells per processor
        cells_per_proc = [length(cells) for cells in partition_info.partition.processor_cells]
        @test all(c -> c == 8, cells_per_proc)
    end
    
    # ========================================================================
    # PARTITION QUALITY TESTS
    # ========================================================================
    
    @testset "Partition Quality Metrics" begin
        mesh = create_test_mesh_2d(10, 10)
        method = MetisPartition(4)
        partition_info = partition_mesh(mesh, method)
        
        # Check that all quality metrics are computed
        metrics = partition_info.quality_metrics
        
        required_metrics = [
            "load_imbalance",
            "load_variance",
            "edge_cut",
            "interface_faces",
            "avg_neighbors",
            "max_neighbors",
            "avg_halo_size",
            "max_halo_size",
            "total_halo_cells",
            "comm_volume",
            "parallel_efficiency"
        ]
        
        for metric in required_metrics
            @test haskey(metrics, metric)
            @test metrics[metric] >= 0  # All metrics should be non-negative
        end
        
        # Sanity checks
        @test metrics["load_imbalance"] >= 1.0  # Always >= 1
        @test metrics["avg_neighbors"] <= 4  # Max 4 neighbors in 2D grid
        @test metrics["parallel_efficiency"] <= 1.0  # Efficiency <= 100%
        
        # Test quality analysis output
        # This should not error
        analyze_partition_quality(partition_info)
    end
    
    # ========================================================================
    # INTERFACE CONSISTENCY TESTS
    # ========================================================================
    
    @testset "Interface Consistency" begin
        mesh = create_test_mesh_2d(8, 8)
        method = SimplePartition(4, :xy)
        partition_info = partition_mesh(mesh, method)
        
        # Check interface faces
        for (face_id, proc1, proc2) in partition_info.interface_faces
            @test proc1 != proc2  # Different processors
            @test 0 <= proc1 < 4  # Valid processor IDs
            @test 0 <= proc2 < 4
            
            # Check that processors are neighbors
            @test proc2 in partition_info.processor_neighbors[proc1+1]
            @test proc1 in partition_info.processor_neighbors[proc2+1]
        end
        
        # Check halo cells
        for proc in 0:3
            halos = partition_info.halo_cells[proc+1]
            owned_cells = Set(partition_info.partition.processor_cells[proc+1])
            
            # Halo cells should not be owned by this processor
            @test isempty(intersect(halos, owned_cells))
            
            # Halo cells should be owned by neighbor processors
            for halo_cell in halos
                owner_proc = partition_info.partition.cell_processor[halo_cell]
                @test owner_proc in partition_info.processor_neighbors[proc+1]
            end
        end
    end
    
    # ========================================================================
    # ERROR HANDLING TESTS
    # ========================================================================
    
    @testset "Error Handling" begin
        mesh = create_test_mesh_2d(10, 10)
        
        # Invalid processor count
        @test_throws AssertionError SimplePartition(0, :x)
        @test_throws AssertionError SimplePartition(-1, :x)
        
        # Invalid direction
        @test_throws AssertionError SimplePartition(4, :invalid)
        
        # Invalid METIS method
        @test_throws AssertionError MetisPartition(4, method=:invalid)
        
        # Invalid imbalance
        @test_throws AssertionError MetisPartition(4, imbalance=0.5)
        
        # Manual partition with wrong size
        wrong_map = zeros(Int, 50)  # Wrong size for 100 cell mesh
        method = ManualPartition(wrong_map)
        @test_throws AssertionError partition_mesh(mesh, method)
    end
    
    # ========================================================================
    # PERFORMANCE TESTS
    # ========================================================================
    
    @testset "Performance" begin
        # Test that partitioning completes in reasonable time
        mesh = create_test_mesh_3d(20, 20, 20)  # 8000 cells
        
        # Simple partition should be fast
        method = SimplePartition(8, :xyz)
        @time partition_info = partition_mesh(mesh, method)
        @test partition_info.partition.n_cells == 8000
        
        # METIS should handle larger meshes
        method = MetisPartition(16)
        @time partition_info = partition_mesh(mesh, method)
        @test partition_info.partition.n_cells == 8000
    end
end