"""
Unit tests for DecomposePar.jl

Tests domain decomposition functionality including mesh and field decomposition.
"""

using Test
using LinearAlgebra
using JSON

# Include the module paths
push!(LOAD_PATH, joinpath(@__DIR__, "../../../src/parallel"))
push!(LOAD_PATH, joinpath(@__DIR__, ".."))

using DecomposePar
using MeshPartitioning
using TestUtilities

@testset "DecomposePar.jl Tests" begin
    
    # ========================================================================
    # CONFIGURATION TESTS
    # ========================================================================
    
    @testset "DecomposeConfig" begin
        
        @testset "Default configuration" begin
            method = SimplePartition(4, :xyz)
            config = DecomposeConfig(method=method)
            
            @test config.method == method
            @test config.output_dir == "."
            @test config.preserve_patches == true
            @test config.face_weight_method == :uniform
            @test config.write_graph == false
            @test config.force == false
            @test isempty(config.time_dirs)
            @test isempty(config.fields)
            @test config.parallel_io == false
            @test config.verbose == true
        end
        
        @testset "Custom configuration" begin
            method = MetisPartition(8, face_weights=true)
            config = DecomposeConfig(
                method=method,
                output_dir="/tmp/test",
                preserve_patches=false,
                face_weight_method=:area,
                write_graph=true,
                force=true,
                time_dirs=["0", "100"],
                fields=["U", "p"],
                parallel_io=true,
                verbose=false
            )
            
            @test config.method == method
            @test config.output_dir == "/tmp/test"
            @test config.preserve_patches == false
            @test config.face_weight_method == :area
            @test config.write_graph == true
            @test config.force == true
            @test config.time_dirs == ["0", "100"]
            @test config.fields == ["U", "p"]
            @test config.parallel_io == true
            @test config.verbose == false
        end
    end
    
    # ========================================================================
    # BASIC DECOMPOSITION TESTS
    # ========================================================================
    
    @testset "Basic Decomposition" begin
        
        @testset "Simple 2D decomposition" begin
            # Create temporary test directory
            test_dir = mktempdir()
            mkpath(joinpath(test_dir, "constant", "polyMesh"))
            
            # Create test mesh
            mesh = create_test_mesh_2d(8, 8)
            
            # Mock mesh I/O functions
            DecomposePar.eval(quote
                function read_mesh(mesh_path::String)
                    return $mesh
                end
                
                function write_processor_mesh(mesh_dir::String, proc_mesh::Dict)
                    # Mock implementation - just check it's called
                    return nothing
                end
                
                function write_partition_data(file_path::String, partition_info)
                    # Mock implementation
                    return nothing
                end
            end)
            
            # Configure decomposition
            method = SimplePartition(4, :xy)
            config = DecomposeConfig(
                method=method,
                force=true,
                verbose=false
            )
            
            # Run decomposition
            decomp_info = decompose_par(test_dir, config)
            
            # Verify results
            @test decomp_info.n_processors == 4
            @test decomp_info.case_dir == test_dir
            @test occursin("SimplePartition", decomp_info.method)
            
            # Check processor directories were created
            for proc in 0:3
                proc_dir = joinpath(test_dir, "processor$proc")
                @test isdir(proc_dir)
                @test isdir(joinpath(proc_dir, "constant", "polyMesh"))
                @test isdir(joinpath(proc_dir, "system"))
            end
            
            # Check decomposition info was written
            info_file = joinpath(test_dir, "system", "decomposeParDict.json")
            @test isfile(info_file)
            
            # Read and verify JSON
            info_dict = JSON.parsefile(info_file)
            @test info_dict["n_processors"] == 4
            @test haskey(info_dict, "decomposition_time")
            @test haskey(info_dict, "mesh_stats")
            
            # Cleanup
            rm(test_dir, recursive=true)
        end
        
        @testset "Force overwrite" begin
            test_dir = mktempdir()
            mkpath(joinpath(test_dir, "constant", "polyMesh"))
            
            # Create existing processor directories
            for proc in 0:1
                mkpath(joinpath(test_dir, "processor$proc"))
            end
            
            mesh = create_test_mesh_2d(4, 4)
            DecomposePar.eval(:(read_mesh(path::String) = $mesh))
            
            # Without force - should error
            method = SimplePartition(2, :x)
            config = DecomposeConfig(method=method, force=false, verbose=false)
            @test_throws ErrorException decompose_par(test_dir, config)
            
            # With force - should succeed
            config_force = DecomposeConfig(method=method, force=true, verbose=false)
            decomp_info = decompose_par(test_dir, config_force)
            @test decomp_info.n_processors == 2
            
            # Cleanup
            rm(test_dir, recursive=true)
        end
    end
    
    # ========================================================================
    # MESH DECOMPOSITION TESTS
    # ========================================================================
    
    @testset "Mesh Decomposition" begin
        
        @testset "Cell distribution" begin
            mesh = create_test_mesh_2d(8, 8)  # 64 cells
            method = SimplePartition(4, :xy)
            partition_info = partition_mesh(mesh, method)
            
            # Test extract_processor_mesh
            config = DecomposeConfig(method=method, preserve_patches=true)
            
            for proc in 0:3
                proc_mesh = DecomposePar.extract_processor_mesh(
                    mesh, partition_info, proc, config
                )
                
                # Check basic properties
                @test haskey(proc_mesh, "points")
                @test haskey(proc_mesh, "faces")
                @test haskey(proc_mesh, "cells")
                @test haskey(proc_mesh, "n_cells")
                @test haskey(proc_mesh, "n_halo_cells")
                
                # Verify cell counts
                proc_cells = partition_info.partition.processor_cells[proc+1]
                @test proc_mesh["n_cells"] == length(proc_cells)
                
                # Check mappings
                @test haskey(proc_mesh, "global_to_local_cell")
                @test haskey(proc_mesh, "global_to_local_point")
                
                # All processor cells should be in mapping
                for global_cell in proc_cells
                    @test haskey(proc_mesh["global_to_local_cell"], global_cell)
                end
            end
        end
        
        @testset "Boundary preservation" begin
            mesh = create_test_mesh_2d(8, 8)
            
            # Add boundary patches
            mesh.boundary_patches["inlet"] = Dict(
                "type" => "patch",
                "faces" => [1, 2, 3, 4]
            )
            mesh.boundary_patches["outlet"] = Dict(
                "type" => "patch", 
                "faces" => [61, 62, 63, 64]
            )
            
            method = SimplePartition(4, :xy)
            partition_info = partition_mesh(mesh, method)
            config = DecomposeConfig(method=method, preserve_patches=true)
            
            # Extract processor meshes
            for proc in 0:3
                proc_mesh = DecomposePar.extract_processor_mesh(
                    mesh, partition_info, proc, config
                )
                
                # Should have boundary patches
                @test haskey(proc_mesh, "boundary_patches")
                
                # Check if processor has any boundary faces
                if !isempty(proc_mesh["boundary_patches"])
                    for (name, patch) in proc_mesh["boundary_patches"]
                        @test haskey(patch, "type")
                        @test haskey(patch, "faces")
                    end
                end
            end
        end
    end
    
    # ========================================================================
    # FIELD DECOMPOSITION TESTS
    # ========================================================================
    
    @testset "Field Decomposition" begin
        
        @testset "Scalar field decomposition" begin
            mesh = create_test_mesh_2d(4, 4)  # 16 cells
            fields = create_test_fields(mesh)
            
            method = SimplePartition(2, :x)
            partition_info = partition_mesh(mesh, method)
            
            # Get scalar field
            p_field = first(filter(f -> f.name == "p", fields))
            
            # Create field dict format
            field_dict = Dict(
                "name" => p_field.name,
                "type" => p_field.type,
                "dimensions" => p_field.dimensions,
                "data" => p_field.data
            )
            
            # Mock read_field
            DecomposePar.eval(:(read_field(path::String) = $field_dict))
            
            # Extract for each processor
            for proc in 0:1
                proc_field = DecomposePar.extract_processor_field(
                    field_dict, partition_info, proc
                )
                
                # Check structure
                @test proc_field["name"] == "p"
                @test proc_field["type"] == "volScalarField"
                @test haskey(proc_field, "data")
                @test haskey(proc_field, "n_cells")
                @test haskey(proc_field, "n_halo_cells")
                
                # Check data size
                proc_cells = partition_info.partition.processor_cells[proc+1]
                halo_cells = partition_info.halo_cells[proc+1]
                total_cells = length(proc_cells) + length(halo_cells)
                @test length(proc_field["data"]) == total_cells
            end
        end
        
        @testset "Vector field decomposition" begin
            mesh = create_test_mesh_2d(4, 4)
            fields = create_test_fields(mesh)
            
            method = SimplePartition(2, :x)
            partition_info = partition_mesh(mesh, method)
            
            # Get vector field
            U_field = first(filter(f -> f.name == "U", fields))
            
            field_dict = Dict(
                "name" => U_field.name,
                "type" => U_field.type,
                "dimensions" => U_field.dimensions,
                "data" => U_field.data
            )
            
            for proc in 0:1
                proc_field = DecomposePar.extract_processor_field(
                    field_dict, partition_info, proc
                )
                
                @test proc_field["type"] == "volVectorField"
                
                # Check vector data
                for vec in proc_field["data"]
                    @test isa(vec, Vector)
                    @test length(vec) == 3  # 3D vector
                end
            end
        end
    end
    
    # ========================================================================
    # TIME DIRECTORY TESTS
    # ========================================================================
    
    @testset "Time Directory Handling" begin
        
        @testset "Time directory detection" begin
            test_dir = mktempdir()
            
            # Create time directories
            mkpath(joinpath(test_dir, "0"))
            mkpath(joinpath(test_dir, "0.5"))
            mkpath(joinpath(test_dir, "1"))
            mkpath(joinpath(test_dir, "10"))
            mkpath(joinpath(test_dir, "system"))  # Should not be detected
            mkpath(joinpath(test_dir, "constant"))  # Should not be detected
            
            # Test detection
            time_dirs = DecomposePar.determine_time_directories(test_dir, String[])
            
            @test "0" in time_dirs
            @test "0.5" in time_dirs
            @test "1" in time_dirs
            @test "10" in time_dirs
            @test !("system" in time_dirs)
            @test !("constant" in time_dirs)
            
            # Check sorting
            @test time_dirs == ["0", "0.5", "1", "10"]
            
            # Test with specified directories
            specified = ["0", "10"]
            time_dirs2 = DecomposePar.determine_time_directories(test_dir, specified)
            @test time_dirs2 == specified
            
            # Cleanup
            rm(test_dir, recursive=true)
        end
        
        @testset "Field detection in time directory" begin
            test_dir = mktempdir()
            time_dir = joinpath(test_dir, "0")
            mkpath(time_dir)
            
            # Create field files
            touch(joinpath(time_dir, "U"))
            touch(joinpath(time_dir, "p"))
            touch(joinpath(time_dir, "T"))
            touch(joinpath(time_dir, ".hidden"))  # Should be ignored
            
            # Test detection
            fields = DecomposePar.get_fields_to_decompose(time_dir, String[])
            
            @test "U" in fields
            @test "p" in fields
            @test "T" in fields
            @test !(".hidden" in fields)
            
            # Test with specified fields
            specified = ["U", "p", "nonexistent"]
            fields2 = DecomposePar.get_fields_to_decompose(time_dir, specified)
            @test "U" in fields2
            @test "p" in fields2
            @test !("nonexistent" in fields2)  # Filtered out
            
            # Cleanup
            rm(test_dir, recursive=true)
        end
    end
    
    # ========================================================================
    # DECOMPOSITION INFO I/O TESTS
    # ========================================================================
    
    @testset "Decomposition Info I/O" begin
        
        @testset "Write and read decomposition info" begin
            test_dir = mktempdir()
            mkpath(joinpath(test_dir, "system"))
            
            # Create test partition info
            mesh = create_test_mesh_2d(4, 4)
            method = SimplePartition(2, :x)
            partition_info = partition_mesh(mesh, method)
            
            # Create decomposition info
            decomp_info = DecompositionInfo(
                2,  # n_processors
                "SimplePartition",
                partition_info,
                Dict("n_cells" => 16, "n_faces" => 40),
                1.23,  # decomposition_time
                test_dir,
                ["0", "1"]
            )
            
            # Write info
            DecomposePar.write_decomposition_info(test_dir, decomp_info)
            
            # Check files exist
            json_file = joinpath(test_dir, "system", "decomposeParDict.json")
            @test isfile(json_file)
            
            # Read JSON and verify
            info_dict = JSON.parsefile(json_file)
            @test info_dict["n_processors"] == 2
            @test info_dict["method"] == "SimplePartition"
            @test info_dict["decomposition_time"] ≈ 1.23
            @test info_dict["time_dirs"] == ["0", "1"]
            @test haskey(info_dict, "partition_quality")
            
            # Note: read_decomposition_info would fail without proper 
            # partition data serialization, which is mocked
            
            # Cleanup
            rm(test_dir, recursive=true)
        end
    end
    
    # ========================================================================
    # PARALLEL DECOMPOSITION TESTS
    # ========================================================================
    
    @testset "Parallel Decomposition" begin
        
        @testset "Work distribution" begin
            # Test work distribution function
            items = ["0", "1", "2", "3", "4", "5", "6", "7"]
            
            # 4 ranks
            dist0 = DecomposePar.distribute_work(items, 0, 4)
            dist1 = DecomposePar.distribute_work(items, 1, 4)
            dist2 = DecomposePar.distribute_work(items, 2, 4)
            dist3 = DecomposePar.distribute_work(items, 3, 4)
            
            # Each should get 2 items
            @test length(dist0) == 2
            @test length(dist1) == 2
            @test length(dist2) == 2
            @test length(dist3) == 2
            
            # No overlap
            all_distributed = vcat(dist0, dist1, dist2, dist3)
            @test length(unique(all_distributed)) == 8
            @test sort(all_distributed) == items
            
            # Test uneven distribution
            items2 = ["0", "1", "2", "3", "4"]
            dist0_uneven = DecomposePar.distribute_work(items2, 0, 3)
            dist1_uneven = DecomposePar.distribute_work(items2, 1, 3)
            dist2_uneven = DecomposePar.distribute_work(items2, 2, 3)
            
            @test length(dist0_uneven) == 2  # Gets extra
            @test length(dist1_uneven) == 2  # Gets extra
            @test length(dist2_uneven) == 1
        end
    end
    
    # ========================================================================
    # ERROR HANDLING TESTS
    # ========================================================================
    
    @testset "Error Handling" begin
        
        @testset "Missing directories" begin
            test_dir = mktempdir()
            
            # No mesh directory
            method = SimplePartition(2, :x)
            config = DecomposeConfig(method=method, verbose=false)
            
            # Mock read_mesh to throw error
            DecomposePar.eval(:(read_mesh(path::String) = error("Mesh not found")))
            
            @test_throws ErrorException decompose_par(test_dir, config)
            
            # Cleanup
            rm(test_dir, recursive=true)
        end
        
        @testset "Invalid configuration" begin
            # This is handled at partition method level
            @test_throws AssertionError SimplePartition(0, :x)
            @test_throws AssertionError SimplePartition(-1, :x)
        end
    end
    
    # ========================================================================
    # UTILITY FUNCTION TESTS  
    # ========================================================================
    
    @testset "Utility Functions" begin
        
        @testset "Mesh statistics" begin
            mesh = create_test_mesh_2d(10, 10)
            
            # Add some properties for testing
            mesh.cells = [(center=[0.0, 0.0, 0.0], volume=0.01) for _ in 1:100]
            
            stats = DecomposePar.get_mesh_stats(mesh)
            
            @test stats["n_cells"] == 100
            @test haskey(stats, "n_faces")
            @test haskey(stats, "n_points")
            
            # With boundary patches
            if !isempty(mesh.boundary_patches)
                @test haskey(stats, "n_patches")
                @test haskey(stats, "patch_names")
            end
        end
        
        @testset "Processor directory checking" begin
            test_dir = mktempdir()
            
            # No directories exist
            @test !DecomposePar.check_processor_dirs_exist(test_dir, 4)
            
            # Create one directory
            mkpath(joinpath(test_dir, "processor0"))
            @test DecomposePar.check_processor_dirs_exist(test_dir, 4)
            
            # Cleanup
            rm(test_dir, recursive=true)
        end
    end
end