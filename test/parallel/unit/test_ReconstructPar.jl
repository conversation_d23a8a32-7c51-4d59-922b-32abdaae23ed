"""
Unit tests for ReconstructPar.jl

Tests mesh and field reconstruction functionality.
"""

using Test
using LinearAlgebra
using Statistics

# Include the module paths
push!(LOAD_PATH, joinpath(@__DIR__, "../../../src/parallel"))
push!(LOAD_PATH, joinpath(@__DIR__, ".."))

using ReconstructPar
using MeshPartitioning
using DecomposePar
using TestUtilities

@testset "ReconstructPar.jl Tests" begin
    
    # ========================================================================
    # CONFIGURATION TESTS
    # ========================================================================
    
    @testset "ReconstructConfig" begin
        
        @testset "Default configuration" begin
            config = ReconstructConfig()
            
            @test isempty(config.time_dirs)
            @test isempty(config.fields)
            @test config.remove_processor_dirs == false
            @test config.tolerance ≈ 1e-10
            @test config.verify == true
            @test config.parallel_io == false
            @test config.skip_mesh == false
            @test config.verbose == true
        end
        
        @testset "Custom configuration" begin
            config = ReconstructConfig(
                time_dirs=["0", "100"],
                fields=["U", "p"],
                remove_processor_dirs=true,
                tolerance=1e-8,
                verify=false,
                parallel_io=true,
                skip_mesh=true,
                verbose=false
            )
            
            @test config.time_dirs == ["0", "100"]
            @test config.fields == ["U", "p"]
            @test config.remove_processor_dirs == true
            @test config.tolerance ≈ 1e-8
            @test config.verify == false
            @test config.parallel_io == true
            @test config.skip_mesh == true
            @test config.verbose == false
        end
    end
    
    # ========================================================================
    # MESH RECONSTRUCTION TESTS
    # ========================================================================
    
    @testset "Mesh Reconstruction" begin
        
        @testset "Simple 2D mesh reconstruction" begin
            # Create and decompose a simple mesh
            original_mesh = create_test_mesh_2d(8, 8)  # 64 cells
            method = SimplePartition(4, :xy)
            partition_info = partition_mesh(original_mesh, method)
            
            # Extract processor meshes
            processor_meshes = []
            for proc in 0:3
                proc_mesh = DecomposePar.extract_processor_mesh(
                    original_mesh, partition_info, proc, 
                    DecomposeConfig(method=method)
                )
                push!(processor_meshes, proc_mesh)
            end
            
            # Mock read functions
            ReconstructPar.eval(quote
                function read_processor_meshes(case_dir::String, n_procs::Int)
                    return $processor_meshes
                end
                
                function read_decomposition_info(case_dir::String)
                    return DecompositionInfo(
                        4, "SimplePartition", $partition_info,
                        Dict("n_cells" => 64), 0.0, case_dir, String[]
                    )
                end
            end)
            
            # Reconstruct
            global_mesh = ReconstructPar.reconstruct_mesh(processor_meshes, partition_info)
            
            # Verify reconstruction
            @test global_mesh["n_cells"] == original_mesh.n_cells
            @test length(global_mesh["cells"]) == original_mesh.n_cells
            @test length(global_mesh["points"]) == length(original_mesh.points)
            
            # Check point deduplication worked
            unique_points = unique(global_mesh["points"])
            @test length(unique_points) == length(global_mesh["points"])
        end
        
        @testset "Point stitching" begin
            # Test point deduplication with tolerance
            tolerance = 1e-10
            
            # Create points with duplicates
            points1 = [[0.0, 0.0, 0.0], [1.0, 0.0, 0.0], [1.0, 1.0, 0.0]]
            points2 = [[0.999999999, 0.0, 0.0], [2.0, 0.0, 0.0], [1.0, 1.0, 0.0]]
            
            # Point 2 in points1 should match point 1 in points2 (within tolerance)
            all_points = vcat(points1, points2)
            
            # Deduplicate
            unique_points, point_map = ReconstructPar.deduplicate_points(all_points, tolerance)
            
            @test length(unique_points) == 4  # One duplicate removed
            @test point_map[2] == point_map[4]  # These map to same unique point
        end
        
        @testset "Interface face removal" begin
            # Create two processor meshes with shared interface
            proc0_faces = [
                (points=[1, 2, 3, 4], owner=1, neighbor=-1, boundary="procBoundary0to1")
            ]
            proc1_faces = [
                (points=[5, 6, 7, 8], owner=1, neighbor=-1, boundary="procBoundary1to0")
            ]
            
            processor_meshes = [
                Dict("faces" => proc0_faces, "boundary_patches" => Dict(
                    "procBoundary0to1" => Dict("type" => "processor")
                )),
                Dict("faces" => proc1_faces, "boundary_patches" => Dict(
                    "procBoundary1to0" => Dict("type" => "processor")
                ))
            ]
            
            # Filter interface faces
            global_faces = ReconstructPar.reconstruct_faces(processor_meshes)
            
            # Interface faces should be internal (no boundary)
            for face in global_faces
                if haskey(face, :boundary)
                    @test !startswith(face.boundary, "procBoundary")
                end
            end
        end
    end
    
    # ========================================================================
    # FIELD RECONSTRUCTION TESTS
    # ========================================================================
    
    @testset "Field Reconstruction" begin
        
        @testset "Scalar field reconstruction" begin
            # Create original field
            n_cells = 16
            original_data = [100.0 + i for i in 1:n_cells]
            original_field = TestField("p", "volScalarField", "[Pa]", original_data)
            
            # Simulate decomposition to 4 processors (4 cells each)
            proc_fields = []
            for proc in 0:3
                start_idx = proc * 4 + 1
                end_idx = (proc + 1) * 4
                proc_data = original_data[start_idx:end_idx]
                
                proc_field = Dict(
                    "name" => "p",
                    "type" => "volScalarField",
                    "dimensions" => "[Pa]",
                    "data" => proc_data,
                    "n_cells" => 4,
                    "n_halo_cells" => 0
                )
                push!(proc_fields, proc_field)
            end
            
            # Create partition info
            cell_mapping = Dict{Int,Vector{Tuple{Int,Int}}}()
            for global_cell in 1:n_cells
                proc = div(global_cell - 1, 4)
                local_cell = mod(global_cell - 1, 4) + 1
                cell_mapping[global_cell] = [(proc + 1, local_cell)]
            end
            
            # Reconstruct
            global_field = ReconstructPar.reconstruct_field(proc_fields, cell_mapping)
            
            # Verify
            @test global_field["name"] == "p"
            @test global_field["type"] == "volScalarField"
            @test length(global_field["data"]) == n_cells
            @test global_field["data"] == original_data
        end
        
        @testset "Vector field reconstruction" begin
            # Create vector field
            n_cells = 12
            original_data = [[1.0, 0.1 * i, 0.0] for i in 1:n_cells]
            
            # Decompose to 3 processors
            proc_fields = []
            for proc in 0:2
                start_idx = proc * 4 + 1
                end_idx = (proc + 1) * 4
                proc_data = original_data[start_idx:end_idx]
                
                proc_field = Dict(
                    "name" => "U",
                    "type" => "volVectorField",
                    "dimensions" => "[m/s]",
                    "data" => proc_data,
                    "n_cells" => 4,
                    "n_halo_cells" => 0
                )
                push!(proc_fields, proc_field)
            end
            
            # Create mapping
            cell_mapping = Dict{Int,Vector{Tuple{Int,Int}}}()
            for global_cell in 1:n_cells
                proc = div(global_cell - 1, 4)
                local_cell = mod(global_cell - 1, 4) + 1
                cell_mapping[global_cell] = [(proc + 1, local_cell)]
            end
            
            # Reconstruct
            global_field = ReconstructPar.reconstruct_field(proc_fields, cell_mapping)
            
            # Verify
            @test global_field["type"] == "volVectorField"
            @test length(global_field["data"]) == n_cells
            for i in 1:n_cells
                @test global_field["data"][i] == original_data[i]
            end
        end
        
        @testset "Field with halo cells" begin
            # Test reconstruction when processors have halo cells
            # Halo cells should not appear in reconstructed field
            
            proc_fields = []
            for proc in 0:1
                # Each processor has 4 owned cells + 2 halo cells
                proc_data = [Float64(proc * 10 + i) for i in 1:6]
                
                proc_field = Dict(
                    "name" => "T",
                    "type" => "volScalarField",
                    "dimensions" => "[K]",
                    "data" => proc_data,
                    "n_cells" => 4,
                    "n_halo_cells" => 2
                )
                push!(proc_fields, proc_field)
            end
            
            # Mapping for 8 global cells
            cell_mapping = Dict{Int,Vector{Tuple{Int,Int}}}()
            for global_cell in 1:8
                proc = div(global_cell - 1, 4)
                local_cell = mod(global_cell - 1, 4) + 1
                cell_mapping[global_cell] = [(proc + 1, local_cell)]
            end
            
            # Reconstruct
            global_field = ReconstructPar.reconstruct_field(proc_fields, cell_mapping)
            
            # Should only have 8 cells (no halos)
            @test length(global_field["data"]) == 8
        end
    end
    
    # ========================================================================
    # VERIFICATION TESTS
    # ========================================================================
    
    @testset "Reconstruction Verification" begin
        
        @testset "Conservation check" begin
            # Create fields with known sums
            n_cells = 20
            original_data = [1.0 for _ in 1:n_cells]  # Sum = 20.0
            
            # Decompose to 4 processors
            proc_fields = []
            for proc in 0:3
                n_proc_cells = proc < 3 ? 5 : 5  # Even distribution
                proc_data = [1.0 for _ in 1:n_proc_cells]
                
                proc_field = Dict(
                    "name" => "rho",
                    "type" => "volScalarField", 
                    "data" => proc_data,
                    "n_cells" => n_proc_cells
                )
                push!(proc_fields, proc_field)
            end
            
            # Mock processor field reading
            ReconstructPar.eval(quote
                function read_processor_fields(case_dir::String, time_dir::String, 
                                            field_name::String, n_procs::Int)
                    return $proc_fields
                end
            end)
            
            # Create simple partition info
            partition = Partition(n_cells, 4, zeros(Int, n_cells), 
                                [Int[] for _ in 1:4], 0, 0, 1.0)
            partition_info = PartitionInfo(
                partition, [Set{Int}() for _ in 1:4], Tuple{Int,Int,Int}[],
                [Set{Int}() for _ in 1:4], nothing, Dict()
            )
            
            # Test conservation verification
            conserved, total_before, total_after = ReconstructPar.verify_field_conservation(
                proc_fields, partition_info
            )
            
            @test conserved
            @test total_before ≈ 20.0
            @test total_after ≈ 20.0
        end
        
        @testset "Continuity check" begin
            # Test that interface values match
            # This is simplified - real implementation would check actual interfaces
            
            proc0_data = [1.0, 2.0, 3.0, 4.0]  # Last value at interface
            proc1_data = [4.0, 5.0, 6.0, 7.0]  # First value at interface
            
            # Check interface continuity
            interface_value_0 = proc0_data[end]
            interface_value_1 = proc1_data[1]
            
            @test interface_value_0 ≈ interface_value_1
        end
    end
    
    # ========================================================================
    # COMPLETE RECONSTRUCTION WORKFLOW
    # ========================================================================
    
    @testset "Complete Reconstruction Workflow" begin
        test_case_dir = mktempdir()
        
        try
            # Setup case
            mkpath(joinpath(test_case_dir, "system"))
            for proc in 0:3
                mkpath(joinpath(test_case_dir, "processor$proc", "0"))
            end
            
            # Mock all necessary functions
            ReconstructPar.eval(quote
                function read_decomposition_info(case_dir::String)
                    partition = Partition(16, 4, zeros(Int, 16), 
                                       [collect(1:4), collect(5:8), 
                                        collect(9:12), collect(13:16)],
                                       0, 0, 1.0)
                    partition_info = PartitionInfo(
                        partition, [Set{Int}() for _ in 1:4],
                        Tuple{Int,Int,Int}[], [Set{Int}() for _ in 1:4],
                        SimplePartition(4, :xy), Dict("load_imbalance" => 1.0)
                    )
                    return DecompositionInfo(4, "SimplePartition", partition_info,
                                           Dict("n_cells" => 16), 0.0, case_dir, ["0"])
                end
                
                function write_global_mesh(mesh_dir::String, global_mesh::Dict)
                    return nothing
                end
                
                function write_field(field_path::String, field_data::Dict)
                    return nothing
                end
            end)
            
            # Run reconstruction
            config = ReconstructConfig(
                time_dirs=["0"],
                verify=true,
                verbose=false
            )
            
            stats = reconstruct_par(test_case_dir, config)
            
            # Check stats
            @test stats.n_cells_reconstructed > 0
            @test stats.verification_passed
            @test stats.reconstruction_time >= 0.0
            
        finally
            rm(test_case_dir, recursive=true)
        end
    end
    
    # ========================================================================
    # ERROR HANDLING TESTS
    # ========================================================================
    
    @testset "Error Handling" begin
        
        @testset "Missing processor directories" begin
            test_case_dir = mktempdir()
            
            try
                # Only create some processor directories
                mkpath(joinpath(test_case_dir, "processor0"))
                mkpath(joinpath(test_case_dir, "processor1"))
                # Missing processor2 and processor3
                
                config = ReconstructConfig(verbose=false)
                
                # Should error when trying to read missing processors
                @test_throws ErrorException reconstruct_par(test_case_dir, config)
                
            finally
                rm(test_case_dir, recursive=true)
            end
        end
        
        @testset "Incompatible processor data" begin
            # Test handling of mismatched processor fields
            proc_fields = [
                Dict("name" => "p", "type" => "volScalarField", "data" => [1.0, 2.0]),
                Dict("name" => "p", "type" => "volVectorField", "data" => [[1.0, 0.0, 0.0]])  # Wrong type!
            ]
            
            # Should detect type mismatch
            @test_throws AssertionError ReconstructPar.check_field_compatibility(proc_fields)
        end
    end
    
    # ========================================================================
    # PERFORMANCE TESTS
    # ========================================================================
    
    @testset "Performance" begin
        
        @testset "Large mesh reconstruction" begin
            # Create a larger mesh
            n_cells = 10000
            n_procs = 16
            
            # Create processor meshes
            processor_meshes = []
            cells_per_proc = div(n_cells, n_procs)
            
            for proc in 0:n_procs-1
                proc_mesh = Dict(
                    "n_cells" => cells_per_proc,
                    "cells" => collect(1:cells_per_proc),
                    "points" => [[Float64(i), 0.0, 0.0] for i in 1:cells_per_proc+1],
                    "faces" => [],
                    "global_to_local_cell" => Dict(),
                    "global_to_local_point" => Dict()
                )
                push!(processor_meshes, proc_mesh)
            end
            
            # Time reconstruction
            partition = Partition(n_cells, n_procs, zeros(Int, n_cells),
                                [Int[] for _ in 1:n_procs], 0, 0, 1.0)
            partition_info = PartitionInfo(
                partition, [Set{Int}() for _ in 1:n_procs],
                Tuple{Int,Int,Int}[], [Set{Int}() for _ in 1:n_procs],
                nothing, Dict()
            )
            
            @time global_mesh = ReconstructPar.reconstruct_mesh(processor_meshes, partition_info)
            
            @test global_mesh["n_cells"] == n_cells
        end
    end
end