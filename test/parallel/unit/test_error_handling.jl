"""
Error handling and edge case tests for parallel module

Tests robustness of the parallel decomposition system under various
error conditions and edge cases.
"""

using Test
using LinearAlgebra

# Include the module paths
push!(LOAD_PATH, joinpath(@__DIR__, "../../../src/parallel"))
push!(LOAD_PATH, joinpath(@__DIR__, ".."))

using MeshPartitioning
using ProcessorBoundaries
using DecomposePar
using ReconstructPar
using RedistributePar
using LoadBalancing
using TestUtilities

@testset "Error Handling and Edge Cases" begin
    
    # ========================================================================
    # INVALID INPUT TESTS
    # ========================================================================
    
    @testset "Invalid Inputs" begin
        
        @testset "Invalid processor counts" begin
            # Zero processors
            @test_throws AssertionError SimplePartition(0, :x)
            @test_throws AssertionError MetisPartition(0)
            @test_throws AssertionError HierarchicalPartition(0, 2)
            
            # Negative processors
            @test_throws AssertionError SimplePartition(-1, :x)
            @test_throws AssertionError MetisPartition(-5)
            
            # Too many processors
            mesh = create_test_mesh_2d(2, 2)  # 4 cells
            method = SimplePartition(10, :xy)  # 10 procs > 4 cells
            
            # Should still work, but some processors will be empty
            partition_info = partition_mesh(mesh, method)
            @test partition_info.partition.n_subdomains == 10
            
            # Count empty processors
            cells_per_proc = [length(cells) for cells in partition_info.partition.processor_cells]
            empty_procs = count(c -> c == 0, cells_per_proc)
            @test empty_procs == 6  # 10 - 4 = 6 empty
        end
        
        @testset "Invalid decomposition directions" begin
            @test_throws AssertionError SimplePartition(4, :invalid)
            @test_throws AssertionError SimplePartition(4, :xyzw)  # 4D not supported
            @test_throws MethodError SimplePartition(4, "xyz")  # String instead of symbol
        end
        
        @testset "Invalid partition methods" begin
            # Invalid METIS method
            @test_throws AssertionError MetisPartition(4, method=:invalid)
            
            # Invalid imbalance tolerance
            @test_throws AssertionError MetisPartition(4, imbalance=0.5)   # Too low
            @test_throws AssertionError MetisPartition(4, imbalance=2.5)   # Too high
            
            # Invalid hierarchical levels
            @test_throws AssertionError HierarchicalPartition(8, 0)   # Zero levels
            @test_throws AssertionError HierarchicalPartition(8, 10)  # Too many levels
        end
        
        @testset "Manual partition errors" begin
            mesh = create_test_mesh_2d(4, 4)  # 16 cells
            
            # Wrong size mapping
            wrong_size_map = zeros(Int, 10)  # Should be 16
            method = ManualPartition(wrong_size_map)
            @test_throws AssertionError partition_mesh(mesh, method)
            
            # Invalid processor IDs
            invalid_map = [-1, 0, 1, 2, 0, 1, 2, 3, 0, 1, 2, 3, 0, 1, 2, 3]
            method = ManualPartition(invalid_map)
            @test_throws AssertionError partition_mesh(mesh, method)
            
            # Missing processors
            missing_proc_map = [0, 0, 0, 0, 2, 2, 2, 2, 3, 3, 3, 3, 4, 4, 4, 4]  # No proc 1
            method = ManualPartition(missing_proc_map)
            @test_throws AssertionError partition_mesh(mesh, method)
        end
    end
    
    # ========================================================================
    # MESH EDGE CASES
    # ========================================================================
    
    @testset "Mesh Edge Cases" begin
        
        @testset "Single cell mesh" begin
            # Create mesh with 1 cell
            mesh = create_test_mesh_2d(1, 1)
            
            # Single processor
            method = SimplePartition(1, :x)
            partition_info = partition_mesh(mesh, method)
            @test partition_info.partition.n_cells == 1
            @test partition_info.partition.cell_processor[1] == 0
            @test partition_info.partition.n_interface_faces == 0
            
            # Multiple processors (only proc 0 gets the cell)
            method = SimplePartition(4, :xy)
            partition_info = partition_mesh(mesh, method)
            @test partition_info.partition.cell_processor[1] == 0
            @test count(isempty, partition_info.partition.processor_cells[2:4]) == 3
        end
        
        @testset "Empty mesh" begin
            # Create empty mesh
            empty_mesh = TestMesh(0, 0, 0, [], [], [], Dict(), nothing, nothing, nothing)
            
            method = SimplePartition(2, :x)
            partition_info = partition_mesh(empty_mesh, method)
            
            @test partition_info.partition.n_cells == 0
            @test isempty(partition_info.partition.cell_processor)
            @test all(isempty, partition_info.partition.processor_cells)
        end
        
        @testset "Highly skewed mesh" begin
            # Very thin mesh (100x1)
            mesh = create_test_mesh_2d(100, 1)
            
            # Try to decompose in Y direction (only 1 cell)
            method = SimplePartition(4, :y)
            partition_info = partition_mesh(mesh, method)
            
            # Should fallback to X direction or handle gracefully
            @test partition_info.partition.n_subdomains == 4
            # All cells should be assigned
            @test all(p -> 0 <= p < 4, partition_info.partition.cell_processor)
        end
        
        @testset "Disconnected mesh regions" begin
            # Create mesh with disconnected regions
            # This is simulated by having cells with no shared faces
            n_cells = 10
            cells = [(center=rand(3), volume=0.1) for _ in 1:n_cells]
            faces = []  # No faces connecting cells
            
            disconnected_mesh = TestMesh(
                n_cells, 0, n_cells,
                [rand(3) for _ in 1:n_cells],
                faces, cells,
                Dict(), nothing, nothing, nothing
            )
            
            # METIS should handle disconnected components
            method = MetisPartition(2)
            partition_info = partition_mesh(disconnected_mesh, method)
            
            @test partition_info.partition.n_cells == n_cells
            @test all(p -> p in [0, 1], partition_info.partition.cell_processor)
        end
    end
    
    # ========================================================================
    # I/O ERROR CASES
    # ========================================================================
    
    @testset "I/O Errors" begin
        
        @testset "Missing directories" begin
            test_dir = mktempdir()
            
            try
                # Try to decompose without mesh directory
                method = SimplePartition(2, :x)
                config = DecomposeConfig(method=method, verbose=false)
                
                # Mock read_mesh to throw appropriate error
                DecomposePar.eval(:(read_mesh(path::String) = error("No such directory")))
                
                @test_throws ErrorException decompose_par(test_dir, config)
                
                # Try to reconstruct without processor directories
                recon_config = ReconstructConfig(verbose=false)
                @test_throws ErrorException reconstruct_par(test_dir, recon_config)
                
            finally
                rm(test_dir, recursive=true)
            end
        end
        
        @testset "Corrupted decomposition info" begin
            test_dir = mktempdir()
            mkpath(joinpath(test_dir, "system"))
            
            try
                # Write invalid JSON
                open(joinpath(test_dir, "system", "decomposeParDict.json"), "w") do f
                    write(f, "{ invalid json }")
                end
                
                # Mock to test error handling
                DecomposePar.eval(quote
                    function read_decomposition_info(case_dir::String)
                        error("Invalid decomposition info")
                    end
                end)
                
                @test_throws ErrorException DecomposePar.read_decomposition_info(test_dir)
                
            finally
                rm(test_dir, recursive=true)
            end
        end
        
        @testset "Permission errors" begin
            if Sys.isunix()  # Only test on Unix-like systems
                test_dir = mktempdir()
                restricted_dir = joinpath(test_dir, "restricted")
                mkpath(restricted_dir)
                
                try
                    # Make directory read-only
                    chmod(restricted_dir, 0o444)
                    
                    # Try to create processor directories
                    method = SimplePartition(2, :x)
                    config = DecomposeConfig(method=method, output_dir=restricted_dir)
                    
                    # This should fail due to permissions
                    # Note: actual behavior depends on OS and filesystem
                    
                finally
                    # Restore permissions for cleanup
                    chmod(restricted_dir, 0o755)
                    rm(test_dir, recursive=true)
                end
            end
        end
    end
    
    # ========================================================================
    # NUMERICAL EDGE CASES
    # ========================================================================
    
    @testset "Numerical Edge Cases" begin
        
        @testset "Zero-volume cells" begin
            mesh = create_test_mesh_2d(4, 4)
            
            # Set some cells to have zero volume
            mesh.cells[1] = (center=[0.0, 0.0, 0.0], volume=0.0)
            mesh.cells[5] = (center=[0.5, 0.5, 0.0], volume=0.0)
            
            # Partitioning should still work
            method = MetisPartition(2)
            partition_info = partition_mesh(mesh, method)
            
            @test partition_info.partition.n_cells == 16
            # Zero-volume cells should still be assigned
            @test partition_info.partition.cell_processor[1] in [0, 1]
            @test partition_info.partition.cell_processor[5] in [0, 1]
        end
        
        @testset "Degenerate faces" begin
            # Create mesh with some degenerate faces (zero area)
            mesh = create_test_mesh_2d(3, 3)
            
            # Make some faces degenerate
            if length(mesh.faces) > 0
                mesh.faces[1] = (
                    points=mesh.faces[1].points,
                    owner=mesh.faces[1].owner,
                    neighbor=mesh.faces[1].neighbor,
                    area=0.0
                )
            end
            
            # Should handle gracefully
            method = SimplePartition(2, :x)
            partition_info = partition_mesh(mesh, method)
            
            @test partition_info.partition.n_cells == 9
        end
        
        @testset "Extreme aspect ratios" begin
            # Very thin cells (high aspect ratio)
            nx, ny = 1000, 2  # Extreme aspect ratio
            mesh = create_test_mesh_2d(nx, ny)
            
            # Should decompose without issues
            method = MetisPartition(8)
            partition_info = partition_mesh(mesh, method)
            
            @test partition_info.partition.n_cells == 2000
            @test partition_info.partition.load_imbalance < 1.5  # Reasonable balance
        end
    end
    
    # ========================================================================
    # CONCURRENT ACCESS TESTS
    # ========================================================================
    
    @testset "Concurrent Access" begin
        
        @testset "Multiple decompositions" begin
            test_dir = mktempdir()
            mkpath(joinpath(test_dir, "constant", "polyMesh"))
            
            try
                mesh = create_test_mesh_2d(10, 10)
                DecomposePar.eval(:(read_mesh(path::String) = $mesh))
                
                # First decomposition
                method1 = SimplePartition(2, :x)
                config1 = DecomposeConfig(method=method1, force=true, verbose=false)
                decompose_par(test_dir, config1)
                
                # Second decomposition (different method)
                method2 = SimplePartition(4, :xy)
                config2 = DecomposeConfig(method=method2, force=true, verbose=false)
                
                # Should overwrite with force=true
                decompose_par(test_dir, config2)
                
                # Check final state
                for proc in 0:3
                    @test isdir(joinpath(test_dir, "processor$proc"))
                end
                
                # Old processor directories should be removed
                @test !isdir(joinpath(test_dir, "processor4"))
                
            finally
                rm(test_dir, recursive=true)
            end
        end
    end
    
    # ========================================================================
    # MEMORY STRESS TESTS
    # ========================================================================
    
    @testset "Memory Stress Tests" begin
        
        @testset "Large processor counts" begin
            # Test with many processors but small mesh
            mesh = create_test_mesh_2d(10, 10)  # 100 cells
            
            # 100 processors (one cell each)
            method = SimplePartition(100, :xy)
            partition_info = partition_mesh(mesh, method)
            
            @test partition_info.partition.n_subdomains == 100
            
            # Each processor should have at most 1 cell
            cells_per_proc = [length(cells) for cells in partition_info.partition.processor_cells]
            @test all(c -> c <= 1, cells_per_proc)
            @test sum(cells_per_proc) == 100
        end
        
        @testset "Very large mesh" begin
            # Test memory efficiency with larger mesh
            # Keep reasonable for CI
            nx, ny = 50, 50  # 2500 cells
            mesh = create_test_mesh_2d(nx, ny)
            
            # Decompose to many processors
            method = MetisPartition(25)
            
            # Should complete without memory issues
            partition_info = partition_mesh(mesh, method)
            
            @test partition_info.partition.n_cells == 2500
            @test partition_info.partition.n_subdomains == 25
            
            # Average 100 cells per processor
            avg_cells = mean(length(cells) for cells in partition_info.partition.processor_cells)
            @test avg_cells ≈ 100.0
        end
    end
    
    # ========================================================================
    # RECOVERY TESTS
    # ========================================================================
    
    @testset "Error Recovery" begin
        
        @testset "Partial decomposition recovery" begin
            test_dir = mktempdir()
            
            try
                # Create partial decomposition (simulating interrupted process)
                mkpath(joinpath(test_dir, "processor0"))
                mkpath(joinpath(test_dir, "processor1"))
                # processor2 and processor3 missing
                
                # Try to use force to recover
                mesh = create_test_mesh_2d(4, 4)
                DecomposePar.eval(:(read_mesh(path::String) = $mesh))
                
                method = SimplePartition(4, :xy)
                config = DecomposeConfig(method=method, force=true, verbose=false)
                
                # Should complete successfully with force
                decompose_par(test_dir, config)
                
                # All processors should now exist
                for proc in 0:3
                    @test isdir(joinpath(test_dir, "processor$proc"))
                end
                
            finally
                rm(test_dir, recursive=true)
            end
        end
        
        @testset "Reconstruction with missing data" begin
            # Test graceful handling when some processor data is missing
            proc_fields = [
                Dict("name" => "p", "type" => "volScalarField", "data" => [1.0, 2.0]),
                Dict("name" => "p", "type" => "volScalarField", "data" => [3.0, 4.0]),
                nothing,  # Missing processor 2 data
                Dict("name" => "p", "type" => "volScalarField", "data" => [5.0, 6.0]),
            ]
            
            # Filter out missing data
            valid_fields = filter(!isnothing, proc_fields)
            
            # Should be able to work with available data
            @test length(valid_fields) == 3
            @test all(f -> f["name"] == "p", valid_fields)
        end
    end
end