"""
Unit tests for ProcessorBoundaries.jl

Tests processor boundary creation, communication patterns, and halo management.
"""

using Test
using LinearAlgebra

# Include the module paths
push!(LOAD_PATH, joinpath(@__DIR__, "../../../src/parallel"))
push!(LOAD_PATH, joinpath(@__DIR__, ".."))

using ProcessorBoundaries
using MeshPartitioning
using TestUtilities

@testset "ProcessorBoundaries.jl Tests" begin
    
    # ========================================================================
    # PROCESSOR BOUNDARY CREATION TESTS
    # ========================================================================
    
    @testset "Processor Boundary Creation" begin
        
        @testset "2x2 processor grid" begin
            # Create a simple 2D mesh and partition it
            mesh = create_test_mesh_2d(8, 8)  # 64 cells
            method = SimplePartition(4, :xy)  # 2x2 processor grid
            partition_info = partition_mesh(mesh, method)
            
            # Create processor boundaries for each processor
            proc_interfaces = ProcessorInterface[]
            for proc in 0:3
                interface = create_processor_boundaries(mesh, partition_info, proc)
                push!(proc_interfaces, interface)
                
                # Basic checks
                @test interface.proc_id == proc
                @test length(interface.boundaries) == length(interface.neighbor_procs)
                @test interface.total_send_cells >= 0
                @test interface.total_receive_cells >= 0
            end
            
            # Check neighbor symmetry
            for proc in 0:3
                interface = proc_interfaces[proc+1]
                for (i, neighbor) in enumerate(interface.neighbor_procs)
                    neighbor_interface = proc_interfaces[neighbor+1]
                    @test proc in neighbor_interface.neighbor_procs
                    
                    # Check send/receive symmetry
                    boundary = interface.boundaries[i]
                    neighbor_idx = findfirst(p -> p == proc, neighbor_interface.neighbor_procs)
                    neighbor_boundary = neighbor_interface.boundaries[neighbor_idx]
                    
                    # What proc sends to neighbor, neighbor should receive from proc
                    @test length(boundary.send_cells) == length(neighbor_boundary.receive_cells)
                end
            end
        end
        
        @testset "Linear processor arrangement" begin
            mesh = create_test_mesh_2d(16, 4)  # 64 cells
            method = SimplePartition(4, :x)  # Linear in X
            partition_info = partition_mesh(mesh, method)
            
            # Check processor 1 (middle processor)
            interface = create_processor_boundaries(mesh, partition_info, 1)
            
            # Should have 2 neighbors (0 and 2)
            @test length(interface.neighbor_procs) == 2
            @test 0 in interface.neighbor_procs
            @test 2 in interface.neighbor_procs
            
            # Check boundary processors
            interface0 = create_processor_boundaries(mesh, partition_info, 0)
            interface3 = create_processor_boundaries(mesh, partition_info, 3)
            
            # Boundary processors should have only 1 neighbor
            @test length(interface0.neighbor_procs) == 1
            @test length(interface3.neighbor_procs) == 1
        end
        
        @testset "Single processor" begin
            mesh = create_test_mesh_2d(8, 8)
            method = SimplePartition(1, :x)
            partition_info = partition_mesh(mesh, method)
            
            interface = create_processor_boundaries(mesh, partition_info, 0)
            
            # No neighbors
            @test length(interface.neighbor_procs) == 0
            @test length(interface.boundaries) == 0
            @test interface.total_send_cells == 0
            @test interface.total_receive_cells == 0
        end
    end
    
    # ========================================================================
    # COMMUNICATION PATTERN TESTS
    # ========================================================================
    
    @testset "Communication Pattern Optimization" begin
        
        @testset "Tag generation" begin
            # Test that communication tags are unique
            proc_id = 2
            neighbor_procs = [0, 1, 3, 5]
            
            comm_pattern = optimize_communication_pattern(proc_id, neighbor_procs)
            
            # Check tags are unique
            all_tags = Set{Int}()
            for (neighbor, tag) in comm_pattern.send_tags
                @test !(tag in all_tags)
                push!(all_tags, tag)
            end
            for (neighbor, tag) in comm_pattern.recv_tags
                @test !(tag in all_tags)
                push!(all_tags, tag)
            end
        end
        
        @testset "Send/receive ordering" begin
            # Test deadlock-free ordering
            proc_id = 2
            neighbor_procs = [0, 1, 3, 4]
            
            comm_pattern = optimize_communication_pattern(proc_id, neighbor_procs)
            
            # Lower rank should send first to avoid deadlock
            # Processor 2 should send to 3,4 first, then to 0,1
            expected_send_first = filter(n -> n > proc_id, neighbor_procs)
            expected_recv_first = filter(n -> n < proc_id, neighbor_procs)
            
            # Check ordering
            n_higher = length(expected_send_first)
            actual_send_first = comm_pattern.send_order[1:n_higher]
            @test sort(actual_send_first) == sort(expected_send_first)
        end
        
        @testset "Communication strategy" begin
            # Few neighbors - should not use persistent
            comm_pattern1 = optimize_communication_pattern(0, [1, 2])
            @test !comm_pattern1.use_persistent
            @test comm_pattern1.use_nonblocking
            
            # Many neighbors - should use persistent
            comm_pattern2 = optimize_communication_pattern(0, [1, 2, 3, 4, 5, 6])
            @test comm_pattern2.use_persistent
            @test comm_pattern2.use_nonblocking
        end
    end
    
    # ========================================================================
    # HALO CELL MANAGEMENT TESTS
    # ========================================================================
    
    @testset "Halo Cell Management" begin
        
        @testset "Single halo layer" begin
            mesh = create_test_mesh_2d(8, 8)
            method = SimplePartition(4, :xy)
            partition_info = partition_mesh(mesh, method)
            
            # Create single halo layer for processor 0
            halo_cells = create_halo_layers(mesh, partition_info, 0, 1)
            
            # Check that halo cells are not owned by processor 0
            for cell in halo_cells
                @test partition_info.partition.cell_processor[cell] != 0
            end
            
            # Halo cells should be from neighboring processors
            interface = create_processor_boundaries(mesh, partition_info, 0)
            neighbor_procs = Set(interface.neighbor_procs)
            
            for cell in halo_cells
                owner = partition_info.partition.cell_processor[cell]
                @test owner in neighbor_procs
            end
        end
        
        @testset "Multiple halo layers" begin
            mesh = create_test_mesh_2d(10, 10)
            method = SimplePartition(4, :xy)
            partition_info = partition_mesh(mesh, method)
            
            # Create 2 halo layers
            halo1 = create_halo_layers(mesh, partition_info, 0, 1)
            halo2 = create_halo_layers(mesh, partition_info, 0, 2)
            
            # Second layer should include first layer
            @test all(cell in halo2 for cell in halo1)
            @test length(halo2) > length(halo1)
        end
    end
    
    # ========================================================================
    # BOUNDARY MAPPING TESTS
    # ========================================================================
    
    @testset "Boundary Condition Mapping" begin
        
        @testset "Physical boundary preservation" begin
            mesh = create_test_mesh_2d(8, 8)
            
            # Add some boundary patches to mesh
            mesh.boundary_patches["left"] = Dict(
                "type" => "wall",
                "faces" => [1, 9, 17, 25]  # Example face IDs
            )
            mesh.boundary_patches["right"] = Dict(
                "type" => "outlet",
                "faces" => [8, 16, 24, 32]
            )
            
            method = SimplePartition(4, :xy)
            partition_info = partition_mesh(mesh, method)
            
            # Map boundaries to processors
            boundary_mapping = map_boundary_conditions_to_processors(mesh, partition_info)
            
            # Check physical patches are preserved
            @test haskey(boundary_mapping.physical_patches, "left")
            @test haskey(boundary_mapping.physical_patches, "right")
        end
        
        @testset "Processor boundary creation" begin
            mesh = create_test_mesh_2d(8, 8)
            method = SimplePartition(4, :xy)
            partition_info = partition_mesh(mesh, method)
            
            # Create processor boundaries
            for proc in 0:3
                interface = create_processor_boundaries(mesh, partition_info, proc)
                
                # Each processor boundary should be properly formatted
                for boundary in interface.boundaries
                    @test boundary.neighbor_proc >= 0
                    @test boundary.neighbor_proc < 4
                    @test boundary.neighbor_proc != proc
                    @test length(boundary.shared_faces) > 0
                end
            end
        end
    end
    
    # ========================================================================
    # I/O TESTS
    # ========================================================================
    
    @testset "Processor Boundary I/O" begin
        
        @testset "Write boundary information" begin
            # Create test directory
            test_dir = mktempdir()
            proc_dir = joinpath(test_dir, "processor0")
            mkpath(joinpath(proc_dir, "constant", "polyMesh"))
            
            mesh = create_test_mesh_2d(4, 4)
            method = SimplePartition(2, :x)
            partition_info = partition_mesh(mesh, method)
            
            # Create simple proc_mesh structure
            proc_mesh = Dict(
                "boundary_patches" => Dict(
                    "wall" => Dict("type" => "wall", "faces" => [1, 2], "start_face" => 1)
                )
            )
            
            # Write boundary info
            write_processor_boundary_info(proc_dir, proc_mesh, partition_info, 0)
            
            # Check that boundary file was created
            boundary_file = joinpath(proc_dir, "constant", "polyMesh", "boundary")
            @test isfile(boundary_file)
            
            # Read and verify format
            content = read(boundary_file, String)
            @test occursin("procBoundary0to1", content)
            @test occursin("type            processor", content)
            
            # Cleanup
            rm(test_dir, recursive=true)
        end
    end
    
    # ========================================================================
    # ANALYSIS TESTS
    # ========================================================================
    
    @testset "Boundary Analysis" begin
        
        @testset "Communication matrix" begin
            mesh = create_test_mesh_2d(8, 8)
            method = SimplePartition(4, :xy)
            partition_info = partition_mesh(mesh, method)
            
            # Create all processor interfaces
            proc_interfaces = ProcessorInterface[]
            for proc in 0:3
                interface = create_processor_boundaries(mesh, partition_info, proc)
                push!(proc_interfaces, interface)
            end
            
            # Analyze (should not error)
            analyze_processor_boundaries(proc_interfaces)
            
            # Check symmetry of communication
            for i in 1:4
                interface_i = proc_interfaces[i]
                for boundary in interface_i.boundaries
                    j = boundary.neighbor_proc + 1
                    interface_j = proc_interfaces[j]
                    
                    # Find corresponding boundary
                    found = false
                    for boundary_j in interface_j.boundaries
                        if boundary_j.neighbor_proc == i-1
                            found = true
                            # Check symmetry
                            @test length(boundary.shared_faces) == length(boundary_j.shared_faces)
                        end
                    end
                    @test found
                end
            end
        end
    end
    
    # ========================================================================
    # ERROR HANDLING TESTS
    # ========================================================================
    
    @testset "Error Handling" begin
        
        @testset "Invalid processor ID" begin
            mesh = create_test_mesh_2d(4, 4)
            method = SimplePartition(2, :x)
            partition_info = partition_mesh(mesh, method)
            
            # Try to create boundaries for non-existent processor
            @test_throws BoundsError create_processor_boundaries(mesh, partition_info, 5)
        end
        
        @testset "Missing decomposition info" begin
            test_dir = mktempdir()
            proc_dir = joinpath(test_dir, "processor0")
            
            # Try to read non-existent info
            @test_throws ErrorException read_processor_boundary_info(proc_dir)
            
            rm(test_dir, recursive=true)
        end
    end
    
    # ========================================================================
    # PERFORMANCE TESTS
    # ========================================================================
    
    @testset "Performance" begin
        
        @testset "Large processor count" begin
            mesh = create_test_mesh_3d(10, 10, 10)  # 1000 cells
            method = SimplePartition(64, :xyz)  # Many processors
            partition_info = partition_mesh(mesh, method)
            
            # Time boundary creation
            @time for proc in 0:15  # Test subset
                interface = create_processor_boundaries(mesh, partition_info, proc)
                @test interface.proc_id == proc
            end
        end
    end
end