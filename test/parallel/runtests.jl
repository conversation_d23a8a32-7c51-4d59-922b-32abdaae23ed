"""
Main test runner for JuliaFOAM parallel module

Executes all unit tests, integration tests, and validation tests.
Provides summary of test results and coverage.
"""

using Test
using Printf
using Dates

# Add source paths
push!(LOAD_PATH, joinpath(@__DIR__, "../../src/parallel"))

println("\n" * "="^70)
println("JULIAFOAM PARALLEL MODULE TEST SUITE")
println("="^70)
println("Started: ", Dates.now())
println("Julia version: ", VERSION)
println()

# Track overall test results
total_tests = 0
passed_tests = 0
failed_tests = 0
errors = String[]

# ============================================================================
# UNIT TESTS
# ============================================================================

println("Running Unit Tests...")
println("-"^70)

# Test modules in dependency order
unit_test_files = [
    "unit/test_MeshPartitioning.jl",
    "unit/test_ProcessorBoundaries.jl", 
    "unit/test_DecomposePar.jl",
    "unit/test_ReconstructPar.jl",
    # "unit/test_RedistributePar.jl",  # Add when created
    # "unit/test_LoadBalancing.jl",     # Add when created
]

for test_file in unit_test_files
    if isfile(test_file)
        println("\n▶ Running $test_file")
        try
            @time include(test_file)
            passed_tests += 1
        catch e
            failed_tests += 1
            push!(errors, "$test_file: $e")
            println("  ❌ FAILED: $e")
        end
        total_tests += 1
    else
        println("  ⚠️  Skipping $test_file (not found)")
    end
end

# ============================================================================
# INTEGRATION TESTS
# ============================================================================

println("\n\nRunning Integration Tests...")
println("-"^70)

integration_test_files = [
    "integration/test_complete_workflow.jl",
]

for test_file in integration_test_files
    if isfile(test_file)
        println("\n▶ Running $test_file")
        try
            @time include(test_file)
            passed_tests += 1
        catch e
            failed_tests += 1
            push!(errors, "$test_file: $e")
            println("  ❌ FAILED: $e")
        end
        total_tests += 1
    else
        println("  ⚠️  Skipping $test_file (not found)")
    end
end

# ============================================================================
# VALIDATION TESTS
# ============================================================================

println("\n\nRunning Validation Tests...")
println("-"^70)

validation_test_files = [
    "validation/test_openfoam_validation.jl",
]

for test_file in validation_test_files
    if isfile(test_file)
        println("\n▶ Running $test_file")
        try
            @time include(test_file)
            passed_tests += 1
        catch e
            failed_tests += 1
            push!(errors, "$test_file: $e")
            println("  ❌ FAILED: $e")
        end
        total_tests += 1
    else
        println("  ⚠️  Skipping $test_file (not found)")
    end
end

# ============================================================================
# PERFORMANCE TESTS (Optional)
# ============================================================================

if get(ENV, "RUN_PERFORMANCE_TESTS", "false") == "true"
    println("\n\nRunning Performance Tests...")
    println("-"^70)
    
    performance_test_files = [
        "performance/test_scalability.jl",
        "performance/test_memory_usage.jl",
    ]
    
    for test_file in performance_test_files
        if isfile(test_file)
            println("\n▶ Running $test_file")
            try
                @time include(test_file)
                passed_tests += 1
            catch e
                failed_tests += 1
                push!(errors, "$test_file: $e")
                println("  ❌ FAILED: $e")
            end
            total_tests += 1
        else
            println("  ⚠️  Skipping $test_file (not found)")
        end
    end
end

# ============================================================================
# TEST SUMMARY
# ============================================================================

println("\n" * "="^70)
println("TEST SUMMARY")
println("="^70)
println("Total test files: $total_tests")
println("Passed: $passed_tests ✓")
println("Failed: $failed_tests ✗")
println()

if failed_tests > 0
    println("Failed tests:")
    for error in errors
        println("  • $error")
    end
    println()
end

# Calculate pass rate
pass_rate = passed_tests / total_tests * 100
@printf("Pass rate: %.1f%%\n", pass_rate)

# Determine overall status
if failed_tests == 0
    println("\n✅ All tests passed!")
    exit_code = 0
else
    println("\n❌ Some tests failed!")
    exit_code = 1
end

println("\nCompleted: ", Dates.now())
println("="^70)

# Exit with appropriate code
exit(exit_code)