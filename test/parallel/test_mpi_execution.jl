"""
Test MPI execution infrastructure and communication patterns
"""

using Test
using LinearAlgebra
using StaticArrays

# Add the source directory to the path
push!(LOAD_PATH, joinpath(@__DIR__, "..", "..", "src"))

# Import required modules
include("../../src/parallel/TransparentParallel.jl")
using .TransparentParallel

@testset "MPI Execution Infrastructure Tests" begin
    
    @testset "MPI Initialization and Finalization" begin
        # Test basic MPI initialization (single process)
        comm, rank, nprocs = init_parallel()
        
        @test rank == 0  # Single process test
        @test nprocs == 1
        @test comm isa Any  # MPI.Comm type
        
        # Test finalization
        finalize_parallel()
        
        # Test with_parallel convenience function
        result = with_parallel() do comm, rank, nprocs
            return (rank, nprocs)
        end
        
        @test result[1] == 0  # rank
        @test result[2] == 1  # nprocs
    end
    
    @testset "Distributed Field Creation and Basic Operations" begin
        with_parallel() do comm, rank, nprocs
            # Create a simple mesh (mock)
            n_cells = 100
            mock_mesh = Dict(
                :cells => [Dict(:id => i) for i in 1:n_cells],
                :cell_partition => zeros(Int, n_cells),  # All cells on rank 0
                :halo_cells => Int[]
            )
            
            # Test scalar field creation
            scalar_field = create_distributed_field("temperature", mock_mesh, 300.0, comm=comm)
            
            @test scalar_field isa Any  # DistributedField type
            @test length(local_values(scalar_field)) == n_cells
            @test all(local_values(scalar_field) .== 300.0)
            
            # Test vector field creation
            vector_field = create_distributed_field("velocity", mock_mesh, 
                                                   SVector{3,Float64}(1.0, 0.0, 0.0), comm=comm)
            
            @test length(local_values(vector_field)) == n_cells
            @test all(local_values(vector_field) .== SVector{3,Float64}(1.0, 0.0, 0.0))
        end
    end
    
    @testset "Communication Pattern Testing" begin
        with_parallel() do comm, rank, nprocs
            # Create test mesh with partition info
            n_cells = 50
            mock_mesh = Dict(
                :cells => [Dict(:id => i) for i in 1:n_cells],
                :cell_partition => zeros(Int, n_cells),
                :halo_cells => Int[]
            )
            
            # Create test field
            test_field = create_distributed_field("test", mock_mesh, 1.0, comm=comm)
            
            # Test synchronization (should work even in single process)
            sync!(test_field)
            @test !test_field.needs_sync
            
            # Test global operations
            field_sum = parallel_sum(test_field)
            @test field_sum ≈ n_cells  # Sum should be number of cells
            
            field_max = parallel_max(test_field)
            @test field_max ≈ 1.0
            
            field_min = parallel_min(test_field)
            @test field_min ≈ 1.0
            
            field_norm = parallel_norm(test_field)
            @test field_norm ≈ sqrt(n_cells)  # L2 norm
            
            # Test L1 norm
            field_norm_l1 = parallel_norm(test_field, 1)
            @test field_norm_l1 ≈ n_cells
            
            # Test L∞ norm
            field_norm_inf = parallel_norm(test_field, Inf)
            @test field_norm_inf ≈ 1.0
        end
    end
    
    @testset "Field Arithmetic Operations" begin
        with_parallel() do comm, rank, nprocs
            n_cells = 30
            mock_mesh = Dict(
                :cells => [Dict(:id => i) for i in 1:n_cells],
                :cell_partition => zeros(Int, n_cells),
                :halo_cells => Int[]
            )
            
            # Create test fields
            field_a = create_distributed_field("a", mock_mesh, 2.0, comm=comm)
            field_b = create_distributed_field("b", mock_mesh, 3.0, comm=comm)
            
            # Test field addition
            field_sum = field_a + field_b
            @test all(local_values(field_sum) .≈ 5.0)
            
            # Test field subtraction
            field_diff = field_b - field_a
            @test all(local_values(field_diff) .≈ 1.0)
            
            # Test field multiplication
            field_prod = field_a * field_b
            @test all(local_values(field_prod) .≈ 6.0)
            
            # Test scalar operations
            field_scaled = field_a * 2.5
            @test all(local_values(field_scaled) .≈ 5.0)
            
            field_offset = field_a + 1.0
            @test all(local_values(field_offset) .≈ 3.0)
        end
    end
    
    @testset "Gather and Scatter Operations" begin
        with_parallel() do comm, rank, nprocs
            n_cells = 20
            mock_mesh = Dict(
                :cells => [Dict(:id => i) for i in 1:n_cells],
                :cell_partition => zeros(Int, n_cells),
                :halo_cells => Int[]
            )
            
            # Create test field with varying values
            test_values = Float64[i for i in 1:n_cells]
            test_field = create_distributed_field("test", mock_mesh, 0.0, comm=comm)
            
            # Set local values
            local_values(test_field) .= test_values
            
            # Test gather operation
            gathered_field = gather_field(test_field, 0)
            
            if rank == 0
                @test gathered_field !== nothing
                @test length(gathered_field.values) == n_cells
                @test gathered_field.values ≈ test_values
            else
                @test gathered_field === nothing
            end
            
            # Test scatter operation (only meaningful if we had gathered data)
            if rank == 0 && gathered_field !== nothing
                # Create new distributed field
                new_field = create_distributed_field("new", mock_mesh, 0.0, comm=comm)
                
                # Scatter the gathered field
                scatter_field!(gathered_field, new_field, 0)
                
                @test local_values(new_field) ≈ test_values
            end
        end
    end
    
    @testset "Broadcast Operations" begin
        with_parallel() do comm, rank, nprocs
            n_cells = 15
            mock_mesh = Dict(
                :cells => [Dict(:id => i) for i in 1:n_cells],
                :cell_partition => zeros(Int, n_cells),
                :halo_cells => Int[]
            )
            
            # Create test field
            test_field = create_distributed_field("broadcast_test", mock_mesh, 0.0, comm=comm)
            
            # Set values on root
            if rank == 0
                local_values(test_field) .= [Float64(i) for i in 1:n_cells]
            end
            
            # Broadcast from root
            broadcast_field!(test_field, 0)
            
            # All processes should have the same values
            expected_values = [Float64(i) for i in 1:n_cells]
            @test local_values(test_field) ≈ expected_values
        end
    end
    
    @testset "Asynchronous Communication" begin
        with_parallel() do comm, rank, nprocs
            n_cells = 25
            mock_mesh = Dict(
                :cells => [Dict(:id => i) for i in 1:n_cells],
                :cell_partition => zeros(Int, n_cells),
                :halo_cells => Int[]
            )
            
            # Create test field
            async_field = create_distributed_field("async_test", mock_mesh, 1.0, comm=comm)
            
            # Test async synchronization
            sync_async!(async_field)
            @test length(async_field.sync_requests) >= 0  # May be 0 for single process
            
            # Wait for completion
            wait_sync!(async_field)
            @test length(async_field.sync_requests) == 0
            @test !async_field.needs_sync
        end
    end
    
    @testset "Performance and Optimization" begin
        with_parallel() do comm, rank, nprocs
            n_cells = 1000
            mock_mesh = Dict(
                :cells => [Dict(:id => i) for i in 1:n_cells],
                :cell_partition => zeros(Int, n_cells),
                :halo_cells => Int[]
            )
            
            # Create large field for performance testing
            perf_field = create_distributed_field("performance", mock_mesh, 1.0, comm=comm)
            
            # Test communication pattern optimization
            optimize_communication_pattern!(perf_field)
            
            # Benchmark basic operations
            start_time = time()
            for i in 1:100
                sync!(perf_field)
            end
            sync_time = time() - start_time
            
            @test sync_time < 1.0  # Should be fast for single process
            
            # Benchmark global operations
            start_time = time()
            for i in 1:50
                field_sum = parallel_sum(perf_field)
            end
            reduction_time = time() - start_time
            
            @test reduction_time < 1.0  # Should be fast
            
            if rank == 0
                println("Sync time for 100 operations: $(sync_time) seconds")
                println("Reduction time for 50 operations: $(reduction_time) seconds")
            end
        end
    end
end

println("MPI execution infrastructure tests completed!")
