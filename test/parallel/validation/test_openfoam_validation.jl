"""
Validation tests against OpenFOAM decomposition

Compares JuliaFOAM parallel decomposition with OpenFOAM's decomposePar
to ensure compatibility and similar quality.
"""

using Test
using LinearAlgebra
using Statistics
using Printf

# Include the module paths
push!(LOAD_PATH, joinpath(@__DIR__, "../../../src/parallel"))
push!(LOAD_PATH, joinpath(@__DIR__, ".."))

using MeshPartitioning
using DecomposePar
using ReconstructPar
using TestUtilities

@testset "OpenFOAM Validation Tests" begin
    
    # ========================================================================
    # DECOMPOSITION METHOD COMPARISON
    # ========================================================================
    
    @testset "Simple Decomposition Comparison" begin
        
        @testset "2D cavity case - simple method" begin
            # Standard 2D cavity benchmark
            nx, ny = 20, 20
            mesh = create_test_mesh_2d(nx, ny)
            
            # Test different processor counts
            test_cases = [
                (n_procs=2, direction=:x, expected_cut_ratio=0.05),
                (n_procs=4, direction=:xy, expected_cut_ratio=0.10),
                (n_procs=8, direction=:xy, expected_cut_ratio=0.15),
            ]
            
            for test_case in test_cases
                println("\nTesting simple decomposition with $(test_case.n_procs) processors...")
                
                method = SimplePartition(test_case.n_procs, test_case.direction)
                partition_info = partition_mesh(mesh, method)
                
                # Check load balance (OpenFOAM simple gives perfect balance)
                @test partition_info.partition.load_imbalance ≈ 1.0 atol=0.01
                
                # Check edge cut ratio
                total_faces = mesh.n_faces
                interface_faces = partition_info.partition.n_interface_faces
                cut_ratio = interface_faces / total_faces
                
                @test cut_ratio <= test_case.expected_cut_ratio
                
                # Print quality metrics
                print_partition_comparison(
                    "Simple $(test_case.direction)",
                    test_case.n_procs,
                    partition_info.quality_metrics
                )
            end
        end
        
        @testset "3D block mesh - simple method" begin
            # 3D structured mesh similar to OpenFOAM blockMesh
            mesh = create_test_mesh_3d(10, 10, 10)  # 1000 cells
            
            # Compare with expected OpenFOAM results
            n_procs = 8
            method = SimplePartition(n_procs, :xyz)
            partition_info = partition_mesh(mesh, method)
            
            # OpenFOAM simple method for 8 procs on 10x10x10 gives 2x2x2 decomposition
            # Each subdomain should have exactly 125 cells
            cells_per_proc = [length(cells) for cells in partition_info.partition.processor_cells]
            @test all(c -> c == 125, cells_per_proc)
            
            # Check interface pattern
            # In 2x2x2 arrangement, corner procs have 3 neighbors, others have more
            min_neighbors = minimum(length(n) for n in partition_info.processor_neighbors)
            max_neighbors = maximum(length(n) for n in partition_info.processor_neighbors)
            
            @test min_neighbors == 3  # Corner processors
            @test max_neighbors == 5  # Face-center processors
        end
    end
    
    # ========================================================================
    # METIS DECOMPOSITION COMPARISON
    # ========================================================================
    
    @testset "METIS Decomposition Comparison" begin
        
        @testset "Quality metrics comparison" begin
            # Create test cases of increasing complexity
            test_meshes = [
                ("small_2d", create_test_mesh_2d(10, 10)),      # 100 cells
                ("medium_2d", create_test_mesh_2d(30, 30)),     # 900 cells
                ("small_3d", create_test_mesh_3d(6, 6, 6)),     # 216 cells
            ]
            
            println("\nMETIS decomposition quality comparison:")
            println("="^70)
            
            for (name, mesh) in test_meshes
                for n_procs in [4, 8]
                    method = MetisPartition(n_procs, imbalance=1.03)
                    partition_info = partition_mesh(mesh, method)
                    
                    quality = partition_info.quality_metrics
                    
                    # OpenFOAM METIS typically achieves:
                    # - Load imbalance < 1.05 (with 3% tolerance setting)
                    # - Edge cut minimization
                    # - Good communication balance
                    
                    @test quality["load_imbalance"] <= 1.05
                    @test quality["parallel_efficiency"] >= 0.85
                    
                    @printf("%-12s %2d procs: imbalance=%.3f, edge_cut=%d, efficiency=%.2f\n",
                            name, n_procs, quality["load_imbalance"], 
                            Int(quality["edge_cut"]), quality["parallel_efficiency"])
                end
            end
        end
        
        @testset "Communication pattern analysis" begin
            mesh = create_test_mesh_2d(20, 20)
            n_procs = 16
            
            # METIS should minimize communication
            method = MetisPartition(n_procs, face_weights=true)
            partition_info = partition_mesh(mesh, method)
            
            # Analyze communication matrix
            comm_matrix = zeros(Int, n_procs, n_procs)
            for (face_id, proc1, proc2) in partition_info.interface_faces
                comm_matrix[proc1+1, proc2+1] += 1
                comm_matrix[proc2+1, proc1+1] += 1
            end
            
            # Check sparsity (most processors shouldn't communicate)
            sparsity = count(x -> x == 0, comm_matrix) / length(comm_matrix)
            @test sparsity > 0.7  # At least 70% zeros
            
            # Check maximum communication load
            max_comm_per_proc = maximum(sum(comm_matrix, dims=2))
            avg_comm_per_proc = mean(sum(comm_matrix, dims=2))
            
            # Good partitioning should balance communication
            @test max_comm_per_proc / avg_comm_per_proc < 2.0
        end
    end
    
    # ========================================================================
    # BOUNDARY PRESERVATION VALIDATION
    # ========================================================================
    
    @testset "Boundary Preservation" begin
        
        @testset "Physical boundary distribution" begin
            # Create mesh with specific boundary configuration
            mesh = create_test_mesh_2d(16, 16)
            
            # Define boundaries similar to OpenFOAM cavity
            mesh.boundary_patches = Dict(
                "movingWall" => Dict("type" => "wall", "faces" => collect(241:256)),
                "fixedWalls" => Dict("type" => "wall", "faces" => vcat(1:16, 17:32, 33:48)),
                "frontAndBack" => Dict("type" => "empty", "faces" => collect(257:512))
            )
            
            n_procs = 4
            method = SimplePartition(n_procs, :xy)
            config = DecomposeConfig(method=method, preserve_patches=true)
            
            # In OpenFOAM, boundary faces are distributed to owning processors
            # Check that all boundary faces are accounted for
            partition_info = partition_mesh(mesh, method)
            
            # Count boundary faces per processor
            boundary_faces_per_proc = zeros(Int, n_procs)
            
            for (name, patch) in mesh.boundary_patches
                for face_id in patch["faces"]
                    if face_id <= length(mesh.faces)
                        owner_cell = mesh.faces[face_id].owner
                        if owner_cell > 0
                            proc = partition_info.partition.cell_processor[owner_cell]
                            boundary_faces_per_proc[proc+1] += 1
                        end
                    end
                end
            end
            
            # All boundary faces should be assigned
            total_boundary_faces = sum(length(p["faces"]) for (_, p) in mesh.boundary_patches)
            @test sum(boundary_faces_per_proc) <= total_boundary_faces
        end
        
        @testset "Processor boundary naming" begin
            # OpenFOAM convention: procBoundaryXtoY where X < Y
            
            proc_boundaries = [
                ProcessorBoundary(1, 0, Int[], Int[], Int[]),
                ProcessorBoundary(3, 0, Int[], Int[], Int[]),
                ProcessorBoundary(2, 1, Int[], Int[], Int[]),
            ]
            
            # Check naming convention
            for boundary in proc_boundaries
                proc = 0  # Current processor
                neighbor = boundary.neighbor_proc
                
                expected_name = if proc < neighbor
                    "procBoundary$(proc)to$(neighbor)"
                else
                    "procBoundary$(neighbor)to$(proc)"
                end
                
                # In actual implementation, this would be checked
                # For now, just verify the logic
                @test proc != neighbor
            end
        end
    end
    
    # ========================================================================
    # RECONSTRUCTION VALIDATION
    # ========================================================================
    
    @testset "Reconstruction Validation" begin
        
        @testset "Field reconstruction accuracy" begin
            # Create a field with known analytical solution
            nx, ny = 10, 10
            mesh = create_test_mesh_2d(nx, ny)
            
            # Create analytical pressure field: p = 100000 + 1000*sin(πx)*cos(πy)
            p_analytical = Float64[]
            for cell in mesh.cells
                x, y = cell.center[1], cell.center[2]
                p = 100000.0 + 1000.0 * sin(π * x) * cos(π * y)
                push!(p_analytical, p)
            end
            
            # Test reconstruction with different processor counts
            for n_procs in [2, 4, 8]
                # Decompose
                method = MetisPartition(n_procs)
                partition_info = partition_mesh(mesh, method)
                
                # Distribute field to processors
                proc_fields = []
                for proc in 0:n_procs-1
                    proc_cells = partition_info.partition.processor_cells[proc+1]
                    proc_data = p_analytical[proc_cells]
                    
                    proc_field = Dict(
                        "name" => "p",
                        "type" => "volScalarField",
                        "data" => proc_data,
                        "n_cells" => length(proc_cells)
                    )
                    push!(proc_fields, proc_field)
                end
                
                # Create cell mapping
                cell_mapping = Dict{Int,Vector{Tuple{Int,Int}}}()
                for proc in 0:n_procs-1
                    proc_cells = partition_info.partition.processor_cells[proc+1]
                    for (local_idx, global_cell) in enumerate(proc_cells)
                        cell_mapping[global_cell] = [(proc+1, local_idx)]
                    end
                end
                
                # Reconstruct
                reconstructed = ReconstructPar.reconstruct_field(proc_fields, cell_mapping)
                
                # Verify accuracy
                max_error = maximum(abs.(reconstructed["data"] - p_analytical))
                @test max_error < 1e-10
                
                # Check conservation
                sum_original = sum(p_analytical)
                sum_reconstructed = sum(reconstructed["data"])
                @test abs(sum_original - sum_reconstructed) / sum_original < 1e-14
            end
        end
    end
    
    # ========================================================================
    # PERFORMANCE COMPARISON
    # ========================================================================
    
    @testset "Performance Benchmarks" begin
        
        @testset "Decomposition timing" begin
            println("\nDecomposition performance comparison:")
            println("="^70)
            
            # Test different mesh sizes
            mesh_sizes = [
                (10, 10, 1, "100 cells"),
                (20, 20, 1, "400 cells"),
                (30, 30, 1, "900 cells"),
                (10, 10, 10, "1000 cells"),
            ]
            
            for (nx, ny, nz, desc) in mesh_sizes
                mesh = nz == 1 ? create_test_mesh_2d(nx, ny) : 
                                create_test_mesh_3d(nx, ny, nz)
                
                # Time different methods
                for (method_name, method) in [
                    ("Simple", SimplePartition(8, :xyz)),
                    ("METIS", MetisPartition(8)),
                ]
                    time_taken = @elapsed partition_info = partition_mesh(mesh, method)
                    
                    @printf("%-8s %-12s: %6.3f ms\n", method_name, desc, time_taken * 1000)
                    
                    # Performance should be reasonable
                    @test time_taken < 1.0  # Less than 1 second for these sizes
                end
            end
        end
        
        @testset "Scalability analysis" begin
            # Test weak scaling: constant cells per processor
            cells_per_proc = 1000
            
            println("\nWeak scaling analysis (1000 cells/proc):")
            println("="^50)
            
            for n_procs in [1, 2, 4, 8, 16]
                total_cells = cells_per_proc * n_procs
                mesh_size = Int(ceil(total_cells^(1/3)))
                
                # Create approximately cubic mesh
                mesh = create_test_mesh_3d(mesh_size, mesh_size, mesh_size)
                
                method = MetisPartition(n_procs)
                time_taken = @elapsed partition_info = partition_mesh(mesh, method)
                
                quality = partition_info.quality_metrics
                
                @printf("%2d procs (%5d cells): %6.3f ms, imbalance=%.3f\n",
                        n_procs, mesh.n_cells, time_taken * 1000,
                        quality["load_imbalance"])
                
                # Quality should remain good
                @test quality["load_imbalance"] < 1.1
            end
        end
    end
end

# ============================================================================
# HELPER FUNCTIONS
# ============================================================================

"""
Print partition quality comparison
"""
function print_partition_comparison(method_name::String, n_procs::Int, metrics::Dict)
    println("\n$method_name ($n_procs processors):")
    @printf("  Load imbalance: %.3f\n", metrics["load_imbalance"])
    @printf("  Edge cut: %d\n", Int(metrics["edge_cut"]))
    @printf("  Interface faces: %d\n", Int(metrics["interface_faces"]))
    @printf("  Avg neighbors: %.2f\n", metrics["avg_neighbors"])
    @printf("  Parallel efficiency: %.2f%%\n", metrics["parallel_efficiency"] * 100)
end