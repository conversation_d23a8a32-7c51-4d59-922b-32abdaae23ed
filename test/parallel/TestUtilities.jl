"""
TestUtilities.jl

Common utilities for testing the parallel domain decomposition system.
Provides functions for creating test meshes, verifying decompositions,
and checking conservation properties.
"""

module TestUtilities

using Test
using LinearAlgebra
using Statistics

export create_test_mesh_2d, create_test_mesh_3d, create_unstructured_test_mesh
export verify_decomposition, verify_reconstruction, verify_conservation
export check_partition_quality, check_load_balance
export create_test_fields, compare_fields
export TestMesh, TestField

# ============================================================================
# TEST DATA STRUCTURES
# ============================================================================

"""
Simple mesh structure for testing
"""
struct TestMesh
    n_cells::Int
    n_faces::Int
    n_points::Int
    points::Vector{Vector{Float64}}
    faces::Vector{NamedTuple{(:points, :owner, :neighbor, :area), Tuple{Vector{Int}, Int, Int, Float64}}}
    cells::Vector{NamedTuple{(:center, :volume), Tuple{Vector{Float64}, Float64}}}
    boundary_patches::Dict{String, Any}
    
    # For structured meshes
    nx::Union{Int, Nothing}
    ny::Union{Int, Nothing}
    nz::Union{Int, Nothing}
end

"""
Test field structure
"""
struct TestField{T}
    name::String
    type::String
    dimensions::String
    data::Vector{T}
end

# ============================================================================
# MESH CREATION FUNCTIONS
# ============================================================================

"""
Create a 2D structured test mesh (as thin 3D)
"""
function create_test_mesh_2d(nx::Int, ny::Int, lx::Float64=1.0, ly::Float64=1.0)
    nz = 1
    dz = 0.1
    
    # Create points
    points = Vector{Float64}[]
    for k in 0:nz, j in 0:ny, i in 0:nx
        x = i * lx / nx
        y = j * ly / ny
        z = k * dz
        push!(points, [x, y, z])
    end
    
    # Create cells and faces
    n_cells = nx * ny * nz
    cells = []
    faces = []
    
    # Cell centers and volumes
    dx = lx / nx
    dy = ly / ny
    cell_volume = dx * dy * dz
    
    for k in 1:nz, j in 1:ny, i in 1:nx
        # Cell center
        cx = (i - 0.5) * dx
        cy = (j - 0.5) * dy
        cz = (k - 0.5) * dz
        push!(cells, (center=[cx, cy, cz], volume=cell_volume))
    end
    
    # Create internal faces (simplified)
    # X-direction faces
    for k in 1:nz, j in 1:ny, i in 1:nx+1
        if i == 1
            # Left boundary
            owner = (k-1)*nx*ny + (j-1)*nx + 1
            neighbor = -1  # Boundary
            area = dy * dz
        elseif i == nx+1
            # Right boundary
            owner = (k-1)*nx*ny + (j-1)*nx + nx
            neighbor = -1  # Boundary
            area = dy * dz
        else
            # Internal face
            owner = (k-1)*nx*ny + (j-1)*nx + (i-1)
            neighbor = owner + 1
            area = dy * dz
        end
        
        # Face points (simplified - just storing indices)
        p1 = (k-1)*(nx+1)*(ny+1) + (j-1)*(nx+1) + i
        p2 = (k-1)*(nx+1)*(ny+1) + j*(nx+1) + i
        p3 = k*(nx+1)*(ny+1) + j*(nx+1) + i
        p4 = k*(nx+1)*(ny+1) + (j-1)*(nx+1) + i
        
        push!(faces, (points=[p1, p2, p3, p4], owner=owner, neighbor=neighbor, area=area))
    end
    
    # Y-direction faces
    for k in 1:nz, j in 1:ny+1, i in 1:nx
        if j == 1
            # Bottom boundary
            owner = (k-1)*nx*ny + i
            neighbor = -1
            area = dx * dz
        elseif j == ny+1
            # Top boundary
            owner = (k-1)*nx*ny + (ny-1)*nx + i
            neighbor = -1
            area = dx * dz
        else
            # Internal face
            owner = (k-1)*nx*ny + (j-2)*nx + i
            neighbor = owner + nx
            area = dx * dz
        end
        
        # Face points
        p1 = (k-1)*(nx+1)*(ny+1) + (j-1)*(nx+1) + i
        p2 = (k-1)*(nx+1)*(ny+1) + (j-1)*(nx+1) + i + 1
        p3 = k*(nx+1)*(ny+1) + (j-1)*(nx+1) + i + 1
        p4 = k*(nx+1)*(ny+1) + (j-1)*(nx+1) + i
        
        push!(faces, (points=[p1, p2, p3, p4], owner=owner, neighbor=neighbor, area=area))
    end
    
    # Create boundary patches
    boundary_patches = Dict{String, Any}(
        "left" => Dict("type" => "wall", "faces" => Int[]),
        "right" => Dict("type" => "wall", "faces" => Int[]),
        "top" => Dict("type" => "wall", "faces" => Int[]),
        "bottom" => Dict("type" => "wall", "faces" => Int[]),
        "front" => Dict("type" => "empty", "faces" => Int[]),
        "back" => Dict("type" => "empty", "faces" => Int[])
    )
    
    # Assign faces to patches (simplified)
    for (idx, face) in enumerate(faces)
        if face.neighbor == -1
            # Boundary face - assign to appropriate patch
            # This is simplified - real implementation would check geometry
            push!(boundary_patches["left"]["faces"], idx)
        end
    end
    
    return TestMesh(
        n_cells,
        length(faces),
        length(points),
        points,
        faces,
        cells,
        boundary_patches,
        nx, ny, nz
    )
end

"""
Create a 3D structured test mesh
"""
function create_test_mesh_3d(nx::Int, ny::Int, nz::Int, 
                           lx::Float64=1.0, ly::Float64=1.0, lz::Float64=1.0)
    # Similar to 2D but with full 3D
    # For brevity, returning a simplified version
    n_cells = nx * ny * nz
    n_points = (nx + 1) * (ny + 1) * (nz + 1)
    n_faces = nx*ny*(nz+1) + nx*(ny+1)*nz + (nx+1)*ny*nz
    
    points = Vector{Float64}[]
    faces = []
    cells = []
    
    # Create points
    for k in 0:nz, j in 0:ny, i in 0:nx
        x = i * lx / nx
        y = j * ly / ny
        z = k * lz / nz
        push!(points, [x, y, z])
    end
    
    # Create cells
    dx, dy, dz = lx/nx, ly/ny, lz/nz
    cell_volume = dx * dy * dz
    
    for k in 1:nz, j in 1:ny, i in 1:nx
        cx = (i - 0.5) * dx
        cy = (j - 0.5) * dy
        cz = (k - 0.5) * dz
        push!(cells, (center=[cx, cy, cz], volume=cell_volume))
    end
    
    boundary_patches = Dict{String, Any}()
    
    return TestMesh(
        n_cells,
        n_faces,
        n_points,
        points,
        faces,
        cells,
        boundary_patches,
        nx, ny, nz
    )
end

"""
Create a simple unstructured test mesh
"""
function create_unstructured_test_mesh(n_target_cells::Int)
    # Create a simple unstructured mesh (triangular/tetrahedral)
    # This is a placeholder - real implementation would create actual unstructured mesh
    
    # For now, create a quasi-random mesh
    n_cells = n_target_cells
    n_points = div(n_cells * 4, 3)  # Rough estimate
    n_faces = n_cells * 3  # Rough estimate
    
    # Random points
    points = [rand(3) for _ in 1:n_points]
    
    # Create random connectivity
    faces = []
    cells = []
    
    for i in 1:n_cells
        # Random cell center and volume
        center = rand(3)
        volume = 0.001 + 0.01 * rand()  # Small random volume
        push!(cells, (center=center, volume=volume))
    end
    
    # Create faces with random connectivity
    for i in 1:n_faces
        # Random face
        n_points_face = rand(3:4)  # Triangle or quad
        face_points = unique(rand(1:n_points, n_points_face))
        
        owner = rand(1:n_cells)
        neighbor = rand() < 0.7 ? rand(1:n_cells) : -1  # 30% boundary faces
        area = 0.01 * rand()
        
        push!(faces, (points=face_points, owner=owner, neighbor=neighbor, area=area))
    end
    
    boundary_patches = Dict{String, Any}(
        "boundary" => Dict("type" => "wall", "faces" => Int[])
    )
    
    return TestMesh(
        n_cells,
        n_faces,
        n_points,
        points,
        faces,
        cells,
        boundary_patches,
        nothing, nothing, nothing
    )
end

# ============================================================================
# FIELD CREATION FUNCTIONS
# ============================================================================

"""
Create test fields for a mesh
"""
function create_test_fields(mesh::TestMesh)
    fields = TestField[]
    
    # Scalar field (e.g., pressure)
    p_data = [100000.0 + 1000.0 * sin(c.center[1]) * cos(c.center[2]) 
              for c in mesh.cells]
    push!(fields, TestField("p", "volScalarField", "[Pa]", p_data))
    
    # Vector field (e.g., velocity)
    U_data = [[1.0, 0.1 * sin(c.center[1]), 0.0] for c in mesh.cells]
    push!(fields, TestField("U", "volVectorField", "[m/s]", U_data))
    
    # Another scalar field (e.g., temperature)
    T_data = [300.0 + 10.0 * c.center[1] for c in mesh.cells]
    push!(fields, TestField("T", "volScalarField", "[K]", T_data))
    
    return fields
end

# ============================================================================
# VERIFICATION FUNCTIONS
# ============================================================================

"""
Verify decomposition correctness
"""
function verify_decomposition(mesh::TestMesh, partition_info, n_procs::Int)
    errors = String[]
    
    partition = partition_info.partition
    
    # Check 1: All cells are assigned
    if length(partition.cell_processor) != mesh.n_cells
        push!(errors, "Cell count mismatch: $(length(partition.cell_processor)) != $(mesh.n_cells)")
    end
    
    # Check 2: Valid processor IDs
    for (i, proc) in enumerate(partition.cell_processor)
        if proc < 0 || proc >= n_procs
            push!(errors, "Invalid processor ID for cell $i: $proc")
        end
    end
    
    # Check 3: Each processor has cells
    for proc in 0:n_procs-1
        n_cells_proc = count(p -> p == proc, partition.cell_processor)
        if n_cells_proc == 0
            push!(errors, "Processor $proc has no cells")
        end
    end
    
    # Check 4: Load balance
    cells_per_proc = [length(cells) for cells in partition.processor_cells]
    imbalance = maximum(cells_per_proc) / mean(cells_per_proc)
    if imbalance > 2.0
        push!(errors, "Poor load balance: $(round(imbalance, digits=2))")
    end
    
    # Check 5: Interface faces are symmetric
    for (face_id, proc1, proc2) in partition_info.interface_faces
        if proc1 == proc2
            push!(errors, "Self-interface at face $face_id")
        end
    end
    
    return length(errors) == 0, errors
end

"""
Verify reconstruction correctness
"""
function verify_reconstruction(original_mesh::TestMesh, reconstructed_mesh,
                             original_fields, reconstructed_fields)
    errors = String[]
    tolerance = 1e-10
    
    # Check mesh reconstruction
    if reconstructed_mesh.n_cells != original_mesh.n_cells
        push!(errors, "Cell count mismatch: $(reconstructed_mesh.n_cells) != $(original_mesh.n_cells)")
    end
    
    # Check field reconstruction
    for (orig_field, recon_field) in zip(original_fields, reconstructed_fields)
        if orig_field.name != recon_field.name
            push!(errors, "Field name mismatch: $(orig_field.name) != $(recon_field.name)")
            continue
        end
        
        # Check data consistency
        if length(orig_field.data) != length(recon_field.data)
            push!(errors, "Field $(orig_field.name) size mismatch")
            continue
        end
        
        # Check values
        for (i, (orig_val, recon_val)) in enumerate(zip(orig_field.data, recon_field.data))
            if isa(orig_val, Vector)
                diff = norm(orig_val - recon_val)
            else
                diff = abs(orig_val - recon_val)
            end
            
            if diff > tolerance
                push!(errors, "Field $(orig_field.name) value mismatch at cell $i: diff = $diff")
                break  # Don't report all mismatches
            end
        end
    end
    
    return length(errors) == 0, errors
end

"""
Verify conservation properties
"""
function verify_conservation(original_fields, decomposed_fields_list, tolerance=1e-12)
    errors = String[]
    
    for field in original_fields
        # Calculate original sum
        if isa(field.data[1], Vector)
            original_sum = sum(norm.(field.data))
        else
            original_sum = sum(field.data)
        end
        
        # Calculate decomposed sum
        decomposed_sum = 0.0
        for proc_fields in decomposed_fields_list
            proc_field = findfirst(f -> f.name == field.name, proc_fields)
            if proc_field !== nothing
                if isa(proc_fields[proc_field].data[1], Vector)
                    decomposed_sum += sum(norm.(proc_fields[proc_field].data))
                else
                    decomposed_sum += sum(proc_fields[proc_field].data)
                end
            end
        end
        
        # Check conservation
        relative_error = abs(original_sum - decomposed_sum) / max(abs(original_sum), 1e-10)
        if relative_error > tolerance
            push!(errors, "Conservation violated for field $(field.name): " *
                         "original = $original_sum, decomposed = $decomposed_sum, " *
                         "relative error = $relative_error")
        end
    end
    
    return length(errors) == 0, errors
end

"""
Check partition quality metrics
"""
function check_partition_quality(partition_info, expected_metrics::Dict)
    errors = String[]
    tolerance = 0.1  # 10% tolerance
    
    quality = partition_info.quality_metrics
    
    for (metric, expected) in expected_metrics
        if haskey(quality, metric)
            actual = quality[metric]
            relative_error = abs(actual - expected) / max(abs(expected), 1e-10)
            
            if relative_error > tolerance
                push!(errors, "$metric mismatch: expected $expected, got $actual")
            end
        else
            push!(errors, "Missing metric: $metric")
        end
    end
    
    return length(errors) == 0, errors
end

"""
Check load balance quality
"""
function check_load_balance(partition_info, max_allowed_imbalance::Float64=1.1)
    partition = partition_info.partition
    
    if partition.load_imbalance > max_allowed_imbalance
        return false, ["Load imbalance $(partition.load_imbalance) exceeds maximum $(max_allowed_imbalance)"]
    end
    
    return true, String[]
end

"""
Compare two fields for equality
"""
function compare_fields(field1::TestField, field2::TestField, tolerance::Float64=1e-10)
    if field1.name != field2.name || field1.type != field2.type
        return false, ["Field metadata mismatch"]
    end
    
    if length(field1.data) != length(field2.data)
        return false, ["Field size mismatch: $(length(field1.data)) != $(length(field2.data))"]
    end
    
    errors = String[]
    for (i, (v1, v2)) in enumerate(zip(field1.data, field2.data))
        if isa(v1, Vector)
            diff = norm(v1 - v2)
        else
            diff = abs(v1 - v2)
        end
        
        if diff > tolerance
            push!(errors, "Value mismatch at index $i: diff = $diff")
            if length(errors) > 10  # Limit error messages
                push!(errors, "... and more")
                break
            end
        end
    end
    
    return length(errors) == 0, errors
end

# ============================================================================
# PERFORMANCE MEASUREMENT
# ============================================================================

"""
Measure partition time and memory usage
"""
function measure_partition_performance(mesh::TestMesh, method, n_runs::Int=3)
    times = Float64[]
    memory_usage = Float64[]
    
    for _ in 1:n_runs
        gc()  # Clean garbage collection
        start_mem = Base.gc_bytes()
        
        start_time = time()
        partition_info = partition_mesh(mesh, method)
        end_time = time()
        
        end_mem = Base.gc_bytes()
        
        push!(times, end_time - start_time)
        push!(memory_usage, (end_mem - start_mem) / 1e6)  # MB
    end
    
    return (
        avg_time = mean(times),
        min_time = minimum(times),
        max_time = maximum(times),
        avg_memory = mean(memory_usage)
    )
end

end # module TestUtilities