# Parallel Domain Decomposition Test Plan

## Overview

This document outlines the comprehensive testing strategy for JuliaFOAM's parallel domain decomposition system. The goal is to ensure robustness, correctness, and performance across all components.

## Testing Categories

### 1. Unit Tests

#### 1.1 MeshPartitioning.jl
- **Test simple partitioning**:
  - 1D, 2D, 3D decomposition directions
  - Power-of-2 and non-power-of-2 processor counts
  - Edge cases (1 processor, more processors than cells)
  
- **Test METIS partitioning**:
  - Graph construction correctness
  - Load balance quality
  - Edge cut minimization
  - Face weight handling
  
- **Test hierarchical partitioning**:
  - Multi-level decomposition
  - Consistency across levels
  
- **Test partition quality metrics**:
  - Load imbalance calculation
  - Communication cost estimation
  - Halo cell identification

#### 1.2 ProcessorBoundaries.jl
- **Test boundary detection**:
  - Correct identification of interface faces
  - Halo cell determination
  - Neighbor processor mapping
  
- **Test communication patterns**:
  - Send/receive lists correctness
  - Tag generation uniqueness
  - Optimization strategies
  
- **Test boundary mapping**:
  - Physical boundary preservation
  - Cyclic boundary handling
  - Processor boundary creation

#### 1.3 DecomposePar.jl
- **Test mesh decomposition**:
  - Cell distribution correctness
  - Point mapping consistency
  - Face ownership rules
  
- **Test field decomposition**:
  - Scalar field distribution
  - Vector field distribution
  - Boundary field handling
  
- **Test I/O operations**:
  - Processor directory creation
  - File writing correctness
  - Decomposition info storage

#### 1.4 ReconstructPar.jl
- **Test mesh reconstruction**:
  - Point deduplication
  - Face stitching at interfaces
  - Global numbering consistency
  
- **Test field reconstruction**:
  - Value mapping correctness
  - Conservation properties
  - Boundary field reconstruction
  
- **Test verification**:
  - Mass conservation checks
  - Field continuity at interfaces

#### 1.5 RedistributePar.jl
- **Test redistribution strategies**:
  - Reconstruction-based redistribution
  - Direct remapping
  - Incremental migration
  
- **Test load balancing**:
  - Imbalance detection
  - Optimal processor suggestion
  - Migration minimization

#### 1.6 LoadBalancing.jl
- **Test metrics calculation**:
  - Load imbalance factors
  - Efficiency estimation
  - Communication cost analysis
  
- **Test analysis functions**:
  - Bottleneck identification
  - Trend analysis
  - Suggestion generation

### 2. Integration Tests

#### 2.1 Complete Workflow Tests
- **Serial to parallel to serial**:
  ```
  Original mesh → Decompose → Parallel run → Reconstruct → Verify
  ```
  
- **Redistribution workflow**:
  ```
  4 procs → 8 procs → 2 procs → Verify consistency
  ```

#### 2.2 Boundary Condition Preservation
- Test all boundary types through decomposition/reconstruction
- Verify BC consistency across processor boundaries

#### 2.3 Field Conservation Tests
- Global mass conservation
- Momentum conservation
- Energy conservation (if applicable)

### 3. Performance Tests

#### 3.1 Scalability Tests
- Strong scaling: Fixed problem size, varying processors
- Weak scaling: Fixed size per processor
- Communication overhead measurement

#### 3.2 Partitioning Performance
- Time vs mesh size for different methods
- Memory usage profiling
- Optimization effectiveness

### 4. Validation Tests

#### 4.1 OpenFOAM Compatibility
- Compare decomposition with OpenFOAM decomposePar
- Validate reconstruction against OpenFOAM reconstructPar
- Benchmark partition quality

#### 4.2 Known Solutions
- Regular mesh decomposition (analytical)
- Load balance for uniform meshes
- Communication pattern validation

### 5. Robustness Tests

#### 5.1 Edge Cases
- Empty processors
- Single cell per processor
- Highly skewed meshes
- Disconnected mesh regions

#### 5.2 Error Handling
- Missing files
- Corrupted data
- Incompatible configurations
- MPI failures

#### 5.3 Stress Tests
- Very large meshes (millions of cells)
- Extreme processor counts
- Rapid redistribution cycles

## Test Data

### Mesh Types
1. **Structured meshes**:
   - 2D rectangular (various sizes)
   - 3D hexahedral (various sizes)
   
2. **Unstructured meshes**:
   - 2D triangular
   - 3D tetrahedral
   - Mixed polyhedra

3. **Special cases**:
   - Meshes with hanging nodes
   - Meshes with periodic boundaries
   - Multi-region meshes

### Test Metrics

For each test, measure and verify:
- **Correctness**: Results match expected values
- **Performance**: Execution time within bounds
- **Memory**: No memory leaks or excessive usage
- **Stability**: Consistent results across runs

## Implementation Strategy

1. **Phase 1**: Core unit tests (Critical path)
2. **Phase 2**: Integration tests
3. **Phase 3**: Performance benchmarks
4. **Phase 4**: Validation against OpenFOAM
5. **Phase 5**: Robustness and stress tests

## Success Criteria

- All unit tests pass with 100% assertion success
- Integration tests show < 1e-10 conservation error
- Performance within 20% of OpenFOAM
- No crashes or hangs in stress tests
- Clear error messages for invalid inputs