"""
Test enhanced mesh support for complex geometries
"""

using Test
using LinearAlgebra
using StaticArrays

# Add the source directory to the path
push!(LOAD_PATH, joinpath(@__DIR__, "..", "..", "src"))

# Import required modules
include("../../src/mesh/UnstructuredMesh.jl")
using .UnstructuredMesh

@testset "Complex Geometry Mesh Support Tests" begin
    
    @testset "Complex Geometry Validation" begin
        # Create a simple test mesh
        points = [
            Point3D(0.0, 0.0, 0.0),  # 1
            Point3D(1.0, 0.0, 0.0),  # 2
            Point3D(1.0, 1.0, 0.0),  # 3
            Point3D(0.0, 1.0, 0.0),  # 4
            Point3D(0.0, 0.0, 1.0),  # 5
            Point3D(1.0, 0.0, 1.0),  # 6
            Point3D(1.0, 1.0, 1.0),  # 7
            Point3D(0.0, 1.0, 1.0)   # 8
        ]
        
        # Create faces for a hexahedral cell
        face_point_lists = [
            [1, 4, 3, 2],  # bottom
            [5, 6, 7, 8],  # top
            [1, 2, 6, 5],  # front
            [4, 8, 7, 3],  # back
            [1, 5, 8, 4],  # left
            [2, 3, 7, 6]   # right
        ]
        
        face_owners = [1, 1, 1, 1, 1, 1]
        face_neighbors = [-1, -1, -1, -1, -1, -1]  # All boundary faces
        
        # Create boundary patches
        boundary_patches = [
            UnstructuredBoundaryPatch("walls", :wall, [1, 2, 3, 4, 5, 6], 1, 6)
        ]
        
        # Create mesh
        test_mesh = Mesh(points, face_point_lists, face_owners, face_neighbors, boundary_patches)
        
        # Test complex geometry validation
        issues = validate_complex_geometry(test_mesh)
        @test isa(issues, Vector{String})
        
        # For a simple hex, should have minimal issues
        println("Validation issues found: $(length(issues))")
        for issue in issues
            println("  - $issue")
        end
    end
    
    @testset "Non-Convex Cell Detection" begin
        # Create a simple convex cell (should pass)
        points = [
            Point3D(0.0, 0.0, 0.0),
            Point3D(1.0, 0.0, 0.0),
            Point3D(0.5, 1.0, 0.0),
            Point3D(0.5, 0.5, 1.0)
        ]
        
        # Tetrahedron faces
        face_point_lists = [
            [1, 2, 3],     # base
            [1, 4, 2],     # side 1
            [2, 4, 3],     # side 2
            [3, 4, 1]      # side 3
        ]
        
        face_owners = [1, 1, 1, 1]
        face_neighbors = [-1, -1, -1, -1]
        boundary_patches = [UnstructuredBoundaryPatch("walls", :wall, [1, 2, 3, 4], 1, 4)]
        
        tet_mesh = Mesh(points, face_point_lists, face_owners, face_neighbors, boundary_patches)
        
        non_convex_cells = detect_non_convex_cells(tet_mesh)
        @test length(non_convex_cells) == 0  # Tetrahedron should be convex
        
        # Test convexity check for individual cell
        is_non_convex = is_cell_non_convex(tet_mesh, 1)
        @test !is_non_convex  # Should be convex
    end
    
    @testset "Face Planarity Detection" begin
        # Create a mesh with a non-planar face
        points = [
            Point3D(0.0, 0.0, 0.0),
            Point3D(1.0, 0.0, 0.0),
            Point3D(1.0, 1.0, 0.0),
            Point3D(0.0, 1.0, 0.1)  # Slightly out of plane
        ]
        
        face_point_lists = [
            [1, 2, 3, 4]  # Quadrilateral face
        ]
        
        face_owners = [1]
        face_neighbors = [-1]
        boundary_patches = [UnstructuredBoundaryPatch("wall", :wall, [1], 1, 1)]
        
        # Create a minimal mesh structure for testing
        test_mesh = Mesh(points, face_point_lists, face_owners, face_neighbors, boundary_patches)
        
        # Test planarity detection
        non_planar_faces = detect_non_planar_faces(test_mesh, 1e-3)
        @test length(non_planar_faces) >= 0  # May detect the non-planar face
        
        # Test planarity error calculation
        if !isempty(test_mesh.faces)
            planarity_error = calculate_face_planarity_error(test_mesh, 1)
            @test planarity_error >= 0.0
            println("Face planarity error: $planarity_error")
        end
    end
    
    @testset "Boundary Curvature Detection" begin
        # Create a simple mesh to test curvature detection
        points = [
            Point3D(0.0, 0.0, 0.0),
            Point3D(1.0, 0.0, 0.0),
            Point3D(1.0, 1.0, 0.0),
            Point3D(0.0, 1.0, 0.0)
        ]
        
        face_point_lists = [
            [1, 2, 3, 4]
        ]
        
        face_owners = [1]
        face_neighbors = [-1]
        boundary_patches = [UnstructuredBoundaryPatch("curved_wall", :wall, [1], 1, 1)]
        
        test_mesh = Mesh(points, face_point_lists, face_owners, face_neighbors, boundary_patches)
        
        # Test curved boundary detection
        curved_boundaries = detect_curved_boundaries(test_mesh)
        @test isa(curved_boundaries, Vector{String})
        
        # Test patch curvature calculation
        if !isempty(test_mesh.boundary_patches)
            curvature = calculate_patch_curvature(test_mesh, test_mesh.boundary_patches[1])
            @test curvature >= 0.0
            println("Patch curvature: $curvature")
        end
    end
    
    @testset "Cell Closure Validation" begin
        # Create a simple test mesh
        points = [
            Point3D(0.0, 0.0, 0.0),
            Point3D(1.0, 0.0, 0.0),
            Point3D(0.5, 1.0, 0.0),
            Point3D(0.5, 0.5, 1.0)
        ]
        
        face_point_lists = [
            [1, 2, 3],
            [1, 4, 2],
            [2, 4, 3],
            [3, 4, 1]
        ]
        
        face_owners = [1, 1, 1, 1]
        face_neighbors = [-1, -1, -1, -1]
        boundary_patches = [UnstructuredBoundaryPatch("walls", :wall, [1, 2, 3, 4], 1, 4)]
        
        test_mesh = Mesh(points, face_point_lists, face_owners, face_neighbors, boundary_patches)
        
        # Test open cell detection
        open_cells = detect_open_cells(test_mesh)
        @test isa(open_cells, Vector{Int})
        
        # Test cell closure check
        if !isempty(test_mesh.cells)
            is_closed = is_cell_closed(test_mesh, 1)
            @test isa(is_closed, Bool)
            println("Cell 1 is closed: $is_closed")
        end
    end
    
    @testset "Degenerate Element Detection" begin
        # Create a mesh with potential degenerate elements
        points = [
            Point3D(0.0, 0.0, 0.0),
            Point3D(1.0, 0.0, 0.0),
            Point3D(0.5, 1.0, 0.0),
            Point3D(0.5, 0.5, 1.0)
        ]
        
        face_point_lists = [
            [1, 2, 3],
            [1, 4, 2],
            [2, 4, 3],
            [3, 4, 1]
        ]
        
        face_owners = [1, 1, 1, 1]
        face_neighbors = [-1, -1, -1, -1]
        boundary_patches = [UnstructuredBoundaryPatch("walls", :wall, [1, 2, 3, 4], 1, 4)]
        
        test_mesh = Mesh(points, face_point_lists, face_owners, face_neighbors, boundary_patches)
        
        # Test degenerate element detection
        degenerate_elements = detect_degenerate_elements(test_mesh)
        @test isa(degenerate_elements, Vector{Int})
        println("Degenerate elements found: $(length(degenerate_elements))")
    end
    
    @testset "Boundary Layer Quality Assessment" begin
        # Create a simple mesh to test boundary layer assessment
        points = [
            Point3D(0.0, 0.0, 0.0),
            Point3D(1.0, 0.0, 0.0),
            Point3D(1.0, 1.0, 0.0),
            Point3D(0.0, 1.0, 0.0)
        ]
        
        face_point_lists = [
            [1, 2, 3, 4]
        ]
        
        face_owners = [1]
        face_neighbors = [-1]
        boundary_patches = [UnstructuredBoundaryPatch("wall", :wall, [1], 1, 1)]
        
        test_mesh = Mesh(points, face_point_lists, face_owners, face_neighbors, boundary_patches)
        
        # Test boundary layer quality assessment
        bl_issues = assess_boundary_layer_quality(test_mesh)
        @test isa(bl_issues, Vector{String})
        println("Boundary layer issues: $(length(bl_issues))")
        
        # Test wall distance calculation
        if !isempty(test_mesh.boundary_patches)
            wall_distances = calculate_wall_distances(test_mesh, test_mesh.boundary_patches[1])
            @test isa(wall_distances, Vector{Float64})
            @test all(wall_distances .>= 0.0)
            println("Wall distances calculated: $(length(wall_distances))")
        end
    end
    
    @testset "Mesh Repair and Optimization" begin
        # Create a simple test mesh
        points = [
            Point3D(0.0, 0.0, 0.0),
            Point3D(1.0, 0.0, 0.0),
            Point3D(0.5, 1.0, 0.0),
            Point3D(0.5, 0.5, 1.0)
        ]
        
        face_point_lists = [
            [1, 2, 3],
            [1, 4, 2],
            [2, 4, 3],
            [3, 4, 1]
        ]
        
        face_owners = [1, 1, 1, 1]
        face_neighbors = [-1, -1, -1, -1]
        boundary_patches = [UnstructuredBoundaryPatch("walls", :wall, [1, 2, 3, 4], 1, 4)]
        
        original_mesh = Mesh(points, face_point_lists, face_owners, face_neighbors, boundary_patches)
        
        # Test mesh repair
        repaired_mesh = repair_mesh_connectivity(original_mesh)
        @test isa(repaired_mesh, Mesh)
        @test length(repaired_mesh.points) == length(original_mesh.points)
        @test length(repaired_mesh.faces) == length(original_mesh.faces)
        @test length(repaired_mesh.cells) == length(original_mesh.cells)
        
        # Test mesh optimization
        optimized_mesh = optimize_mesh_topology(original_mesh)
        @test isa(optimized_mesh, Mesh)
        @test length(optimized_mesh.points) == length(original_mesh.points)
        @test length(optimized_mesh.faces) == length(original_mesh.faces)
        @test length(optimized_mesh.cells) == length(original_mesh.cells)
    end
    
    @testset "Face Neighbor Finding" begin
        # Create a simple mesh with multiple faces
        points = [
            Point3D(0.0, 0.0, 0.0),
            Point3D(1.0, 0.0, 0.0),
            Point3D(1.0, 1.0, 0.0),
            Point3D(0.0, 1.0, 0.0),
            Point3D(2.0, 0.0, 0.0),
            Point3D(2.0, 1.0, 0.0)
        ]
        
        face_point_lists = [
            [1, 2, 3, 4],  # Face 1
            [2, 5, 6, 3]   # Face 2 (shares edge with Face 1)
        ]
        
        face_owners = [1, 1]
        face_neighbors = [-1, -1]
        boundary_patches = [UnstructuredBoundaryPatch("wall", :wall, [1, 2], 1, 2)]
        
        test_mesh = Mesh(points, face_point_lists, face_owners, face_neighbors, boundary_patches)
        
        # Test face neighbor finding
        if !isempty(test_mesh.boundary_patches)
            neighbors = find_face_neighbors_on_patch(test_mesh, 1, test_mesh.boundary_patches[1])
            @test isa(neighbors, Vector{Int})
            println("Face neighbors found: $(length(neighbors))")
        end
    end
end

println("Complex geometry mesh support tests completed!")
