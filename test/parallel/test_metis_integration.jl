"""
Test METIS integration for mesh partitioning
"""

using Test
using LinearAlgebra
using StaticArrays

# Add the source directory to the path
push!(LOAD_PATH, joinpath(@__DIR__, "..", "..", "src"))

# Import required modules directly
include("../../src/mesh/UnstructuredMesh.jl")
include("../../src/parallel/MeshPartitioning.jl")

using .UnstructuredMesh
using .MeshPartitioning

@testset "METIS Integration Tests" begin
    
    @testset "Simple Unstructured Mesh Creation" begin
        # Create a simple 2x2 quad mesh as unstructured
        points = [
            Point3D(0.0, 0.0, 0.0),  # 1
            Point3D(1.0, 0.0, 0.0),  # 2
            Point3D(2.0, 0.0, 0.0),  # 3
            Point3D(0.0, 1.0, 0.0),  # 4
            Point3D(1.0, 1.0, 0.0),  # 5
            Point3D(2.0, 1.0, 0.0),  # 6
            Point3D(0.0, 2.0, 0.0),  # 7
            Point3D(1.0, 2.0, 0.0),  # 8
            Point3D(2.0, 2.0, 0.0),  # 9
        ]
        
        # Define faces for 4 quad cells
        # Cell 1: points 1,2,5,4
        # Cell 2: points 2,3,6,5  
        # Cell 3: points 4,5,8,7
        # Cell 4: points 5,6,9,8
        
        face_point_lists = [
            # Internal faces
            [2, 5],     # Face 1: between cell 1 and 2
            [4, 5],     # Face 2: between cell 1 and 3
            [5, 6],     # Face 3: between cell 2 and 4
            [5, 8],     # Face 4: between cell 3 and 4
            
            # Boundary faces for cell 1
            [1, 2],     # Face 5: bottom of cell 1
            [1, 4],     # Face 6: left of cell 1
            [4, 7],     # Face 7: top of cell 1 (part)
            
            # Boundary faces for cell 2
            [2, 3],     # Face 8: bottom of cell 2
            [3, 6],     # Face 9: right of cell 2
            
            # Boundary faces for cell 3
            [7, 8],     # Face 10: top of cell 3
            
            # Boundary faces for cell 4
            [6, 9],     # Face 11: right of cell 4
            [8, 9],     # Face 12: top of cell 4
        ]
        
        face_owners = [1, 1, 2, 3, 1, 1, 1, 2, 2, 3, 4, 4]
        face_neighbors = [2, 3, 4, 4, -1, -1, -1, -1, -1, -1, -1, -1]
        
        # Create boundary patches
        boundary_patches = [
            UnstructuredBoundaryPatch("bottom", :wall, [5, 8], 5, 2),
            UnstructuredBoundaryPatch("left", :wall, [6], 6, 1),
            UnstructuredBoundaryPatch("top", :wall, [7, 10, 12], 7, 3),
            UnstructuredBoundaryPatch("right", :wall, [9, 11], 9, 2),
        ]

        # Create mesh
        mesh = Mesh(points, face_point_lists, face_owners, face_neighbors, boundary_patches)
        
        @test length(mesh.cells) == 4
        @test length(mesh.faces) == 12
        @test length(mesh.points) == 9
    end
    
    @testset "Simple Geometric Partitioning" begin
        # Create simple test mesh
        points = [
            Point3D(0.0, 0.0, 0.0),
            Point3D(1.0, 0.0, 0.0),
            Point3D(0.0, 1.0, 0.0),
            Point3D(1.0, 1.0, 0.0),
        ]
        
        face_point_lists = [
            [1, 2],  # Internal face
            [1, 3],  # Boundary
            [2, 4],  # Boundary  
            [3, 4],  # Boundary
        ]
        
        face_owners = [1, 1, 2, 2]
        face_neighbors = [2, -1, -1, -1]
        boundary_patches = UnstructuredBoundaryPatch[]

        mesh = Mesh(points, face_point_lists, face_owners, face_neighbors, boundary_patches)
        
        # Test simple partitioning
        method = SimplePartition(2, :x)
        partition_info = partition_mesh(mesh, method)
        
        @test partition_info.partition.n_subdomains == 2
        @test partition_info.partition.n_cells == 2
        @test length(partition_info.partition.cell_processor) == 2
    end
    
    @testset "METIS Partitioning" begin
        # Create a larger test mesh for METIS
        points = Point3D[]
        for i in 1:5, j in 1:5
            push!(points, Point3D(Float64(i-1), Float64(j-1), 0.0))
        end

        # Create a simple grid connectivity (simplified)
        face_point_lists = Vector{Int}[]
        face_owners = Int[]
        face_neighbors = Int[]
        
        # Add some internal faces for a 4x4 grid of cells
        cell_id = 1
        for i in 1:4, j in 1:4
            # Horizontal internal faces
            if i < 4
                push!(face_point_lists, [i + (j-1)*5, i+1 + (j-1)*5])
                push!(face_owners, cell_id)
                push!(face_neighbors, cell_id + 1)
            end
            
            # Vertical internal faces  
            if j < 4
                push!(face_point_lists, [i + (j-1)*5, i + j*5])
                push!(face_owners, cell_id)
                push!(face_neighbors, cell_id + 4)
            end
            
            cell_id += 1
        end
        
        # Add boundary faces (simplified)
        for i in 1:4
            push!(face_point_lists, [i, i+1])  # Bottom boundary
            push!(face_owners, i)
            push!(face_neighbors, -1)
        end
        
        boundary_patches = UnstructuredBoundaryPatch[]
        mesh = Mesh(points, face_point_lists, face_owners, face_neighbors, boundary_patches)
        
        # Test METIS partitioning
        method = MetisPartition(4)
        partition_info = partition_mesh(mesh, method)
        
        @test partition_info.partition.n_subdomains == 4
        @test partition_info.partition.n_cells == 16
        @test length(partition_info.partition.cell_processor) == 16
        
        # Check that all processors have at least one cell
        cells_per_proc = [length(cells) for cells in partition_info.partition.processor_cells]
        @test all(x -> x > 0, cells_per_proc)
        
        # Check load balance is reasonable
        @test partition_info.partition.load_imbalance < 2.0  # Allow some imbalance for small mesh
    end
    
    @testset "Partition Quality Metrics" begin
        # Create simple 2-cell mesh
        points = [
            Point3D(0.0, 0.0, 0.0),
            Point3D(1.0, 0.0, 0.0),
            Point3D(2.0, 0.0, 0.0),
            Point3D(0.0, 1.0, 0.0),
            Point3D(1.0, 1.0, 0.0),
            Point3D(2.0, 1.0, 0.0),
        ]
        
        face_point_lists = [
            [2, 5],     # Internal face between cells
            [1, 2],     # Boundary
            [1, 4],     # Boundary
            [4, 5],     # Boundary
            [2, 3],     # Boundary
            [3, 6],     # Boundary
            [5, 6],     # Boundary
        ]
        
        face_owners = [1, 1, 1, 1, 2, 2, 2]
        face_neighbors = [2, -1, -1, -1, -1, -1, -1]
        boundary_patches = UnstructuredBoundaryPatch[]

        mesh = Mesh(points, face_point_lists, face_owners, face_neighbors, boundary_patches)
        
        # Partition into 2 processors
        method = SimplePartition(2, :x)
        partition_info = partition_mesh(mesh, method)
        
        # Test quality metrics
        @test haskey(partition_info.quality_metrics, "load_imbalance")
        @test haskey(partition_info.quality_metrics, "edge_cut")
        @test haskey(partition_info.quality_metrics, "parallel_efficiency")
        
        # For this simple case, should have perfect load balance
        @test partition_info.quality_metrics["load_imbalance"] ≈ 1.0
    end
end

println("METIS integration tests completed!")
