"""
Test comprehensive field I/O system
"""

using Test
using LinearAlgebra
using StaticArrays

# Add the source directory to the path
push!(LOAD_PATH, joinpath(@__DIR__, "..", "..", "src"))

# Import required modules
include("../../src/parallel/ParallelIO.jl")
using .ParallelIO

@testset "Field I/O System Tests" begin
    
    # Create temporary test directory
    test_dir = mktempdir()
    
    @testset "Scalar Field Writing and Reading" begin
        # Test uniform scalar field
        field_file = joinpath(test_dir, "test_scalar_uniform.txt")
        internal_values = [1.5, 1.5, 1.5, 1.5]  # Uniform field
        boundary_conditions = Dict{String, Any}(
            "inlet" => Dict{String, Any}("type" => "fixedValue", "value" => 2.0),
            "outlet" => Dict{String, Any}("type" => "zeroGradient"),
            "walls" => Dict{String, Any}("type" => "fixedValue", "value" => 0.0)
        )
        
        # Write field
        write_processor_scalar_field(field_file, "temperature", internal_values, 
                                   boundary_conditions, "[0 0 0 1 0 0 0]")
        
        @test isfile(field_file)
        
        # Read field back
        read_data = read_processor_scalar_field(field_file)
        
        @test read_data["field_type"] == "scalar"
        @test read_data["dimensions"] == "[0 0 0 1 0 0 0]"
        @test length(read_data["internal_values"]) >= 1
        @test haskey(read_data["boundary_conditions"], "inlet")
        @test read_data["boundary_conditions"]["inlet"]["type"] == "fixedValue"
        
        # Test non-uniform scalar field
        field_file_nonuniform = joinpath(test_dir, "test_scalar_nonuniform.txt")
        internal_values_nonuniform = [1.0, 2.0, 3.0, 4.0, 5.0]
        
        write_processor_scalar_field(field_file_nonuniform, "pressure", 
                                   internal_values_nonuniform, boundary_conditions)
        
        read_data_nonuniform = read_processor_scalar_field(field_file_nonuniform)
        @test length(read_data_nonuniform["internal_values"]) == 5
    end
    
    @testset "Vector Field Writing and Reading" begin
        # Test uniform vector field
        field_file = joinpath(test_dir, "test_vector_uniform.txt")
        uniform_vector = SVector{3,Float64}(1.0, 0.0, 0.0)
        internal_values = [uniform_vector, uniform_vector, uniform_vector]
        boundary_conditions = Dict{String, Any}(
            "inlet" => Dict{String, Any}("type" => "fixedValue", "value" => [2.0, 0.0, 0.0]),
            "outlet" => Dict{String, Any}("type" => "zeroGradient"),
            "walls" => Dict{String, Any}("type" => "fixedValue", "value" => [0.0, 0.0, 0.0])
        )
        
        # Write field
        write_processor_vector_field(field_file, "velocity", internal_values, 
                                   boundary_conditions, "[0 1 -1 0 0 0 0]")
        
        @test isfile(field_file)
        
        # Read field back
        read_data = read_processor_vector_field(field_file)
        
        @test read_data["field_type"] == "vector"
        @test read_data["dimensions"] == "[0 1 -1 0 0 0 0]"
        @test length(read_data["internal_values"]) >= 1
        @test haskey(read_data["boundary_conditions"], "inlet")
        
        # Test non-uniform vector field
        field_file_nonuniform = joinpath(test_dir, "test_vector_nonuniform.txt")
        internal_values_nonuniform = [
            SVector{3,Float64}(1.0, 0.0, 0.0),
            SVector{3,Float64}(0.0, 1.0, 0.0),
            SVector{3,Float64}(0.0, 0.0, 1.0),
            SVector{3,Float64}(1.0, 1.0, 0.0)
        ]
        
        write_processor_vector_field(field_file_nonuniform, "displacement", 
                                   internal_values_nonuniform, boundary_conditions)
        
        read_data_nonuniform = read_processor_vector_field(field_file_nonuniform)
        @test length(read_data_nonuniform["internal_values"]) == 4
    end
    
    @testset "Field Decomposition and Reconstruction" begin
        # Create test partition info
        partition_info = Dict{String, Any}(
            "processor_0" => [1, 2, 3],
            "processor_1" => [4, 5, 6],
            "processor_2" => [7, 8, 9, 10]
        )
        
        # Create test case directory structure
        case_dir = joinpath(test_dir, "test_case")
        time_dir = "0"
        mkpath(joinpath(case_dir, time_dir))
        
        # Create original scalar field
        original_scalar_field = joinpath(case_dir, time_dir, "T")
        original_values = [100.0, 200.0, 300.0, 400.0, 500.0, 600.0, 700.0, 800.0, 900.0, 1000.0]
        boundary_conditions = Dict{String, Any}(
            "inlet" => Dict{String, Any}("type" => "fixedValue", "value" => 300.0),
            "outlet" => Dict{String, Any}("type" => "zeroGradient")
        )
        
        write_processor_scalar_field(original_scalar_field, "T", original_values, 
                                   boundary_conditions, "[0 0 0 1 0 0 0]")
        
        # Test decomposition
        decomposed_fields = decompose_fields(case_dir, time_dir, ["T"], partition_info, 3)
        
        @test haskey(decomposed_fields, "T")
        @test length(decomposed_fields["T"]) == 3
        
        # Check processor 0 has cells 1,2,3
        proc0_field = decomposed_fields["T"][1]
        @test length(proc0_field["internal_values"]) == 3
        @test proc0_field["field_type"] == "scalar"
        
        # Write decomposed fields
        write_processor_fields(case_dir, time_dir, decomposed_fields, 3)
        
        # Check that processor directories were created
        @test isdir(joinpath(case_dir, "processor0", time_dir))
        @test isdir(joinpath(case_dir, "processor1", time_dir))
        @test isdir(joinpath(case_dir, "processor2", time_dir))
        
        # Test reconstruction
        reconstructed_fields = reconstruct_fields(case_dir, time_dir, ["T"], partition_info, 3)
        
        @test haskey(reconstructed_fields, "T")
        reconstructed_T = reconstructed_fields["T"]
        @test length(reconstructed_T["internal_values"]) == 10
        @test reconstructed_T["field_type"] == "scalar"
    end
    
    @testset "Field Validation" begin
        # Create test fields for validation
        original_field = Dict(
            "internal_values" => [1.0, 2.0, 3.0, 4.0, 5.0, 6.0],
            "field_type" => "scalar",
            "boundary_conditions" => Dict(),
            "dimensions" => "[0 0 0 0 0 0 0]"
        )
        
        decomposed_fields = [
            Dict("internal_values" => [1.0, 2.0], "field_type" => "scalar"),
            Dict("internal_values" => [3.0, 4.0], "field_type" => "scalar"),
            Dict("internal_values" => [5.0, 6.0], "field_type" => "scalar")
        ]
        
        validation_results = validate_field_decomposition(original_field, decomposed_fields)
        
        @test validation_results["size_conservation"] == true
        @test validation_results["original_size"] == 6
        @test validation_results["decomposed_size"] == 6
        @test validation_results["value_conservation"] == true
        @test validation_results["original_sum"] ≈ validation_results["decomposed_sum"]
    end
    
    @testset "Performance Benchmarking" begin
        # Test scalar field benchmark
        scalar_field = Dict(
            "internal_values" => rand(1000),
            "field_type" => "scalar",
            "boundary_conditions" => Dict{String, Any}("inlet" => Dict{String, Any}("type" => "fixedValue", "value" => 1.0)),
            "dimensions" => "[0 0 0 0 0 0 0]"
        )
        
        benchmark_file = joinpath(test_dir, "benchmark_scalar.txt")
        scalar_benchmark = benchmark_field_io(scalar_field, benchmark_file)
        
        @test haskey(scalar_benchmark, "write_time_seconds")
        @test haskey(scalar_benchmark, "read_time_seconds")
        @test haskey(scalar_benchmark, "field_size")
        @test scalar_benchmark["field_size"] == 1000
        @test scalar_benchmark["write_rate_fields_per_second"] > 0
        
        # Test vector field benchmark
        vector_field = Dict(
            "internal_values" => [SVector{3,Float64}(rand(3)...) for _ in 1:500],
            "field_type" => "vector",
            "boundary_conditions" => Dict{String, Any}("inlet" => Dict{String, Any}("type" => "fixedValue", "value" => [1.0, 0.0, 0.0])),
            "dimensions" => "[0 1 -1 0 0 0 0]"
        )
        
        benchmark_file_vector = joinpath(test_dir, "benchmark_vector.txt")
        vector_benchmark = benchmark_field_io(vector_field, benchmark_file_vector)
        
        @test vector_benchmark["field_size"] == 500
        @test vector_benchmark["write_rate_fields_per_second"] > 0
        @test vector_benchmark["read_rate_fields_per_second"] > 0
    end
    
    @testset "Mesh I/O Benchmark" begin
        # Test mesh I/O benchmark (fallback mode)
        proc_dir = joinpath(test_dir, "processor_test")
        mesh_benchmark = ParallelIO.benchmark_mesh_io(nothing, proc_dir)
        
        @test haskey(mesh_benchmark, "write_time_seconds")
        @test haskey(mesh_benchmark, "read_time_seconds")
        @test haskey(mesh_benchmark, "mesh_size_cells")
        @test mesh_benchmark["mesh_size_cells"] > 0
    end
    
    # Cleanup
    rm(test_dir, recursive=true)
end

println("Field I/O system tests completed!")
