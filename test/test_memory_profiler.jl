"""
Test suite for memory profiling functionality in JuliaFOAM.
"""

# Add the parent directory to the load path for local development
push!(LOAD_PATH, joinpath(@__DIR__, "..", "src"))

using Test
using JuliaFOAM
using JuliaFOAM.Tools
using Statistics
using LinearAlgebra

@testset "Memory Profiler Tests" begin
    
    @testset "Basic Memory Profiling" begin
        # Test simple function profiling
        function test_allocation_function(n::Int)
            # Deliberately allocate memory
            arrays = [rand(100) for _ in 1:n]
            return sum(sum(arr) for arr in arrays)
        end
        
        # Profile the function
        profile, result = profile_memory_usage(test_allocation_function, 10; 
                                             profile_name="test_allocation")
        
        @test profile.name == "test_allocation"
        @test profile.end_time !== nothing
        @test profile.peak_memory >= profile.baseline_memory
        @test haskey(profile.gc_stats, "total_allocations")
        @test haskey(profile.gc_stats, "allocd")
        
        # Test that result is returned correctly
        @test result isa Float64
        @test result > 0
    end
    
    @testset "Allocation Analysis" begin
        # Create a test function with known allocation pattern
        function allocation_heavy_function()
            # Multiple allocation patterns
            a = rand(1000)  # Large allocation
            b = [rand(10) for _ in 1:100]  # Many small allocations
            c = zeros(500)  # Zero allocation
            return sum(a) + sum(sum(arr) for arr in b) + sum(c)
        end
        
        # Profile and analyze
        profile, _ = profile_memory_usage(allocation_heavy_function; 
                                        profile_name="allocation_analysis")
        analysis = analyze_allocations(profile)
        
        @test analysis isa ProfileResult
        @test analysis.profile_name == "allocation_analysis"
        @test analysis.total_allocations >= 0
        @test analysis.peak_memory_mb >= 0
        @test analysis.gc_time_percentage >= 0
        @test length(analysis.allocation_hotspots) >= 0
        @test length(analysis.recommendations) >= 0
        
        # Test that hotspots are sorted by allocation count
        if length(analysis.allocation_hotspots) > 1
            for i in 1:(length(analysis.allocation_hotspots)-1)
                @test analysis.allocation_hotspots[i][2] >= analysis.allocation_hotspots[i+1][2]
            end
        end
    end
    
    @testset "Memory Report Generation" begin
        # Create a simple test case
        function simple_test_function()
            return sum(rand(100))
        end
        
        profile, _ = profile_memory_usage(simple_test_function; 
                                        profile_name="report_test")
        analysis = analyze_allocations(profile)
        
        # Generate report
        report_file = "test_memory_report.md"
        create_memory_report(analysis, report_file)
        
        # Verify report file exists and has content
        @test isfile(report_file)
        content = read(report_file, String)
        @test contains(content, "Memory Analysis Report")
        @test contains(content, "report_test")
        @test contains(content, "Summary")
        @test contains(content, "Allocation Hotspots")
        @test contains(content, "Optimization Recommendations")
        
        # Clean up
        rm(report_file, force=true)
    end
    
    @testset "Benchmark Memory Performance" begin
        # Test benchmarking functionality
        function benchmark_test_function(n::Int)
            return sum(rand(n))
        end
        
        # Run benchmark with small sample size for testing
        results = benchmark_memory_performance(benchmark_test_function, 100; 
                                             samples=3, profile_name="benchmark_test")
        
        @test length(results) == 3
        @test all(r -> r isa ProfileResult, results)
        @test all(r -> r.profile_name == "benchmark_test_1" || 
                      r.profile_name == "benchmark_test_2" || 
                      r.profile_name == "benchmark_test_3", results)
    end
    
    @testset "Allocation Hotspot Tracking" begin
        # Create multiple test functions with different allocation patterns
        function low_allocation_function()
            return sum(1:100)  # No allocations
        end
        
        function medium_allocation_function()
            return sum(rand(100))  # Small allocation
        end
        
        function high_allocation_function()
            return sum(rand(1000))  # Large allocation
        end
        
        functions = [low_allocation_function, medium_allocation_function, high_allocation_function]
        args_list = [(), (), ()]  # No arguments for any function
        
        results = track_allocation_hotspots(functions, args_list; 
                                          profile_name="hotspot_test")
        
        @test length(results) == 3
        @test all(r -> r isa ProfileResult, results)
        
        # Verify that high allocation function uses more memory
        # Note: This might not always be true due to GC timing, so we make it a soft check
        if results[3].peak_memory_mb > 0 && results[1].peak_memory_mb >= 0
            @test results[3].peak_memory_mb >= results[1].peak_memory_mb
        end
    end
    
    @testset "Error Handling" begin
        # Test error handling for incomplete profiles
        incomplete_profile = MemoryProfile("incomplete")
        @test_throws ErrorException analyze_allocations(incomplete_profile)
        
        # Test error handling for mismatched function/args lists
        functions = [x -> x^2, x -> x^3]
        args_list = [(2,)]  # Only one argument list for two functions
        
        @test_throws ErrorException track_allocation_hotspots(functions, args_list)
    end
    
    @testset "JuliaFOAM Integration" begin
        # Test integration with JuliaFOAM types (if available)
        try
            # Create a simple mesh for testing
            mesh = create_box_mesh(10, 10, 10, 1.0, 1.0, 1.0)
            
            # Create test fields
            U = Field("U", mesh, SVector{3,Float64}(0.0, 0.0, 0.0))
            p = Field("p", mesh, 0.0)
            fields = Dict("U" => U, "p" => p)
            
            # Create a simple test solver function
            function test_solver(mesh, fields, config)
                # Simulate some solver operations
                n_cells = length(mesh.cells)
                temp_array = zeros(n_cells)
                for i in 1:n_cells
                    temp_array[i] = i * 0.1
                end
                return fields, Dict("residual" => norm(temp_array))
            end
            
            # Test solver profiling
            config = Dict("max_iterations" => 10)
            analysis, profile = profile_juliafoam_solver(test_solver, mesh, fields, config; 
                                                       profile_name="test_solver")
            
            @test analysis isa ProfileResult
            @test analysis.profile_name == "test_solver"
            @test profile isa MemoryProfile
            
            # Test that solver-specific recommendations are generated
            @test length(analysis.recommendations) >= 0
            
        catch e
            # If JuliaFOAM types are not available, skip this test
            if isa(e, UndefVarError) || isa(e, MethodError)
                @test_skip "JuliaFOAM integration test skipped - types not available"
            else
                rethrow(e)
            end
        end
    end
    
    @testset "Performance Thresholds" begin
        # Test recommendation generation based on performance thresholds
        
        # Create a high-allocation function to trigger recommendations
        function high_gc_function()
            arrays = [rand(1000) for _ in 1:100]  # Force many allocations
            GC.gc()  # Force garbage collection
            return sum(sum(arr) for arr in arrays)
        end
        
        profile, _ = profile_memory_usage(high_gc_function; 
                                        profile_name="high_gc_test")
        analysis = analyze_allocations(profile)
        
        # Should have some recommendations due to allocations
        @test length(analysis.recommendations) > 0
        
        # Test that recommendations contain relevant advice
        rec_text = join(analysis.recommendations, " ")
        # At least one recommendation should be present for high allocation patterns
        @test length(analysis.recommendations) >= 1
    end
end
