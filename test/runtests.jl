using Test
using Pkg

# Activate the project environment
Pkg.activate(dirname(@__DIR__))

# Add test dependencies if not already added
if !haskey(Pkg.project().dependencies, "Test")
    Pkg.add("Test")
end
if !haskey(Pkg.project().dependencies, "BenchmarkTools")
    Pkg.add("BenchmarkTools")
end

# Add the package directory to LOAD_PATH
push!(LOAD_PATH, joinpath(dirname(@__DIR__), "src"))

# Define test directories
test_dirs = [
    joinpath(@__DIR__, "..", "experiments", "tests", "unit"),
    joinpath(@__DIR__, "..", "experiments", "tests", "test")
]

# Function to find all test files
function find_test_files(dirs)
    test_files = String[]
    for dir in dirs
        isdir(dir) || continue
        for (root, _, files) in walkdir(dir)
            for file in files
                if endswith(file, ".jl") && (occursin(r"test[^/]*\.jl$", file) || occursin(r"_test\.jl$", file))
                    push!(test_files, joinpath(root, file))
                end
            end
        end
    end
    return test_files
end

# Find and run all test files
@testset "JuliaFOAM Tests" begin
    test_files = find_test_files(test_dirs)
    if isempty(test_files)
        @warn "No test files found in: $(join(test_dirs, ", "))"
    else
        println("Found $(length(test_files)) test files")
        for test_file in test_files
            @testset "$(basename(test_file))" begin
                println("\nRunning tests in: $(relpath(test_file, dirname(@__DIR__)))")
                include(test_file)
            end
        end
    end
end
