"""
Test suite for communication pattern analysis functionality in JuliaFOAM.
"""

using Test
using MPI
using StaticArrays
using LinearAlgebra
using Statistics

# Include the communication analyzer directly for testing
include("../src/tools/CommunicationAnalyzer.jl")
using .CommunicationAnalyzer

# Mock mesh structure for testing
struct MockOptimizedMesh
    cells::Vector{Any}
    send_maps::Dict{Int, Vector{Int}}
    recv_maps::Dict{Int, Vector{Int}}
end

function create_mock_mesh(n_cells::Int, n_neighbors::Int)
    cells = [nothing for _ in 1:n_cells]  # Simplified cells
    
    send_maps = Dict{Int, Vector{Int}}()
    recv_maps = Dict{Int, Vector{Int}}()
    
    # Create mock communication patterns
    for neighbor in 1:n_neighbors
        # Each neighbor gets some cells to send/receive
        send_indices = collect(1:min(10, n_cells÷2))
        recv_indices = collect((n_cells÷2+1):min(n_cells÷2+10, n_cells))
        
        send_maps[neighbor] = send_indices
        recv_maps[neighbor] = recv_indices
    end
    
    return MockOptimizedMesh(cells, send_maps, recv_maps)
end

@testset "Communication Analyzer Tests" begin
    
    # Initialize MPI for testing (if not already initialized)
    if !MPI.Initialized()
        MPI.Init()
    end
    
    comm = MPI.COMM_WORLD
    rank = MPI.Comm_rank(comm)
    size = MPI.Comm_size(comm)
    
    @testset "CommunicationProfile Creation" begin
        profile = CommunicationProfile("test_profile")
        
        @test profile.name == "test_profile"
        @test profile.end_time === nothing
        @test isempty(profile.message_counts)
        @test isempty(profile.message_sizes)
        @test profile.communication_volume == 0.0
        @test profile.load_imbalance == 0.0
        
        println("✓ CommunicationProfile creation test passed")
    end
    
    @testset "Communication Pattern Analysis" begin
        # Create mock mesh
        mesh = create_mock_mesh(100, 3)
        
        # Analyze communication patterns
        profile = analyze_communication_patterns(mesh, comm; profile_name="pattern_test")
        
        @test profile.name == "pattern_test"
        @test profile.end_time !== nothing
        @test profile.communication_volume >= 0.0
        @test profile.load_imbalance >= 0.0
        @test profile.communication_imbalance >= 0.0
        
        # Check that message counts are recorded
        @test length(profile.message_counts) <= 3  # At most 3 neighbors
        
        println("✓ Communication pattern analysis test passed")
        println("  Communication volume: $(round(profile.communication_volume / 1024, digits=2)) KB")
        println("  Load imbalance: $(round(profile.load_imbalance * 100, digits=2))%")
    end
    
    @testset "Load Balance Analysis" begin
        # Create mock mesh and work weights
        mesh = create_mock_mesh(100, 2)
        work_weights = rand(100)  # Random work distribution
        
        # Analyze load balance
        metrics = analyze_load_balance(mesh, comm, work_weights; profile_name="load_test")
        
        @test metrics.profile_name == "load_test"
        @test metrics.n_processes == size
        @test length(metrics.work_per_process) == size
        @test metrics.work_imbalance >= 0.0
        @test metrics.max_work_ratio >= 1.0
        @test metrics.parallel_efficiency >= 0.0
        @test metrics.parallel_efficiency <= 1.0
        @test length(metrics.recommendations) >= 0
        
        println("✓ Load balance analysis test passed")
        println("  Work imbalance: $(round(metrics.work_imbalance * 100, digits=2))%")
        println("  Parallel efficiency: $(round(metrics.parallel_efficiency * 100, digits=2))%")
        println("  Recommendations: $(length(metrics.recommendations))")
    end
    
    @testset "Halo Exchange Profiling" begin
        # Create test field and mesh
        mesh = create_mock_mesh(50, 2)
        field = rand(50)
        
        # Profile halo exchange (with reduced iterations for testing)
        profile = profile_halo_exchange(field, mesh, comm; n_iterations=3, profile_name="halo_test")
        
        @test profile.name == "halo_test"
        @test profile.communication_time >= 0.0
        @test profile.peak_bandwidth >= 0.0
        
        # Check that timing data is recorded
        total_send_times = sum(length(times) for times in values(profile.send_times))
        @test total_send_times >= 0
        
        println("✓ Halo exchange profiling test passed")
        println("  Communication time: $(round(profile.communication_time * 1000, digits=2)) ms")
        println("  Peak bandwidth: $(round(profile.peak_bandwidth, digits=2)) MB/s")
    end
    
    @testset "Overlap Analysis" begin
        # Create test setup
        mesh = create_mock_mesh(30, 2)
        field = rand(30)
        
        # Simple computation function for testing
        function test_computation(field, mesh)
            # Simulate some computation
            for i in 1:length(field)
                field[i] = field[i] * 1.1 + 0.1
            end
        end
        
        # Analyze overlap opportunities
        analysis = identify_overlap_opportunities(mesh, comm, test_computation, field; 
                                                profile_name="overlap_test")
        
        @test analysis.profile_name == "overlap_test"
        @test analysis.total_time >= 0.0
        @test analysis.computation_time >= 0.0
        @test analysis.communication_time >= 0.0
        @test analysis.overlap_ratio >= 0.0
        @test analysis.efficiency_gain >= 0.0
        @test analysis.current_overlap_efficiency >= 0.0
        @test length(analysis.overlap_opportunities) >= 0
        
        println("✓ Overlap analysis test passed")
        println("  Overlap ratio: $(round(analysis.overlap_ratio * 100, digits=2))%")
        println("  Efficiency gain: $(round(analysis.efficiency_gain * 100, digits=2))%")
        println("  Opportunities: $(length(analysis.overlap_opportunities))")
    end
    
    @testset "Communication Report Generation" begin
        # Create test data
        mesh = create_mock_mesh(40, 2)
        field = rand(40)
        work_weights = rand(40)
        
        function simple_computation(field, mesh)
            field .= field .* 1.01
        end
        
        # Generate all analysis components
        comm_profile = analyze_communication_patterns(mesh, comm; profile_name="report_test")
        load_metrics = analyze_load_balance(mesh, comm, work_weights; profile_name="report_test")
        overlap_analysis = identify_overlap_opportunities(mesh, comm, simple_computation, field; 
                                                        profile_name="report_test")
        
        # Create report
        report_file = "test_communication_report.md"
        create_communication_report(comm_profile, load_metrics, overlap_analysis, report_file)
        
        # Verify report file exists and has content
        @test isfile(report_file)
        content = read(report_file, String)
        @test contains(content, "Communication Pattern Analysis Report")
        @test contains(content, "Executive Summary")
        @test contains(content, "Load Balance Analysis")
        @test contains(content, "Overlap Analysis")
        @test contains(content, "Optimization Recommendations")
        
        println("✓ Communication report generation test passed")
        println("  Report saved to: $report_file")
        
        # Clean up
        rm(report_file, force=true)
    end
    
    @testset "Performance Metrics Validation" begin
        # Test that metrics are within expected ranges
        mesh = create_mock_mesh(60, 3)
        work_weights = ones(60)  # Perfectly balanced work
        
        metrics = analyze_load_balance(mesh, comm, work_weights; profile_name="validation_test")
        
        # With perfectly balanced work, imbalance should be very low
        @test metrics.work_imbalance < 0.1
        @test metrics.max_work_ratio < 1.1
        @test metrics.parallel_efficiency > 0.8
        
        # Test with imbalanced work
        imbalanced_weights = [i <= 30 ? 2.0 : 0.5 for i in 1:60]
        imbalanced_metrics = analyze_load_balance(mesh, comm, imbalanced_weights; 
                                                profile_name="imbalanced_test")
        
        # Should detect imbalance
        @test imbalanced_metrics.work_imbalance > metrics.work_imbalance
        @test imbalanced_metrics.max_work_ratio > metrics.max_work_ratio
        @test imbalanced_metrics.parallel_efficiency < metrics.parallel_efficiency
        
        println("✓ Performance metrics validation test passed")
        println("  Balanced work imbalance: $(round(metrics.work_imbalance * 100, digits=2))%")
        println("  Imbalanced work imbalance: $(round(imbalanced_metrics.work_imbalance * 100, digits=2))%")
    end
    
    @testset "Error Handling and Edge Cases" begin
        # Test with empty mesh
        empty_mesh = MockOptimizedMesh([], Dict{Int, Vector{Int}}(), Dict{Int, Vector{Int}}())
        
        # Should handle empty mesh gracefully
        @test_nowarn analyze_communication_patterns(empty_mesh, comm; profile_name="empty_test")
        
        # Test with single process (if applicable)
        if size == 1
            mesh = create_mock_mesh(20, 0)  # No neighbors
            profile = analyze_communication_patterns(mesh, comm; profile_name="single_proc_test")
            
            @test profile.communication_volume == 0.0
            @test profile.communication_imbalance == 0.0
        end
        
        # Test with zero work
        mesh = create_mock_mesh(10, 1)
        zero_work = zeros(10)
        
        @test_nowarn analyze_load_balance(mesh, comm, zero_work; profile_name="zero_work_test")
        
        println("✓ Error handling and edge cases test passed")
    end
end

println("\n" * "="^60)
println("Communication Analyzer Test Summary")
println("="^60)
println("All tests passed successfully!")
println("The communication analyzer is ready for use in JuliaFOAM parallel optimization.")
println("MPI processes: $size")
println("="^60)
