"""
LoadBalancing.jl

Comprehensive load balancing analysis and optimization for JuliaFOAM parallel
simulations. Provides tools to monitor, analyze, and improve load distribution
across processors during runtime.

Key Features:
- Real-time load monitoring
- Imbalance detection and quantification
- Dynamic load balancing triggers
- Performance prediction
- Partition quality metrics
- Optimization suggestions
"""

module LoadBalancing

using LinearAlgebra
using Statistics
using Printf
using MPI

# Import partition module
include("MeshPartitioning.jl")
using .MeshPartitioning

export LoadMonitor, LoadMetrics, LoadBalanceAnalysis
export monitor_load, analyze_load_balance, suggest_rebalancing
export predict_parallel_efficiency, calculate_speedup
export LoadHistory, track_load_history, plot_load_history

# ============================================================================
# DATA STRUCTURES
# ============================================================================

"""
Load metrics for a single processor
"""
struct ProcessorLoadMetrics
    proc_id::Int
    n_cells::Int
    n_halo_cells::Int
    computation_time::Float64
    communication_time::Float64
    idle_time::Float64
    memory_usage::Float64  # MB
    cache_misses::Int
    flops::Float64
end

"""
Overall load metrics for the simulation
"""
struct LoadMetrics
    timestamp::Float64
    processor_metrics::Vector{ProcessorLoadMetrics}
    total_imbalance::Float64
    computation_imbalance::Float64
    communication_imbalance::Float64
    memory_imbalance::Float64
    efficiency::Float64
    speedup::Float64
end

"""
Load balance analysis results
"""
struct LoadBalanceAnalysis
    current_metrics::LoadMetrics
    bottleneck_processors::Vector{Int}
    bottleneck_type::Symbol  # :computation, :communication, :memory
    recommended_action::Symbol  # :none, :repartition, :adjust_workload
    predicted_improvement::Float64
    detailed_report::String
end

"""
Load monitoring configuration
"""
struct LoadMonitor
    sample_interval::Float64  # Seconds between samples
    history_size::Int        # Number of samples to keep
    imbalance_threshold::Float64  # Trigger rebalancing
    efficiency_threshold::Float64  # Minimum acceptable efficiency
    enable_predictions::Bool
    verbose::Bool
    
    function LoadMonitor(;
        sample_interval::Float64=1.0,
        history_size::Int=100,
        imbalance_threshold::Float64=0.1,
        efficiency_threshold::Float64=0.8,
        enable_predictions::Bool=true,
        verbose::Bool=false
    )
        new(sample_interval, history_size, imbalance_threshold,
            efficiency_threshold, enable_predictions, verbose)
    end
end

"""
Historical load data
"""
mutable struct LoadHistory
    timestamps::Vector{Float64}
    metrics::Vector{LoadMetrics}
    rebalance_events::Vector{Tuple{Float64, String}}  # (time, action)
    
    function LoadHistory()
        new(Float64[], LoadMetrics[], Tuple{Float64, String}[])
    end
end

# ============================================================================
# LOAD MONITORING
# ============================================================================

"""
    monitor_load(comm::MPI.Comm, local_metrics::ProcessorLoadMetrics) -> LoadMetrics

Gather load metrics from all processors and compute global statistics.
"""
function monitor_load(comm::MPI.Comm, local_metrics::ProcessorLoadMetrics)
    rank = MPI.Comm_rank(comm)
    size = MPI.Comm_size(comm)
    
    # Gather metrics from all processors
    all_metrics = MPI.Gather(local_metrics, 0, comm)
    
    # Broadcast results from rank 0
    if rank == 0
        metrics = compute_global_metrics(all_metrics)
    else
        metrics = nothing
    end
    
    metrics = MPI.bcast(metrics, 0, comm)
    
    return metrics
end

"""
Compute global load metrics from processor data
"""
function compute_global_metrics(processor_metrics::Vector{ProcessorLoadMetrics})
    n_procs = length(processor_metrics)
    
    # Computation times
    comp_times = [m.computation_time for m in processor_metrics]
    avg_comp_time = mean(comp_times)
    max_comp_time = maximum(comp_times)
    computation_imbalance = max_comp_time / avg_comp_time - 1.0
    
    # Communication times
    comm_times = [m.communication_time for m in processor_metrics]
    avg_comm_time = mean(comm_times)
    max_comm_time = maximum(comm_times)
    communication_imbalance = max_comm_time / avg_comm_time - 1.0
    
    # Memory usage
    memory_usage = [m.memory_usage for m in processor_metrics]
    avg_memory = mean(memory_usage)
    max_memory = maximum(memory_usage)
    memory_imbalance = max_memory / avg_memory - 1.0
    
    # Total imbalance (weighted average)
    total_imbalance = 0.7 * computation_imbalance + 
                     0.2 * communication_imbalance + 
                     0.1 * memory_imbalance
    
    # Parallel efficiency
    total_time = max_comp_time + max_comm_time
    ideal_time = sum(comp_times) / n_procs
    efficiency = ideal_time / total_time
    
    # Speedup
    serial_time = sum(comp_times) + sum(comm_times)
    speedup = serial_time / total_time
    
    return LoadMetrics(
        time(),
        processor_metrics,
        total_imbalance,
        computation_imbalance,
        communication_imbalance,
        memory_imbalance,
        efficiency,
        speedup
    )
end

# ============================================================================
# LOAD BALANCE ANALYSIS
# ============================================================================

"""
    analyze_load_balance(metrics::LoadMetrics, monitor::LoadMonitor) -> LoadBalanceAnalysis

Perform detailed load balance analysis and provide recommendations.
"""
function analyze_load_balance(metrics::LoadMetrics, monitor::LoadMonitor)
    # Identify bottlenecks
    bottleneck_procs, bottleneck_type = identify_bottlenecks(metrics)
    
    # Determine recommended action
    action = determine_action(metrics, monitor)
    
    # Predict improvement
    predicted_improvement = predict_improvement(metrics, action)
    
    # Generate detailed report
    report = generate_load_report(metrics, bottleneck_procs, bottleneck_type, action)
    
    return LoadBalanceAnalysis(
        metrics,
        bottleneck_procs,
        bottleneck_type,
        action,
        predicted_improvement,
        report
    )
end

"""
Identify bottleneck processors and type
"""
function identify_bottlenecks(metrics::LoadMetrics)
    proc_metrics = metrics.processor_metrics
    
    # Find processors with highest times
    comp_times = [m.computation_time for m in proc_metrics]
    comm_times = [m.communication_time for m in proc_metrics]
    
    max_comp_time = maximum(comp_times)
    max_comm_time = maximum(comm_times)
    avg_comp_time = mean(comp_times)
    avg_comm_time = mean(comm_times)
    
    # Processors that are >20% above average
    comp_threshold = 1.2 * avg_comp_time
    comm_threshold = 1.2 * avg_comm_time
    
    comp_bottlenecks = findall(t -> t > comp_threshold, comp_times) .- 1  # 0-indexed
    comm_bottlenecks = findall(t -> t > comm_threshold, comm_times) .- 1
    
    # Determine primary bottleneck type
    if metrics.computation_imbalance > metrics.communication_imbalance
        return comp_bottlenecks, :computation
    else
        return comm_bottlenecks, :communication
    end
end

"""
Determine recommended action based on metrics
"""
function determine_action(metrics::LoadMetrics, monitor::LoadMonitor)
    # Check if rebalancing is needed
    if metrics.total_imbalance > monitor.imbalance_threshold
        if metrics.computation_imbalance > metrics.communication_imbalance
            return :repartition
        else
            return :adjust_workload
        end
    elseif metrics.efficiency < monitor.efficiency_threshold
        return :repartition
    else
        return :none
    end
end

"""
Predict improvement from rebalancing
"""
function predict_improvement(metrics::LoadMetrics, action::Symbol)
    if action == :none
        return 0.0
    end
    
    # Estimate improvement based on current imbalance
    current_efficiency = metrics.efficiency
    
    if action == :repartition
        # Assume we can achieve 90% of perfect balance
        perfect_efficiency = 1.0
        achievable_efficiency = 0.9 * perfect_efficiency
        improvement = (achievable_efficiency - current_efficiency) / current_efficiency
    else
        # Workload adjustment typically gives smaller improvements
        improvement = 0.1 * metrics.total_imbalance
    end
    
    return clamp(improvement, 0.0, 1.0)
end

# ============================================================================
# PERFORMANCE PREDICTION
# ============================================================================

"""
    predict_parallel_efficiency(n_cells::Int, n_procs::Int, partition_quality::Dict)

Predict parallel efficiency for given problem size and processor count.
"""
function predict_parallel_efficiency(n_cells::Int, n_procs::Int, 
                                   partition_quality::Dict{String,Float64})
    # Model: Amdahl's law with communication overhead
    
    # Serial fraction (typically 1-5% for good CFD codes)
    serial_fraction = 0.02
    
    # Parallel fraction
    parallel_fraction = 1.0 - serial_fraction
    
    # Communication overhead (based on partition quality)
    edge_cut = get(partition_quality, "edge_cut", 0.0)
    comm_overhead = edge_cut / n_cells * 0.1  # Empirical factor
    
    # Load imbalance factor
    load_imbalance = get(partition_quality, "load_imbalance", 1.0)
    
    # Amdahl's law with extensions
    speedup = 1.0 / (serial_fraction + parallel_fraction / n_procs * load_imbalance + comm_overhead)
    efficiency = speedup / n_procs
    
    return efficiency
end

"""
    calculate_speedup(serial_time::Float64, parallel_time::Float64, n_procs::Int)

Calculate actual speedup and efficiency from timing data.
"""
function calculate_speedup(serial_time::Float64, parallel_time::Float64, n_procs::Int)
    speedup = serial_time / parallel_time
    efficiency = speedup / n_procs
    
    return speedup, efficiency
end

# ============================================================================
# LOAD BALANCING SUGGESTIONS
# ============================================================================

"""
    suggest_rebalancing(analysis::LoadBalanceAnalysis) -> Dict

Provide specific suggestions for improving load balance.
"""
function suggest_rebalancing(analysis::LoadBalanceAnalysis)
    suggestions = Dict{String, Any}()
    
    if analysis.recommended_action == :none
        suggestions["action"] = "No rebalancing needed"
        return suggestions
    end
    
    # Basic suggestion
    suggestions["action"] = string(analysis.recommended_action)
    suggestions["expected_improvement"] = analysis.predicted_improvement
    
    # Specific recommendations based on bottleneck type
    if analysis.bottleneck_type == :computation
        suggestions["details"] = suggest_computation_rebalancing(analysis)
    elseif analysis.bottleneck_type == :communication
        suggestions["details"] = suggest_communication_rebalancing(analysis)
    else
        suggestions["details"] = suggest_memory_rebalancing(analysis)
    end
    
    # Timing recommendation
    if analysis.predicted_improvement > 0.2
        suggestions["urgency"] = "high"
        suggestions["recommendation"] = "Rebalance at next checkpoint"
    elseif analysis.predicted_improvement > 0.1
        suggestions["urgency"] = "medium"
        suggestions["recommendation"] = "Plan rebalancing for next restart"
    else
        suggestions["urgency"] = "low"
        suggestions["recommendation"] = "Monitor and rebalance if degradation continues"
    end
    
    return suggestions
end

"""
Suggest computation rebalancing strategies
"""
function suggest_computation_rebalancing(analysis::LoadBalanceAnalysis)
    details = Dict{String, Any}()
    
    # Analyze cell distribution
    proc_metrics = analysis.current_metrics.processor_metrics
    cell_counts = [m.n_cells for m in proc_metrics]
    
    details["current_distribution"] = Dict(
        "min_cells" => minimum(cell_counts),
        "max_cells" => maximum(cell_counts),
        "avg_cells" => mean(cell_counts),
        "std_cells" => std(cell_counts)
    )
    
    # Suggest partitioning method
    cv = std(cell_counts) / mean(cell_counts)  # Coefficient of variation
    
    if cv > 0.2
        details["suggested_method"] = "METIS with face weights"
        details["reason"] = "High variation in cell distribution"
    else
        details["suggested_method"] = "Simple geometric"
        details["reason"] = "Relatively uniform distribution"
    end
    
    # Identify processors to offload
    overloaded = analysis.bottleneck_processors
    details["overloaded_processors"] = overloaded
    details["cells_to_migrate"] = sum(cell_counts[p+1] - mean(cell_counts) 
                                     for p in overloaded if cell_counts[p+1] > mean(cell_counts))
    
    return details
end

"""
Suggest communication rebalancing strategies
"""
function suggest_communication_rebalancing(analysis::LoadBalanceAnalysis)
    details = Dict{String, Any}()
    
    # Analyze communication patterns
    proc_metrics = analysis.current_metrics.processor_metrics
    comm_times = [m.communication_time for m in proc_metrics]
    halo_counts = [m.n_halo_cells for m in proc_metrics]
    
    details["communication_analysis"] = Dict(
        "max_comm_time" => maximum(comm_times),
        "avg_comm_time" => mean(comm_times),
        "max_halo_cells" => maximum(halo_counts),
        "avg_halo_cells" => mean(halo_counts)
    )
    
    # Suggest strategies
    if maximum(halo_counts) > 2 * mean(halo_counts)
        details["suggested_action"] = "Minimize edge cuts"
        details["reason"] = "Some processors have excessive halo cells"
        details["suggested_method"] = "METIS with communication minimization"
    else
        details["suggested_action"] = "Optimize communication pattern"
        details["reason"] = "Communication overhead is distributed"
        details["optimization"] = [
            "Use non-blocking communication",
            "Overlap computation with communication",
            "Consider hierarchical partitioning"
        ]
    end
    
    return details
end

"""
Suggest memory rebalancing strategies
"""
function suggest_memory_rebalancing(analysis::LoadBalanceAnalysis)
    details = Dict{String, Any}()
    
    proc_metrics = analysis.current_metrics.processor_metrics
    memory_usage = [m.memory_usage for m in proc_metrics]
    
    details["memory_analysis"] = Dict(
        "max_memory" => maximum(memory_usage),
        "min_memory" => minimum(memory_usage),
        "avg_memory" => mean(memory_usage),
        "total_memory" => sum(memory_usage)
    )
    
    details["suggested_action"] = "Balance memory usage"
    details["strategies"] = [
        "Consider memory-aware partitioning",
        "Distribute large data structures",
        "Use memory pooling"
    ]
    
    return details
end

# ============================================================================
# LOAD HISTORY TRACKING
# ============================================================================

"""
    track_load_history!(history::LoadHistory, metrics::LoadMetrics, monitor::LoadMonitor)

Add current metrics to load history and maintain size limit.
"""
function track_load_history!(history::LoadHistory, metrics::LoadMetrics, 
                           monitor::LoadMonitor)
    push!(history.timestamps, metrics.timestamp)
    push!(history.metrics, metrics)
    
    # Maintain history size
    if length(history.metrics) > monitor.history_size
        popfirst!(history.timestamps)
        popfirst!(history.metrics)
    end
end

"""
Analyze load history for trends
"""
function analyze_load_trends(history::LoadHistory)
    if length(history.metrics) < 10
        return Dict("status" => "Insufficient data")
    end
    
    # Extract time series
    imbalances = [m.total_imbalance for m in history.metrics]
    efficiencies = [m.efficiency for m in history.metrics]
    
    # Calculate trends
    n = length(imbalances)
    recent = n - min(10, n÷4) : n
    
    trends = Dict{String, Any}()
    
    # Imbalance trend
    recent_imbalance = mean(imbalances[recent])
    overall_imbalance = mean(imbalances)
    trends["imbalance_trend"] = recent_imbalance > overall_imbalance ? "increasing" : "stable"
    trends["imbalance_change"] = (recent_imbalance - overall_imbalance) / overall_imbalance
    
    # Efficiency trend
    recent_efficiency = mean(efficiencies[recent])
    overall_efficiency = mean(efficiencies)
    trends["efficiency_trend"] = recent_efficiency < overall_efficiency ? "decreasing" : "stable"
    trends["efficiency_change"] = (recent_efficiency - overall_efficiency) / overall_efficiency
    
    # Stability
    trends["stability"] = std(imbalances[recent]) / mean(imbalances[recent])
    
    return trends
end

# ============================================================================
# REPORTING
# ============================================================================

"""
Generate detailed load balance report
"""
function generate_load_report(metrics::LoadMetrics, bottleneck_procs::Vector{Int},
                            bottleneck_type::Symbol, action::Symbol)
    io = IOBuffer()
    
    println(io, "\n" * "="^60)
    println(io, "LOAD BALANCE REPORT")
    println(io, "="^60)
    println(io, "Timestamp: $(round(metrics.timestamp, digits=2))")
    println(io, "")
    
    # Summary metrics
    println(io, "SUMMARY:")
    @printf(io, "  Total imbalance: %.1f%%\n", metrics.total_imbalance * 100)
    @printf(io, "  Parallel efficiency: %.1f%%\n", metrics.efficiency * 100)
    @printf(io, "  Speedup: %.2fx (of %.2fx ideal)\n", 
            metrics.speedup, length(metrics.processor_metrics))
    println(io, "")
    
    # Imbalance breakdown
    println(io, "IMBALANCE BREAKDOWN:")
    @printf(io, "  Computation: %.1f%%\n", metrics.computation_imbalance * 100)
    @printf(io, "  Communication: %.1f%%\n", metrics.communication_imbalance * 100)
    @printf(io, "  Memory: %.1f%%\n", metrics.memory_imbalance * 100)
    println(io, "")
    
    # Bottleneck analysis
    println(io, "BOTTLENECK ANALYSIS:")
    println(io, "  Type: $bottleneck_type")
    println(io, "  Processors: $(join(bottleneck_procs, ", "))")
    println(io, "")
    
    # Per-processor statistics
    println(io, "PER-PROCESSOR STATISTICS:")
    println(io, "  Proc | Cells | Halo | Comp(s) | Comm(s) | Mem(MB)")
    println(io, "  -----|-------|------|---------|---------|--------")
    
    for m in metrics.processor_metrics
        @printf(io, "  %4d | %5d | %4d | %7.3f | %7.3f | %6.1f\n",
                m.proc_id, m.n_cells, m.n_halo_cells,
                m.computation_time, m.communication_time, m.memory_usage)
    end
    println(io, "")
    
    # Recommendation
    println(io, "RECOMMENDATION:")
    println(io, "  Action: $action")
    
    println(io, "="^60)
    
    return String(take!(io))
end

"""
Generate CSV report for analysis
"""
function export_load_history_csv(history::LoadHistory, filename::String)
    open(filename, "w") do f
        # Header
        println(f, "timestamp,total_imbalance,comp_imbalance,comm_imbalance,efficiency,speedup")
        
        # Data
        for (t, m) in zip(history.timestamps, history.metrics)
            @printf(f, "%.3f,%.4f,%.4f,%.4f,%.4f,%.2f\n",
                    t, m.total_imbalance, m.computation_imbalance,
                    m.communication_imbalance, m.efficiency, m.speedup)
        end
    end
end

# ============================================================================
# VISUALIZATION HELPERS
# ============================================================================

"""
Generate data for load balance visualization
"""
function prepare_visualization_data(metrics::LoadMetrics)
    proc_ids = [m.proc_id for m in metrics.processor_metrics]
    comp_times = [m.computation_time for m in metrics.processor_metrics]
    comm_times = [m.communication_time for m in metrics.processor_metrics]
    idle_times = [m.idle_time for m in metrics.processor_metrics]
    
    return Dict(
        "processors" => proc_ids,
        "computation" => comp_times,
        "communication" => comm_times,
        "idle" => idle_times,
        "total" => comp_times .+ comm_times .+ idle_times
    )
end

"""
Generate load balance summary statistics
"""
function load_balance_summary(metrics::LoadMetrics)
    return Dict(
        "n_processors" => length(metrics.processor_metrics),
        "imbalance_percent" => round(metrics.total_imbalance * 100, digits=1),
        "efficiency_percent" => round(metrics.efficiency * 100, digits=1),
        "speedup" => round(metrics.speedup, digits=2),
        "bottleneck_type" => identify_bottlenecks(metrics)[2],
        "action_needed" => metrics.total_imbalance > 0.1
    )
end

end # module LoadBalancing