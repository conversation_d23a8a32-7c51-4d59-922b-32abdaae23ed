"""
    DistributedMesh.jl

Transparent distributed mesh abstractions for JuliaFOAM.
Provides automatic mesh partitioning and transparent access to mesh entities
across process boundaries.

Key features:
- Lazy automatic partitioning
- Transparent face/cell neighbor access
- Ghost layer management
- Load balancing support
"""

module DistributedMesh

using MPI
using SparseArrays
using StaticArrays
using LinearAlgebra

# Import types from parent modules
import ...JuliaFOAM: Mesh, Cell, Face, BoundaryCondition
import ...JuliaFOAM: FixedValueBC, FixedGradientBC, ZeroGradientBC

export DistributedMeshData, partition_mesh!, get_local_mesh
export is_owner, get_owner_rank, get_local_cells, get_local_faces
export update_ghost_layers!, repartition!

# ============================================================================
# DISTRIBUTED MESH TYPE
# ============================================================================

"""
    DistributedMeshData

Extended mesh data for distributed computing. This wraps a regular Mesh
and adds parallel-specific information.

# Fields
- `mesh::Mesh`: The base mesh (can be global on rank 0 or local on each rank)
- `comm::MPI.Comm`: MPI communicator
- `rank::Int`: Process rank
- `nprocs::Int`: Number of processes
- `is_partitioned::Bool`: Whether mesh has been partitioned
- `global_to_local_cells::Dict{Int,Int}`: Map from global to local cell indices
- `global_to_local_faces::Dict{Int,Int}`: Map from global to local face indices
- `local_to_global_cells::Vector{Int}`: Map from local to global cell indices
- `local_to_global_faces::Vector{Int}`: Map from local to global face indices
- `ghost_cells::Vector{Int}`: Local indices of ghost cells
- `ghost_layers::Int`: Number of ghost cell layers
- `partition_method::Symbol`: Partitioning method used (:metis, :simple, :scotch)
"""
mutable struct DistributedMeshData
    mesh::Mesh
    comm::MPI.Comm
    rank::Int
    nprocs::Int
    is_partitioned::Bool
    global_to_local_cells::Dict{Int,Int}
    global_to_local_faces::Dict{Int,Int}
    local_to_global_cells::Vector{Int}
    local_to_global_faces::Vector{Int}
    ghost_cells::Vector{Int}
    ghost_layers::Int
    partition_method::Symbol
    
    # Constructor for wrapping existing mesh
    function DistributedMeshData(mesh::Mesh, comm::MPI.Comm=MPI.COMM_WORLD; 
                                ghost_layers::Int=1, partition_method::Symbol=:metis)
        rank = MPI.Comm_rank(comm)
        nprocs = MPI.Comm_size(comm)
        
        # Check if mesh is already partitioned
        is_partitioned = !all(p -> p == 0, mesh.cell_partition)
        
        if nprocs == 1
            # Serial case - create identity mappings
            n_cells = length(mesh.cells)
            n_faces = length(mesh.faces)
            
            global_to_local_cells = Dict(i => i for i in 1:n_cells)
            global_to_local_faces = Dict(i => i for i in 1:n_faces)
            local_to_global_cells = collect(1:n_cells)
            local_to_global_faces = collect(1:n_faces)
            
            return new(
                mesh, comm, rank, nprocs, true,
                global_to_local_cells, global_to_local_faces,
                local_to_global_cells, local_to_global_faces,
                Int[], ghost_layers, partition_method
            )
        else
            # Parallel case
            if is_partitioned
                # Mesh already partitioned, build mappings
                local_cells = findall(i -> mesh.cell_partition[i] == rank, 1:length(mesh.cells))
                ghost_cells = mesh.halo_cells
                
                all_cells = vcat(local_cells, ghost_cells)
                global_to_local_cells = Dict(all_cells[i] => i for i in 1:length(all_cells))
                local_to_global_cells = all_cells
                
                # Build face mappings (complex - needs communication)
                global_to_local_faces = Dict{Int,Int}()
                local_to_global_faces = Int[]
                
                return new(
                    mesh, comm, rank, nprocs, true,
                    global_to_local_cells, global_to_local_faces,
                    local_to_global_cells, local_to_global_faces,
                    collect(length(local_cells)+1:length(all_cells)),
                    ghost_layers, partition_method
                )
            else
                # Unpartitioned mesh - will partition lazily
                return new(
                    mesh, comm, rank, nprocs, false,
                    Dict{Int,Int}(), Dict{Int,Int}(),
                    Int[], Int[], Int[],
                    ghost_layers, partition_method
                )
            end
        end
    end
end

# ============================================================================
# MESH PARTITIONING
# ============================================================================

"""
    partition_mesh!(dmesh::DistributedMeshData; method::Symbol=:metis, weights::Vector{Float64}=Float64[])

Partition the mesh across MPI processes. This modifies the mesh in-place.

# Arguments
- `dmesh`: Distributed mesh data
- `method`: Partitioning method (:metis, :scotch, :simple)
- `weights`: Optional cell weights for load balancing
"""
function partition_mesh!(dmesh::DistributedMeshData; 
                        method::Symbol=dmesh.partition_method, 
                        weights::Vector{Float64}=Float64[])
    
    if dmesh.is_partitioned
        return  # Already partitioned
    end
    
    if dmesh.nprocs == 1
        dmesh.is_partitioned = true
        return  # No partitioning needed
    end
    
    # Only rank 0 computes the partition
    if dmesh.rank == 0
        partition = compute_partition(dmesh.mesh, dmesh.nprocs, method, weights)
        
        # Update mesh cell_partition
        dmesh.mesh.cell_partition .= partition
    end
    
    # Broadcast partition to all ranks
    if dmesh.rank == 0
        for r in 1:dmesh.nprocs-1
            MPI.Send(dmesh.mesh.cell_partition, r, 0, dmesh.comm)
        end
    else
        dmesh.mesh.cell_partition = Vector{Int32}(undef, length(dmesh.mesh.cells))
        MPI.Recv!(dmesh.mesh.cell_partition, 0, 0, dmesh.comm)
    end
    
    # Extract local mesh for each process
    extract_local_mesh!(dmesh)
    
    dmesh.is_partitioned = true
    dmesh.partition_method = method
end

"""
    compute_partition(mesh::Mesh, nprocs::Int, method::Symbol, weights::Vector{Float64})

Compute mesh partition using specified method.
"""
function compute_partition(mesh::Mesh, nprocs::Int, method::Symbol, weights::Vector{Float64})
    n_cells = length(mesh.cells)
    
    if method == :simple
        # Simple geometric partitioning
        return simple_partition(mesh, nprocs)
    elseif method == :metis
        # METIS graph partitioning
        return metis_partition(mesh, nprocs, weights)
    elseif method == :scotch
        # SCOTCH partitioning
        return scotch_partition(mesh, nprocs, weights)
    else
        error("Unknown partitioning method: $method")
    end
end

"""
    simple_partition(mesh::Mesh, nprocs::Int)

Simple geometric partitioning based on cell centers.
"""
function simple_partition(mesh::Mesh, nprocs::Int)
    n_cells = length(mesh.cells)
    partition = Vector{Int32}(undef, n_cells)
    
    # Get cell centers
    centers = [cell.center for cell in mesh.cells]
    
    # Find bounding box
    xmin = minimum(c -> c.x, centers)
    xmax = maximum(c -> c.x, centers)
    ymin = minimum(c -> c.y, centers)
    ymax = maximum(c -> c.y, centers)
    zmin = minimum(c -> c.z, centers)
    zmax = maximum(c -> c.z, centers)
    
    # Determine best splitting direction (largest dimension)
    dx = xmax - xmin
    dy = ymax - ymin
    dz = zmax - zmin
    
    # Use recursive coordinate bisection
    cell_indices = collect(1:n_cells)
    recursive_bisection!(partition, centers, cell_indices, 0, nprocs, 1)
    
    return partition
end

"""
    recursive_bisection!(partition, centers, indices, rank_start, rank_end, level)

Recursive coordinate bisection for mesh partitioning.
"""
function recursive_bisection!(partition::Vector{Int32}, centers, indices::Vector{Int}, 
                            rank_start::Int, n_ranks::Int, level::Int)
    
    if n_ranks == 1
        # Assign all cells to rank_start
        for idx in indices
            partition[idx] = rank_start
        end
        return
    end
    
    # Find splitting direction (cycle through x, y, z)
    dir = mod(level - 1, 3) + 1
    
    # Get coordinates in splitting direction
    coords = if dir == 1
        [centers[i].x for i in indices]
    elseif dir == 2
        [centers[i].y for i in indices]
    else
        [centers[i].z for i in indices]
    end
    
    # Find median
    sorted_indices = sortperm(coords)
    n = length(indices)
    
    # Split into two groups
    n_left = n_ranks ÷ 2
    n_right = n_ranks - n_left
    split_point = (n * n_left) ÷ n_ranks
    
    left_indices = indices[sorted_indices[1:split_point]]
    right_indices = indices[sorted_indices[split_point+1:end]]
    
    # Recurse
    recursive_bisection!(partition, centers, left_indices, rank_start, n_left, level + 1)
    recursive_bisection!(partition, centers, right_indices, rank_start + n_left, n_right, level + 1)
end

"""
    metis_partition(mesh::Mesh, nprocs::Int, weights::Vector{Float64})

Use METIS for graph-based partitioning (requires Metis.jl).
"""
function metis_partition(mesh::Mesh, nprocs::Int, weights::Vector{Float64})
    # Try to use Metis if available
    try
        # Dynamic loading of Metis
        @eval using Metis
        
        n_cells = length(mesh.cells)
        
        # Build adjacency graph
        I = Int32[]
        J = Int32[]
        
        for (i, cell) in enumerate(mesh.cells)
            for face_idx in cell.faces
                face = mesh.faces[face_idx]
                neighbor = (face.owner == i) ? face.neighbour : face.owner
                
                if neighbor > 0 && neighbor != i
                    push!(I, i)
                    push!(J, neighbor)
                end
            end
        end
        
        # Create adjacency matrix
        adj_matrix = sparse(I, J, ones(Int32, length(I)), n_cells, n_cells)
        
        # Convert to METIS format
        xadj = Vector{Int32}(undef, n_cells + 1)
        xadj[1] = 0
        
        for i in 1:n_cells
            xadj[i+1] = xadj[i] + nnz(adj_matrix[i, :])
        end
        
        adjncy = Vector{Int32}()
        for i in 1:n_cells
            for j in findnz(adj_matrix[i, :])[1]
                push!(adjncy, j - 1)  # METIS uses 0-based indexing
            end
        end
        
        # Add vertex weights if provided
        vwgt = if isempty(weights)
            nothing
        else
            Int32.(round.(weights * 1000))  # Scale and convert to integers
        end
        
        # Call METIS
        _, partition = Metis.partition(xadj, adjncy, nprocs; vwgt=vwgt)
        
        return partition
        
    catch e
        @warn "METIS not available, falling back to simple partitioning" exception=e
        return simple_partition(mesh, nprocs)
    end
end

"""
    scotch_partition(mesh::Mesh, nprocs::Int, weights::Vector{Float64})

Use SCOTCH for partitioning (placeholder - would need SCOTCH.jl).
"""
function scotch_partition(mesh::Mesh, nprocs::Int, weights::Vector{Float64})
    @warn "SCOTCH partitioning not implemented, using simple partitioning"
    return simple_partition(mesh, nprocs)
end

# ============================================================================
# LOCAL MESH EXTRACTION
# ============================================================================

"""
    extract_local_mesh!(dmesh::DistributedMeshData)

Extract the local portion of the mesh for this process, including ghost cells.
"""
function extract_local_mesh!(dmesh::DistributedMeshData)
    rank = dmesh.rank
    
    # Find local cells
    local_cells = findall(i -> dmesh.mesh.cell_partition[i] == rank, 1:length(dmesh.mesh.cells))
    
    # Find ghost cells (cells that neighbor local cells but are owned by other ranks)
    ghost_cell_set = Set{Int}()
    
    for _ in 1:dmesh.ghost_layers
        new_ghosts = Set{Int}()
        
        for cell_idx in union(local_cells, ghost_cell_set)
            cell = dmesh.mesh.cells[cell_idx]
            
            for face_idx in cell.faces
                face = dmesh.mesh.faces[face_idx]
                neighbor = (face.owner == cell_idx) ? face.neighbour : face.owner
                
                if neighbor > 0 && dmesh.mesh.cell_partition[neighbor] != rank
                    push!(new_ghosts, neighbor)
                end
            end
        end
        
        union!(ghost_cell_set, new_ghosts)
    end
    
    ghost_cells = collect(ghost_cell_set)
    sort!(ghost_cells)
    
    # Build cell mappings
    all_cells = vcat(local_cells, ghost_cells)
    dmesh.global_to_local_cells = Dict(all_cells[i] => i for i in 1:length(all_cells))
    dmesh.local_to_global_cells = all_cells
    dmesh.ghost_cells = collect(length(local_cells)+1:length(all_cells))
    
    # Update mesh halo_cells
    dmesh.mesh.halo_cells = ghost_cells
    
    # Find local faces (faces where at least one cell is local or ghost)
    local_face_set = Set{Int}()
    
    for cell_idx in all_cells
        cell = dmesh.mesh.cells[cell_idx]
        for face_idx in cell.faces
            push!(local_face_set, face_idx)
        end
    end
    
    local_faces = collect(local_face_set)
    sort!(local_faces)
    
    # Build face mappings
    dmesh.global_to_local_faces = Dict(local_faces[i] => i for i in 1:length(local_faces))
    dmesh.local_to_global_faces = local_faces
end

# ============================================================================
# MESH ACCESS INTERFACE
# ============================================================================

"""
    get_local_mesh(dmesh::DistributedMeshData)

Get the local mesh for this process. Automatically partitions if needed.
"""
function get_local_mesh(dmesh::DistributedMeshData)
    if !dmesh.is_partitioned
        partition_mesh!(dmesh)
    end
    
    return dmesh.mesh
end

"""
    is_owner(dmesh::DistributedMeshData, global_cell_idx::Int)

Check if this process owns a given cell.
"""
function is_owner(dmesh::DistributedMeshData, global_cell_idx::Int)
    if !dmesh.is_partitioned
        partition_mesh!(dmesh)
    end
    
    return dmesh.mesh.cell_partition[global_cell_idx] == dmesh.rank
end

"""
    get_owner_rank(dmesh::DistributedMeshData, global_cell_idx::Int)

Get the rank that owns a given cell.
"""
function get_owner_rank(dmesh::DistributedMeshData, global_cell_idx::Int)
    if !dmesh.is_partitioned
        partition_mesh!(dmesh)
    end
    
    return dmesh.mesh.cell_partition[global_cell_idx]
end

"""
    get_local_cells(dmesh::DistributedMeshData; include_ghost::Bool=false)

Get local cell indices. Optionally include ghost cells.
"""
function get_local_cells(dmesh::DistributedMeshData; include_ghost::Bool=false)
    if !dmesh.is_partitioned
        partition_mesh!(dmesh)
    end
    
    n_local = length(dmesh.local_to_global_cells) - length(dmesh.ghost_cells)
    
    if include_ghost
        return collect(1:length(dmesh.local_to_global_cells))
    else
        return collect(1:n_local)
    end
end

"""
    get_local_faces(dmesh::DistributedMeshData)

Get local face indices.
"""
function get_local_faces(dmesh::DistributedMeshData)
    if !dmesh.is_partitioned
        partition_mesh!(dmesh)
    end
    
    return collect(1:length(dmesh.local_to_global_faces))
end

# ============================================================================
# GHOST LAYER MANAGEMENT
# ============================================================================

"""
    update_ghost_layers!(dmesh::DistributedMeshData, n_layers::Int)

Update the number of ghost cell layers. This requires re-extracting the local mesh.
"""
function update_ghost_layers!(dmesh::DistributedMeshData, n_layers::Int)
    if n_layers == dmesh.ghost_layers
        return  # No change needed
    end
    
    dmesh.ghost_layers = n_layers
    
    if dmesh.is_partitioned && dmesh.nprocs > 1
        # Re-extract local mesh with new ghost layers
        extract_local_mesh!(dmesh)
    end
end

# ============================================================================
# DYNAMIC REPARTITIONING
# ============================================================================

"""
    repartition!(dmesh::DistributedMeshData; 
                method::Symbol=:metis, 
                weights::Vector{Float64}=Float64[],
                imbalance_threshold::Float64=0.1)

Dynamically repartition the mesh for better load balance.

# Arguments
- `dmesh`: Distributed mesh data
- `method`: Partitioning method
- `weights`: Cell weights for load balancing
- `imbalance_threshold`: Repartition if imbalance exceeds this threshold
"""
function repartition!(dmesh::DistributedMeshData; 
                     method::Symbol=:metis, 
                     weights::Vector{Float64}=Float64[],
                     imbalance_threshold::Float64=0.1)
    
    if dmesh.nprocs == 1
        return  # No repartitioning needed
    end
    
    # Calculate current load imbalance
    n_local = length(dmesh.local_to_global_cells) - length(dmesh.ghost_cells)
    all_loads = MPI.Allgather(n_local, dmesh.comm)
    
    avg_load = sum(all_loads) / dmesh.nprocs
    max_load = maximum(all_loads)
    imbalance = (max_load - avg_load) / avg_load
    
    if imbalance < imbalance_threshold
        if dmesh.rank == 0
            println("Load imbalance $imbalance < threshold $imbalance_threshold, skipping repartition")
        end
        return
    end
    
    if dmesh.rank == 0
        println("Load imbalance $imbalance >= threshold $imbalance_threshold, repartitioning...")
    end
    
    # Gather global mesh on rank 0 (this could be optimized)
    # For now, assume we have the global mesh structure available
    
    # Reset partitioning flag
    dmesh.is_partitioned = false
    
    # Repartition
    partition_mesh!(dmesh; method=method, weights=weights)
end

# ============================================================================
# MESH STATISTICS
# ============================================================================

"""
    mesh_stats(dmesh::DistributedMeshData)

Compute and return mesh statistics for the distributed mesh.
"""
function mesh_stats(dmesh::DistributedMeshData)
    if !dmesh.is_partitioned
        partition_mesh!(dmesh)
    end
    
    # Local statistics
    n_local_cells = length(dmesh.local_to_global_cells) - length(dmesh.ghost_cells)
    n_ghost_cells = length(dmesh.ghost_cells)
    n_local_faces = length(dmesh.local_to_global_faces)
    
    # Gather global statistics
    all_local_cells = MPI.Allgather(n_local_cells, dmesh.comm)
    all_ghost_cells = MPI.Allgather(n_ghost_cells, dmesh.comm)
    all_local_faces = MPI.Allgather(n_local_faces, dmesh.comm)
    
    # Compute load balance metrics
    avg_cells = sum(all_local_cells) / dmesh.nprocs
    max_cells = maximum(all_local_cells)
    min_cells = minimum(all_local_cells)
    
    load_imbalance = (max_cells - avg_cells) / avg_cells
    
    # Communication volume estimate
    total_ghost = sum(all_ghost_cells)
    
    return (
        rank = dmesh.rank,
        n_local_cells = n_local_cells,
        n_ghost_cells = n_ghost_cells,
        n_local_faces = n_local_faces,
        global_cells = sum(all_local_cells),
        load_imbalance = load_imbalance,
        max_cells = max_cells,
        min_cells = min_cells,
        avg_cells = avg_cells,
        total_ghost_cells = total_ghost,
        ghost_ratio = total_ghost / sum(all_local_cells)
    )
end

# ============================================================================
# MESH MODIFICATION TRACKING
# ============================================================================

"""
    mark_partition_outdated!(dmesh::DistributedMeshData)

Mark the partition as outdated, requiring repartitioning before next use.
This should be called when the mesh topology changes.
"""
function mark_partition_outdated!(dmesh::DistributedMeshData)
    dmesh.is_partitioned = false
end

end # module DistributedMesh