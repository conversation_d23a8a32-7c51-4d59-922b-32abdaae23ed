"""
CLITools.jl

Command-line interface tools for JuliaFOAM parallel utilities.
Provides user-friendly commands similar to OpenFOAM for domain decomposition,
reconstruction, and load balancing operations.

Key Commands:
- juliafoam-decomposePar: Decompose case for parallel execution
- juliafoam-reconstructPar: Reconstruct parallel case
- juliafoam-redistributePar: Redistribute running case
- juliafoam-checkMesh: Check mesh quality and partitioning
- juliafoam-loadBalance: Analyze and optimize load balance
"""

module CLITools

using ArgParse
using Printf
using Dates

# Import parallel modules
include("MeshPartitioning.jl")
using .MeshPartitioning

include("DecomposePar.jl")
using .DecomposePar

include("ReconstructPar.jl")
using .ReconstructPar

include("RedistributePar.jl")
using .RedistributePar

include("LoadBalancing.jl")
using .LoadBalancing

export main_decomposePar, main_reconstructPar, main_redistributePar
export main_checkMesh, main_loadBalance
export parse_arguments, run_command

# ============================================================================
# DECOMPOSE PAR COMMAND
# ============================================================================

"""
Parse command-line arguments for decomposePar
"""
function parse_decomposePar_args()
    s = ArgParseSettings(
        prog = "juliafoam-decomposePar",
        description = "Decompose JuliaFOAM case for parallel execution",
        version = "1.0.0",
        add_version = true
    )
    
    @add_arg_table! s begin
        "--case", "-c"
            help = "Case directory"
            arg_type = String
            default = "."
        "--method", "-m"
            help = "Decomposition method: simple, metis, scotch, hierarchical, manual"
            arg_type = String
            default = "metis"
        "--nprocs", "-n"
            help = "Number of processors"
            arg_type = Int
            required = true
        "--direction", "-d"
            help = "Direction for simple decomposition: x, y, z, xy, xz, yz, xyz"
            arg_type = String
            default = "xyz"
        "--cellWeights"
            help = "Use cell weights from file"
            arg_type = String
        "--faceWeights"
            help = "Use face area weights"
            action = :store_true
        "--force", "-f"
            help = "Overwrite existing processor directories"
            action = :store_true
        "--fields"
            help = "Specific fields to decompose (comma-separated)"
            arg_type = String
        "--times"
            help = "Specific time directories (comma-separated)"
            arg_type = String
        "--preservePatches"
            help = "Preserve boundary patches"
            action = :store_true
            default = true
        "--writeGraph"
            help = "Write partition visualization"
            action = :store_true
        "--verbose", "-v"
            help = "Verbose output"
            action = :store_true
        "--parallel"
            help = "Use parallel decomposition"
            action = :store_true
    end
    
    return parse_args(s)
end

"""
Main entry point for decomposePar command
"""
function main_decomposePar(args::Vector{String}=ARGS)
    # Parse arguments
    parsed_args = parse_decomposePar_args()
    
    # Create partition method
    method = create_partition_method(
        parsed_args["method"],
        parsed_args["nprocs"],
        parsed_args
    )
    
    # Parse fields and times
    fields = parsed_args["fields"] !== nothing ? 
             split(parsed_args["fields"], ",") : String[]
    times = parsed_args["times"] !== nothing ? 
            split(parsed_args["times"], ",") : String[]
    
    # Create configuration
    config = DecomposeConfig(
        method = method,
        output_dir = parsed_args["case"],
        preserve_patches = parsed_args["preservePatches"],
        face_weight_method = parsed_args["faceWeights"] ? :area : :uniform,
        write_graph = parsed_args["writeGraph"],
        force = parsed_args["force"],
        time_dirs = times,
        fields = fields,
        parallel_io = parsed_args["parallel"],
        verbose = parsed_args["verbose"]
    )
    
    # Print header
    if parsed_args["verbose"]
        print_decompose_header(parsed_args, method)
    end
    
    # Run decomposition
    try
        if parsed_args["parallel"]
            decomp_info = decompose_par_parallel(parsed_args["case"], config)
        else
            decomp_info = decompose_par(parsed_args["case"], config)
        end
        
        println("\nDecomposition completed successfully!")
        return 0
    catch e
        println("\nError: ", e)
        return 1
    end
end

# ============================================================================
# RECONSTRUCT PAR COMMAND
# ============================================================================

"""
Parse command-line arguments for reconstructPar
"""
function parse_reconstructPar_args()
    s = ArgParseSettings(
        prog = "juliafoam-reconstructPar",
        description = "Reconstruct parallel JuliaFOAM case",
        version = "1.0.0",
        add_version = true
    )
    
    @add_arg_table! s begin
        "--case", "-c"
            help = "Case directory"
            arg_type = String
            default = "."
        "--times"
            help = "Specific time directories (comma-separated)"
            arg_type = String
        "--fields"
            help = "Specific fields to reconstruct (comma-separated)"
            arg_type = String
        "--latestTime"
            help = "Reconstruct only latest time"
            action = :store_true
        "--newTimes"
            help = "Reconstruct only new times"
            action = :store_true
        "--skipMesh"
            help = "Skip mesh reconstruction"
            action = :store_true
        "--remove"
            help = "Remove processor directories after reconstruction"
            action = :store_true
        "--tolerance"
            help = "Tolerance for stitching boundaries"
            arg_type = Float64
            default = 1e-10
        "--verify"
            help = "Verify reconstruction quality"
            action = :store_true
            default = true
        "--parallel"
            help = "Use parallel reconstruction"
            action = :store_true
        "--verbose", "-v"
            help = "Verbose output"
            action = :store_true
    end
    
    return parse_args(s)
end

"""
Main entry point for reconstructPar command
"""
function main_reconstructPar(args::Vector{String}=ARGS)
    # Parse arguments
    parsed_args = parse_reconstructPar_args()
    
    # Determine time directories
    times = String[]
    if parsed_args["times"] !== nothing
        times = split(parsed_args["times"], ",")
    elseif parsed_args["latestTime"]
        times = [get_latest_time(parsed_args["case"])]
    elseif parsed_args["newTimes"]
        times = get_new_times(parsed_args["case"])
    end
    
    # Parse fields
    fields = parsed_args["fields"] !== nothing ? 
             split(parsed_args["fields"], ",") : String[]
    
    # Create configuration
    config = ReconstructConfig(
        time_dirs = times,
        fields = fields,
        remove_processor_dirs = parsed_args["remove"],
        tolerance = parsed_args["tolerance"],
        verify = parsed_args["verify"],
        parallel_io = parsed_args["parallel"],
        skip_mesh = parsed_args["skipMesh"],
        verbose = parsed_args["verbose"]
    )
    
    # Print header
    if parsed_args["verbose"]
        print_reconstruct_header(parsed_args)
    end
    
    # Run reconstruction
    try
        if parsed_args["parallel"]
            stats = reconstruct_par_distributed(parsed_args["case"], config)
        else
            stats = reconstruct_par(parsed_args["case"], config)
        end
        
        println("\nReconstruction completed successfully!")
        return 0
    catch e
        println("\nError: ", e)
        return 1
    end
end

# ============================================================================
# REDISTRIBUTE PAR COMMAND
# ============================================================================

"""
Parse command-line arguments for redistributePar
"""
function parse_redistributePar_args()
    s = ArgParseSettings(
        prog = "juliafoam-redistributePar",
        description = "Redistribute parallel JuliaFOAM case",
        version = "1.0.0",
        add_version = true
    )
    
    @add_arg_table! s begin
        "--case", "-c"
            help = "Case directory"
            arg_type = String
            default = "."
        "--oldProcs"
            help = "Current number of processors"
            arg_type = Int
            required = true
        "--newProcs"
            help = "New number of processors"
            arg_type = Int
            required = true
        "--method", "-m"
            help = "New decomposition method"
            arg_type = String
            default = "metis"
        "--times"
            help = "Specific time directories (comma-separated)"
            arg_type = String
        "--fields"
            help = "Specific fields to redistribute (comma-separated)"
            arg_type = String
        "--reconstruct"
            help = "Reconstruct before redistributing"
            action = :store_true
        "--checkpoint"
            help = "Create checkpoint before redistribution"
            action = :store_true
            default = true
        "--minimize"
            help = "Minimize data migration"
            action = :store_true
            default = true
        "--threshold"
            help = "Load balance threshold for triggering"
            arg_type = Float64
            default = 0.1
        "--verbose", "-v"
            help = "Verbose output"
            action = :store_true
    end
    
    return parse_args(s)
end

"""
Main entry point for redistributePar command
"""
function main_redistributePar(args::Vector{String}=ARGS)
    # Parse arguments
    parsed_args = parse_redistributePar_args()
    
    # Create partition method
    method = create_partition_method(
        parsed_args["method"],
        parsed_args["newProcs"],
        parsed_args
    )
    
    # Parse fields and times
    fields = parsed_args["fields"] !== nothing ? 
             split(parsed_args["fields"], ",") : String[]
    times = parsed_args["times"] !== nothing ? 
            split(parsed_args["times"], ",") : String[]
    
    # Create configuration
    config = RedistributeConfig(
        old_n_procs = parsed_args["oldProcs"],
        new_n_procs = parsed_args["newProcs"],
        new_method = method,
        time_dirs = times,
        fields = fields,
        reconstruct_first = parsed_args["reconstruct"],
        checkpoint = parsed_args["checkpoint"],
        minimize_migration = parsed_args["minimize"],
        load_balance_threshold = parsed_args["threshold"],
        verbose = parsed_args["verbose"]
    )
    
    # Print header
    if parsed_args["verbose"]
        print_redistribute_header(parsed_args)
    end
    
    # Run redistribution
    try
        stats = redistribute_par(parsed_args["case"], config)
        
        println("\nRedistribution completed successfully!")
        @printf("Cells migrated: %d\n", stats.cells_migrated)
        @printf("Load imbalance: %.1f%% → %.1f%%\n",
                (stats.load_imbalance_before - 1.0) * 100,
                (stats.load_imbalance_after - 1.0) * 100)
        return 0
    catch e
        println("\nError: ", e)
        return 1
    end
end

# ============================================================================
# CHECK MESH COMMAND
# ============================================================================

"""
Parse command-line arguments for checkMesh
"""
function parse_checkMesh_args()
    s = ArgParseSettings(
        prog = "juliafoam-checkMesh",
        description = "Check mesh quality and partitioning",
        version = "1.0.0",
        add_version = true
    )
    
    @add_arg_table! s begin
        "--case", "-c"
            help = "Case directory"
            arg_type = String
            default = "."
        "--parallel"
            help = "Check parallel decomposition"
            action = :store_true
        "--nprocs"
            help = "Number of processors (for parallel check)"
            arg_type = Int
        "--quality"
            help = "Check mesh quality metrics"
            action = :store_true
            default = true
        "--partition"
            help = "Analyze partition quality"
            action = :store_true
        "--verbose", "-v"
            help = "Verbose output"
            action = :store_true
    end
    
    return parse_args(s)
end

"""
Main entry point for checkMesh command
"""
function main_checkMesh(args::Vector{String}=ARGS)
    # Parse arguments
    parsed_args = parse_checkMesh_args()
    
    try
        if parsed_args["parallel"]
            # Check parallel mesh
            check_parallel_mesh(
                parsed_args["case"],
                parsed_args["nprocs"],
                parsed_args["verbose"]
            )
        else
            # Check serial mesh
            check_serial_mesh(
                parsed_args["case"],
                parsed_args["quality"],
                parsed_args["verbose"]
            )
        end
        
        return 0
    catch e
        println("\nError: ", e)
        return 1
    end
end

# ============================================================================
# LOAD BALANCE COMMAND
# ============================================================================

"""
Parse command-line arguments for loadBalance
"""
function parse_loadBalance_args()
    s = ArgParseSettings(
        prog = "juliafoam-loadBalance",
        description = "Analyze and optimize load balance",
        version = "1.0.0",
        add_version = true
    )
    
    @add_arg_table! s begin
        "--case", "-c"
            help = "Case directory"
            arg_type = String
            default = "."
        "--nprocs"
            help = "Number of processors"
            arg_type = Int
            required = true
        "--analyze"
            help = "Analyze current load balance"
            action = :store_true
            default = true
        "--suggest"
            help = "Suggest optimization"
            action = :store_true
        "--history"
            help = "Analyze load history from file"
            arg_type = String
        "--export"
            help = "Export analysis to file"
            arg_type = String
        "--verbose", "-v"
            help = "Verbose output"
            action = :store_true
    end
    
    return parse_args(s)
end

"""
Main entry point for loadBalance command
"""
function main_loadBalance(args::Vector{String}=ARGS)
    # Parse arguments
    parsed_args = parse_loadBalance_args()
    
    try
        # Analyze load balance
        imbalance_info = analyze_load_imbalance(
            parsed_args["case"],
            parsed_args["nprocs"]
        )
        
        # Print analysis
        print_load_balance_analysis(imbalance_info, parsed_args["verbose"])
        
        # Suggest optimization if requested
        if parsed_args["suggest"] && imbalance_info.requires_redistribution
            suggestion = suggest_redistribution(
                imbalance_info,
                parsed_args["nprocs"]
            )
            print_redistribution_suggestion(suggestion)
        end
        
        # Export if requested
        if parsed_args["export"] !== nothing
            export_load_analysis(imbalance_info, parsed_args["export"])
        end
        
        return 0
    catch e
        println("\nError: ", e)
        return 1
    end
end

# ============================================================================
# HELPER FUNCTIONS
# ============================================================================

"""
Create partition method from string specification
"""
function create_partition_method(method::String, n_procs::Int, args::Dict)
    if method == "simple"
        direction = Symbol(get(args, "direction", "xyz"))
        return SimplePartition(n_procs, direction)
        
    elseif method == "metis"
        face_weights = get(args, "faceWeights", false)
        return MetisPartition(n_procs, face_weights=face_weights)
        
    elseif method == "scotch"
        return ScotchPartition(n_procs)
        
    elseif method == "hierarchical"
        return HierarchicalPartition(n_procs)
        
    elseif method == "manual"
        # Read cell processor mapping from file
        cellWeights = get(args, "cellWeights", nothing)
        if cellWeights === nothing
            error("Manual method requires --cellWeights file")
        end
        mapping = read_cell_processor_mapping(cellWeights)
        return ManualPartition(mapping)
        
    else
        error("Unknown decomposition method: $method")
    end
end

"""
Get latest time directory
"""
function get_latest_time(case_dir::String)
    times = Float64[]
    
    for item in readdir(case_dir)
        if isdir(joinpath(case_dir, item)) && occursin(r"^\d+\.?\d*$", item)
            push!(times, parse(Float64, item))
        end
    end
    
    if isempty(times)
        error("No time directories found")
    end
    
    return string(maximum(times))
end

"""
Get new time directories (not yet reconstructed)
"""
function get_new_times(case_dir::String)
    # Check processor0 for times
    proc0_dir = joinpath(case_dir, "processor0")
    if !isdir(proc0_dir)
        error("No processor directories found")
    end
    
    proc_times = Set{String}()
    for item in readdir(proc0_dir)
        if isdir(joinpath(proc0_dir, item)) && occursin(r"^\d+\.?\d*$", item)
            push!(proc_times, item)
        end
    end
    
    # Check reconstructed times
    recon_times = Set{String}()
    for item in readdir(case_dir)
        if isdir(joinpath(case_dir, item)) && occursin(r"^\d+\.?\d*$", item)
            push!(recon_times, item)
        end
    end
    
    # Return difference
    new_times = setdiff(proc_times, recon_times)
    return sort(collect(new_times), by=x->parse(Float64, x))
end

# ============================================================================
# PRINTING FUNCTIONS
# ============================================================================

"""
Print decomposePar header
"""
function print_decompose_header(args::Dict, method::PartitionMethod)
    println("\n" * "="^60)
    println("JULIAFOAM DECOMPOSE PAR")
    println("="^60)
    println("Date: ", Dates.now())
    println("Case: ", args["case"])
    println("Method: ", typeof(method))
    println("Processors: ", method.n_subdomains)
    println("="^60)
end

"""
Print reconstructPar header
"""
function print_reconstruct_header(args::Dict)
    println("\n" * "="^60)
    println("JULIAFOAM RECONSTRUCT PAR")
    println("="^60)
    println("Date: ", Dates.now())
    println("Case: ", args["case"])
    if !isempty(args["times"])
        println("Times: ", args["times"])
    end
    println("="^60)
end

"""
Print redistributePar header
"""
function print_redistribute_header(args::Dict)
    println("\n" * "="^60)
    println("JULIAFOAM REDISTRIBUTE PAR")
    println("="^60)
    println("Date: ", Dates.now())
    println("Case: ", args["case"])
    println("Processors: ", args["oldProcs"], " → ", args["newProcs"])
    println("Method: ", args["method"])
    println("="^60)
end

"""
Print load balance analysis
"""
function print_load_balance_analysis(info::LoadImbalanceInfo, verbose::Bool)
    println("\n" * "="^60)
    println("LOAD BALANCE ANALYSIS")
    println("="^60)
    
    @printf("Load imbalance factor: %.3f\n", info.imbalance_factor)
    @printf("Efficiency estimate: %.1f%%\n", 100.0 / info.imbalance_factor)
    
    if verbose
        println("\nProcessor loads:")
        for (i, load) in enumerate(info.processor_loads)
            @printf("  Processor %d: %.0f cells\n", i-1, load)
        end
        
        println("\nCommunication costs:")
        for (i, cost) in enumerate(info.communication_costs)
            @printf("  Processor %d: %.3f\n", i-1, cost)
        end
    end
    
    println("\nRecommendation:")
    if info.requires_redistribution
        println("  Redistribution recommended")
        println("  Suggested processors: ", info.suggested_n_procs)
    else
        println("  Load balance is acceptable")
    end
    
    println("="^60)
end

"""
Print redistribution suggestion
"""
function print_redistribution_suggestion(suggestion::Dict)
    println("\n" * "-"^60)
    println("REDISTRIBUTION SUGGESTION")
    println("-"^60)
    
    println("Action: ", suggestion["action"])
    @printf("Expected improvement: %.1f%%\n", 
            suggestion["expected_improvement"] * 100)
    
    if haskey(suggestion, "urgency")
        println("Urgency: ", suggestion["urgency"])
        println("Recommendation: ", suggestion["recommendation"])
    end
    
    if haskey(suggestion, "details")
        println("\nDetails:")
        for (key, value) in suggestion["details"]
            println("  $key: $value")
        end
    end
    
    println("-"^60)
end

# ============================================================================
# MESH CHECKING FUNCTIONS
# ============================================================================

"""
Check serial mesh quality
"""
function check_serial_mesh(case_dir::String, check_quality::Bool, verbose::Bool)
    println("\n" * "="^60)
    println("MESH CHECK")
    println("="^60)
    println("Case: ", case_dir)
    
    # This would implement actual mesh checking
    # For now, placeholder
    println("\nMesh statistics:")
    println("  (Mesh checking not yet implemented)")
    
    if check_quality
        println("\nQuality metrics:")
        println("  (Quality checking not yet implemented)")
    end
    
    println("="^60)
end

"""
Check parallel mesh decomposition
"""
function check_parallel_mesh(case_dir::String, n_procs::Int, verbose::Bool)
    println("\n" * "="^60)
    println("PARALLEL MESH CHECK")
    println("="^60)
    println("Case: ", case_dir)
    println("Processors: ", n_procs)
    
    # Read decomposition info
    try
        decomp_info = DecomposePar.read_decomposition_info(case_dir)
        
        println("\nDecomposition info:")
        println("  Method: ", decomp_info.method)
        println("  Processors: ", decomp_info.n_processors)
        
        # Analyze partition quality
        metrics = decomp_info.partition_info.quality_metrics
        
        println("\nPartition quality:")
        @printf("  Load imbalance: %.1f%%\n", 
                (metrics["load_imbalance"] - 1.0) * 100)
        @printf("  Edge cut: %d\n", Int(metrics["edge_cut"]))
        @printf("  Interface faces: %d\n", Int(metrics["interface_faces"]))
        @printf("  Average neighbors: %.1f\n", metrics["avg_neighbors"])
        
    catch e
        println("\nError reading decomposition info: ", e)
    end
    
    println("="^60)
end

# ============================================================================
# EXPORT FUNCTIONS
# ============================================================================

"""
Export load analysis to file
"""
function export_load_analysis(info::LoadImbalanceInfo, filename::String)
    open(filename, "w") do f
        println(f, "# JuliaFOAM Load Balance Analysis")
        println(f, "# Date: ", Dates.now())
        println(f, "")
        println(f, "imbalance_factor,", info.imbalance_factor)
        println(f, "suggested_processors,", info.suggested_n_procs)
        println(f, "requires_redistribution,", info.requires_redistribution)
        println(f, "")
        println(f, "# Processor loads")
        println(f, "processor,load,communication_cost")
        for (i, (load, cost)) in enumerate(zip(info.processor_loads, 
                                               info.communication_costs))
            println(f, i-1, ",", load, ",", cost)
        end
    end
    
    println("Analysis exported to: ", filename)
end

"""
Read cell processor mapping from file
"""
function read_cell_processor_mapping(filename::String)
    # This would read actual mapping file
    # For now, placeholder
    error("read_cell_processor_mapping not implemented")
end

end # module CLITools