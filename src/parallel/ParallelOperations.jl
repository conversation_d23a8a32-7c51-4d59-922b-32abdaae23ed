"""
    ParallelOperations.jl

High-level parallel finite volume operations for JuliaFOAM.
Provides transparent parallel versions of gradient, divergence, Laplacian,
and other FVM operators that work seamlessly with distributed fields and meshes.

Key features:
- Automatic halo synchronization
- Computation-communication overlap
- Load-balanced execution
- Compatible with serial code
"""

module ParallelOperations

using MPI
using LinearAlgebra
using SparseArrays
using StaticArrays

# Import modules and types
using ..DistributedFields
using ..DistributedMesh
import ...JuliaFOAM: Mesh, Field, Face, Cell
import ...JuliaFOAM: BoundaryCondition, FixedValueBC, FixedGradientBC, ZeroGradientBC

export grad, div, laplacian
export interpolate_to_faces, surface_integral
export parallel_solve, parallel_residual

# ============================================================================
# GRADIENT OPERATOR
# ============================================================================

"""
    grad(field::DistributedField{T}, dmesh::DistributedMeshData) where T<:Number

Compute the gradient of a scalar distributed field using the Gauss theorem.
Returns a distributed vector field.

The gradient is computed as:
∇φ = (1/V) ∑_f (φ_f * A_f)

where φ_f is the face value and A_f is the face area vector.
"""
function grad(field::DistributedField{T}, dmesh::DistributedMeshData) where T<:Number
    # Ensure field is synchronized
    sync!(field)
    
    # Get local mesh
    mesh = get_local_mesh(dmesh)
    
    # Prepare result field (vector field)
    VT = SVector{3, T}
    result_values = Vector{VT}(undef, length(field.local_field.values))
    fill!(result_values, SVector(zero(T), zero(T), zero(T)))
    
    # Compute gradient using Gauss theorem
    n_local_cells = length(field.local_cells)
    
    # Process internal faces
    for face_idx in dmesh.local_to_global_faces
        face = mesh.faces[face_idx]
        
        # Skip boundary faces for now
        if face.neighbour <= 0
            continue
        end
        
        # Get local cell indices
        owner_local = get(dmesh.global_to_local_cells, face.owner, 0)
        neighbor_local = get(dmesh.global_to_local_cells, face.neighbour, 0)
        
        # Skip if both cells are not in local domain
        if owner_local == 0 && neighbor_local == 0
            continue
        end
        
        # Interpolate face value (simple averaging for now)
        if owner_local > 0 && neighbor_local > 0
            φ_f = 0.5 * (field.local_field.values[owner_local] + 
                        field.local_field.values[neighbor_local])
        elseif owner_local > 0
            # Neighbor is outside domain, use owner value
            φ_f = field.local_field.values[owner_local]
        else
            # Owner is outside domain, use neighbor value
            φ_f = field.local_field.values[neighbor_local]
        end
        
        # Face area vector
        A_f = SVector(face.area[1], face.area[2], face.area[3])
        
        # Contribute to gradient
        if owner_local > 0 && owner_local <= n_local_cells
            V = mesh.cells[face.owner].volume
            result_values[owner_local] += (φ_f * A_f) / V
        end
        
        if neighbor_local > 0 && neighbor_local <= n_local_cells
            V = mesh.cells[face.neighbour].volume
            result_values[neighbor_local] -= (φ_f * A_f) / V
        end
    end
    
    # Process boundary faces
    for (patch_name, face_indices) in mesh.boundary_patches
        bc = mesh.boundary_conditions[patch_name]
        
        for face_idx in face_indices
            # Check if this face is in local domain
            local_face_idx = get(dmesh.global_to_local_faces, face_idx, 0)
            if local_face_idx == 0
                continue
            end
            
            face = mesh.faces[face_idx]
            owner_local = get(dmesh.global_to_local_cells, face.owner, 0)
            
            if owner_local > 0 && owner_local <= n_local_cells
                # Get boundary value
                φ_f = get_boundary_value(field, face_idx, patch_name, bc)
                
                # Face area vector
                A_f = SVector(face.area[1], face.area[2], face.area[3])
                
                # Contribute to gradient
                V = mesh.cells[face.owner].volume
                result_values[owner_local] += (φ_f * A_f) / V
            end
        end
    end
    
    # Create result distributed field
    result_field = Field{VT}(
        "grad(" * field.local_field.name * ")",
        mesh,
        result_values,
        field.local_field.location
    )
    
    result = DistributedField{VT}(result_field, mesh, field.comm)
    
    # Copy communication structure
    result.local_cells = field.local_cells
    result.halo_cells = field.halo_cells
    result.send_list = field.send_list
    result.recv_list = field.recv_list
    
    return result
end

"""
    grad(field::DistributedField{SVector{3,T}}, dmesh::DistributedMeshData) where T<:Number

Compute the gradient of a vector distributed field.
Returns a distributed tensor field.
"""
function grad(field::DistributedField{SVector{3,T}}, dmesh::DistributedMeshData) where T<:Number
    # TODO: Implement vector gradient (returns tensor)
    error("Vector gradient not yet implemented")
end

# ============================================================================
# DIVERGENCE OPERATOR
# ============================================================================

"""
    div(field::DistributedField{SVector{3,T}}, dmesh::DistributedMeshData) where T<:Number

Compute the divergence of a vector distributed field using the Gauss theorem.
Returns a distributed scalar field.

The divergence is computed as:
∇·v = (1/V) ∑_f (v_f · A_f)
"""
function div(field::DistributedField{SVector{3,T}}, dmesh::DistributedMeshData) where T<:Number
    # Ensure field is synchronized
    sync!(field)
    
    # Get local mesh
    mesh = get_local_mesh(dmesh)
    
    # Prepare result field (scalar field)
    result_values = zeros(T, length(field.local_field.values))
    
    # Compute divergence using Gauss theorem
    n_local_cells = length(field.local_cells)
    
    # Process internal faces
    for face_idx in dmesh.local_to_global_faces
        face = mesh.faces[face_idx]
        
        # Skip boundary faces for now
        if face.neighbour <= 0
            continue
        end
        
        # Get local cell indices
        owner_local = get(dmesh.global_to_local_cells, face.owner, 0)
        neighbor_local = get(dmesh.global_to_local_cells, face.neighbour, 0)
        
        # Skip if both cells are not in local domain
        if owner_local == 0 && neighbor_local == 0
            continue
        end
        
        # Interpolate face value
        if owner_local > 0 && neighbor_local > 0
            v_f = 0.5 * (field.local_field.values[owner_local] + 
                        field.local_field.values[neighbor_local])
        elseif owner_local > 0
            v_f = field.local_field.values[owner_local]
        else
            v_f = field.local_field.values[neighbor_local]
        end
        
        # Face area vector
        A_f = SVector(face.area[1], face.area[2], face.area[3])
        
        # Compute flux
        flux = dot(v_f, A_f)
        
        # Contribute to divergence
        if owner_local > 0 && owner_local <= n_local_cells
            V = mesh.cells[face.owner].volume
            result_values[owner_local] += flux / V
        end
        
        if neighbor_local > 0 && neighbor_local <= n_local_cells
            V = mesh.cells[face.neighbour].volume
            result_values[neighbor_local] -= flux / V
        end
    end
    
    # Process boundary faces
    for (patch_name, face_indices) in mesh.boundary_patches
        bc = mesh.boundary_conditions[patch_name]
        
        for face_idx in face_indices
            local_face_idx = get(dmesh.global_to_local_faces, face_idx, 0)
            if local_face_idx == 0
                continue
            end
            
            face = mesh.faces[face_idx]
            owner_local = get(dmesh.global_to_local_cells, face.owner, 0)
            
            if owner_local > 0 && owner_local <= n_local_cells
                # Get boundary value
                v_f = get_boundary_value_vector(field, face_idx, patch_name, bc)
                
                # Face area vector
                A_f = SVector(face.area[1], face.area[2], face.area[3])
                
                # Compute flux
                flux = dot(v_f, A_f)
                
                # Contribute to divergence
                V = mesh.cells[face.owner].volume
                result_values[owner_local] += flux / V
            end
        end
    end
    
    # Create result distributed field
    result_field = Field{T}(
        "div(" * field.local_field.name * ")",
        mesh,
        result_values,
        field.local_field.location
    )
    
    result = DistributedField{T}(result_field, mesh, field.comm)
    
    # Copy communication structure
    result.local_cells = field.local_cells
    result.halo_cells = field.halo_cells
    result.send_list = field.send_list
    result.recv_list = field.recv_list
    
    return result
end

# ============================================================================
# LAPLACIAN OPERATOR
# ============================================================================

"""
    laplacian(field::DistributedField{T}, dmesh::DistributedMeshData; 
             diffusion_coeff::Union{T,DistributedField{T}}=one(T)) where T<:Number

Compute the Laplacian of a scalar distributed field.
Returns a distributed scalar field.

The Laplacian is computed as:
∇²φ = ∇·(Γ∇φ)

where Γ is the diffusion coefficient.
"""
function laplacian(field::DistributedField{T}, dmesh::DistributedMeshData; 
                  diffusion_coeff::Union{T,DistributedField{T}}=one(T)) where T<:Number
    
    # Ensure field is synchronized
    sync!(field)
    
    # Get local mesh
    mesh = get_local_mesh(dmesh)
    
    # Handle diffusion coefficient
    if diffusion_coeff isa DistributedField
        sync!(diffusion_coeff)
        Γ_values = diffusion_coeff.local_field.values
    else
        Γ_values = fill(diffusion_coeff, length(field.local_field.values))
    end
    
    # Prepare result field
    result_values = zeros(T, length(field.local_field.values))
    
    # Compute Laplacian using face gradients
    n_local_cells = length(field.local_cells)
    
    # Process internal faces
    for face_idx in dmesh.local_to_global_faces
        face = mesh.faces[face_idx]
        
        # Skip boundary faces for now
        if face.neighbour <= 0
            continue
        end
        
        # Get local cell indices
        owner_local = get(dmesh.global_to_local_cells, face.owner, 0)
        neighbor_local = get(dmesh.global_to_local_cells, face.neighbour, 0)
        
        # Skip if both cells are not in local domain
        if owner_local == 0 && neighbor_local == 0
            continue
        end
        
        # Get cell values
        φ_owner = owner_local > 0 ? field.local_field.values[owner_local] : zero(T)
        φ_neighbor = neighbor_local > 0 ? field.local_field.values[neighbor_local] : zero(T)
        
        # Get cell centers
        owner_center = mesh.cells[face.owner].center
        neighbor_center = mesh.cells[face.neighbour].center
        
        # Distance between cell centers
        d = SVector(
            neighbor_center[1] - owner_center[1],
            neighbor_center[2] - owner_center[2],
            neighbor_center[3] - owner_center[3]
        )
        d_mag = norm(d)
        
        # Face area magnitude
        A_mag = norm(face.area)
        
        # Interpolate diffusion coefficient
        if owner_local > 0 && neighbor_local > 0
            Γ_f = 0.5 * (Γ_values[owner_local] + Γ_values[neighbor_local])
        elseif owner_local > 0
            Γ_f = Γ_values[owner_local]
        else
            Γ_f = Γ_values[neighbor_local]
        end
        
        # Compute diffusive flux
        flux = Γ_f * A_mag * (φ_neighbor - φ_owner) / d_mag
        
        # Contribute to Laplacian
        if owner_local > 0 && owner_local <= n_local_cells
            V = mesh.cells[face.owner].volume
            result_values[owner_local] += flux / V
        end
        
        if neighbor_local > 0 && neighbor_local <= n_local_cells
            V = mesh.cells[face.neighbour].volume
            result_values[neighbor_local] -= flux / V
        end
    end
    
    # Process boundary faces
    for (patch_name, face_indices) in mesh.boundary_patches
        bc = mesh.boundary_conditions[patch_name]
        
        for face_idx in face_indices
            local_face_idx = get(dmesh.global_to_local_faces, face_idx, 0)
            if local_face_idx == 0
                continue
            end
            
            face = mesh.faces[face_idx]
            owner_local = get(dmesh.global_to_local_cells, face.owner, 0)
            
            if owner_local > 0 && owner_local <= n_local_cells
                # Handle different boundary conditions
                if bc isa FixedValueBC
                    # Dirichlet BC
                    φ_owner = field.local_field.values[owner_local]
                    φ_boundary = bc.value
                    
                    # Distance from cell center to face center
                    owner_center = mesh.cells[face.owner].center
                    face_center = face.center
                    d = norm(SVector(
                        face_center[1] - owner_center[1],
                        face_center[2] - owner_center[2],
                        face_center[3] - owner_center[3]
                    ))
                    
                    # Face area magnitude
                    A_mag = norm(face.area)
                    
                    # Diffusion coefficient at face
                    Γ_f = Γ_values[owner_local]
                    
                    # Compute flux
                    flux = Γ_f * A_mag * (φ_boundary - φ_owner) / d
                    
                    # Contribute to Laplacian
                    V = mesh.cells[face.owner].volume
                    result_values[owner_local] += flux / V
                    
                elseif bc isa FixedGradientBC
                    # Neumann BC
                    gradient = bc.gradient
                    
                    # Face area vector
                    A_f = face.area
                    A_mag = norm(A_f)
                    
                    # Diffusion coefficient at face
                    Γ_f = Γ_values[owner_local]
                    
                    # Flux = Γ * gradient · A
                    flux = Γ_f * gradient * A_mag
                    
                    # Contribute to Laplacian
                    V = mesh.cells[face.owner].volume
                    result_values[owner_local] += flux / V
                    
                elseif bc isa ZeroGradientBC
                    # Zero flux - nothing to add
                end
            end
        end
    end
    
    # Create result distributed field
    result_field = Field{T}(
        "laplacian(" * field.local_field.name * ")",
        mesh,
        result_values,
        field.local_field.location
    )
    
    result = DistributedField{T}(result_field, mesh, field.comm)
    
    # Copy communication structure
    result.local_cells = field.local_cells
    result.halo_cells = field.halo_cells
    result.send_list = field.send_list
    result.recv_list = field.recv_list
    
    return result
end

# ============================================================================
# HELPER FUNCTIONS
# ============================================================================

"""
    interpolate_to_faces(field::DistributedField{T}, dmesh::DistributedMeshData) where T

Interpolate cell-centered values to face centers.
"""
function interpolate_to_faces(field::DistributedField{T}, dmesh::DistributedMeshData) where T
    sync!(field)
    mesh = get_local_mesh(dmesh)
    
    face_values = Dict{Int, T}()
    
    for face_idx in dmesh.local_to_global_faces
        face = mesh.faces[face_idx]
        
        owner_local = get(dmesh.global_to_local_cells, face.owner, 0)
        neighbor_local = get(dmesh.global_to_local_cells, face.neighbour, 0)
        
        if face.neighbour > 0 && owner_local > 0 && neighbor_local > 0
            # Internal face - linear interpolation
            face_values[face_idx] = 0.5 * (field.local_field.values[owner_local] + 
                                          field.local_field.values[neighbor_local])
        elseif owner_local > 0
            # Boundary face or neighbor not in domain
            face_values[face_idx] = field.local_field.values[owner_local]
        end
    end
    
    return face_values
end

"""
    surface_integral(field::DistributedField{T}, patch_name::String, dmesh::DistributedMeshData) where T

Compute surface integral of a field over a boundary patch.
"""
function surface_integral(field::DistributedField{T}, patch_name::String, 
                         dmesh::DistributedMeshData) where T
    sync!(field)
    mesh = get_local_mesh(dmesh)
    
    local_integral = zero(T)
    
    if haskey(mesh.boundary_patches, patch_name)
        face_indices = mesh.boundary_patches[patch_name]
        
        for face_idx in face_indices
            local_face_idx = get(dmesh.global_to_local_faces, face_idx, 0)
            if local_face_idx == 0
                continue
            end
            
            face = mesh.faces[face_idx]
            owner_local = get(dmesh.global_to_local_cells, face.owner, 0)
            
            if owner_local > 0
                # Get field value at boundary
                bc = mesh.boundary_conditions[patch_name]
                φ_f = get_boundary_value(field, face_idx, patch_name, bc)
                
                # Add contribution
                A_mag = norm(face.area)
                local_integral += φ_f * A_mag
            end
        end
    end
    
    # Global reduction
    return MPI.Allreduce(local_integral, +, field.comm)
end

"""
    get_boundary_value(field::DistributedField{T}, face_idx::Int, 
                      patch_name::String, bc::BoundaryCondition) where T

Get boundary value for a scalar field based on boundary condition.
"""
function get_boundary_value(field::DistributedField{T}, face_idx::Int, 
                           patch_name::String, bc::BoundaryCondition) where T<:Number
    
    if bc isa FixedValueBC
        return bc.value
    elseif bc isa ZeroGradientBC
        # Use cell center value
        mesh = field.mesh
        face = mesh.faces[face_idx]
        owner_local = get(field.dmesh.global_to_local_cells, face.owner, 0)
        return owner_local > 0 ? field.local_field.values[owner_local] : zero(T)
    else
        # Other BC types
        return zero(T)
    end
end

"""
    get_boundary_value_vector(field::DistributedField{SVector{3,T}}, face_idx::Int,
                             patch_name::String, bc::BoundaryCondition) where T

Get boundary value for a vector field based on boundary condition.
"""
function get_boundary_value_vector(field::DistributedField{SVector{3,T}}, face_idx::Int,
                                  patch_name::String, bc::BoundaryCondition) where T<:Number
    
    if bc isa FixedValueBC
        return bc.value
    elseif bc isa ZeroGradientBC
        # Use cell center value
        mesh = field.mesh
        face = mesh.faces[face_idx]
        owner_local = get(field.dmesh.global_to_local_cells, face.owner, 0)
        return owner_local > 0 ? field.local_field.values[owner_local] : SVector(zero(T), zero(T), zero(T))
    else
        return SVector(zero(T), zero(T), zero(T))
    end
end

# ============================================================================
# PARALLEL LINEAR SOLVER INTERFACE
# ============================================================================

"""
    parallel_solve(A::SparseMatrixCSC, b::DistributedField{T}, dmesh::DistributedMeshData;
                  method::Symbol=:cg, preconditioner::Symbol=:jacobi,
                  tol::Float64=1e-6, maxiter::Int=1000) where T

Solve a linear system in parallel using iterative methods.
"""
function parallel_solve(A::SparseMatrixCSC, b::DistributedField{T}, dmesh::DistributedMeshData;
                       method::Symbol=:cg, preconditioner::Symbol=:jacobi,
                       tol::Float64=1e-6, maxiter::Int=1000) where T
    
    # This is a placeholder - would integrate with PETSc.jl or similar
    # For now, use simple parallel Jacobi iteration
    
    sync!(b)
    n = length(b.local_field.values)
    
    # Initial guess
    x = DistributedField(b.local_field.name * "_solution", dmesh.mesh, zero(T), 
                        b.local_field.location, b.comm)
    
    # Copy communication structure
    x.local_cells = b.local_cells
    x.halo_cells = b.halo_cells
    x.send_list = b.send_list
    x.recv_list = b.recv_list
    
    # Simple Jacobi iteration
    for iter in 1:maxiter
        # Store old values
        x_old = copy(x.local_field.values)
        
        # Update local values
        for i in 1:length(b.local_cells)
            sum_off_diag = zero(T)
            
            for j in A.colptr[i]:A.colptr[i+1]-1
                col = A.rowval[j]
                if col != i
                    sum_off_diag += A.nzval[j] * x_old[col]
                end
            end
            
            # Jacobi update
            diag = A[i,i]
            if abs(diag) > eps(T)
                x.local_field.values[i] = (b.local_field.values[i] - sum_off_diag) / diag
            end
        end
        
        # Synchronize
        sync!(x)
        
        # Check convergence (simplified)
        if iter % 10 == 0
            residual = parallel_residual(A, x, b)
            if residual < tol
                if x.rank == 0
                    println("Converged in $iter iterations, residual = $residual")
                end
                break
            end
        end
    end
    
    return x
end

"""
    parallel_residual(A::SparseMatrixCSC, x::DistributedField{T}, b::DistributedField{T}) where T

Compute the residual ||b - Ax|| in parallel.
"""
function parallel_residual(A::SparseMatrixCSC, x::DistributedField{T}, b::DistributedField{T}) where T
    sync!(x)
    sync!(b)
    
    # Compute local residual
    local_residual = zero(T)
    
    for i in 1:length(x.local_cells)
        # Compute (Ax)_i
        ax_i = zero(T)
        for j in A.colptr[i]:A.colptr[i+1]-1
            col = A.rowval[j]
            ax_i += A.nzval[j] * x.local_field.values[col]
        end
        
        # Add to residual
        res_i = b.local_field.values[i] - ax_i
        local_residual += res_i * res_i
    end
    
    # Global reduction
    global_residual = MPI.Allreduce(local_residual, +, x.comm)
    
    return sqrt(global_residual)
end

end # module ParallelOperations