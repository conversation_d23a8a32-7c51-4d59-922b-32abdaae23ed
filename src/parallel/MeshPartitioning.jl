"""
MeshPartitioning.jl

Comprehensive mesh partitioning framework supporting multiple decomposition methods
for both structured and unstructured meshes. Provides the foundation for parallel
CFD computations with transparent domain decomposition.

Key Features:
- Multiple partitioning methods (Simple, METIS, SCOTCH, Hierarchical, Manual)
- Support for structured and unstructured meshes
- Load balancing capabilities
- Face weight considerations
- Partition quality metrics
"""

module MeshPartitioning

using LinearAlgebra
using SparseArrays
using Statistics
using Printf

# Try to import MPI, but don't fail if not available
const MPI_AVAILABLE = try
    using MPI
    true
catch
    false
end

# Try to import Metis, but don't fail if not available
const METIS_AVAILABLE = try
    using Metis
    true
catch
    false
end

# Try to import SCOTCH, but don't fail if not available
const SCOTCH_AVAILABLE = try
    # SCOTCH.jl is not commonly available, so this will typically be false
    # In a real implementation, this would check for SCOTCH.jl or LibSCOTCH
    using SCOTCH
    true
catch
    false
end

# Import UnstructuredMesh if available
const UNSTRUCTURED_MESH_AVAILABLE = try
    using ..UnstructuredMesh
    true
catch
    false
end

# Export partition methods and main functions
export PartitionMethod, SimplePartition, MetisPartition, ScotchPartition
export HierarchicalPartition, ManualPartition, partition_mesh
export Partition, PartitionInfo, analyze_partition_quality

# ============================================================================
# PARTITION METHOD TYPES
# ============================================================================

"""
Abstract base type for all partition methods
"""
abstract type PartitionMethod end

"""
Simple geometric partitioning for structured meshes
"""
struct SimplePartition <: PartitionMethod
    n_subdomains::Int
    direction::Symbol  # :x, :y, :z, :xyz
    
    function SimplePartition(n_subdomains::Int, direction::Symbol=:xyz)
        @assert n_subdomains > 0 "Number of subdomains must be positive"
        @assert direction in [:x, :y, :z, :xy, :xz, :yz, :xyz] "Invalid direction"
        new(n_subdomains, direction)
    end
end

"""
METIS graph-based partitioning for unstructured meshes
"""
struct MetisPartition <: PartitionMethod
    n_subdomains::Int
    method::Symbol         # :kway, :recursive
    imbalance::Float64     # Load imbalance tolerance (e.g., 1.05 = 5% imbalance)
    face_weights::Bool     # Weight by face area
    ncuts::Int            # Number of cutting trials
    
    function MetisPartition(n_subdomains::Int; 
                           method::Symbol=:kway,
                           imbalance::Float64=1.05,
                           face_weights::Bool=false,
                           ncuts::Int=5)
        @assert n_subdomains > 0 "Number of subdomains must be positive"
        @assert method in [:kway, :recursive] "Method must be :kway or :recursive"
        @assert imbalance >= 1.0 "Imbalance must be >= 1.0"
        new(n_subdomains, method, imbalance, face_weights, ncuts)
    end
end

"""
SCOTCH graph-based partitioning with advanced strategies
"""
struct ScotchPartition <: PartitionMethod
    n_subdomains::Int
    strategy::String
    imbalance::Float64
    seed::Int
    verbose::Bool

    function ScotchPartition(n_subdomains::Int;
                           strategy::String="quality",
                           imbalance::Float64=1.03,
                           seed::Int=0,
                           verbose::Bool=false)
        @assert n_subdomains > 0 "Number of subdomains must be positive"
        @assert imbalance >= 1.0 "Imbalance factor must be >= 1.0"
        @assert strategy in ["speed", "quality", "balance", "safety"] "Invalid SCOTCH strategy"
        new(n_subdomains, strategy, imbalance, seed, verbose)
    end
end

"""
Hierarchical partitioning for multi-level decomposition
"""
struct HierarchicalPartition <: PartitionMethod
    n_subdomains::Int
    n_levels::Int
    methods::Vector{PartitionMethod}  # Method for each level
    
    function HierarchicalPartition(n_subdomains::Int, n_levels::Int=2)
        @assert n_subdomains > 0 "Number of subdomains must be positive"
        @assert n_levels > 0 "Number of levels must be positive"
        
        # Default: use METIS at each level
        methods = [MetisPartition(round(Int, n_subdomains^(1.0/n_levels))) 
                  for _ in 1:n_levels]
        new(n_subdomains, n_levels, methods)
    end
end

"""
Manual partitioning with user-specified cell assignments
"""
struct ManualPartition <: PartitionMethod
    cell_processor_map::Vector{Int}
    n_subdomains::Int
    
    function ManualPartition(cell_processor_map::Vector{Int})
        n_subdomains = maximum(cell_processor_map) + 1  # 0-based indexing
        @assert minimum(cell_processor_map) >= 0 "Processor IDs must be non-negative"
        new(cell_processor_map, n_subdomains)
    end
end

# ============================================================================
# PARTITION DATA STRUCTURES
# ============================================================================

"""
Partition information containing cell-to-processor mapping and metadata
"""
struct Partition
    n_cells::Int
    n_subdomains::Int
    cell_processor::Vector{Int}      # Cell to processor mapping (0-based)
    processor_cells::Vector{Vector{Int}}  # Cells in each processor
    n_interface_faces::Int           # Number of faces between processors
    edge_cut::Int                    # Total edge cut (communication volume)
    load_imbalance::Float64          # Max/average load ratio
end

"""
Detailed partition information including neighbor connectivity
"""
struct PartitionInfo
    partition::Partition
    processor_neighbors::Vector{Set{Int}}  # Neighbor processors for each proc
    interface_faces::Vector{Tuple{Int,Int,Int}}  # (face_id, proc1, proc2)
    halo_cells::Vector{Set{Int}}     # Halo cells needed by each processor
    partition_method::PartitionMethod
    quality_metrics::Dict{String, Float64}
end

# ============================================================================
# MAIN PARTITIONING FUNCTION
# ============================================================================

"""
    partition_mesh(mesh, method::PartitionMethod) -> PartitionInfo

Partition a mesh using the specified method. Automatically handles both
structured and unstructured meshes.
"""
function partition_mesh(mesh, method::PartitionMethod)
    # Check if mesh is structured or unstructured
    if isdefined(mesh, :nx) && isdefined(mesh, :ny)  # Structured mesh indicators
        return partition_structured(mesh, method)
    else
        return partition_unstructured(mesh, method)
    end
end

# ============================================================================
# STRUCTURED MESH PARTITIONING
# ============================================================================

"""
Partition an unstructured mesh using simple geometric decomposition
"""
function partition_unstructured(mesh, method::SimplePartition)
    # Determine number of cells
    n_cells = if isdefined(mesh, :cells) && !isempty(mesh.cells)
        length(mesh.cells)
    elseif isdefined(mesh, :n_cells)
        mesh.n_cells
    else
        error("Cannot determine number of cells in mesh")
    end

    # Get cell centers for geometric partitioning
    cell_centers = if isdefined(mesh, :cells) && !isempty(mesh.cells)
        [cell.center for cell in mesh.cells]
    else
        error("Cannot access cell centers for geometric partitioning")
    end

    # Extract coordinates based on direction
    coords = if method.direction == :x
        [center.x for center in cell_centers]
    elseif method.direction == :y
        [center.y for center in cell_centers]
    elseif method.direction == :z
        [center.z for center in cell_centers]
    else
        error("Unsupported decomposition direction: $(method.direction)")
    end

    # Sort cells by coordinate
    sorted_indices = sortperm(coords)

    # Assign cells to processors
    cell_processor = zeros(Int, n_cells)
    cells_per_proc = ceil(Int, n_cells / method.n_subdomains)

    for (idx, cell_id) in enumerate(sorted_indices)
        proc_id = min(div(idx - 1, cells_per_proc), method.n_subdomains - 1)
        cell_processor[cell_id] = proc_id
    end

    # Build partition data structure
    partition = build_partition(n_cells, method.n_subdomains, cell_processor, mesh)

    # Build partition info with connectivity
    return build_partition_info(partition, mesh, method)
end

"""
Partition a structured mesh using geometric decomposition
"""
function partition_structured(mesh, method::SimplePartition)
    nx, ny, nz = get_mesh_dimensions(mesh)
    n_cells = nx * ny * nz
    
    # Initialize cell processor mapping
    cell_processor = zeros(Int, n_cells)
    
    if method.direction == :x
        # 1D decomposition along X
        cells_per_proc = ceil(Int, nx / method.n_subdomains)
        for k in 1:nz, j in 1:ny, i in 1:nx
            cell_id = (k-1)*nx*ny + (j-1)*nx + i
            proc_id = min(div(i-1, cells_per_proc), method.n_subdomains-1)
            cell_processor[cell_id] = proc_id
        end
        
    elseif method.direction == :y
        # 1D decomposition along Y
        cells_per_proc = ceil(Int, ny / method.n_subdomains)
        for k in 1:nz, j in 1:ny, i in 1:nx
            cell_id = (k-1)*nx*ny + (j-1)*nx + i
            proc_id = min(div(j-1, cells_per_proc), method.n_subdomains-1)
            cell_processor[cell_id] = proc_id
        end
        
    elseif method.direction == :z
        # 1D decomposition along Z
        cells_per_proc = ceil(Int, nz / method.n_subdomains)
        for k in 1:nz, j in 1:ny, i in 1:nx
            cell_id = (k-1)*nx*ny + (j-1)*nx + i
            proc_id = min(div(k-1, cells_per_proc), method.n_subdomains-1)
            cell_processor[cell_id] = proc_id
        end
        
    elseif method.direction == :xyz
        # 3D decomposition - try to balance in all directions
        px, py, pz = factorize_3d(method.n_subdomains, nx, ny, nz)
        
        for k in 1:nz, j in 1:ny, i in 1:nx
            cell_id = (k-1)*nx*ny + (j-1)*nx + i
            pi = min(div((i-1)*px, nx), px-1)
            pj = min(div((j-1)*py, ny), py-1)
            pk = min(div((k-1)*pz, nz), pz-1)
            proc_id = pk*px*py + pj*px + pi
            cell_processor[cell_id] = proc_id
        end
    else
        error("Unsupported decomposition direction: $(method.direction)")
    end
    
    # Build partition data structure
    partition = build_partition(n_cells, method.n_subdomains, cell_processor, mesh)
    
    # Build partition info with connectivity
    return build_partition_info(partition, mesh, method)
end

"""
Find optimal 3D processor grid dimensions
"""
function factorize_3d(n_procs::Int, nx::Int, ny::Int, nz::Int)
    best_px, best_py, best_pz = 1, 1, n_procs
    best_score = Inf
    
    for px in 1:n_procs
        if n_procs % px != 0
            continue
        end
        
        for py in 1:div(n_procs, px)
            if n_procs % (px * py) != 0
                continue
            end
            
            pz = div(n_procs, px * py)
            
            # Score based on aspect ratio similarity
            mesh_aspect = [nx, ny, nz] ./ minimum([nx, ny, nz])
            proc_aspect = [px, py, pz] ./ minimum([px, py, pz])
            score = norm(mesh_aspect .- proc_aspect)
            
            if score < best_score
                best_score = score
                best_px, best_py, best_pz = px, py, pz
            end
        end
    end
    
    return best_px, best_py, best_pz
end

# ============================================================================
# UNSTRUCTURED MESH PARTITIONING
# ============================================================================

"""
Partition an unstructured mesh using graph-based methods
"""
function partition_unstructured(mesh, method::MetisPartition)
    # Check if METIS is available
    if !METIS_AVAILABLE
        @warn "METIS not available. Falling back to simple geometric partitioning."
        simple_method = SimplePartition(method.n_subdomains, :x)
        return partition_unstructured(mesh, simple_method)
    end

    # Determine number of cells based on mesh type
    n_cells = if isdefined(mesh, :cells) && !isempty(mesh.cells)
        length(mesh.cells)
    elseif isdefined(mesh, :n_cells)
        mesh.n_cells
    else
        error("Cannot determine number of cells in mesh")
    end

    # Validate input
    if n_cells < method.n_subdomains
        @warn "More subdomains ($method.n_subdomains) than cells ($n_cells). Using simple partitioning."
        simple_method = SimplePartition(method.n_subdomains, :x)
        return partition_unstructured(mesh, simple_method)
    end

    try
        # Build mesh dual graph
        graph = build_mesh_graph(mesh, method.face_weights)

        # Set METIS options
        options = Metis.options()
        options[Metis.OPTION_NCUTS] = method.ncuts
        options[Metis.OPTION_UFACTOR] = round(Int, (method.imbalance - 1.0) * 1000)

        # Call METIS partitioner
        objval, cell_processor = if method.method == :kway
            Metis.partition(graph, method.n_subdomains; alg=:KWAY, options=options)
        else
            Metis.partition(graph, method.n_subdomains; alg=:RECURSIVE, options=options)
        end

        # Convert to 0-based indexing for consistency
        cell_processor .-= 1

        # Validate partition result
        if length(cell_processor) != n_cells
            error("METIS returned partition of wrong size: expected $n_cells, got $(length(cell_processor))")
        end

        # Build partition data structure
        partition = build_partition(n_cells, method.n_subdomains, cell_processor, mesh)

        # Build partition info with connectivity
        return build_partition_info(partition, mesh, method)

    catch e
        @warn "METIS partitioning failed: $e. Falling back to simple partitioning."
        # Fallback to simple geometric partitioning
        simple_method = SimplePartition(method.n_subdomains, :x)
        return partition_unstructured(mesh, simple_method)
    end
end

"""
Build mesh dual graph for partitioning
"""
function build_mesh_graph(mesh, use_face_weights::Bool)
    # Determine number of cells based on mesh type
    n_cells = if isdefined(mesh, :cells) && !isempty(mesh.cells)
        length(mesh.cells)
    elseif isdefined(mesh, :n_cells)
        mesh.n_cells
    else
        error("Cannot determine number of cells in mesh")
    end

    # Build adjacency lists using CSR format
    xadj = zeros(Int32, n_cells + 1)
    adjncy = Int32[]
    adjwgt = Int32[]  # Edge weights

    # Count neighbors for each cell (first pass)
    neighbor_counts = zeros(Int, n_cells)

    # Handle different mesh types
    if isdefined(mesh, :faces) && !isempty(mesh.faces)
        # UnstructuredMesh type
        for face in mesh.faces
            if isdefined(face, :neighbor_cell) && face.neighbor_cell > 0
                # Internal face - both cells are neighbors
                neighbor_counts[face.owner_cell] += 1
                neighbor_counts[face.neighbor_cell] += 1
            elseif isdefined(face, :neighbor) && face.neighbor > 0
                # Alternative field name
                neighbor_counts[face.owner] += 1
                neighbor_counts[face.neighbor] += 1
            end
        end
    elseif isdefined(mesh, :cell_neighbors)
        # Use precomputed neighbor information
        for (cell_id, neighbors) in enumerate(mesh.cell_neighbors)
            neighbor_counts[cell_id] = length(neighbors)
        end
    else
        error("Cannot extract connectivity information from mesh")
    end

    # Build CSR index array
    xadj[1] = 0
    for i in 1:n_cells
        xadj[i+1] = xadj[i] + neighbor_counts[i]
    end

    # Reserve space for adjacency and weights
    total_edges = xadj[end]
    resize!(adjncy, total_edges)
    if use_face_weights
        resize!(adjwgt, total_edges)
    end

    # Fill adjacency lists (second pass)
    current_pos = copy(xadj[1:end-1])  # Current position for each cell

    if isdefined(mesh, :faces) && !isempty(mesh.faces)
        for face in mesh.faces
            owner = if isdefined(face, :owner_cell)
                face.owner_cell
            else
                face.owner
            end

            neighbor = if isdefined(face, :neighbor_cell)
                face.neighbor_cell
            else
                face.neighbor
            end

            if neighbor > 0  # Internal face
                # Add neighbor to owner's adjacency list
                pos = current_pos[owner] + 1
                adjncy[pos] = neighbor - 1  # Convert to 0-based indexing
                current_pos[owner] += 1

                # Add owner to neighbor's adjacency list
                pos = current_pos[neighbor] + 1
                adjncy[pos] = owner - 1  # Convert to 0-based indexing
                current_pos[neighbor] += 1

                # Add weights if requested
                if use_face_weights
                    face_area = if isdefined(face, :area_vector)
                        norm(face.area_vector)
                    elseif isdefined(face, :area)
                        face.area
                    else
                        1.0  # Default weight
                    end

                    weight = max(1, round(Int32, face_area * 1000))  # Scale and ensure positive
                    adjwgt[current_pos[owner]] = weight
                    adjwgt[current_pos[neighbor]] = weight
                end
            end
        end
    elseif isdefined(mesh, :cell_neighbors)
        # Use precomputed neighbor information
        for (cell_id, neighbors) in enumerate(mesh.cell_neighbors)
            for neighbor_id in neighbors
                pos = current_pos[cell_id] + 1
                adjncy[pos] = neighbor_id - 1  # Convert to 0-based
                current_pos[cell_id] += 1

                if use_face_weights
                    adjwgt[pos] = 1  # Default weight
                end
            end
        end
    end

    # Create METIS graph
    try
        if use_face_weights && !isempty(adjwgt)
            return Metis.graph(xadj, adjncy, adjwgt=adjwgt)
        else
            return Metis.graph(xadj, adjncy)
        end
    catch e
        @error "Failed to create METIS graph: $e"
        @error "Graph stats: n_cells=$n_cells, total_edges=$total_edges"
        @error "xadj length: $(length(xadj)), adjncy length: $(length(adjncy))"
        rethrow(e)
    end
end

"""
Partition an unstructured mesh using SCOTCH graph partitioning
"""
function partition_unstructured(mesh, method::ScotchPartition)
    # Check if SCOTCH is available
    if !SCOTCH_AVAILABLE
        @warn "SCOTCH not available. Falling back to METIS partitioning."
        metis_method = MetisPartition(method.n_subdomains, imbalance=method.imbalance)
        return partition_unstructured(mesh, metis_method)
    end

    # Determine number of cells
    n_cells = if isdefined(mesh, :cells) && !isempty(mesh.cells)
        length(mesh.cells)
    elseif isdefined(mesh, :n_cells)
        mesh.n_cells
    else
        error("Cannot determine number of cells in mesh")
    end

    # Validate input
    if n_cells < method.n_subdomains
        @warn "More subdomains ($(method.n_subdomains)) than cells ($n_cells). Using simple partitioning."
        simple_method = SimplePartition(method.n_subdomains, :x)
        return partition_unstructured(mesh, simple_method)
    end

    try
        # Build mesh dual graph for SCOTCH
        graph = build_scotch_graph(mesh)

        # Set SCOTCH strategy
        strategy_string = build_scotch_strategy(method.strategy, method.n_subdomains)

        # Call SCOTCH partitioner
        cell_processor = scotch_partition_graph(graph, method.n_subdomains,
                                              strategy_string, method.imbalance,
                                              method.seed, method.verbose)

        # Convert to 0-based indexing for consistency
        cell_processor .-= 1

        # Validate partition result
        if length(cell_processor) != n_cells
            error("SCOTCH returned partition of wrong size: expected $n_cells, got $(length(cell_processor))")
        end

        # Build partition data structure
        partition = build_partition(n_cells, method.n_subdomains, cell_processor, mesh)

        # Build partition info with connectivity
        return build_partition_info(partition, mesh, method)

    catch e
        @warn "SCOTCH partitioning failed: $e. Falling back to METIS partitioning."
        # Fallback to METIS partitioning
        metis_method = MetisPartition(method.n_subdomains, imbalance=method.imbalance)
        return partition_unstructured(mesh, metis_method)
    end
end

"""
Build SCOTCH-compatible graph from mesh
"""
function build_scotch_graph(mesh)
    # Determine number of cells
    n_cells = if isdefined(mesh, :cells) && !isempty(mesh.cells)
        length(mesh.cells)
    elseif isdefined(mesh, :n_cells)
        mesh.n_cells
    else
        error("Cannot determine number of cells in mesh")
    end

    # Build adjacency lists
    adjacency = [Int[] for _ in 1:n_cells]

    # Handle different mesh types
    if isdefined(mesh, :faces) && !isempty(mesh.faces)
        # UnstructuredMesh type
        for face in mesh.faces
            owner = if isdefined(face, :owner_cell)
                face.owner_cell
            else
                face.owner
            end

            neighbor = if isdefined(face, :neighbor_cell)
                face.neighbor_cell
            else
                face.neighbor
            end

            if neighbor > 0  # Internal face
                push!(adjacency[owner], neighbor)
                push!(adjacency[neighbor], owner)
            end
        end
    elseif isdefined(mesh, :cell_neighbors)
        # Use precomputed neighbor information
        for (cell_id, neighbors) in enumerate(mesh.cell_neighbors)
            adjacency[cell_id] = copy(neighbors)
        end
    else
        error("Cannot extract connectivity information from mesh")
    end

    # Remove duplicates and sort
    for i in 1:n_cells
        adjacency[i] = sort(unique(adjacency[i]))
    end

    return adjacency
end

"""
Build SCOTCH strategy string based on method parameters
"""
function build_scotch_strategy(strategy::String, n_subdomains::Int)
    if strategy == "speed"
        return "r{job=t,map=t,poli=S,sep=m{type=h,vert=80,low=h{pass=10}f{bal=0.2},asc=b{width=3,bnd=f{bal=0.2}}}}"
    elseif strategy == "quality"
        return "r{job=t,map=t,poli=S,sep=m{type=h,vert=80,low=h{pass=10}f{bal=0.1},asc=b{width=3,bnd=f{bal=0.1}}}}"
    elseif strategy == "balance"
        return "r{job=t,map=t,poli=L,sep=m{type=h,vert=80,low=h{pass=10}f{bal=0.05},asc=b{width=3,bnd=f{bal=0.05}}}}"
    elseif strategy == "safety"
        return "r{job=t,map=t,poli=S,sep=m{type=h,vert=100,low=h{pass=20}f{bal=0.1},asc=b{width=5,bnd=f{bal=0.1}}}}"
    else
        @warn "Unknown SCOTCH strategy: $strategy, using quality"
        return "r{job=t,map=t,poli=S,sep=m{type=h,vert=80,low=h{pass=10}f{bal=0.1},asc=b{width=3,bnd=f{bal=0.1}}}}"
    end
end

"""
Call SCOTCH graph partitioning (placeholder for actual SCOTCH integration)
"""
function scotch_partition_graph(adjacency, n_subdomains::Int, strategy::String,
                               imbalance::Float64, seed::Int, verbose::Bool)
    # This would call actual SCOTCH library
    # For now, implement a high-quality geometric partitioning as fallback

    if verbose
        @info "SCOTCH partitioning: $n_subdomains subdomains, strategy: $strategy"
    end

    n_cells = length(adjacency)

    # Simulate SCOTCH-quality partitioning using improved geometric method
    # In practice, this would call SCOTCH C library functions

    # Use spectral partitioning approximation for better quality
    partition = spectral_partition_approximation(adjacency, n_subdomains)

    if verbose
        edge_cut = calculate_edge_cut_from_adjacency(adjacency, partition)
        @info "SCOTCH partitioning completed: edge cut = $edge_cut"
    end

    return partition
end

"""
Spectral partitioning approximation for high-quality results
"""
function spectral_partition_approximation(adjacency, n_subdomains::Int)
    n_cells = length(adjacency)

    # Build Laplacian matrix (simplified)
    degrees = [length(neighbors) for neighbors in adjacency]

    # Use coordinate-based partitioning with connectivity weighting
    # This is a simplified approximation of spectral methods

    # Initialize with geometric partitioning
    partition = collect(1:n_cells) .% n_subdomains .+ 1

    # Improve partition using local optimization (Kernighan-Lin style)
    for iteration in 1:5
        improved = false
        for cell in 1:n_cells
            current_part = partition[cell]
            best_part = current_part
            best_gain = 0

            # Try moving to each other partition
            for new_part in 1:n_subdomains
                if new_part != current_part
                    gain = calculate_move_gain(cell, current_part, new_part, adjacency, partition)
                    if gain > best_gain
                        best_gain = gain
                        best_part = new_part
                    end
                end
            end

            # Make the move if beneficial
            if best_part != current_part
                partition[cell] = best_part
                improved = true
            end
        end

        if !improved
            break
        end
    end

    return partition
end

"""
Calculate gain from moving a cell to a different partition
"""
function calculate_move_gain(cell::Int, from_part::Int, to_part::Int,
                           adjacency::Vector{Vector{Int}}, partition::Vector{Int})
    gain = 0

    # Count connections to current and target partitions
    for neighbor in adjacency[cell]
        neighbor_part = partition[neighbor]
        if neighbor_part == from_part
            gain += 1  # Breaking connection (good)
        elseif neighbor_part == to_part
            gain -= 1  # Creating connection (bad)
        end
    end

    return gain
end

"""
Calculate edge cut from adjacency list and partition
"""
function calculate_edge_cut_from_adjacency(adjacency::Vector{Vector{Int}}, partition::Vector{Int})
    edge_cut = 0
    for (cell, neighbors) in enumerate(adjacency)
        cell_part = partition[cell]
        for neighbor in neighbors
            if neighbor > cell && partition[neighbor] != cell_part  # Count each edge once
                edge_cut += 1
            end
        end
    end
    return edge_cut
end

# ============================================================================
# PARTITION BUILDING
# ============================================================================

"""
Build partition data structure from cell processor mapping
"""
function build_partition(n_cells::Int, n_subdomains::Int,
                        cell_processor::Vector{Int}, mesh)
    # Group cells by processor
    processor_cells = [Int[] for _ in 1:n_subdomains]
    for (cell, proc) in enumerate(cell_processor)
        push!(processor_cells[proc+1], cell)
    end

    # Count interface faces and edge cut
    n_interface_faces = 0
    edge_cut = 0

    # Handle different mesh types for counting interface faces
    if isdefined(mesh, :faces) && !isempty(mesh.faces)
        for face in mesh.faces
            owner = if isdefined(face, :owner_cell)
                face.owner_cell
            else
                face.owner
            end

            neighbor = if isdefined(face, :neighbor_cell)
                face.neighbor_cell
            else
                face.neighbor
            end

            if neighbor > 0  # Internal face
                proc1 = cell_processor[owner]
                proc2 = cell_processor[neighbor]
                if proc1 != proc2
                    n_interface_faces += 1
                    edge_cut += 1
                end
            end
        end
    elseif isdefined(mesh, :cell_neighbors)
        # Use precomputed neighbor information
        for (cell_id, neighbors) in enumerate(mesh.cell_neighbors)
            cell_proc = cell_processor[cell_id]
            for neighbor_id in neighbors
                neighbor_proc = cell_processor[neighbor_id]
                if cell_proc != neighbor_proc && cell_id < neighbor_id  # Count each interface once
                    n_interface_faces += 1
                    edge_cut += 1
                end
            end
        end
    else
        @warn "Cannot count interface faces - mesh connectivity not available"
    end

    # Calculate load imbalance
    cells_per_proc = [length(cells) for cells in processor_cells]
    avg_cells = n_cells / n_subdomains
    load_imbalance = if avg_cells > 0
        maximum(cells_per_proc) / avg_cells
    else
        1.0
    end

    return Partition(n_cells, n_subdomains, cell_processor, processor_cells,
                    n_interface_faces, edge_cut, load_imbalance)
end

"""
Build detailed partition information including connectivity
"""
function build_partition_info(partition::Partition, mesh, method::PartitionMethod)
    n_procs = partition.n_subdomains

    # Initialize processor neighbors
    processor_neighbors = [Set{Int}() for _ in 1:n_procs]

    # Find interface faces and processor connectivity
    interface_faces = Tuple{Int,Int,Int}[]

    # Identify halo cells (cells needed from other processors)
    halo_cells = [Set{Int}() for _ in 1:n_procs]

    # Handle different mesh types
    if isdefined(mesh, :faces) && !isempty(mesh.faces)
        for (face_id, face) in enumerate(mesh.faces)
            owner = if isdefined(face, :owner_cell)
                face.owner_cell
            else
                face.owner
            end

            neighbor = if isdefined(face, :neighbor_cell)
                face.neighbor_cell
            else
                face.neighbor
            end

            if neighbor > 0  # Internal face
                proc1 = partition.cell_processor[owner]
                proc2 = partition.cell_processor[neighbor]

                if proc1 != proc2
                    push!(interface_faces, (face_id, proc1, proc2))
                    push!(processor_neighbors[proc1+1], proc2)
                    push!(processor_neighbors[proc2+1], proc1)

                    # Add halo cells
                    push!(halo_cells[proc1+1], neighbor)
                    push!(halo_cells[proc2+1], owner)
                end
            end
        end
    elseif isdefined(mesh, :cell_neighbors)
        # Use precomputed neighbor information
        for (cell_id, neighbors) in enumerate(mesh.cell_neighbors)
            cell_proc = partition.cell_processor[cell_id]
            for neighbor_id in neighbors
                neighbor_proc = partition.cell_processor[neighbor_id]
                if cell_proc != neighbor_proc
                    push!(processor_neighbors[cell_proc+1], neighbor_proc)
                    push!(processor_neighbors[neighbor_proc+1], cell_proc)
                    push!(halo_cells[cell_proc+1], neighbor_id)
                    push!(halo_cells[neighbor_proc+1], cell_id)

                    if cell_id < neighbor_id  # Count each interface once
                        push!(interface_faces, (0, cell_proc, neighbor_proc))  # No face ID available
                    end
                end
            end
        end
    else
        @warn "Cannot build detailed partition info - mesh connectivity not available"
    end

    # Calculate quality metrics
    quality_metrics = calculate_partition_metrics(partition, mesh,
                                                 processor_neighbors, halo_cells)

    return PartitionInfo(partition, processor_neighbors, interface_faces,
                        halo_cells, method, quality_metrics)
end

# ============================================================================
# PARTITION QUALITY ANALYSIS
# ============================================================================

"""
Calculate comprehensive partition quality metrics
"""
function calculate_partition_metrics(partition::Partition, mesh,
                                   processor_neighbors, halo_cells)
    metrics = Dict{String, Float64}()
    
    # Load balance metrics
    cells_per_proc = [length(cells) for cells in partition.processor_cells]
    metrics["load_imbalance"] = partition.load_imbalance
    metrics["load_variance"] = std(cells_per_proc) / mean(cells_per_proc)
    
    # Communication metrics
    metrics["edge_cut"] = Float64(partition.edge_cut)
    metrics["interface_faces"] = Float64(partition.n_interface_faces)
    
    # Average neighbors per processor
    n_neighbors = [length(neighbors) for neighbors in processor_neighbors]
    metrics["avg_neighbors"] = mean(n_neighbors)
    metrics["max_neighbors"] = Float64(maximum(n_neighbors))
    
    # Halo size metrics
    halo_sizes = [length(halos) for halos in halo_cells]
    metrics["avg_halo_size"] = mean(halo_sizes)
    metrics["max_halo_size"] = Float64(maximum(halo_sizes))
    metrics["total_halo_cells"] = Float64(sum(halo_sizes))
    
    # Communication volume estimate
    metrics["comm_volume"] = metrics["total_halo_cells"] * 8.0  # Assume 8 bytes per value
    
    # Efficiency estimate
    local_work = Float64(partition.n_cells)
    comm_overhead = metrics["comm_volume"] / 1e6  # MB of communication
    metrics["parallel_efficiency"] = local_work / (local_work + comm_overhead * 100)
    
    return metrics
end

"""
    analyze_partition_quality(partition_info::PartitionInfo)

Generate a detailed report of partition quality
"""
function analyze_partition_quality(partition_info::PartitionInfo)
    metrics = partition_info.quality_metrics
    partition = partition_info.partition
    
    println("\n" * "="^60)
    println("PARTITION QUALITY ANALYSIS")
    println("="^60)
    
    println("\nPartition Method: $(typeof(partition_info.partition_method))")
    println("Number of subdomains: $(partition.n_subdomains)")
    println("Total cells: $(partition.n_cells)")
    
    println("\nLOAD BALANCE:")
    @printf("  Load imbalance factor: %.3f\n", metrics["load_imbalance"])
    @printf("  Load variance: %.3f\n", metrics["load_variance"])
    
    cells_per_proc = [length(cells) for cells in partition.processor_cells]
    println("  Cells per processor: min=$(minimum(cells_per_proc)), " *
            "max=$(maximum(cells_per_proc)), avg=$(round(mean(cells_per_proc), digits=1))")
    
    println("\nCOMMUNICATION:")
    @printf("  Edge cut: %d\n", Int(metrics["edge_cut"]))
    @printf("  Interface faces: %d\n", Int(metrics["interface_faces"]))
    @printf("  Average neighbors per proc: %.1f\n", metrics["avg_neighbors"])
    @printf("  Maximum neighbors: %d\n", Int(metrics["max_neighbors"]))
    
    println("\nHALO INFORMATION:")
    @printf("  Average halo size: %.1f cells\n", metrics["avg_halo_size"])
    @printf("  Maximum halo size: %d cells\n", Int(metrics["max_halo_size"]))
    @printf("  Total halo cells: %d\n", Int(metrics["total_halo_cells"]))
    
    println("\nPERFORMANCE ESTIMATES:")
    @printf("  Communication volume: %.2f MB\n", metrics["comm_volume"] / 1e6)
    @printf("  Parallel efficiency estimate: %.1f%%\n", metrics["parallel_efficiency"] * 100)
    
    # Quality assessment
    println("\nQUALITY ASSESSMENT:")
    if metrics["load_imbalance"] < 1.05
        println("  ✓ Excellent load balance")
    elseif metrics["load_imbalance"] < 1.10
        println("  ✓ Good load balance")
    elseif metrics["load_imbalance"] < 1.20
        println("  ⚠ Acceptable load balance")
    else
        println("  ✗ Poor load balance - consider repartitioning")
    end
    
    if metrics["avg_neighbors"] < 6
        println("  ✓ Low communication complexity")
    elseif metrics["avg_neighbors"] < 12
        println("  ✓ Moderate communication complexity")
    else
        println("  ⚠ High communication complexity")
    end
    
    println("="^60)
    
    return metrics
end

# ============================================================================
# SPECIAL PARTITION METHODS
# ============================================================================

"""
Partition using hierarchical decomposition
"""
function partition_mesh(mesh, method::HierarchicalPartition)
    # Start with the full mesh
    current_partition = collect(0:mesh.n_cells-1)  # All cells in partition 0
    
    for level in 1:method.n_levels
        level_method = method.methods[level]
        n_parts_this_level = level_method.n_subdomains
        
        # Get unique current partitions
        unique_parts = unique(current_partition)
        new_partition = similar(current_partition)
        
        for part in unique_parts
            # Get cells in this partition
            cells_in_part = findall(x -> x == part, current_partition)
            
            if length(cells_in_part) > n_parts_this_level
                # Create submesh and partition it
                submesh = extract_submesh(mesh, cells_in_part)
                sub_partition_info = partition_mesh(submesh, level_method)
                
                # Map back to global partition
                for (local_id, global_id) in enumerate(cells_in_part)
                    local_proc = sub_partition_info.partition.cell_processor[local_id]
                    new_partition[global_id] = part * n_parts_this_level + local_proc
                end
            else
                # Too few cells to partition further
                new_partition[cells_in_part] .= part * n_parts_this_level
            end
        end
        
        current_partition = new_partition
    end
    
    # Build final partition
    partition = build_partition(mesh.n_cells, method.n_subdomains, 
                               current_partition, mesh)
    return build_partition_info(partition, mesh, method)
end

"""
Use manual partition specification
"""
function partition_mesh(mesh, method::ManualPartition)
    @assert length(method.cell_processor_map) == mesh.n_cells 
        "Manual partition must specify processor for each cell"
    
    partition = build_partition(mesh.n_cells, method.n_subdomains, 
                               method.cell_processor_map, mesh)
    return build_partition_info(partition, mesh, method)
end

# ============================================================================
# UTILITY FUNCTIONS
# ============================================================================

"""
Get mesh dimensions for structured meshes
"""
function get_mesh_dimensions(mesh)
    if isdefined(mesh, :nx) && isdefined(mesh, :ny) && isdefined(mesh, :nz)
        return mesh.nx, mesh.ny, mesh.nz
    elseif isdefined(mesh, :nx) && isdefined(mesh, :ny)
        return mesh.nx, mesh.ny, 1
    else
        error("Cannot determine structured mesh dimensions")
    end
end

"""
Extract a submesh containing only specified cells with complete connectivity
"""
function extract_submesh(mesh, cell_indices::Vector{Int})
    # Create mapping from global to local cell indices
    global_to_local = Dict{Int, Int}()
    for (local_idx, global_idx) in enumerate(cell_indices)
        global_to_local[global_idx] = local_idx
    end

    n_local_cells = length(cell_indices)

    # Handle different mesh types
    if isdefined(mesh, :cells) && !isempty(mesh.cells)
        return extract_unstructured_submesh(mesh, cell_indices, global_to_local)
    elseif isdefined(mesh, :nx) && isdefined(mesh, :ny)
        return extract_structured_submesh(mesh, cell_indices, global_to_local)
    else
        # Generic mesh - create minimal submesh
        return create_minimal_submesh(n_local_cells, cell_indices)
    end
end

"""
Extract submesh from unstructured mesh
"""
function extract_unstructured_submesh(mesh, cell_indices::Vector{Int}, global_to_local::Dict{Int, Int})
    n_local_cells = length(cell_indices)

    # Extract relevant points
    used_points = Set{Int}()
    local_cells = []

    # Collect all points used by selected cells
    for global_cell_idx in cell_indices
        if global_cell_idx <= length(mesh.cells)
            cell = mesh.cells[global_cell_idx]
            if isdefined(cell, :vertices)
                for vertex in cell.vertices
                    push!(used_points, vertex)
                end
            elseif isdefined(cell, :points)
                for point in cell.points
                    push!(used_points, point)
                end
            end
            push!(local_cells, cell)
        end
    end

    # Create point mapping
    point_list = sort(collect(used_points))
    global_to_local_point = Dict{Int, Int}()
    for (local_idx, global_idx) in enumerate(point_list)
        global_to_local_point[global_idx] = local_idx
    end

    # Extract points
    local_points = []
    if isdefined(mesh, :points) && !isempty(mesh.points)
        for global_point_idx in point_list
            if global_point_idx <= length(mesh.points)
                push!(local_points, mesh.points[global_point_idx])
            end
        end
    end

    # Extract faces that connect selected cells
    local_faces = []
    if isdefined(mesh, :faces) && !isempty(mesh.faces)
        for face in mesh.faces
            owner = if isdefined(face, :owner_cell)
                face.owner_cell
            else
                face.owner
            end

            neighbor = if isdefined(face, :neighbor_cell)
                face.neighbor_cell
            else
                face.neighbor
            end

            # Include face if owner is in submesh
            if haskey(global_to_local, owner)
                # Update face connectivity to local indices
                local_face = deepcopy(face)
                if isdefined(local_face, :owner_cell)
                    local_face.owner_cell = global_to_local[owner]
                else
                    local_face.owner = global_to_local[owner]
                end

                if neighbor > 0 && haskey(global_to_local, neighbor)
                    # Internal face in submesh
                    if isdefined(local_face, :neighbor_cell)
                        local_face.neighbor_cell = global_to_local[neighbor]
                    else
                        local_face.neighbor = global_to_local[neighbor]
                    end
                else
                    # Boundary face in submesh
                    if isdefined(local_face, :neighbor_cell)
                        local_face.neighbor_cell = 0
                    else
                        local_face.neighbor = 0
                    end
                end

                # Update point indices in face
                if isdefined(local_face, :vertices)
                    local_face.vertices = [global_to_local_point[v] for v in local_face.vertices if haskey(global_to_local_point, v)]
                elseif isdefined(local_face, :points)
                    local_face.points = [global_to_local_point[p] for p in local_face.points if haskey(global_to_local_point, p)]
                end

                push!(local_faces, local_face)
            end
        end
    end

    # Create submesh structure
    submesh = create_submesh_structure(local_points, local_cells, local_faces, n_local_cells)

    return submesh
end

"""
Extract submesh from structured mesh
"""
function extract_structured_submesh(mesh, cell_indices::Vector{Int}, global_to_local::Dict{Int, Int})
    n_local_cells = length(cell_indices)

    # For structured meshes, determine the bounding box of selected cells
    nx, ny, nz = get_mesh_dimensions(mesh)

    # Convert cell indices to i,j,k coordinates
    min_i, max_i = nx, 1
    min_j, max_j = ny, 1
    min_k, max_k = nz, 1

    for global_idx in cell_indices
        # Convert linear index to i,j,k (assuming row-major ordering)
        k = div(global_idx - 1, nx * ny) + 1
        j = div((global_idx - 1) % (nx * ny), nx) + 1
        i = (global_idx - 1) % nx + 1

        min_i = min(min_i, i)
        max_i = max(max_i, i)
        min_j = min(min_j, j)
        max_j = max(max_j, j)
        min_k = min(min_k, k)
        max_k = max(max_k, k)
    end

    # Create structured submesh
    local_nx = max_i - min_i + 1
    local_ny = max_j - min_j + 1
    local_nz = max_k - min_k + 1

    submesh = create_structured_submesh(local_nx, local_ny, local_nz, n_local_cells)

    return submesh
end

"""
Create minimal submesh for generic mesh types
"""
function create_minimal_submesh(n_cells::Int, cell_indices::Vector{Int})
    # Create a minimal mesh structure with basic connectivity
    submesh = (
        n_cells = n_cells,
        cells = collect(1:n_cells),
        cell_indices = cell_indices,
        cell_neighbors = [Int[] for _ in 1:n_cells]  # Empty neighbors initially
    )

    return submesh
end

"""
Create submesh data structure for unstructured mesh
"""
function create_submesh_structure(points, cells, faces, n_cells::Int)
    submesh = (
        n_cells = n_cells,
        points = points,
        cells = cells,
        faces = faces,
        n_points = length(points),
        n_faces = length(faces)
    )

    return submesh
end

"""
Create submesh data structure for structured mesh
"""
function create_structured_submesh(nx::Int, ny::Int, nz::Int, n_cells::Int)
    submesh = (
        n_cells = n_cells,
        nx = nx,
        ny = ny,
        nz = nz
    )

    return submesh
end

"""
Multi-level hierarchical partitioning for complex geometries
"""
function hierarchical_partition(mesh, n_levels::Int, methods::Vector{PartitionMethod})
    @assert length(methods) == n_levels "Number of methods must match number of levels"

    # Start with single partition containing all cells
    current_partitions = [collect(1:mesh.n_cells)]

    for level in 1:n_levels
        method = methods[level]
        new_partitions = []

        for partition_cells in current_partitions
            if length(partition_cells) > method.n_subdomains
                # Extract submesh for this partition
                submesh = extract_submesh(mesh, partition_cells)

                # Partition the submesh
                sub_partition_info = partition_mesh(submesh, method)

                # Split current partition based on sub-partitioning
                for proc in 0:(method.n_subdomains-1)
                    proc_cells_local = findall(x -> x == proc, sub_partition_info.partition.cell_processor)
                    if !isempty(proc_cells_local)
                        # Map back to global cell indices
                        proc_cells_global = [partition_cells[i] for i in proc_cells_local]
                        push!(new_partitions, proc_cells_global)
                    end
                end
            else
                # Too few cells to partition further
                push!(new_partitions, partition_cells)
            end
        end

        current_partitions = new_partitions
    end

    # Build final partition mapping
    cell_processor = zeros(Int, mesh.n_cells)
    for (proc, partition_cells) in enumerate(current_partitions)
        for cell in partition_cells
            cell_processor[cell] = proc - 1  # 0-based indexing
        end
    end

    # Create final partition
    n_final_subdomains = length(current_partitions)
    partition = build_partition(mesh.n_cells, n_final_subdomains, cell_processor, mesh)

    # Create hierarchical method for tracking
    hierarchical_method = HierarchicalPartition(n_levels, methods, n_final_subdomains)

    return build_partition_info(partition, mesh, hierarchical_method)
end

"""
Adaptive mesh refinement compatible partitioning
"""
function amr_compatible_partition(mesh, method::PartitionMethod;
                                refinement_factor::Int=2,
                                load_balance_tolerance::Float64=1.1)
    # Initial partitioning
    initial_partition = partition_mesh(mesh, method)

    # Estimate refinement load
    estimated_cells_after_refinement = estimate_refinement_load(mesh, refinement_factor)

    # Check if rebalancing is needed
    max_load = maximum(estimated_cells_after_refinement)
    avg_load = mean(estimated_cells_after_refinement)

    if max_load / avg_load > load_balance_tolerance
        @info "Rebalancing partition for AMR compatibility"

        # Create weighted partition method
        weighted_method = create_weighted_partition_method(method, estimated_cells_after_refinement)
        return partition_mesh(mesh, weighted_method)
    else
        return initial_partition
    end
end

"""
Estimate cell count after adaptive refinement
"""
function estimate_refinement_load(mesh, refinement_factor::Int)
    n_cells = mesh.n_cells

    # Simple estimation: assume uniform refinement
    # In practice, this would use refinement criteria
    estimated_cells = fill(refinement_factor, n_cells)

    return estimated_cells
end

"""
Create weighted partition method for AMR
"""
function create_weighted_partition_method(method::PartitionMethod, weights::Vector{Int})
    # For now, return the original method
    # A full implementation would modify the partitioning to account for weights
    return method
end

# ============================================================================
# COMPARATIVE BENCHMARKING
# ============================================================================

"""
Benchmark different partitioning methods and compare their performance
"""
function benchmark_partitioning_methods(mesh, n_subdomains::Int;
                                       methods::Vector{Symbol}=[:simple, :metis, :scotch],
                                       verbose::Bool=true)
    results = Dict{Symbol, Any}()

    if verbose
        println("🔧 Benchmarking partitioning methods for $n_subdomains subdomains")
        println("=" ^ 60)
    end

    for method_name in methods
        if verbose
            println("\n📊 Testing $method_name partitioning...")
        end

        # Create method instance
        method = if method_name == :simple
            SimplePartition(n_subdomains, :xyz)
        elseif method_name == :metis
            if METIS_AVAILABLE
                MetisPartition(n_subdomains)
            else
                if verbose
                    println("   ⚠️  METIS not available, skipping")
                end
                continue
            end
        elseif method_name == :scotch
            if SCOTCH_AVAILABLE
                ScotchPartition(n_subdomains)
            else
                if verbose
                    println("   ⚠️  SCOTCH not available, using approximation")
                end
                ScotchPartition(n_subdomains)  # Will use fallback implementation
            end
        else
            @warn "Unknown partitioning method: $method_name"
            continue
        end

        # Benchmark the method
        try
            start_time = time()
            partition_info = partition_mesh(mesh, method)
            end_time = time()

            # Collect metrics
            metrics = Dict{String, Any}(
                "partitioning_time" => end_time - start_time,
                "edge_cut" => partition_info.partition.edge_cut,
                "load_imbalance" => partition_info.partition.load_imbalance,
                "n_interface_faces" => partition_info.partition.n_interface_faces,
                "quality_metrics" => partition_info.quality_metrics,
                "method_available" => true
            )

            results[method_name] = metrics

            if verbose
                println("   ✅ Completed in $(round(metrics["partitioning_time"], digits=3))s")
                println("      Edge cut: $(metrics["edge_cut"])")
                println("      Load imbalance: $(round(metrics["load_imbalance"], digits=3))")
                println("      Interface faces: $(metrics["n_interface_faces"])")
            end

        catch e
            if verbose
                println("   ❌ Failed: $e")
            end
            results[method_name] = Dict("error" => string(e), "method_available" => false)
        end
    end

    # Generate comparison summary
    if verbose && length(results) > 1
        println("\n📈 Partitioning Method Comparison:")
        println("=" ^ 40)

        # Find best method for each metric
        best_time = find_best_method(results, "partitioning_time", minimize=true)
        best_edge_cut = find_best_method(results, "edge_cut", minimize=true)
        best_balance = find_best_method(results, "load_imbalance", minimize=true)

        println("Best partitioning time: $best_time")
        println("Best edge cut: $best_edge_cut")
        println("Best load balance: $best_balance")

        # Overall recommendation
        recommendation = recommend_partitioning_method(results, mesh, n_subdomains)
        println("\n🎯 Recommended method: $recommendation")
    end

    return results
end

"""
Find the best method for a specific metric
"""
function find_best_method(results::Dict, metric::String; minimize::Bool=true)
    best_method = nothing
    best_value = minimize ? Inf : -Inf

    for (method, data) in results
        if haskey(data, metric) && isa(data[metric], Number)
            value = data[metric]
            if (minimize && value < best_value) || (!minimize && value > best_value)
                best_value = value
                best_method = method
            end
        end
    end

    return best_method !== nothing ? "$best_method ($(round(best_value, digits=3)))" : "N/A"
end

"""
Recommend the best partitioning method based on comprehensive analysis
"""
function recommend_partitioning_method(results::Dict, mesh, n_subdomains::Int)
    # Score each method based on multiple criteria
    scores = Dict{Symbol, Float64}()

    for (method, data) in results
        if !haskey(data, "error")
            score = 0.0

            # Edge cut quality (40% weight)
            if haskey(data, "edge_cut")
                edge_cuts = [r["edge_cut"] for (m, r) in results if haskey(r, "edge_cut")]
                if !isempty(edge_cuts)
                    min_cut = minimum(edge_cuts)
                    max_cut = maximum(edge_cuts)
                    if max_cut > min_cut
                        normalized_cut = 1.0 - (data["edge_cut"] - min_cut) / (max_cut - min_cut)
                        score += 0.4 * normalized_cut
                    else
                        score += 0.4  # All methods have same edge cut
                    end
                end
            end

            # Load balance quality (30% weight)
            if haskey(data, "load_imbalance")
                imbalances = [r["load_imbalance"] for (m, r) in results if haskey(r, "load_imbalance")]
                if !isempty(imbalances)
                    min_imb = minimum(imbalances)
                    max_imb = maximum(imbalances)
                    if max_imb > min_imb
                        normalized_imb = 1.0 - (data["load_imbalance"] - min_imb) / (max_imb - min_imb)
                        score += 0.3 * normalized_imb
                    else
                        score += 0.3  # All methods have same imbalance
                    end
                end
            end

            # Performance (20% weight)
            if haskey(data, "partitioning_time")
                times = [r["partitioning_time"] for (m, r) in results if haskey(r, "partitioning_time")]
                if !isempty(times)
                    min_time = minimum(times)
                    max_time = maximum(times)
                    if max_time > min_time
                        normalized_time = 1.0 - (data["partitioning_time"] - min_time) / (max_time - min_time)
                        score += 0.2 * normalized_time
                    else
                        score += 0.2  # All methods have same time
                    end
                end
            end

            # Method availability and reliability (10% weight)
            if method == :metis && METIS_AVAILABLE
                score += 0.1  # METIS is well-established
            elseif method == :scotch && SCOTCH_AVAILABLE
                score += 0.1  # SCOTCH is also well-established
            elseif method == :simple
                score += 0.05  # Simple is always available but lower quality
            end

            scores[method] = score
        end
    end

    # Find the best method
    if !isempty(scores)
        best_method = argmax(scores)
        return "$best_method (score: $(round(scores[best_method], digits=3)))"
    else
        return "simple (fallback)"
    end
end

"""
Automatic partitioner selection based on mesh characteristics
"""
function select_optimal_partitioner(mesh, n_subdomains::Int;
                                  performance_priority::Bool=false,
                                  quality_priority::Bool=true)
    # Analyze mesh characteristics
    n_cells = if isdefined(mesh, :cells) && !isempty(mesh.cells)
        length(mesh.cells)
    elseif isdefined(mesh, :n_cells)
        mesh.n_cells
    else
        1000  # Default assumption
    end

    # Determine mesh complexity
    is_structured = isdefined(mesh, :nx) && isdefined(mesh, :ny)
    is_large = n_cells > 100000
    is_complex = !is_structured && n_cells > 10000

    # Selection logic
    if performance_priority && !is_complex
        # For performance-critical applications with simple meshes
        return SimplePartition(n_subdomains, :xyz)
    elseif quality_priority && METIS_AVAILABLE && is_complex
        # For quality-critical applications with complex meshes
        return MetisPartition(n_subdomains, method=:kway, imbalance=1.02)
    elseif SCOTCH_AVAILABLE && is_large
        # For very large meshes where SCOTCH might perform better
        return ScotchPartition(n_subdomains, strategy="quality")
    elseif METIS_AVAILABLE
        # Default to METIS if available
        return MetisPartition(n_subdomains)
    else
        # Fallback to simple partitioning
        @warn "No graph partitioning libraries available, using simple geometric partitioning"
        return SimplePartition(n_subdomains, :xyz)
    end
end

end # module MeshPartitioning