"""
MeshPartitioning.jl

Comprehensive mesh partitioning framework supporting multiple decomposition methods
for both structured and unstructured meshes. Provides the foundation for parallel
CFD computations with transparent domain decomposition.

Key Features:
- Multiple partitioning methods (Simple, METIS, SCOTCH, Hierarchical, Manual)
- Support for structured and unstructured meshes
- Load balancing capabilities
- Face weight considerations
- Partition quality metrics
"""

module MeshPartitioning

using LinearAlgebra
using SparseArrays
using Statistics
using Printf

# Try to import MPI, but don't fail if not available
const MPI_AVAILABLE = try
    using MPI
    true
catch
    false
end

# Try to import Metis, but don't fail if not available
const METIS_AVAILABLE = try
    using Metis
    true
catch
    false
end

# Import UnstructuredMesh if available
const UNSTRUCTURED_MESH_AVAILABLE = try
    using ..UnstructuredMesh
    true
catch
    false
end

# Export partition methods and main functions
export PartitionMethod, SimplePartition, MetisPartition, ScotchPartition
export HierarchicalPartition, ManualPartition, partition_mesh
export Partition, PartitionInfo, analyze_partition_quality

# ============================================================================
# PARTITION METHOD TYPES
# ============================================================================

"""
Abstract base type for all partition methods
"""
abstract type PartitionMethod end

"""
Simple geometric partitioning for structured meshes
"""
struct SimplePartition <: PartitionMethod
    n_subdomains::Int
    direction::Symbol  # :x, :y, :z, :xyz
    
    function SimplePartition(n_subdomains::Int, direction::Symbol=:xyz)
        @assert n_subdomains > 0 "Number of subdomains must be positive"
        @assert direction in [:x, :y, :z, :xy, :xz, :yz, :xyz] "Invalid direction"
        new(n_subdomains, direction)
    end
end

"""
METIS graph-based partitioning for unstructured meshes
"""
struct MetisPartition <: PartitionMethod
    n_subdomains::Int
    method::Symbol         # :kway, :recursive
    imbalance::Float64     # Load imbalance tolerance (e.g., 1.05 = 5% imbalance)
    face_weights::Bool     # Weight by face area
    ncuts::Int            # Number of cutting trials
    
    function MetisPartition(n_subdomains::Int; 
                           method::Symbol=:kway,
                           imbalance::Float64=1.05,
                           face_weights::Bool=false,
                           ncuts::Int=5)
        @assert n_subdomains > 0 "Number of subdomains must be positive"
        @assert method in [:kway, :recursive] "Method must be :kway or :recursive"
        @assert imbalance >= 1.0 "Imbalance must be >= 1.0"
        new(n_subdomains, method, imbalance, face_weights, ncuts)
    end
end

"""
SCOTCH graph-based partitioning (placeholder for future implementation)
"""
struct ScotchPartition <: PartitionMethod
    n_subdomains::Int
    strategy::String
    
    function ScotchPartition(n_subdomains::Int; strategy::String="quality")
        @assert n_subdomains > 0 "Number of subdomains must be positive"
        new(n_subdomains, strategy)
    end
end

"""
Hierarchical partitioning for multi-level decomposition
"""
struct HierarchicalPartition <: PartitionMethod
    n_subdomains::Int
    n_levels::Int
    methods::Vector{PartitionMethod}  # Method for each level
    
    function HierarchicalPartition(n_subdomains::Int, n_levels::Int=2)
        @assert n_subdomains > 0 "Number of subdomains must be positive"
        @assert n_levels > 0 "Number of levels must be positive"
        
        # Default: use METIS at each level
        methods = [MetisPartition(round(Int, n_subdomains^(1.0/n_levels))) 
                  for _ in 1:n_levels]
        new(n_subdomains, n_levels, methods)
    end
end

"""
Manual partitioning with user-specified cell assignments
"""
struct ManualPartition <: PartitionMethod
    cell_processor_map::Vector{Int}
    n_subdomains::Int
    
    function ManualPartition(cell_processor_map::Vector{Int})
        n_subdomains = maximum(cell_processor_map) + 1  # 0-based indexing
        @assert minimum(cell_processor_map) >= 0 "Processor IDs must be non-negative"
        new(cell_processor_map, n_subdomains)
    end
end

# ============================================================================
# PARTITION DATA STRUCTURES
# ============================================================================

"""
Partition information containing cell-to-processor mapping and metadata
"""
struct Partition
    n_cells::Int
    n_subdomains::Int
    cell_processor::Vector{Int}      # Cell to processor mapping (0-based)
    processor_cells::Vector{Vector{Int}}  # Cells in each processor
    n_interface_faces::Int           # Number of faces between processors
    edge_cut::Int                    # Total edge cut (communication volume)
    load_imbalance::Float64          # Max/average load ratio
end

"""
Detailed partition information including neighbor connectivity
"""
struct PartitionInfo
    partition::Partition
    processor_neighbors::Vector{Set{Int}}  # Neighbor processors for each proc
    interface_faces::Vector{Tuple{Int,Int,Int}}  # (face_id, proc1, proc2)
    halo_cells::Vector{Set{Int}}     # Halo cells needed by each processor
    partition_method::PartitionMethod
    quality_metrics::Dict{String, Float64}
end

# ============================================================================
# MAIN PARTITIONING FUNCTION
# ============================================================================

"""
    partition_mesh(mesh, method::PartitionMethod) -> PartitionInfo

Partition a mesh using the specified method. Automatically handles both
structured and unstructured meshes.
"""
function partition_mesh(mesh, method::PartitionMethod)
    # Check if mesh is structured or unstructured
    if isdefined(mesh, :nx) && isdefined(mesh, :ny)  # Structured mesh indicators
        return partition_structured(mesh, method)
    else
        return partition_unstructured(mesh, method)
    end
end

# ============================================================================
# STRUCTURED MESH PARTITIONING
# ============================================================================

"""
Partition an unstructured mesh using simple geometric decomposition
"""
function partition_unstructured(mesh, method::SimplePartition)
    # Determine number of cells
    n_cells = if isdefined(mesh, :cells) && !isempty(mesh.cells)
        length(mesh.cells)
    elseif isdefined(mesh, :n_cells)
        mesh.n_cells
    else
        error("Cannot determine number of cells in mesh")
    end

    # Get cell centers for geometric partitioning
    cell_centers = if isdefined(mesh, :cells) && !isempty(mesh.cells)
        [cell.center for cell in mesh.cells]
    else
        error("Cannot access cell centers for geometric partitioning")
    end

    # Extract coordinates based on direction
    coords = if method.direction == :x
        [center.x for center in cell_centers]
    elseif method.direction == :y
        [center.y for center in cell_centers]
    elseif method.direction == :z
        [center.z for center in cell_centers]
    else
        error("Unsupported decomposition direction: $(method.direction)")
    end

    # Sort cells by coordinate
    sorted_indices = sortperm(coords)

    # Assign cells to processors
    cell_processor = zeros(Int, n_cells)
    cells_per_proc = ceil(Int, n_cells / method.n_subdomains)

    for (idx, cell_id) in enumerate(sorted_indices)
        proc_id = min(div(idx - 1, cells_per_proc), method.n_subdomains - 1)
        cell_processor[cell_id] = proc_id
    end

    # Build partition data structure
    partition = build_partition(n_cells, method.n_subdomains, cell_processor, mesh)

    # Build partition info with connectivity
    return build_partition_info(partition, mesh, method)
end

"""
Partition a structured mesh using geometric decomposition
"""
function partition_structured(mesh, method::SimplePartition)
    nx, ny, nz = get_mesh_dimensions(mesh)
    n_cells = nx * ny * nz
    
    # Initialize cell processor mapping
    cell_processor = zeros(Int, n_cells)
    
    if method.direction == :x
        # 1D decomposition along X
        cells_per_proc = ceil(Int, nx / method.n_subdomains)
        for k in 1:nz, j in 1:ny, i in 1:nx
            cell_id = (k-1)*nx*ny + (j-1)*nx + i
            proc_id = min(div(i-1, cells_per_proc), method.n_subdomains-1)
            cell_processor[cell_id] = proc_id
        end
        
    elseif method.direction == :y
        # 1D decomposition along Y
        cells_per_proc = ceil(Int, ny / method.n_subdomains)
        for k in 1:nz, j in 1:ny, i in 1:nx
            cell_id = (k-1)*nx*ny + (j-1)*nx + i
            proc_id = min(div(j-1, cells_per_proc), method.n_subdomains-1)
            cell_processor[cell_id] = proc_id
        end
        
    elseif method.direction == :z
        # 1D decomposition along Z
        cells_per_proc = ceil(Int, nz / method.n_subdomains)
        for k in 1:nz, j in 1:ny, i in 1:nx
            cell_id = (k-1)*nx*ny + (j-1)*nx + i
            proc_id = min(div(k-1, cells_per_proc), method.n_subdomains-1)
            cell_processor[cell_id] = proc_id
        end
        
    elseif method.direction == :xyz
        # 3D decomposition - try to balance in all directions
        px, py, pz = factorize_3d(method.n_subdomains, nx, ny, nz)
        
        for k in 1:nz, j in 1:ny, i in 1:nx
            cell_id = (k-1)*nx*ny + (j-1)*nx + i
            pi = min(div((i-1)*px, nx), px-1)
            pj = min(div((j-1)*py, ny), py-1)
            pk = min(div((k-1)*pz, nz), pz-1)
            proc_id = pk*px*py + pj*px + pi
            cell_processor[cell_id] = proc_id
        end
    else
        error("Unsupported decomposition direction: $(method.direction)")
    end
    
    # Build partition data structure
    partition = build_partition(n_cells, method.n_subdomains, cell_processor, mesh)
    
    # Build partition info with connectivity
    return build_partition_info(partition, mesh, method)
end

"""
Find optimal 3D processor grid dimensions
"""
function factorize_3d(n_procs::Int, nx::Int, ny::Int, nz::Int)
    best_px, best_py, best_pz = 1, 1, n_procs
    best_score = Inf
    
    for px in 1:n_procs
        if n_procs % px != 0
            continue
        end
        
        for py in 1:div(n_procs, px)
            if n_procs % (px * py) != 0
                continue
            end
            
            pz = div(n_procs, px * py)
            
            # Score based on aspect ratio similarity
            mesh_aspect = [nx, ny, nz] ./ minimum([nx, ny, nz])
            proc_aspect = [px, py, pz] ./ minimum([px, py, pz])
            score = norm(mesh_aspect .- proc_aspect)
            
            if score < best_score
                best_score = score
                best_px, best_py, best_pz = px, py, pz
            end
        end
    end
    
    return best_px, best_py, best_pz
end

# ============================================================================
# UNSTRUCTURED MESH PARTITIONING
# ============================================================================

"""
Partition an unstructured mesh using graph-based methods
"""
function partition_unstructured(mesh, method::MetisPartition)
    # Check if METIS is available
    if !METIS_AVAILABLE
        @warn "METIS not available. Falling back to simple geometric partitioning."
        simple_method = SimplePartition(method.n_subdomains, :x)
        return partition_unstructured(mesh, simple_method)
    end

    # Determine number of cells based on mesh type
    n_cells = if isdefined(mesh, :cells) && !isempty(mesh.cells)
        length(mesh.cells)
    elseif isdefined(mesh, :n_cells)
        mesh.n_cells
    else
        error("Cannot determine number of cells in mesh")
    end

    # Validate input
    if n_cells < method.n_subdomains
        @warn "More subdomains ($method.n_subdomains) than cells ($n_cells). Using simple partitioning."
        simple_method = SimplePartition(method.n_subdomains, :x)
        return partition_unstructured(mesh, simple_method)
    end

    try
        # Build mesh dual graph
        graph = build_mesh_graph(mesh, method.face_weights)

        # Set METIS options
        options = Metis.options()
        options[Metis.OPTION_NCUTS] = method.ncuts
        options[Metis.OPTION_UFACTOR] = round(Int, (method.imbalance - 1.0) * 1000)

        # Call METIS partitioner
        objval, cell_processor = if method.method == :kway
            Metis.partition(graph, method.n_subdomains; alg=:KWAY, options=options)
        else
            Metis.partition(graph, method.n_subdomains; alg=:RECURSIVE, options=options)
        end

        # Convert to 0-based indexing for consistency
        cell_processor .-= 1

        # Validate partition result
        if length(cell_processor) != n_cells
            error("METIS returned partition of wrong size: expected $n_cells, got $(length(cell_processor))")
        end

        # Build partition data structure
        partition = build_partition(n_cells, method.n_subdomains, cell_processor, mesh)

        # Build partition info with connectivity
        return build_partition_info(partition, mesh, method)

    catch e
        @warn "METIS partitioning failed: $e. Falling back to simple partitioning."
        # Fallback to simple geometric partitioning
        simple_method = SimplePartition(method.n_subdomains, :x)
        return partition_unstructured(mesh, simple_method)
    end
end

"""
Build mesh dual graph for partitioning
"""
function build_mesh_graph(mesh, use_face_weights::Bool)
    # Determine number of cells based on mesh type
    n_cells = if isdefined(mesh, :cells) && !isempty(mesh.cells)
        length(mesh.cells)
    elseif isdefined(mesh, :n_cells)
        mesh.n_cells
    else
        error("Cannot determine number of cells in mesh")
    end

    # Build adjacency lists using CSR format
    xadj = zeros(Int32, n_cells + 1)
    adjncy = Int32[]
    adjwgt = Int32[]  # Edge weights

    # Count neighbors for each cell (first pass)
    neighbor_counts = zeros(Int, n_cells)

    # Handle different mesh types
    if isdefined(mesh, :faces) && !isempty(mesh.faces)
        # UnstructuredMesh type
        for face in mesh.faces
            if isdefined(face, :neighbor_cell) && face.neighbor_cell > 0
                # Internal face - both cells are neighbors
                neighbor_counts[face.owner_cell] += 1
                neighbor_counts[face.neighbor_cell] += 1
            elseif isdefined(face, :neighbor) && face.neighbor > 0
                # Alternative field name
                neighbor_counts[face.owner] += 1
                neighbor_counts[face.neighbor] += 1
            end
        end
    elseif isdefined(mesh, :cell_neighbors)
        # Use precomputed neighbor information
        for (cell_id, neighbors) in enumerate(mesh.cell_neighbors)
            neighbor_counts[cell_id] = length(neighbors)
        end
    else
        error("Cannot extract connectivity information from mesh")
    end

    # Build CSR index array
    xadj[1] = 0
    for i in 1:n_cells
        xadj[i+1] = xadj[i] + neighbor_counts[i]
    end

    # Reserve space for adjacency and weights
    total_edges = xadj[end]
    resize!(adjncy, total_edges)
    if use_face_weights
        resize!(adjwgt, total_edges)
    end

    # Fill adjacency lists (second pass)
    current_pos = copy(xadj[1:end-1])  # Current position for each cell

    if isdefined(mesh, :faces) && !isempty(mesh.faces)
        for face in mesh.faces
            owner = if isdefined(face, :owner_cell)
                face.owner_cell
            else
                face.owner
            end

            neighbor = if isdefined(face, :neighbor_cell)
                face.neighbor_cell
            else
                face.neighbor
            end

            if neighbor > 0  # Internal face
                # Add neighbor to owner's adjacency list
                pos = current_pos[owner] + 1
                adjncy[pos] = neighbor - 1  # Convert to 0-based indexing
                current_pos[owner] += 1

                # Add owner to neighbor's adjacency list
                pos = current_pos[neighbor] + 1
                adjncy[pos] = owner - 1  # Convert to 0-based indexing
                current_pos[neighbor] += 1

                # Add weights if requested
                if use_face_weights
                    face_area = if isdefined(face, :area_vector)
                        norm(face.area_vector)
                    elseif isdefined(face, :area)
                        face.area
                    else
                        1.0  # Default weight
                    end

                    weight = max(1, round(Int32, face_area * 1000))  # Scale and ensure positive
                    adjwgt[current_pos[owner]] = weight
                    adjwgt[current_pos[neighbor]] = weight
                end
            end
        end
    elseif isdefined(mesh, :cell_neighbors)
        # Use precomputed neighbor information
        for (cell_id, neighbors) in enumerate(mesh.cell_neighbors)
            for neighbor_id in neighbors
                pos = current_pos[cell_id] + 1
                adjncy[pos] = neighbor_id - 1  # Convert to 0-based
                current_pos[cell_id] += 1

                if use_face_weights
                    adjwgt[pos] = 1  # Default weight
                end
            end
        end
    end

    # Create METIS graph
    try
        if use_face_weights && !isempty(adjwgt)
            return Metis.graph(xadj, adjncy, adjwgt=adjwgt)
        else
            return Metis.graph(xadj, adjncy)
        end
    catch e
        @error "Failed to create METIS graph: $e"
        @error "Graph stats: n_cells=$n_cells, total_edges=$total_edges"
        @error "xadj length: $(length(xadj)), adjncy length: $(length(adjncy))"
        rethrow(e)
    end
end

# ============================================================================
# PARTITION BUILDING
# ============================================================================

"""
Build partition data structure from cell processor mapping
"""
function build_partition(n_cells::Int, n_subdomains::Int,
                        cell_processor::Vector{Int}, mesh)
    # Group cells by processor
    processor_cells = [Int[] for _ in 1:n_subdomains]
    for (cell, proc) in enumerate(cell_processor)
        push!(processor_cells[proc+1], cell)
    end

    # Count interface faces and edge cut
    n_interface_faces = 0
    edge_cut = 0

    # Handle different mesh types for counting interface faces
    if isdefined(mesh, :faces) && !isempty(mesh.faces)
        for face in mesh.faces
            owner = if isdefined(face, :owner_cell)
                face.owner_cell
            else
                face.owner
            end

            neighbor = if isdefined(face, :neighbor_cell)
                face.neighbor_cell
            else
                face.neighbor
            end

            if neighbor > 0  # Internal face
                proc1 = cell_processor[owner]
                proc2 = cell_processor[neighbor]
                if proc1 != proc2
                    n_interface_faces += 1
                    edge_cut += 1
                end
            end
        end
    elseif isdefined(mesh, :cell_neighbors)
        # Use precomputed neighbor information
        for (cell_id, neighbors) in enumerate(mesh.cell_neighbors)
            cell_proc = cell_processor[cell_id]
            for neighbor_id in neighbors
                neighbor_proc = cell_processor[neighbor_id]
                if cell_proc != neighbor_proc && cell_id < neighbor_id  # Count each interface once
                    n_interface_faces += 1
                    edge_cut += 1
                end
            end
        end
    else
        @warn "Cannot count interface faces - mesh connectivity not available"
    end

    # Calculate load imbalance
    cells_per_proc = [length(cells) for cells in processor_cells]
    avg_cells = n_cells / n_subdomains
    load_imbalance = if avg_cells > 0
        maximum(cells_per_proc) / avg_cells
    else
        1.0
    end

    return Partition(n_cells, n_subdomains, cell_processor, processor_cells,
                    n_interface_faces, edge_cut, load_imbalance)
end

"""
Build detailed partition information including connectivity
"""
function build_partition_info(partition::Partition, mesh, method::PartitionMethod)
    n_procs = partition.n_subdomains

    # Initialize processor neighbors
    processor_neighbors = [Set{Int}() for _ in 1:n_procs]

    # Find interface faces and processor connectivity
    interface_faces = Tuple{Int,Int,Int}[]

    # Identify halo cells (cells needed from other processors)
    halo_cells = [Set{Int}() for _ in 1:n_procs]

    # Handle different mesh types
    if isdefined(mesh, :faces) && !isempty(mesh.faces)
        for (face_id, face) in enumerate(mesh.faces)
            owner = if isdefined(face, :owner_cell)
                face.owner_cell
            else
                face.owner
            end

            neighbor = if isdefined(face, :neighbor_cell)
                face.neighbor_cell
            else
                face.neighbor
            end

            if neighbor > 0  # Internal face
                proc1 = partition.cell_processor[owner]
                proc2 = partition.cell_processor[neighbor]

                if proc1 != proc2
                    push!(interface_faces, (face_id, proc1, proc2))
                    push!(processor_neighbors[proc1+1], proc2)
                    push!(processor_neighbors[proc2+1], proc1)

                    # Add halo cells
                    push!(halo_cells[proc1+1], neighbor)
                    push!(halo_cells[proc2+1], owner)
                end
            end
        end
    elseif isdefined(mesh, :cell_neighbors)
        # Use precomputed neighbor information
        for (cell_id, neighbors) in enumerate(mesh.cell_neighbors)
            cell_proc = partition.cell_processor[cell_id]
            for neighbor_id in neighbors
                neighbor_proc = partition.cell_processor[neighbor_id]
                if cell_proc != neighbor_proc
                    push!(processor_neighbors[cell_proc+1], neighbor_proc)
                    push!(processor_neighbors[neighbor_proc+1], cell_proc)
                    push!(halo_cells[cell_proc+1], neighbor_id)
                    push!(halo_cells[neighbor_proc+1], cell_id)

                    if cell_id < neighbor_id  # Count each interface once
                        push!(interface_faces, (0, cell_proc, neighbor_proc))  # No face ID available
                    end
                end
            end
        end
    else
        @warn "Cannot build detailed partition info - mesh connectivity not available"
    end

    # Calculate quality metrics
    quality_metrics = calculate_partition_metrics(partition, mesh,
                                                 processor_neighbors, halo_cells)

    return PartitionInfo(partition, processor_neighbors, interface_faces,
                        halo_cells, method, quality_metrics)
end

# ============================================================================
# PARTITION QUALITY ANALYSIS
# ============================================================================

"""
Calculate comprehensive partition quality metrics
"""
function calculate_partition_metrics(partition::Partition, mesh,
                                   processor_neighbors, halo_cells)
    metrics = Dict{String, Float64}()
    
    # Load balance metrics
    cells_per_proc = [length(cells) for cells in partition.processor_cells]
    metrics["load_imbalance"] = partition.load_imbalance
    metrics["load_variance"] = std(cells_per_proc) / mean(cells_per_proc)
    
    # Communication metrics
    metrics["edge_cut"] = Float64(partition.edge_cut)
    metrics["interface_faces"] = Float64(partition.n_interface_faces)
    
    # Average neighbors per processor
    n_neighbors = [length(neighbors) for neighbors in processor_neighbors]
    metrics["avg_neighbors"] = mean(n_neighbors)
    metrics["max_neighbors"] = Float64(maximum(n_neighbors))
    
    # Halo size metrics
    halo_sizes = [length(halos) for halos in halo_cells]
    metrics["avg_halo_size"] = mean(halo_sizes)
    metrics["max_halo_size"] = Float64(maximum(halo_sizes))
    metrics["total_halo_cells"] = Float64(sum(halo_sizes))
    
    # Communication volume estimate
    metrics["comm_volume"] = metrics["total_halo_cells"] * 8.0  # Assume 8 bytes per value
    
    # Efficiency estimate
    local_work = Float64(partition.n_cells)
    comm_overhead = metrics["comm_volume"] / 1e6  # MB of communication
    metrics["parallel_efficiency"] = local_work / (local_work + comm_overhead * 100)
    
    return metrics
end

"""
    analyze_partition_quality(partition_info::PartitionInfo)

Generate a detailed report of partition quality
"""
function analyze_partition_quality(partition_info::PartitionInfo)
    metrics = partition_info.quality_metrics
    partition = partition_info.partition
    
    println("\n" * "="^60)
    println("PARTITION QUALITY ANALYSIS")
    println("="^60)
    
    println("\nPartition Method: $(typeof(partition_info.partition_method))")
    println("Number of subdomains: $(partition.n_subdomains)")
    println("Total cells: $(partition.n_cells)")
    
    println("\nLOAD BALANCE:")
    @printf("  Load imbalance factor: %.3f\n", metrics["load_imbalance"])
    @printf("  Load variance: %.3f\n", metrics["load_variance"])
    
    cells_per_proc = [length(cells) for cells in partition.processor_cells]
    println("  Cells per processor: min=$(minimum(cells_per_proc)), " *
            "max=$(maximum(cells_per_proc)), avg=$(round(mean(cells_per_proc), digits=1))")
    
    println("\nCOMMUNICATION:")
    @printf("  Edge cut: %d\n", Int(metrics["edge_cut"]))
    @printf("  Interface faces: %d\n", Int(metrics["interface_faces"]))
    @printf("  Average neighbors per proc: %.1f\n", metrics["avg_neighbors"])
    @printf("  Maximum neighbors: %d\n", Int(metrics["max_neighbors"]))
    
    println("\nHALO INFORMATION:")
    @printf("  Average halo size: %.1f cells\n", metrics["avg_halo_size"])
    @printf("  Maximum halo size: %d cells\n", Int(metrics["max_halo_size"]))
    @printf("  Total halo cells: %d\n", Int(metrics["total_halo_cells"]))
    
    println("\nPERFORMANCE ESTIMATES:")
    @printf("  Communication volume: %.2f MB\n", metrics["comm_volume"] / 1e6)
    @printf("  Parallel efficiency estimate: %.1f%%\n", metrics["parallel_efficiency"] * 100)
    
    # Quality assessment
    println("\nQUALITY ASSESSMENT:")
    if metrics["load_imbalance"] < 1.05
        println("  ✓ Excellent load balance")
    elseif metrics["load_imbalance"] < 1.10
        println("  ✓ Good load balance")
    elseif metrics["load_imbalance"] < 1.20
        println("  ⚠ Acceptable load balance")
    else
        println("  ✗ Poor load balance - consider repartitioning")
    end
    
    if metrics["avg_neighbors"] < 6
        println("  ✓ Low communication complexity")
    elseif metrics["avg_neighbors"] < 12
        println("  ✓ Moderate communication complexity")
    else
        println("  ⚠ High communication complexity")
    end
    
    println("="^60)
    
    return metrics
end

# ============================================================================
# SPECIAL PARTITION METHODS
# ============================================================================

"""
Partition using hierarchical decomposition
"""
function partition_mesh(mesh, method::HierarchicalPartition)
    # Start with the full mesh
    current_partition = collect(0:mesh.n_cells-1)  # All cells in partition 0
    
    for level in 1:method.n_levels
        level_method = method.methods[level]
        n_parts_this_level = level_method.n_subdomains
        
        # Get unique current partitions
        unique_parts = unique(current_partition)
        new_partition = similar(current_partition)
        
        for part in unique_parts
            # Get cells in this partition
            cells_in_part = findall(x -> x == part, current_partition)
            
            if length(cells_in_part) > n_parts_this_level
                # Create submesh and partition it
                submesh = extract_submesh(mesh, cells_in_part)
                sub_partition_info = partition_mesh(submesh, level_method)
                
                # Map back to global partition
                for (local_id, global_id) in enumerate(cells_in_part)
                    local_proc = sub_partition_info.partition.cell_processor[local_id]
                    new_partition[global_id] = part * n_parts_this_level + local_proc
                end
            else
                # Too few cells to partition further
                new_partition[cells_in_part] .= part * n_parts_this_level
            end
        end
        
        current_partition = new_partition
    end
    
    # Build final partition
    partition = build_partition(mesh.n_cells, method.n_subdomains, 
                               current_partition, mesh)
    return build_partition_info(partition, mesh, method)
end

"""
Use manual partition specification
"""
function partition_mesh(mesh, method::ManualPartition)
    @assert length(method.cell_processor_map) == mesh.n_cells 
        "Manual partition must specify processor for each cell"
    
    partition = build_partition(mesh.n_cells, method.n_subdomains, 
                               method.cell_processor_map, mesh)
    return build_partition_info(partition, mesh, method)
end

# ============================================================================
# UTILITY FUNCTIONS
# ============================================================================

"""
Get mesh dimensions for structured meshes
"""
function get_mesh_dimensions(mesh)
    if isdefined(mesh, :nx) && isdefined(mesh, :ny) && isdefined(mesh, :nz)
        return mesh.nx, mesh.ny, mesh.nz
    elseif isdefined(mesh, :nx) && isdefined(mesh, :ny)
        return mesh.nx, mesh.ny, 1
    else
        error("Cannot determine structured mesh dimensions")
    end
end

"""
Extract a submesh containing only specified cells
"""
function extract_submesh(mesh, cell_indices)
    # This is a placeholder - actual implementation would extract
    # the relevant portion of the mesh
    error("Submesh extraction not yet implemented")
end

end # module MeshPartitioning