"""
RedistributePar.jl

Dynamic repartitioning utility for JuliaFOAM that redistributes a running
parallel simulation to a different number of processors or with a different
decomposition method. Similar to OpenFOAM's redistributePar but with enhanced
functionality for live migration and load balancing.

Key Features:
- Change number of processors during runtime
- Switch decomposition methods
- Load balance optimization
- Minimal downtime during redistribution
- Support for checkpoint/restart
- Handles all fields and time directories
"""

module RedistributePar

using LinearAlgebra
using Printf
using MPI
using Statistics
using JSON

# Import required modules
include("MeshPartitioning.jl")
using .MeshPartitioning

include("DecomposePar.jl")
using .DecomposePar

include("ReconstructPar.jl")
using .ReconstructPar

export RedistributeConfig, redistribute_par, redistribute_par_live
export analyze_load_imbalance, suggest_redistribution

# ============================================================================
# CONFIGURATION STRUCTURES
# ============================================================================

"""
Configuration for redistribution
"""
struct RedistributeConfig
    old_n_procs::Int                # Current number of processors
    new_n_procs::Int                # Target number of processors
    new_method::PartitionMethod     # New decomposition method
    time_dirs::Vector{String}       # Which times to redistribute (empty = all)
    fields::Vector{String}          # Which fields (empty = all)
    reconstruct_first::Bool         # Reconstruct before repartitioning
    parallel_transfer::Bool         # Use parallel data transfer
    checkpoint::Bool                # Create checkpoint before redistribution
    load_balance_threshold::Float64 # Trigger redistribution if imbalance exceeds
    minimize_migration::Bool        # Try to minimize data movement
    verbose::Bool                   # Verbose output
    
    function RedistributeConfig(;
        old_n_procs::Int,
        new_n_procs::Int,
        new_method::PartitionMethod,
        time_dirs::Vector{String}=String[],
        fields::Vector{String}=String[],
        reconstruct_first::Bool=false,
        parallel_transfer::Bool=true,
        checkpoint::Bool=true,
        load_balance_threshold::Float64=0.1,
        minimize_migration::Bool=true,
        verbose::Bool=true
    )
        new(old_n_procs, new_n_procs, new_method, time_dirs, fields,
            reconstruct_first, parallel_transfer, checkpoint,
            load_balance_threshold, minimize_migration, verbose)
    end
end

"""
Redistribution statistics
"""
struct RedistributionStats
    cells_migrated::Int
    data_transferred::Float64  # MB
    load_imbalance_before::Float64
    load_imbalance_after::Float64
    redistribution_time::Float64
    downtime::Float64  # Time simulation was paused
end

"""
Load imbalance information
"""
struct LoadImbalanceInfo
    imbalance_factor::Float64
    processor_loads::Vector{Float64}
    communication_costs::Vector{Float64}
    suggested_n_procs::Int
    requires_redistribution::Bool
end

# ============================================================================
# MAIN REDISTRIBUTION FUNCTION
# ============================================================================

"""
    redistribute_par(case_dir::String, config::RedistributeConfig)

Redistribute a decomposed case to a new processor count or decomposition method.
This version requires stopping the simulation.
"""
function redistribute_par(case_dir::String, config::RedistributeConfig)
    start_time = time()
    
    if config.verbose
        println("\n" * "="^60)
        println("JULIAFOAM REDISTRIBUTION")
        println("="^60)
        println("Case directory: $case_dir")
        println("Processors: $(config.old_n_procs) → $(config.new_n_procs)")
        println("Method: $(typeof(config.new_method))")
    end
    
    # 1. Create checkpoint if requested
    if config.checkpoint
        if config.verbose
            println("\n1. Creating checkpoint...")
        end
        create_checkpoint(case_dir, config.old_n_procs)
    end
    
    # 2. Analyze current distribution
    if config.verbose
        println("\n2. Analyzing current distribution...")
    end
    
    current_imbalance = analyze_current_distribution(case_dir, config.old_n_procs)
    
    if config.verbose
        @printf("   Current load imbalance: %.1f%%\n", (current_imbalance - 1.0) * 100)
    end
    
    # 3. Determine redistribution strategy
    strategy = determine_redistribution_strategy(
        case_dir, config, current_imbalance
    )
    
    if config.verbose
        println("\n3. Redistribution strategy: $(strategy)")
    end
    
    # 4. Perform redistribution based on strategy
    if strategy == :reconstruct_decompose
        # Full reconstruction then re-decomposition
        stats = redistribute_via_reconstruction(case_dir, config)
        
    elseif strategy == :direct_remap
        # Direct processor-to-processor remapping
        stats = redistribute_via_remapping(case_dir, config)
        
    elseif strategy == :incremental
        # Incremental redistribution (minimize data movement)
        stats = redistribute_incrementally(case_dir, config)
        
    else
        error("Unknown redistribution strategy: $strategy")
    end
    
    # 5. Verify redistribution
    if config.verbose
        println("\n5. Verifying redistribution...")
    end
    
    verification_passed = verify_redistribution(case_dir, config.new_n_procs)
    
    if config.verbose
        println("   Verification: $(verification_passed ? "PASSED" : "FAILED")")
    end
    
    # 6. Clean up old processor directories
    if verification_passed && config.old_n_procs != config.new_n_procs
        if config.verbose
            println("\n6. Cleaning up old processor directories...")
        end
        cleanup_old_processors(case_dir, config)
    end
    
    # Print summary
    if config.verbose
        print_redistribution_summary(stats)
    end
    
    return stats
end

# ============================================================================
# REDISTRIBUTION STRATEGIES
# ============================================================================

"""
Redistribute via full reconstruction and re-decomposition
"""
function redistribute_via_reconstruction(case_dir::String, config::RedistributeConfig)
    downtime_start = time()
    
    # 1. Reconstruct all fields
    if config.verbose
        println("   Reconstructing fields...")
    end
    
    recon_config = ReconstructPar.ReconstructConfig(
        time_dirs=config.time_dirs,
        fields=config.fields,
        remove_processor_dirs=false,
        verbose=false
    )
    
    ReconstructPar.reconstruct_par(case_dir, recon_config)
    
    # 2. Re-decompose with new settings
    if config.verbose
        println("   Re-decomposing with new settings...")
    end
    
    decomp_config = DecomposePar.DecomposeConfig(
        method=config.new_method,
        time_dirs=config.time_dirs,
        fields=config.fields,
        force=true,
        verbose=false
    )
    
    decomp_info = DecomposePar.decompose_par(case_dir, decomp_config)
    
    downtime = time() - downtime_start
    
    # Calculate statistics
    new_partition_info = decomp_info.partition_info
    cells_migrated = calculate_cells_migrated_full(
        config.old_n_procs, config.new_n_procs, new_partition_info
    )
    
    return RedistributionStats(
        cells_migrated,
        estimate_data_transferred(cells_migrated, config.fields),
        1.0,  # Old imbalance not tracked in this mode
        new_partition_info.quality_metrics["load_imbalance"],
        time() - downtime_start,
        downtime
    )
end

"""
Redistribute via direct processor-to-processor remapping
"""
function redistribute_via_remapping(case_dir::String, config::RedistributeConfig)
    downtime_start = time()
    
    # 1. Read current decomposition
    old_decomp_info = DecomposePar.read_decomposition_info(case_dir)
    old_partition = old_decomp_info.partition_info.partition
    
    # 2. Create new partitioning
    mesh = read_mesh(joinpath(case_dir, "constant", "polyMesh"))
    new_partition_info = MeshPartitioning.partition_mesh(mesh, config.new_method)
    new_partition = new_partition_info.partition
    
    # 3. Create remapping table
    if config.verbose
        println("   Creating remapping table...")
    end
    
    remap_table = create_remap_table(old_partition, new_partition, config)
    
    # 4. Transfer data between processors
    if config.parallel_transfer
        cells_migrated = parallel_data_transfer(case_dir, remap_table, config)
    else
        cells_migrated = sequential_data_transfer(case_dir, remap_table, config)
    end
    
    # 5. Update decomposition info
    write_new_decomposition_info(case_dir, new_partition_info, config)
    
    downtime = time() - downtime_start
    
    return RedistributionStats(
        cells_migrated,
        estimate_data_transferred(cells_migrated, config.fields),
        old_partition.load_imbalance,
        new_partition.load_imbalance,
        time() - downtime_start,
        downtime
    )
end

"""
Redistribute incrementally to minimize data movement
"""
function redistribute_incrementally(case_dir::String, config::RedistributeConfig)
    downtime_start = time()
    
    # 1. Read current decomposition
    old_decomp_info = DecomposePar.read_decomposition_info(case_dir)
    old_partition = old_decomp_info.partition_info.partition
    
    # 2. Create migration plan
    if config.verbose
        println("   Creating incremental migration plan...")
    end
    
    migration_plan = create_incremental_migration_plan(
        old_partition, config.new_n_procs, config.new_method
    )
    
    # 3. Execute migration in phases
    total_cells_migrated = 0
    
    for (phase, migrations) in enumerate(migration_plan.phases)
        if config.verbose
            println("   Executing migration phase $phase/$(length(migration_plan.phases))...")
        end
        
        cells_migrated = execute_migration_phase(case_dir, migrations, config)
        total_cells_migrated += cells_migrated
    end
    
    downtime = time() - downtime_start
    
    # Get final partition info
    final_decomp_info = DecomposePar.read_decomposition_info(case_dir)
    final_partition = final_decomp_info.partition_info.partition
    
    return RedistributionStats(
        total_cells_migrated,
        estimate_data_transferred(total_cells_migrated, config.fields),
        old_partition.load_imbalance,
        final_partition.load_imbalance,
        time() - downtime_start,
        downtime
    )
end

# ============================================================================
# LIVE REDISTRIBUTION (EXPERIMENTAL)
# ============================================================================

"""
    redistribute_par_live(comm::MPI.Comm, mesh_data, fields, config::RedistributeConfig)

Live redistribution during a running simulation (experimental).
Minimizes downtime by overlapping data transfer with computation.
"""
function redistribute_par_live(comm::MPI.Comm, mesh_data, fields, 
                              config::RedistributeConfig)
    rank = MPI.Comm_rank(comm)
    size = MPI.Comm_size(comm)
    
    if size != config.old_n_procs
        error("MPI size ($size) doesn't match old_n_procs ($(config.old_n_procs))")
    end
    
    # Phase 1: Prepare new partition while simulation continues
    if rank == 0 && config.verbose
        println("Starting live redistribution...")
    end
    
    # Create new partition mapping
    new_partition_info = create_live_partition(mesh_data, config.new_method)
    
    # Phase 2: Pause simulation at synchronization point
    MPI.Barrier(comm)
    pause_time = time()
    
    # Phase 3: Quick data exchange
    migrated_data = exchange_live_data(comm, fields, new_partition_info, config)
    
    # Phase 4: Resume with new distribution
    resume_time = time()
    downtime = resume_time - pause_time
    
    if rank == 0 && config.verbose
        @printf("Live redistribution complete. Downtime: %.3f seconds\n", downtime)
    end
    
    return migrated_data, downtime
end

# ============================================================================
# LOAD BALANCING ANALYSIS
# ============================================================================

"""
    analyze_load_imbalance(case_dir::String, n_procs::Int) -> LoadImbalanceInfo

Analyze the current load imbalance and suggest redistribution.
"""
function analyze_load_imbalance(case_dir::String, n_procs::Int)
    # Read decomposition info
    decomp_info = DecomposePar.read_decomposition_info(case_dir)
    partition = decomp_info.partition_info.partition
    
    # Calculate processor loads
    processor_loads = Float64[]
    for proc in 0:n_procs-1
        proc_dir = joinpath(case_dir, "processor$proc")
        
        # Count cells
        n_cells = length(partition.processor_cells[proc+1])
        
        # Estimate computational load (could be enhanced with timing data)
        load = Float64(n_cells)
        push!(processor_loads, load)
    end
    
    # Calculate imbalance
    avg_load = mean(processor_loads)
    max_load = maximum(processor_loads)
    imbalance_factor = max_load / avg_load
    
    # Estimate communication costs
    communication_costs = estimate_communication_costs(decomp_info.partition_info)
    
    # Suggest optimal processor count
    suggested_n_procs = suggest_optimal_processors(
        partition.n_cells, processor_loads, communication_costs
    )
    
    # Determine if redistribution is needed
    requires_redistribution = imbalance_factor > 1.1 || 
                            suggested_n_procs != n_procs
    
    return LoadImbalanceInfo(
        imbalance_factor,
        processor_loads,
        communication_costs,
        suggested_n_procs,
        requires_redistribution
    )
end

"""
    suggest_redistribution(imbalance_info::LoadImbalanceInfo) -> RedistributeConfig

Suggest redistribution configuration based on load analysis.
"""
function suggest_redistribution(imbalance_info::LoadImbalanceInfo, 
                               current_n_procs::Int)
    # Determine new processor count
    new_n_procs = imbalance_info.suggested_n_procs
    
    # Choose decomposition method based on imbalance pattern
    if std(imbalance_info.processor_loads) / mean(imbalance_info.processor_loads) > 0.2
        # High variance suggests complex load pattern
        new_method = MetisPartition(new_n_procs, face_weights=true)
    else
        # Low variance might work with simple decomposition
        new_method = SimplePartition(new_n_procs, :xyz)
    end
    
    # Create configuration
    return RedistributeConfig(
        old_n_procs=current_n_procs,
        new_n_procs=new_n_procs,
        new_method=new_method,
        minimize_migration=true,
        load_balance_threshold=0.05  # Target 5% imbalance
    )
end

# ============================================================================
# HELPER FUNCTIONS
# ============================================================================

"""
Determine best redistribution strategy
"""
function determine_redistribution_strategy(case_dir::String, config::RedistributeConfig,
                                         current_imbalance::Float64)
    # If explicitly requested reconstruction
    if config.reconstruct_first
        return :reconstruct_decompose
    end
    
    # If changing processor count significantly
    ratio = config.new_n_procs / config.old_n_procs
    if ratio > 2.0 || ratio < 0.5
        return :reconstruct_decompose
    end
    
    # If current imbalance is severe
    if current_imbalance > 1.5
        return :reconstruct_decompose
    end
    
    # If minimizing migration is priority
    if config.minimize_migration
        return :incremental
    end
    
    # Default to direct remapping
    return :direct_remap
end

"""
Create remapping table for processor-to-processor transfer
"""
function create_remap_table(old_partition::Partition, new_partition::Partition,
                           config::RedistributeConfig)
    n_cells = old_partition.n_cells
    
    # Build cell migration table
    migrations = Dict{Tuple{Int,Int}, Vector{Int}}()
    
    for cell in 1:n_cells
        old_proc = old_partition.cell_processor[cell]
        new_proc = new_partition.cell_processor[cell]
        
        if old_proc != new_proc
            key = (old_proc, new_proc)
            if !haskey(migrations, key)
                migrations[key] = Int[]
            end
            push!(migrations[key], cell)
        end
    end
    
    return migrations
end

"""
Create incremental migration plan
"""
function create_incremental_migration_plan(old_partition::Partition, 
                                         new_n_procs::Int,
                                         new_method::PartitionMethod)
    # This is a placeholder for a sophisticated algorithm that would
    # minimize data movement while achieving the target distribution
    
    # For now, return a simple single-phase plan
    mesh = reconstruct_mesh_from_partition(old_partition)
    new_partition_info = MeshPartitioning.partition_mesh(mesh, new_method)
    
    migrations = create_remap_table(old_partition, new_partition_info.partition,
                                   RedistributeConfig(
                                       old_n_procs=old_partition.n_subdomains,
                                       new_n_procs=new_n_procs,
                                       new_method=new_method
                                   ))
    
    return (phases=[migrations],)
end

"""
Execute a migration phase
"""
function execute_migration_phase(case_dir::String, migrations::Dict,
                               config::RedistributeConfig)
    cells_migrated = 0
    
    # Get time directories
    time_dirs = determine_time_directories(case_dir, config.time_dirs)
    
    for ((from_proc, to_proc), cells) in migrations
        cells_migrated += length(cells)
        
        for time_dir in time_dirs
            migrate_cells_between_processors(
                case_dir, time_dir, from_proc, to_proc, cells, config
            )
        end
    end
    
    return cells_migrated
end

"""
Migrate cells between processors
"""
function migrate_cells_between_processors(case_dir::String, time_dir::String,
                                        from_proc::Int, to_proc::Int,
                                        cells::Vector{Int}, config::RedistributeConfig)
    from_dir = joinpath(case_dir, "processor$from_proc", time_dir)
    to_dir = joinpath(case_dir, "processor$to_proc", time_dir)
    
    if !isdir(from_dir)
        return
    end
    
    mkpath(to_dir)
    
    # Get fields to migrate
    fields = get_fields_to_migrate(from_dir, config.fields)
    
    for field_name in fields
        migrate_field_data(from_dir, to_dir, field_name, cells)
    end
end

"""
Create checkpoint before redistribution
"""
function create_checkpoint(case_dir::String, n_procs::Int)
    checkpoint_dir = joinpath(case_dir, "checkpoint_$(time_ns())")
    mkpath(checkpoint_dir)
    
    # Copy processor directories
    for proc in 0:n_procs-1
        src = joinpath(case_dir, "processor$proc")
        dst = joinpath(checkpoint_dir, "processor$proc")
        cp(src, dst, force=true)
    end
    
    # Save checkpoint info
    info = Dict(
        "time" => time(),
        "n_procs" => n_procs,
        "case_dir" => case_dir
    )
    
    open(joinpath(checkpoint_dir, "checkpoint.json"), "w") do f
        JSON.print(f, info, 4)
    end
end

"""
Analyze current distribution quality
"""
function analyze_current_distribution(case_dir::String, n_procs::Int)
    decomp_info = DecomposePar.read_decomposition_info(case_dir)
    return decomp_info.partition_info.partition.load_imbalance
end

"""
Verify redistribution was successful
"""
function verify_redistribution(case_dir::String, n_procs::Int)
    # Check all processor directories exist
    for proc in 0:n_procs-1
        proc_dir = joinpath(case_dir, "processor$proc")
        if !isdir(proc_dir)
            return false
        end
    end
    
    # Verify decomposition info
    try
        decomp_info = DecomposePar.read_decomposition_info(case_dir)
        return decomp_info.n_processors == n_procs
    catch
        return false
    end
end

"""
Clean up old processor directories
"""
function cleanup_old_processors(case_dir::String, config::RedistributeConfig)
    # Remove processor directories outside new range
    for proc in config.new_n_procs:config.old_n_procs-1
        proc_dir = joinpath(case_dir, "processor$proc")
        if isdir(proc_dir)
            rm(proc_dir, recursive=true)
        end
    end
end

"""
Print redistribution summary
"""
function print_redistribution_summary(stats::RedistributionStats)
    println("\n" * "="^60)
    println("REDISTRIBUTION COMPLETE")
    println("="^60)
    println("Cells migrated: $(stats.cells_migrated)")
    @printf("Data transferred: %.2f MB\n", stats.data_transferred)
    @printf("Load imbalance: %.1f%% → %.1f%%\n", 
            (stats.load_imbalance_before - 1.0) * 100,
            (stats.load_imbalance_after - 1.0) * 100)
    @printf("Total time: %.2f seconds\n", stats.redistribution_time)
    @printf("Downtime: %.3f seconds\n", stats.downtime)
    println("="^60)
end

# ============================================================================
# UTILITY FUNCTIONS
# ============================================================================

"""
Calculate cells migrated in full reconstruction
"""
function calculate_cells_migrated_full(old_n_procs::Int, new_n_procs::Int,
                                     new_partition_info::PartitionInfo)
    # In full reconstruction, estimate based on processor count change
    total_cells = new_partition_info.partition.n_cells
    
    if old_n_procs == new_n_procs
        # Same processor count, just rebalancing
        return div(total_cells, 10)  # Estimate 10% migration
    else
        # Different processor count, most cells move
        return div(total_cells * 2, 3)  # Estimate 67% migration
    end
end

"""
Estimate data transferred based on cells and fields
"""
function estimate_data_transferred(cells_migrated::Int, fields::Vector{String})
    # Estimate bytes per cell per field
    bytes_per_cell = 8 * 5  # 5 doubles per cell average
    
    n_fields = isempty(fields) ? 10 : length(fields)  # Assume 10 fields if not specified
    
    total_bytes = cells_migrated * bytes_per_cell * n_fields
    return total_bytes / 1e6  # Convert to MB
end

"""
Estimate communication costs for processors
"""
function estimate_communication_costs(partition_info::PartitionInfo)
    costs = Float64[]
    
    for (proc, neighbors) in enumerate(partition_info.processor_neighbors)
        # Cost proportional to number of neighbors and interface size
        n_neighbors = length(neighbors)
        interface_faces = count(f -> f[2] == proc-1 || f[3] == proc-1,
                               partition_info.interface_faces)
        
        cost = n_neighbors * 1.0 + interface_faces * 0.001
        push!(costs, cost)
    end
    
    return costs
end

"""
Suggest optimal processor count
"""
function suggest_optimal_processors(n_cells::Int, processor_loads::Vector{Float64},
                                  communication_costs::Vector{Float64})
    current_n_procs = length(processor_loads)
    
    # Simple heuristic: aim for ~10000-50000 cells per processor
    ideal_cells_per_proc = 25000
    suggested = clamp(div(n_cells, ideal_cells_per_proc), 1, 1024)
    
    # Adjust based on current imbalance
    imbalance = maximum(processor_loads) / mean(processor_loads)
    if imbalance > 1.2
        # Significant imbalance, might benefit from more processors
        suggested = min(suggested * 2, current_n_procs * 2)
    end
    
    # Round to nice numbers (powers of 2 or multiples of 4)
    if suggested <= 8
        return suggested
    elseif suggested <= 16
        return round(Int, suggested / 4) * 4
    else
        return round(Int, suggested / 8) * 8
    end
end

"""
Determine time directories for redistribution
"""
function determine_time_directories(case_dir::String, specified_dirs::Vector{String})
    if !isempty(specified_dirs)
        return specified_dirs
    end
    
    # Get from processor0
    proc0_dir = joinpath(case_dir, "processor0")
    time_dirs = String[]
    
    for item in readdir(proc0_dir)
        if isdir(joinpath(proc0_dir, item)) && occursin(r"^\d+\.?\d*$", item)
            push!(time_dirs, item)
        end
    end
    
    return sort(time_dirs, by=x->parse(Float64, x))
end

"""
Get fields to migrate from a time directory
"""
function get_fields_to_migrate(time_dir::String, specified_fields::Vector{String})
    if !isempty(specified_fields)
        return filter(f -> isfile(joinpath(time_dir, f)), specified_fields)
    end
    
    # Get all field files
    fields = String[]
    for item in readdir(time_dir)
        if isfile(joinpath(time_dir, item)) && !startswith(item, ".")
            push!(fields, item)
        end
    end
    
    return fields
end

"""
Write updated decomposition info
"""
function write_new_decomposition_info(case_dir::String, partition_info::PartitionInfo,
                                    config::RedistributeConfig)
    decomp_info = DecompositionInfo(
        config.new_n_procs,
        string(typeof(config.new_method)),
        partition_info,
        Dict{String,Any}(),  # Mesh stats
        0.0,  # Time
        case_dir,
        String[]  # Time dirs
    )
    
    DecomposePar.write_decomposition_info(case_dir, decomp_info)
end

# ============================================================================
# PLACEHOLDER FUNCTIONS
# ============================================================================

function read_mesh(mesh_path::String)
    # Placeholder
    error("read_mesh not implemented")
end

function reconstruct_mesh_from_partition(partition::Partition)
    # Placeholder
    error("reconstruct_mesh_from_partition not implemented")
end

function parallel_data_transfer(case_dir::String, remap_table::Dict, config)
    # Placeholder for parallel transfer implementation
    return sequential_data_transfer(case_dir, remap_table, config)
end

function sequential_data_transfer(case_dir::String, remap_table::Dict, config)
    # Placeholder
    cells_migrated = sum(length(cells) for (_, cells) in remap_table)
    return cells_migrated
end

function migrate_field_data(from_dir::String, to_dir::String, field_name::String,
                          cells::Vector{Int})
    # Placeholder
    @warn "migrate_field_data not implemented"
end

function create_live_partition(mesh_data, method::PartitionMethod)
    # Placeholder
    error("create_live_partition not implemented")
end

function exchange_live_data(comm::MPI.Comm, fields, partition_info, config)
    # Placeholder
    error("exchange_live_data not implemented")
end

end # module RedistributePar