"""
    TransparentParallel.jl

Main module for transparent parallel computing in JuliaFOAM.
This module provides high-level abstractions that allow existing serial code
to run in parallel with minimal modifications.

Key features:
- Distributed fields with automatic halo exchange
- Distributed meshes with transparent partitioning
- Parallel finite volume operations
- Automatic load balancing
- Seamless MPI integration

Example usage:
```julia
using JuliaFOAM
using JuliaFOAM.TransparentParallel

# Initialize MPI
init_parallel()

# Load mesh (same as serial)
mesh = load_mesh("case/constant/polyMesh")

# Create distributed mesh wrapper
dmesh = DistributedMeshData(mesh)

# Create fields (automatically distributed)
p = DistributedField("p", dmesh.mesh, 0.0)
U = DistributedField("U", dmesh.mesh, SVector(0.0, 0.0, 0.0))

# Use finite volume operations (automatically parallel)
grad_p = grad(p, dmesh)
div_U = div(U, dmesh)
lap_p = laplacian(p, dmesh, diffusion_coeff=0.1)

# Fields automatically synchronized when needed
p_sum = sum(p)  # Global sum across all processes

# Finalize MPI
finalize_parallel()
```
"""
module TransparentParallel

using LinearAlgebra
using SparseArrays
using StaticArrays

# Production-ready MPI integration - require real MPI for parallel features
const MPI_AVAILABLE = try
    using MPI
    true
catch e
    @error "MPI.jl is required for parallel functionality. Please install with: using Pkg; Pkg.add(\"MPI\")"
    @error "Error loading MPI: $e"
    false
end

# Fail gracefully if MPI is not available
if !MPI_AVAILABLE
    error("""
    MPI.jl is required for JuliaFOAM parallel functionality.

    To install MPI.jl:
    1. Install MPI system library (e.g., OpenMPI, MPICH)
    2. Run: using Pkg; Pkg.add("MPI")
    3. Configure: using MPI; MPI.install_mpiexecjl()

    For serial-only usage, use JuliaFOAM without parallel modules.
    """)
end

# Verify MPI is properly initialized
function check_mpi_initialization()
    if !MPI.Initialized()
        @warn "MPI not initialized. Call init_parallel() before using parallel features."
        return false
    end
    return true
end

# Production MPI validation
function validate_mpi_environment()
    if !MPI_AVAILABLE
        error("MPI.jl not available - parallel features disabled")
    end

    if !MPI.Initialized()
        error("MPI not initialized - call init_parallel() first")
    end

    # Check for proper MPI environment
    comm_size = MPI.Comm_size(MPI.COMM_WORLD)
    comm_rank = MPI.Comm_rank(MPI.COMM_WORLD)

    if comm_size == 1
        @warn "Running with single MPI process - parallel features will have limited benefit"
    end

    return (size=comm_size, rank=comm_rank)
end

# Include submodules
include("DistributedFields.jl")
include("DistributedMesh.jl")
include("ParallelOperations.jl")

# Import and re-export from submodules
using .DistributedFields
using .DistributedMesh
using .ParallelOperations

# Re-export main types and functions
export DistributedField, DistributedMeshData
export sync!, sync_async!, wait_sync!
export local_values, global_size, is_distributed
export partition_mesh!, get_local_mesh, repartition!
export grad, div, laplacian
export init_parallel, finalize_parallel
export with_parallel

# ============================================================================
# INITIALIZATION AND FINALIZATION
# ============================================================================

"""
    init_parallel(; thread_support_level::Symbol=:funneled)

Initialize the parallel environment (MPI).

# Arguments
- `thread_support_level`: MPI thread support level (:single, :funneled, :serialized, :multiple)

# Returns
- `(comm, rank, nprocs)`: MPI communicator, rank, and number of processes
"""
function init_parallel(; thread_support_level::Symbol=:funneled)
    # Validate MPI availability
    if !MPI_AVAILABLE
        error("Cannot initialize parallel execution: MPI.jl not available")
    end

    if MPI.Initialized()
        @warn "MPI already initialized"
        comm = MPI.COMM_WORLD
        rank = MPI.Comm_rank(comm)
        nprocs = MPI.Comm_size(comm)
        return comm, rank, nprocs
    end

    try
        # Map thread support level
        level = if thread_support_level == :single
            MPI.THREAD_SINGLE
        elseif thread_support_level == :funneled
            MPI.THREAD_FUNNELED
        elseif thread_support_level == :serialized
            MPI.THREAD_SERIALIZED
        elseif thread_support_level == :multiple
            MPI.THREAD_MULTIPLE
        else
            @warn "Unknown thread support level $thread_support_level, using :funneled"
            MPI.THREAD_FUNNELED
        end

        provided = MPI.Init(threadlevel=level)

        if provided < level
            @warn "MPI thread support level $thread_support_level not available, got $(provided)"
        end

        # Set up error handling
        MPI.Comm_set_errhandler(MPI.COMM_WORLD, MPI.ERRORS_RETURN)

        comm = MPI.COMM_WORLD
        rank = MPI.Comm_rank(comm)
        nprocs = MPI.Comm_size(comm)

        # Validate MPI environment
        if nprocs < 1
            error("Invalid MPI communicator size: $nprocs")
        end

        # Print initialization info on rank 0
        if rank == 0
            @info "MPI initialized successfully"
            @info "  Total processes: $nprocs"
            @info "  Thread support level: $thread_support_level (provided: $provided)"
        end

        return comm, rank, nprocs

    catch e
        error("Failed to initialize MPI: $e")
    end
end

"""
    finalize_parallel()

Finalize the parallel environment with proper cleanup and error handling.
"""
function finalize_parallel()
    if !MPI_AVAILABLE
        @warn "MPI not available - nothing to finalize"
        return
    end

    if !MPI.Initialized()
        @warn "MPI not initialized - nothing to finalize"
        return
    end

    if MPI.Finalized()
        @warn "MPI already finalized"
        return
    end

    try
        # Ensure all pending operations complete
        MPI.Barrier(MPI.COMM_WORLD)

        # Finalize MPI
        MPI.Finalize()

        rank = MPI.Comm_rank(MPI.COMM_WORLD)
        if rank == 0
            @info "MPI finalized successfully"
        end

    catch e
        @error "Error during MPI finalization: $e"
        # Continue anyway - don't let finalization errors crash the program
    end
end

"""
    with_parallel(f::Function; thread_support_level::Symbol=:funneled)

Execute a function in a parallel context, automatically initializing and
finalizing MPI.

# Example
```julia
with_parallel() do comm, rank, nprocs
    # Your parallel code here
    println("Process \$rank of \$nprocs")
end
```
"""
function with_parallel(f::Function; thread_support_level::Symbol=:funneled)
    comm, rank, nprocs = init_parallel(thread_support_level=thread_support_level)
    
    try
        f(comm, rank, nprocs)
    finally
        finalize_parallel()
    end
end

# ============================================================================
# CONVENIENCE FUNCTIONS
# ============================================================================

"""
    create_distributed_field(name::String, mesh::Mesh, initial_value;
                           comm::MPI.Comm=MPI.COMM_WORLD)

Create a distributed field from a mesh. Automatically handles mesh partitioning
if needed.
"""
function create_distributed_field(name::String, mesh::Mesh, initial_value;
                                comm::MPI.Comm=MPI.COMM_WORLD)
    
    # Check if mesh needs partitioning
    dmesh = DistributedMeshData(mesh, comm)
    
    # Create distributed field
    return DistributedField(name, dmesh.mesh, initial_value, :cellCenters, comm)
end

"""
    automatic_partitioning!(mesh::Mesh; method::Symbol=:metis, comm::MPI.Comm=MPI.COMM_WORLD)

Automatically partition a mesh for optimal parallel performance.
This modifies the mesh in-place.
"""
function automatic_partitioning!(mesh::Mesh; method::Symbol=:metis, comm::MPI.Comm=MPI.COMM_WORLD)
    dmesh = DistributedMeshData(mesh, comm, partition_method=method)
    partition_mesh!(dmesh, method=method)
    
    # Update the original mesh with partition info
    mesh.cell_partition .= dmesh.mesh.cell_partition
    mesh.halo_cells = dmesh.mesh.halo_cells
end

"""
    parallel_mesh_stats(mesh::Mesh; comm::MPI.Comm=MPI.COMM_WORLD)

Compute and display parallel mesh statistics.
"""
function parallel_mesh_stats(mesh::Mesh; comm::MPI.Comm=MPI.COMM_WORLD)
    dmesh = DistributedMeshData(mesh, comm)
    stats = DistributedMesh.mesh_stats(dmesh)
    
    rank = MPI.Comm_rank(comm)
    
    if rank == 0
        println("\nParallel Mesh Statistics:")
        println("========================")
        println("Global cells: $(stats.global_cells)")
        println("Load imbalance: $(round(stats.load_imbalance * 100, digits=1))%")
        println("Max cells per proc: $(stats.max_cells)")
        println("Min cells per proc: $(stats.min_cells)")
        println("Avg cells per proc: $(round(stats.avg_cells, digits=1))")
        println("Total ghost cells: $(stats.total_ghost_cells)")
        println("Ghost ratio: $(round(stats.ghost_ratio * 100, digits=1))%")
    end
    
    return stats
end

# ============================================================================
# FIELD CONVERSION UTILITIES
# ============================================================================

"""
    to_distributed(field::Field, mesh::Mesh; comm::MPI.Comm=MPI.COMM_WORLD)

Convert a regular Field to a DistributedField.
"""
function to_distributed(field::Field{T}, mesh::Mesh; comm::MPI.Comm=MPI.COMM_WORLD) where T
    return DistributedField{T}(field, mesh, comm)
end

"""
    to_serial(dfield::DistributedField; root::Int=0)

Gather a DistributedField to a serial Field on the root process.
Returns nothing on non-root processes.
"""
function to_serial(dfield::DistributedField{T}; root::Int=0) where T
    return DistributedFields.gather(dfield, root)
end

# ============================================================================
# PARALLEL I/O HELPERS
# ============================================================================

"""
    parallel_write(dfield::DistributedField, time_dir::String)

Write a distributed field in parallel. Each process writes its own portion.
"""
function parallel_write(dfield::DistributedField, time_dir::String)
    rank = dfield.rank
    
    # Create processor directory
    proc_dir = joinpath(time_dir, "processor$rank")
    mkpath(proc_dir)
    
    # Write local field data
    field_file = joinpath(proc_dir, dfield.local_field.name)
    
    # TODO: Implement proper OpenFOAM format writing
    # For now, just save as binary
    open(field_file, "w") do io
        write(io, length(dfield.local_cells))
        write(io, dfield.local_field.values[1:length(dfield.local_cells)])
    end
end

"""
    parallel_read(name::String, mesh::Mesh, time_dir::String, ::Type{T};
                 comm::MPI.Comm=MPI.COMM_WORLD) where T

Read a distributed field in parallel.
"""
function parallel_read(name::String, mesh::Mesh, time_dir::String, ::Type{T};
                      comm::MPI.Comm=MPI.COMM_WORLD) where T
    
    rank = MPI.Comm_rank(comm)
    
    # Read from processor directory
    proc_dir = joinpath(time_dir, "processor$rank")
    field_file = joinpath(proc_dir, name)
    
    if !isfile(field_file)
        error("Field file not found: $field_file")
    end
    
    # Read local field data
    local_values = open(field_file, "r") do io
        n = read(io, Int)
        Vector{T}(undef, n)
        read!(io, local_values)
        local_values
    end
    
    # Create distributed field
    # TODO: Properly reconstruct with boundary values etc.
    field = Field{T}(name, mesh, local_values, :cellCenters)
    
    return DistributedField{T}(field, mesh, comm)
end

# ============================================================================
# PERFORMANCE MONITORING
# ============================================================================

"""
    ParallelTimer

Simple timer for parallel performance monitoring.
"""
mutable struct ParallelTimer
    start_time::Float64
    comm::MPI.Comm
    name::String
end

"""
    start_timer(name::String; comm::MPI.Comm=MPI.COMM_WORLD)

Start a parallel timer.
"""
function start_timer(name::String; comm::MPI.Comm=MPI.COMM_WORLD)
    MPI.Barrier(comm)  # Synchronize before timing
    return ParallelTimer(time(), comm, name)
end

"""
    stop_timer(timer::ParallelTimer; verbose::Bool=true)

Stop a parallel timer and report statistics.
"""
function stop_timer(timer::ParallelTimer; verbose::Bool=true)
    local_time = time() - timer.start_time
    
    # Gather timing statistics
    rank = MPI.Comm_rank(timer.comm)
    nprocs = MPI.Comm_size(timer.comm)
    
    max_time = MPI.Allreduce(local_time, max, timer.comm)
    min_time = MPI.Allreduce(local_time, min, timer.comm)
    avg_time = MPI.Allreduce(local_time, +, timer.comm) / nprocs
    
    if verbose && rank == 0
        imbalance = (max_time - min_time) / avg_time * 100
        println("\nTiming for '$(timer.name)':")
        println("  Average: $(round(avg_time, digits=3))s")
        println("  Maximum: $(round(max_time, digits=3))s")
        println("  Minimum: $(round(min_time, digits=3))s")
        println("  Imbalance: $(round(imbalance, digits=1))%")
    end
    
    return (avg_time, max_time, min_time, local_time)
end

# ============================================================================
# HIGH-LEVEL SOLVER INTERFACE
# ============================================================================

"""
    parallel_implicit_solve(operator::Function, rhs::DistributedField{T},
                          dmesh::DistributedMeshData;
                          method::Symbol=:cg,
                          tolerance::Float64=1e-6,
                          max_iterations::Int=1000,
                          preconditioner::Symbol=:jacobi,
                          initial_guess::Union{DistributedField{T}, Nothing}=nothing,
                          verbose::Bool=false) where T

Solve an implicit linear system Ax = b using parallel iterative methods.

The operator function should represent the matrix-vector product A*x.

# Arguments
- `operator`: Function that applies the linear operator A to a distributed field
- `rhs`: Right-hand side vector b as a DistributedField
- `dmesh`: Distributed mesh data structure
- `method`: Iterative solver method (:cg, :gmres, :bicgstab)
- `tolerance`: Convergence tolerance for residual norm
- `max_iterations`: Maximum number of iterations
- `preconditioner`: Preconditioning method (:none, :jacobi, :ilu)
- `initial_guess`: Initial solution guess (zero if not provided)
- `verbose`: Print convergence information

# Returns
- `solution`: Solution vector x as a DistributedField
- `info`: Convergence information dictionary

# Example
```julia
# Solve: laplacian(T) = source
solution, info = parallel_implicit_solve(
    T -> laplacian(T, dmesh, diffusion_coeff=0.1),
    source,
    dmesh,
    method=:cg,
    tolerance=1e-8
)
```
"""
function parallel_implicit_solve(operator::Function, rhs::DistributedField{T},
                               dmesh::DistributedMeshData;
                               method::Symbol=:cg,
                               tolerance::Float64=1e-6,
                               max_iterations::Int=1000,
                               preconditioner::Symbol=:jacobi,
                               initial_guess::Union{DistributedField{T}, Nothing}=nothing,
                               verbose::Bool=false) where T

    # Validate inputs
    if !(method in [:cg, :gmres, :bicgstab])
        throw(ArgumentError("Unsupported solver method: $method. Use :cg, :gmres, or :bicgstab"))
    end

    if !(preconditioner in [:none, :jacobi, :ilu])
        throw(ArgumentError("Unsupported preconditioner: $preconditioner. Use :none, :jacobi, or :ilu"))
    end

    # Initialize solution vector
    solution = if initial_guess !== nothing
        copy(initial_guess)
    else
        DistributedField(rhs.local_field.name * "_solution",
                        dmesh.mesh, zero(T), rhs.local_field.location, rhs.comm)
    end

    # Copy communication structure from RHS
    solution.local_cells = rhs.local_cells
    solution.halo_cells = rhs.halo_cells
    solution.send_list = rhs.send_list
    solution.recv_list = rhs.recv_list

    # Setup preconditioner
    precond_operator = setup_preconditioner(operator, solution, preconditioner, dmesh)

    # Solve using selected method
    if method == :cg
        return parallel_conjugate_gradient(operator, rhs, solution, precond_operator,
                                         tolerance, max_iterations, verbose)
    elseif method == :gmres
        return parallel_gmres(operator, rhs, solution, precond_operator,
                            tolerance, max_iterations, verbose)
    elseif method == :bicgstab
        return parallel_bicgstab(operator, rhs, solution, precond_operator,
                               tolerance, max_iterations, verbose)
    end
end

# ============================================================================
# ITERATIVE SOLVER IMPLEMENTATIONS
# ============================================================================

"""
Setup preconditioner for the linear system
"""
function setup_preconditioner(operator::Function, x::DistributedField{T},
                             precond_type::Symbol, dmesh::DistributedMeshData) where T
    if precond_type == :none
        return identity  # No preconditioning
    elseif precond_type == :jacobi
        return setup_jacobi_preconditioner(operator, x, dmesh)
    elseif precond_type == :ilu
        return setup_ilu_preconditioner(operator, x, dmesh)
    else
        @warn "Unknown preconditioner type: $precond_type, using identity"
        return identity
    end
end

"""
Setup Jacobi (diagonal) preconditioner
"""
function setup_jacobi_preconditioner(operator::Function, x::DistributedField{T},
                                    dmesh::DistributedMeshData) where T
    # Extract diagonal elements by applying operator to unit vectors
    n_local = length(x.local_field.values)
    diagonal = zeros(T, n_local)

    # Create unit vectors and extract diagonal
    unit_field = copy(x)
    for i in 1:n_local
        # Reset to zero
        fill!(unit_field.local_field.values, zero(T))
        # Set i-th component to 1
        unit_field.local_field.values[i] = one(T)

        # Apply operator and extract diagonal element
        result = operator(unit_field)
        diagonal[i] = result.local_field.values[i]
    end

    # Avoid division by zero
    diagonal = max.(abs.(diagonal), 1e-12)

    # Return preconditioner function
    return function jacobi_precond(r::DistributedField{T})
        result = copy(r)
        result.local_field.values ./= diagonal
        return result
    end
end

"""
Setup ILU preconditioner (simplified implementation)
"""
function setup_ilu_preconditioner(operator::Function, x::DistributedField{T},
                                 dmesh::DistributedMeshData) where T
    # For now, fall back to Jacobi preconditioning
    # A full ILU implementation would require matrix assembly
    @warn "ILU preconditioning not fully implemented, using Jacobi"
    return setup_jacobi_preconditioner(operator, x, dmesh)
end

"""
Parallel Conjugate Gradient solver
"""
function parallel_conjugate_gradient(operator::Function, b::DistributedField{T},
                                    x::DistributedField{T}, preconditioner::Function,
                                    tolerance::Float64, max_iterations::Int,
                                    verbose::Bool) where T

    # Initialize vectors
    r = b - operator(x)  # Initial residual
    z = preconditioner(r)  # Preconditioned residual
    p = copy(z)  # Search direction

    # Initial residual norm
    rz_old = parallel_dot(r, z)
    initial_residual = sqrt(parallel_dot(r, r))

    if verbose && MPI.Comm_rank(x.comm) == 0
        println("CG Solver: Initial residual = $(initial_residual)")
    end

    convergence_history = Float64[]

    for iteration in 1:max_iterations
        # Apply operator to search direction
        Ap = operator(p)

        # Compute step size
        pAp = parallel_dot(p, Ap)
        if abs(pAp) < 1e-14
            @warn "CG breakdown: pAp ≈ 0"
            break
        end

        alpha = rz_old / pAp

        # Update solution and residual
        x += alpha * p
        r -= alpha * Ap

        # Check convergence
        residual_norm = sqrt(parallel_dot(r, r))
        push!(convergence_history, residual_norm)

        if verbose && MPI.Comm_rank(x.comm) == 0 && iteration % 10 == 0
            println("CG Iteration $iteration: residual = $(residual_norm)")
        end

        if residual_norm < tolerance
            if verbose && MPI.Comm_rank(x.comm) == 0
                println("CG converged in $iteration iterations")
            end

            info = Dict(
                "converged" => true,
                "iterations" => iteration,
                "final_residual" => residual_norm,
                "initial_residual" => initial_residual,
                "convergence_history" => convergence_history
            )
            return x, info
        end

        # Update search direction
        z = preconditioner(r)
        rz_new = parallel_dot(r, z)
        beta = rz_new / rz_old
        p = z + beta * p
        rz_old = rz_new
    end

    # Did not converge
    if verbose && MPI.Comm_rank(x.comm) == 0
        println("CG did not converge in $max_iterations iterations")
    end

    info = Dict(
        "converged" => false,
        "iterations" => max_iterations,
        "final_residual" => sqrt(parallel_dot(r, r)),
        "initial_residual" => initial_residual,
        "convergence_history" => convergence_history
    )
    return x, info
end

"""
Parallel GMRES solver (simplified implementation)
"""
function parallel_gmres(operator::Function, b::DistributedField{T},
                       x::DistributedField{T}, preconditioner::Function,
                       tolerance::Float64, max_iterations::Int,
                       verbose::Bool; restart::Int=30) where T

    # For now, implement restarted GMRES with simple restart
    # A full implementation would use Arnoldi process

    initial_residual = sqrt(parallel_dot(b - operator(x), b - operator(x)))

    if verbose && MPI.Comm_rank(x.comm) == 0
        println("GMRES Solver: Initial residual = $(initial_residual)")
    end

    convergence_history = Float64[]
    total_iterations = 0

    for restart_cycle in 1:div(max_iterations, restart)
        # Use CG for this restart cycle (simplified)
        x_temp, info_temp = parallel_conjugate_gradient(
            operator, b, x, preconditioner,
            tolerance, restart, false
        )

        x = x_temp
        total_iterations += info_temp["iterations"]
        append!(convergence_history, info_temp["convergence_history"])

        if info_temp["converged"]
            if verbose && MPI.Comm_rank(x.comm) == 0
                println("GMRES converged in $total_iterations iterations")
            end

            info = Dict(
                "converged" => true,
                "iterations" => total_iterations,
                "final_residual" => info_temp["final_residual"],
                "initial_residual" => initial_residual,
                "convergence_history" => convergence_history
            )
            return x, info
        end
    end

    # Did not converge
    if verbose && MPI.Comm_rank(x.comm) == 0
        println("GMRES did not converge in $max_iterations iterations")
    end

    final_residual = sqrt(parallel_dot(b - operator(x), b - operator(x)))
    info = Dict(
        "converged" => false,
        "iterations" => total_iterations,
        "final_residual" => final_residual,
        "initial_residual" => initial_residual,
        "convergence_history" => convergence_history
    )
    return x, info
end

"""
Parallel BiCGStab solver
"""
function parallel_bicgstab(operator::Function, b::DistributedField{T},
                          x::DistributedField{T}, preconditioner::Function,
                          tolerance::Float64, max_iterations::Int,
                          verbose::Bool) where T

    # Initialize vectors
    r = b - operator(x)  # Initial residual
    r_star = copy(r)     # Shadow residual
    p = copy(r)          # Search direction
    v = copy(r)          # Temporary vector

    initial_residual = sqrt(parallel_dot(r, r))

    if verbose && MPI.Comm_rank(x.comm) == 0
        println("BiCGStab Solver: Initial residual = $(initial_residual)")
    end

    convergence_history = Float64[]
    rho_old = one(T)
    alpha = one(T)
    omega = one(T)

    for iteration in 1:max_iterations
        rho = parallel_dot(r_star, r)

        if abs(rho) < 1e-14
            @warn "BiCGStab breakdown: rho ≈ 0"
            break
        end

        if iteration == 1
            p = copy(r)
        else
            beta = (rho / rho_old) * (alpha / omega)
            p = r + beta * (p - omega * v)
        end

        # Apply preconditioner and operator
        p_hat = preconditioner(p)
        v = operator(p_hat)

        alpha = rho / parallel_dot(r_star, v)
        s = r - alpha * v

        # Check if we can stop here
        s_norm = sqrt(parallel_dot(s, s))
        if s_norm < tolerance
            x += alpha * p_hat

            if verbose && MPI.Comm_rank(x.comm) == 0
                println("BiCGStab converged in $iteration iterations")
            end

            info = Dict(
                "converged" => true,
                "iterations" => iteration,
                "final_residual" => s_norm,
                "initial_residual" => initial_residual,
                "convergence_history" => convergence_history
            )
            return x, info
        end

        # Continue with BiCGStab
        s_hat = preconditioner(s)
        t = operator(s_hat)

        omega = parallel_dot(t, s) / parallel_dot(t, t)
        x += alpha * p_hat + omega * s_hat
        r = s - omega * t

        # Check convergence
        residual_norm = sqrt(parallel_dot(r, r))
        push!(convergence_history, residual_norm)

        if verbose && MPI.Comm_rank(x.comm) == 0 && iteration % 10 == 0
            println("BiCGStab Iteration $iteration: residual = $(residual_norm)")
        end

        if residual_norm < tolerance
            if verbose && MPI.Comm_rank(x.comm) == 0
                println("BiCGStab converged in $iteration iterations")
            end

            info = Dict(
                "converged" => true,
                "iterations" => iteration,
                "final_residual" => residual_norm,
                "initial_residual" => initial_residual,
                "convergence_history" => convergence_history
            )
            return x, info
        end

        if abs(omega) < 1e-14
            @warn "BiCGStab breakdown: omega ≈ 0"
            break
        end

        rho_old = rho
    end

    # Did not converge
    if verbose && MPI.Comm_rank(x.comm) == 0
        println("BiCGStab did not converge in $max_iterations iterations")
    end

    info = Dict(
        "converged" => false,
        "iterations" => max_iterations,
        "final_residual" => sqrt(parallel_dot(r, r)),
        "initial_residual" => initial_residual,
        "convergence_history" => convergence_history
    )
    return x, info
end

"""
Parallel dot product for distributed fields
"""
function parallel_dot(x::DistributedField{T}, y::DistributedField{T}) where T
    # Compute local dot product
    local_dot = dot(x.local_field.values, y.local_field.values)

    # Sum across all processes
    global_dot = MPI.Allreduce(local_dot, +, x.comm)

    return global_dot
end

end # module TransparentParallel