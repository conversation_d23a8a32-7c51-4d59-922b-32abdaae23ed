"""
ParallelIO.jl

Complete implementation of OpenFOAM-style I/O for parallel decomposition.
Each processor reads/writes to its own directory. Supports full field I/O
with proper boundary condition handling and integration with JuliaFOAM field structures.
"""

module ParallelIO

using Printf
using LinearAlgebra
using StaticArrays

# Try to import required modules with fallbacks
const UNSTRUCTURED_MESH_AVAILABLE = try
    using ..UnstructuredMesh
    true
catch
    false
end

const OPENFOAM_IMPORTER_AVAILABLE = try
    using ..OpenFOAMImporter
    true
catch
    false
end

# Import core types if available (with fallbacks)
const FIELD_TYPES_AVAILABLE = try
    if isdefined(Main, :JuliaFOAM)
        import Main.JuliaFOAM: Field, Mesh, BoundaryCondition
        import Main.JuliaFOAM: FixedValueBC, FixedGradientBC, ZeroGradientBC
        true
    else
        false
    end
catch
    false
end

export write_processor_mesh, read_processor_mesh
export write_processor_field, read_processor_field, write_processor_fields
export read_processor_scalar_field, read_processor_vector_field
export write_processor_scalar_field, write_processor_vector_field
export create_processor_directories, decompose_fields, reconstruct_fields
export validate_field_decomposition, benchmark_field_io

# ============================================================================
# PROCESSOR DIRECTORY MANAGEMENT
# ============================================================================

"""
Create processor directories following OpenFOAM convention
"""
function create_processor_directories(case_dir::String, n_procs::Int)
    for proc in 0:n_procs-1
        proc_dir = joinpath(case_dir, "processor$proc")
        mkpath(joinpath(proc_dir, "constant", "polyMesh"))
        mkpath(joinpath(proc_dir, "system"))
    end
end

# ============================================================================
# MESH I/O
# ============================================================================

"""
Write processor mesh in OpenFOAM format
"""
function write_processor_mesh(proc_dir::String, mesh,
                            global_to_local_map::Dict{Int,Int})
    mesh_dir = joinpath(proc_dir, "constant", "polyMesh")
    
    # Write points
    write_openfoam_points(joinpath(mesh_dir, "points"), mesh.points)
    
    # Write faces
    write_openfoam_faces(joinpath(mesh_dir, "faces"), mesh.faces)
    
    # Write owner/neighbour
    write_openfoam_owner_neighbour(mesh_dir, mesh.faces)
    
    # Write boundary
    write_openfoam_boundary(joinpath(mesh_dir, "boundary"), mesh.boundary_patches)
    
    # Write addressing for reconstruction
    write_cell_addressing(joinpath(mesh_dir, "cellProcAddressing"), global_to_local_map)
end

"""
Write OpenFOAM points file
"""
function write_openfoam_points(filename::String, points)
    open(filename, "w") do f
        # Write OpenFOAM header
        write_foam_header(f, "vectorField", "points")
        
        # Write data
        println(f, length(points))
        println(f, "(")
        for p in points
            @printf(f, "(%.16e %.16e %.16e)\n", p.x, p.y, p.z)
        end
        println(f, ")")
    end
end

"""
Write OpenFOAM faces file
"""
function write_openfoam_faces(filename::String, faces)
    open(filename, "w") do f
        # Write OpenFOAM header
        write_foam_header(f, "faceList", "faces")
        
        # Write data
        println(f, length(faces))
        println(f, "(")
        for face in faces
            print(f, "$(length(face.point_indices))(")
            for (i, idx) in enumerate(face.point_indices)
                print(f, idx - 1)  # Convert to 0-based
                if i < length(face.point_indices)
                    print(f, " ")
                end
            end
            println(f, ")")
        end
        println(f, ")")
    end
end

"""
Write owner and neighbour files
"""
function write_openfoam_owner_neighbour(mesh_dir::String, faces)
    # Separate internal and boundary faces
    internal_faces = filter(f -> f.neighbor_cell > 0, faces)
    
    # Write owner
    open(joinpath(mesh_dir, "owner"), "w") do f
        write_foam_header(f, "labelList", "owner")
        println(f, length(faces))
        println(f, "(")
        for face in faces
            println(f, face.owner_cell - 1)  # Convert to 0-based
        end
        println(f, ")")
    end
    
    # Write neighbour (only for internal faces)
    open(joinpath(mesh_dir, "neighbour"), "w") do f
        write_foam_header(f, "labelList", "neighbour")
        println(f, length(internal_faces))
        println(f, "(")
        for face in internal_faces
            println(f, face.neighbor_cell - 1)  # Convert to 0-based
        end
        println(f, ")")
    end
end

"""
Write OpenFOAM boundary file
"""
function write_openfoam_boundary(filename::String, patches)
    open(filename, "w") do f
        write_foam_header(f, "polyBoundaryMesh", "boundary")
        
        println(f, length(patches))
        println(f, "(")
        
        for patch in patches
            println(f, "    $(patch.name)")
            println(f, "    {")
            println(f, "        type            $(patch.type);")
            println(f, "        nFaces          $(patch.n_faces);")
            println(f, "        startFace       $(patch.start_face - 1);")  # 0-based
            println(f, "    }")
        end
        
        println(f, ")")
    end
end

"""
Write cell addressing for reconstruction
"""
function write_cell_addressing(filename::String, global_to_local::Dict{Int,Int})
    open(filename, "w") do f
        write_foam_header(f, "labelList", "cellProcAddressing")
        
        # Invert mapping to get local to global
        n_cells = length(global_to_local)
        local_to_global = zeros(Int, n_cells)
        for (global_id, local_id) in global_to_local
            local_to_global[local_id] = global_id - 1  # Convert to 0-based
        end
        
        println(f, n_cells)
        println(f, "(")
        for global_id in local_to_global
            println(f, global_id)
        end
        println(f, ")")
    end
end

# ============================================================================
# COMPREHENSIVE FIELD I/O
# ============================================================================

"""
Write a scalar field in OpenFOAM format with proper boundary conditions
"""
function write_processor_scalar_field(filename::String, field_name::String,
                                     internal_values::Vector{Float64},
                                     boundary_conditions::Dict{String, Any}=Dict{String, Any}(),
                                     dimensions::String="[0 0 0 0 0 0 0]")
    open(filename, "w") do f
        write_foam_header(f, "volScalarField", field_name)

        # Write dimensions
        println(f, "dimensions      $dimensions;")
        println(f)

        # Write internal field
        if length(unique(internal_values)) == 1
            # Uniform field
            println(f, "internalField   uniform $(internal_values[1]);")
        else
            # Non-uniform field
            println(f, "internalField   nonuniform List<scalar>")
            println(f, "$(length(internal_values))")
            println(f, "(")
            for value in internal_values
                println(f, value)
            end
            println(f, ")")
            println(f, ";")
        end
        println(f)

        # Write boundary field
        println(f, "boundaryField")
        println(f, "{")
        for (patch_name, bc_data) in boundary_conditions
            println(f, "    $patch_name")
            println(f, "    {")
            write_boundary_condition(f, bc_data)
            println(f, "    }")
        end
        println(f, "}")
    end
end

"""
Write a vector field in OpenFOAM format with proper boundary conditions
"""
function write_processor_vector_field(filename::String, field_name::String,
                                     internal_values::Vector{SVector{3,Float64}},
                                     boundary_conditions::Dict{String, Any}=Dict{String, Any}(),
                                     dimensions::String="[0 1 -1 0 0 0 0]")
    open(filename, "w") do f
        write_foam_header(f, "volVectorField", field_name)

        # Write dimensions
        println(f, "dimensions      $dimensions;")
        println(f)

        # Write internal field
        if length(unique(internal_values)) == 1
            # Uniform field
            val = internal_values[1]
            println(f, "internalField   uniform ($(val[1]) $(val[2]) $(val[3]));")
        else
            # Non-uniform field
            println(f, "internalField   nonuniform List<vector>")
            println(f, "$(length(internal_values))")
            println(f, "(")
            for value in internal_values
                println(f, "($(value[1]) $(value[2]) $(value[3]))")
            end
            println(f, ")")
            println(f, ";")
        end
        println(f)

        # Write boundary field
        println(f, "boundaryField")
        println(f, "{")
        for (patch_name, bc_data) in boundary_conditions
            println(f, "    $patch_name")
            println(f, "    {")
            write_boundary_condition(f, bc_data)
            println(f, "    }")
        end
        println(f, "}")
    end
end

"""
Write boundary condition data to file
"""
function write_boundary_condition(f::IO, bc_data::Dict{String, Any})
    bc_type = get(bc_data, "type", "zeroGradient")
    println(f, "        type            $bc_type;")

    if bc_type == "fixedValue"
        value = get(bc_data, "value", 0.0)
        if isa(value, Number)
            println(f, "        value           uniform $value;")
        elseif isa(value, AbstractVector) && length(value) == 3
            println(f, "        value           uniform ($(value[1]) $(value[2]) $(value[3]));")
        end
    elseif bc_type == "fixedGradient"
        gradient = get(bc_data, "gradient", 0.0)
        if isa(gradient, Number)
            println(f, "        gradient        uniform $gradient;")
        elseif isa(gradient, AbstractVector) && length(gradient) == 3
            println(f, "        gradient        uniform ($(gradient[1]) $(gradient[2]) $(gradient[3]));")
        end
    end

    # Add other boundary condition parameters
    for (key, value) in bc_data
        if key ∉ ["type", "value", "gradient"]
            if isa(value, String)
                println(f, "        $key            $value;")
            else
                println(f, "        $key            $value;")
            end
        end
    end
end

"""
Read a scalar field from OpenFOAM format
"""
function read_processor_scalar_field(filename::String)
    if !isfile(filename)
        error("Field file not found: $filename")
    end

    internal_values = Float64[]
    boundary_conditions = Dict{String, Any}()
    dimensions = "[0 0 0 0 0 0 0]"

    open(filename, "r") do f
        content = read(f, String)
        lines = split(content, '\n')

        # Parse dimensions
        for line in lines
            if occursin("dimensions", line)
                dim_match = match(r"dimensions\s+\[(.*?)\]", line)
                if dim_match !== nothing
                    dimensions = "[$(dim_match.captures[1])]"
                end
                break
            end
        end

        # Parse internal field
        in_internal = false
        in_list = false
        list_count = 0

        for line in lines
            line = strip(line)

            if occursin("internalField", line)
                in_internal = true
                if occursin("uniform", line)
                    # Uniform field
                    value_match = match(r"uniform\s+([-+]?[0-9]*\.?[0-9]+(?:[eE][-+]?[0-9]+)?)", line)
                    if value_match !== nothing
                        uniform_value = parse(Float64, value_match.captures[1])
                        # We don't know the size yet, will be filled later
                        internal_values = [uniform_value]
                    end
                    break
                elseif occursin("nonuniform", line)
                    # Non-uniform field
                    in_list = true
                    continue
                end
            elseif in_internal && in_list
                if occursin(r"^\d+$", line)
                    # List size
                    list_count = parse(Int, line)
                    continue
                elseif line == "("
                    continue
                elseif line == ")" || line == ");"
                    break
                elseif !isempty(line) && list_count > 0
                    # Parse value
                    try
                        value = parse(Float64, line)
                        push!(internal_values, value)
                    catch
                        # Skip invalid lines
                    end
                end
            end
        end

        # Parse boundary conditions
        boundary_conditions = parse_boundary_field(content)
    end

    return Dict(
        "internal_values" => internal_values,
        "boundary_conditions" => boundary_conditions,
        "dimensions" => dimensions,
        "field_type" => "scalar"
    )
end

"""
Read a vector field from OpenFOAM format
"""
function read_processor_vector_field(filename::String)
    if !isfile(filename)
        error("Field file not found: $filename")
    end

    internal_values = SVector{3,Float64}[]
    boundary_conditions = Dict{String, Any}()
    dimensions = "[0 1 -1 0 0 0 0]"

    open(filename, "r") do f
        content = read(f, String)
        lines = split(content, '\n')

        # Parse dimensions
        for line in lines
            if occursin("dimensions", line)
                dim_match = match(r"dimensions\s+\[(.*?)\]", line)
                if dim_match !== nothing
                    dimensions = "[$(dim_match.captures[1])]"
                end
                break
            end
        end

        # Parse internal field
        in_internal = false
        in_list = false
        list_count = 0

        for line in lines
            line = strip(line)

            if occursin("internalField", line)
                in_internal = true
                if occursin("uniform", line)
                    # Uniform field
                    vector_match = match(r"uniform\s+\(\s*([-+]?[0-9]*\.?[0-9]+(?:[eE][-+]?[0-9]+)?)\s+([-+]?[0-9]*\.?[0-9]+(?:[eE][-+]?[0-9]+)?)\s+([-+]?[0-9]*\.?[0-9]+(?:[eE][-+]?[0-9]+)?)\s*\)", line)
                    if vector_match !== nothing
                        x = parse(Float64, vector_match.captures[1])
                        y = parse(Float64, vector_match.captures[2])
                        z = parse(Float64, vector_match.captures[3])
                        uniform_value = SVector{3,Float64}(x, y, z)
                        internal_values = [uniform_value]
                    end
                    break
                elseif occursin("nonuniform", line)
                    in_list = true
                    continue
                end
            elseif in_internal && in_list
                if occursin(r"^\d+$", line)
                    list_count = parse(Int, line)
                    continue
                elseif line == "("
                    continue
                elseif line == ")" || line == ");"
                    break
                elseif occursin(r"^\(", line) && list_count > 0
                    # Parse vector value
                    vector_match = match(r"\(\s*([-+]?[0-9]*\.?[0-9]+(?:[eE][-+]?[0-9]+)?)\s+([-+]?[0-9]*\.?[0-9]+(?:[eE][-+]?[0-9]+)?)\s+([-+]?[0-9]*\.?[0-9]+(?:[eE][-+]?[0-9]+)?)\s*\)", line)
                    if vector_match !== nothing
                        x = parse(Float64, vector_match.captures[1])
                        y = parse(Float64, vector_match.captures[2])
                        z = parse(Float64, vector_match.captures[3])
                        push!(internal_values, SVector{3,Float64}(x, y, z))
                    end
                end
            end
        end

        # Parse boundary conditions
        boundary_conditions = parse_boundary_field(content)
    end

    return Dict(
        "internal_values" => internal_values,
        "boundary_conditions" => boundary_conditions,
        "dimensions" => dimensions,
        "field_type" => "vector"
    )
end

"""
Parse boundary field section from OpenFOAM file content
"""
function parse_boundary_field(content::String)
    boundary_conditions = Dict{String, Any}()
    lines = split(content, '\n')

    in_boundary = false
    current_patch = ""
    brace_count = 0
    patch_data = Dict{String, Any}()

    for line in lines
        line = strip(line)

        if occursin("boundaryField", line)
            in_boundary = true
            continue
        elseif !in_boundary
            continue
        end

        if line == "{"
            brace_count += 1
            continue
        elseif line == "}"
            brace_count -= 1
            if brace_count == 0
                # End of boundary field
                if !isempty(current_patch)
                    boundary_conditions[current_patch] = copy(patch_data)
                end
                break
            elseif brace_count == 1 && !isempty(current_patch)
                # End of current patch
                boundary_conditions[current_patch] = copy(patch_data)
                current_patch = ""
                patch_data = Dict{String, Any}()
            end
            continue
        end

        if brace_count == 1 && !occursin(r"^\s*//", line) && !isempty(line)
            # Patch name
            current_patch = line
            patch_data = Dict{String, Any}()
        elseif brace_count == 2 && !isempty(current_patch)
            # Parse patch properties
            if occursin(r"^\s*type\s+", line)
                type_match = match(r"type\s+(\w+)", line)
                if type_match !== nothing
                    patch_data["type"] = type_match.captures[1]
                end
            elseif occursin(r"^\s*value\s+", line)
                # Parse value
                if occursin("uniform", line)
                    if occursin(r"\(.*\)", line)
                        # Vector value
                        vector_match = match(r"uniform\s+\(\s*([-+]?[0-9]*\.?[0-9]+(?:[eE][-+]?[0-9]+)?)\s+([-+]?[0-9]*\.?[0-9]+(?:[eE][-+]?[0-9]+)?)\s+([-+]?[0-9]*\.?[0-9]+(?:[eE][-+]?[0-9]+)?)\s*\)", line)
                        if vector_match !== nothing
                            x = parse(Float64, vector_match.captures[1])
                            y = parse(Float64, vector_match.captures[2])
                            z = parse(Float64, vector_match.captures[3])
                            patch_data["value"] = [x, y, z]
                        end
                    else
                        # Scalar value
                        value_match = match(r"uniform\s+([-+]?[0-9]*\.?[0-9]+(?:[eE][-+]?[0-9]+)?)", line)
                        if value_match !== nothing
                            patch_data["value"] = parse(Float64, value_match.captures[1])
                        end
                    end
                end
            elseif occursin(r"^\s*gradient\s+", line)
                # Parse gradient
                if occursin("uniform", line)
                    if occursin(r"\(.*\)", line)
                        # Vector gradient
                        vector_match = match(r"uniform\s+\(\s*([-+]?[0-9]*\.?[0-9]+(?:[eE][-+]?[0-9]+)?)\s+([-+]?[0-9]*\.?[0-9]+(?:[eE][-+]?[0-9]+)?)\s+([-+]?[0-9]*\.?[0-9]+(?:[eE][-+]?[0-9]+)?)\s*\)", line)
                        if vector_match !== nothing
                            x = parse(Float64, vector_match.captures[1])
                            y = parse(Float64, vector_match.captures[2])
                            z = parse(Float64, vector_match.captures[3])
                            patch_data["gradient"] = [x, y, z]
                        end
                    else
                        # Scalar gradient
                        value_match = match(r"uniform\s+([-+]?[0-9]*\.?[0-9]+(?:[eE][-+]?[0-9]+)?)", line)
                        if value_match !== nothing
                            patch_data["gradient"] = parse(Float64, value_match.captures[1])
                        end
                    end
                end
            end
        end
    end

    return boundary_conditions
end

"""
Read a processor mesh using OpenFOAM format
"""
function read_processor_mesh(proc_dir::String)
    mesh_dir = joinpath(proc_dir, "constant", "polyMesh")

    if !isdir(mesh_dir)
        error("Processor mesh directory not found: $mesh_dir")
    end

    # Check if UnstructuredMesh is available for full reading
    if UNSTRUCTURED_MESH_AVAILABLE && isdefined(@__MODULE__, :UnstructuredMesh)
        try
            return UnstructuredMesh.import_openfoam_polymesh(mesh_dir)
        catch e
            @warn "Failed to read mesh with UnstructuredMesh: $e"
        end
    end

    # Fallback: return file paths for manual processing
    return Dict(
        "points_file" => joinpath(mesh_dir, "points"),
        "faces_file" => joinpath(mesh_dir, "faces"),
        "owner_file" => joinpath(mesh_dir, "owner"),
        "neighbour_file" => joinpath(mesh_dir, "neighbour"),
        "boundary_file" => joinpath(mesh_dir, "boundary"),
        "mesh_dir" => mesh_dir
    )
end

# ============================================================================
# UTILITY FUNCTIONS
# ============================================================================

"""
Write OpenFOAM file header
"""
function write_foam_header(io::IO, class::String, object::String)
    println(io, "/*--------------------------------*- C++ -*----------------------------------*\\")
    println(io, "  =========                 |")
    println(io, "  \\\\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox")
    println(io, "   \\\\    /   O peration     | Website:  https://openfoam.org")
    println(io, "    \\\\  /    A nd           | Version:  v2112")
    println(io, "     \\\\/     M anipulation  |")
    println(io, "\\*---------------------------------------------------------------------------*/")
    println(io, "FoamFile")
    println(io, "{")
    println(io, "    version     2.0;")
    println(io, "    format      ascii;")
    println(io, "    class       $class;")
    println(io, "    object      $object;")
    println(io, "}")
    println(io, "// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //")
    println(io)
end

# ============================================================================
# FIELD DECOMPOSITION AND RECONSTRUCTION
# ============================================================================

"""
Decompose a field from serial to parallel format
"""
function decompose_fields(case_dir::String, time_dir::String, field_names::Vector{String},
                         partition_info::Dict{String, Any}, n_processors::Int)

    decomposed_fields = Dict{String, Vector{Dict{String, Any}}}()

    for field_name in field_names
        field_file = joinpath(case_dir, time_dir, field_name)

        if !isfile(field_file)
            @warn "Field file not found: $field_file"
            continue
        end

        # Read the serial field
        field_data = if field_name in ["U", "velocity"]
            read_processor_vector_field(field_file)
        else
            read_processor_scalar_field(field_file)
        end

        # Decompose field data according to partition
        processor_fields = Vector{Dict{String, Any}}(undef, n_processors)

        for proc_id in 0:(n_processors-1)
            # Get cells for this processor
            proc_cells = get(partition_info, "processor_$proc_id", Int[])

            if field_data["field_type"] == "scalar"
                internal_values = field_data["internal_values"]
                if length(internal_values) == 1
                    # Uniform field
                    proc_internal = [internal_values[1] for _ in proc_cells]
                else
                    # Non-uniform field
                    proc_internal = [internal_values[cell_id] for cell_id in proc_cells]
                end
            else
                # Vector field
                internal_values = field_data["internal_values"]
                if length(internal_values) == 1
                    # Uniform field
                    proc_internal = [internal_values[1] for _ in proc_cells]
                else
                    # Non-uniform field
                    proc_internal = [internal_values[cell_id] for cell_id in proc_cells]
                end
            end

            processor_fields[proc_id + 1] = Dict(
                "internal_values" => proc_internal,
                "boundary_conditions" => field_data["boundary_conditions"],
                "dimensions" => field_data["dimensions"],
                "field_type" => field_data["field_type"]
            )
        end

        decomposed_fields[field_name] = processor_fields
    end

    return decomposed_fields
end

"""
Reconstruct fields from parallel to serial format
"""
function reconstruct_fields(case_dir::String, time_dir::String, field_names::Vector{String},
                           partition_info::Dict{String, Any}, n_processors::Int)

    reconstructed_fields = Dict{String, Dict{String, Any}}()

    for field_name in field_names
        # Read processor fields
        processor_field_data = Vector{Dict{String, Any}}(undef, n_processors)

        for proc_id in 0:(n_processors-1)
            proc_dir = joinpath(case_dir, "processor$proc_id")
            field_file = joinpath(proc_dir, time_dir, field_name)

            if !isfile(field_file)
                @warn "Processor field file not found: $field_file"
                continue
            end

            processor_field_data[proc_id + 1] = if field_name in ["U", "velocity"]
                read_processor_vector_field(field_file)
            else
                read_processor_scalar_field(field_file)
            end
        end

        # Reconstruct global field
        total_cells = sum(length(get(partition_info, "processor_$i", Int[])) for i in 0:(n_processors-1))

        if processor_field_data[1]["field_type"] == "scalar"
            global_values = Vector{Float64}(undef, total_cells)
        else
            global_values = Vector{SVector{3,Float64}}(undef, total_cells)
        end

        # Assemble global field from processor data
        for proc_id in 0:(n_processors-1)
            proc_cells = get(partition_info, "processor_$proc_id", Int[])
            proc_data = processor_field_data[proc_id + 1]

            for (local_idx, global_cell_id) in enumerate(proc_cells)
                global_values[global_cell_id] = proc_data["internal_values"][local_idx]
            end
        end

        reconstructed_fields[field_name] = Dict(
            "internal_values" => global_values,
            "boundary_conditions" => processor_field_data[1]["boundary_conditions"],
            "dimensions" => processor_field_data[1]["dimensions"],
            "field_type" => processor_field_data[1]["field_type"]
        )
    end

    return reconstructed_fields
end

"""
Write multiple processor fields to their respective directories
"""
function write_processor_fields(case_dir::String, time_dir::String,
                               decomposed_fields::Dict{String, Vector{Dict{String, Any}}},
                               n_processors::Int)

    for proc_id in 0:(n_processors-1)
        proc_dir = joinpath(case_dir, "processor$proc_id", time_dir)
        mkpath(proc_dir)

        for (field_name, processor_fields) in decomposed_fields
            field_data = processor_fields[proc_id + 1]
            field_file = joinpath(proc_dir, field_name)

            if field_data["field_type"] == "scalar"
                write_processor_scalar_field(
                    field_file, field_name,
                    field_data["internal_values"],
                    field_data["boundary_conditions"],
                    field_data["dimensions"]
                )
            else
                write_processor_vector_field(
                    field_file, field_name,
                    field_data["internal_values"],
                    field_data["boundary_conditions"],
                    field_data["dimensions"]
                )
            end
        end
    end
end

"""
Validate field decomposition by checking conservation properties
"""
function validate_field_decomposition(original_field::Dict{String, Any},
                                     decomposed_fields::Vector{Dict{String, Any}})

    validation_results = Dict{String, Any}()

    # Check total number of cells
    original_size = length(original_field["internal_values"])
    decomposed_size = sum(length(field["internal_values"]) for field in decomposed_fields)

    validation_results["size_conservation"] = (original_size == decomposed_size)
    validation_results["original_size"] = original_size
    validation_results["decomposed_size"] = decomposed_size

    # Check field value conservation (for uniform fields)
    if original_field["field_type"] == "scalar"
        original_sum = sum(original_field["internal_values"])
        decomposed_sum = sum(sum(field["internal_values"]) for field in decomposed_fields)
        validation_results["value_conservation"] = abs(original_sum - decomposed_sum) < 1e-12
        validation_results["original_sum"] = original_sum
        validation_results["decomposed_sum"] = decomposed_sum
    end

    return validation_results
end

# ============================================================================
# PERFORMANCE BENCHMARKING
# ============================================================================

"""
Benchmark field I/O performance
"""
function benchmark_field_io(field_data::Dict{String, Any}, filename::String)
    field_type = field_data["field_type"]

    # Write benchmark
    write_start = time()
    if field_type == "scalar"
        write_processor_scalar_field(
            filename, "testField",
            field_data["internal_values"],
            field_data["boundary_conditions"],
            field_data["dimensions"]
        )
    else
        write_processor_vector_field(
            filename, "testField",
            field_data["internal_values"],
            field_data["boundary_conditions"],
            field_data["dimensions"]
        )
    end
    write_time = time() - write_start

    # Read benchmark
    read_start = time()
    if field_type == "scalar"
        read_data = read_processor_scalar_field(filename)
    else
        read_data = read_processor_vector_field(filename)
    end
    read_time = time() - read_start

    # Calculate performance metrics
    field_size = length(field_data["internal_values"])

    return Dict(
        "write_time_seconds" => write_time,
        "read_time_seconds" => read_time,
        "field_size" => field_size,
        "write_rate_fields_per_second" => field_size / write_time,
        "read_rate_fields_per_second" => field_size / read_time,
        "total_time_seconds" => write_time + read_time
    )
end

"""
Measure mesh I/O performance (with fallback for missing UnstructuredMesh)
"""
function benchmark_mesh_io(mesh, proc_dir::String)
    # Write timing
    write_start = time()
    if UNSTRUCTURED_MESH_AVAILABLE && isdefined(mesh, :cells)
        write_processor_mesh(proc_dir, mesh, Dict(i => i for i in 1:length(mesh.cells)))
        mesh_size = length(mesh.cells)
    else
        # Fallback for testing
        mesh_size = 1000
        mkpath(proc_dir)
    end
    write_time = time() - write_start

    # Read timing
    read_start = time()
    try
        read_processor_mesh(proc_dir)
    catch e
        @warn "Read mesh failed (expected for test): $e"
    end
    read_time = time() - read_start

    return Dict(
        "write_time_seconds" => write_time,
        "read_time_seconds" => read_time,
        "mesh_size_cells" => mesh_size,
        "write_rate_cells_per_second" => mesh_size / max(write_time, 1e-6)
    )
end

end # module ParallelIO