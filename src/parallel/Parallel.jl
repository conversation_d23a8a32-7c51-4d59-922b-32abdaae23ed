"""
Parallel.jl

Main module for JuliaFOAM parallel computing capabilities.
Provides transparent parallelism for CFD simulations with domain decomposition,
load balancing, and OpenFOAM-style utilities.

This module exports all parallel functionality including:
- Domain decomposition (decomposePar)
- Reconstruction (reconstructPar)
- Redistribution (redistributePar)
- Mesh partitioning methods
- Load balancing analysis
- Command-line tools
"""

module Parallel

# Include all submodules
include("MeshPartitioning.jl")
include("ProcessorBoundaries.jl")
include("DecomposePar.jl")
include("ReconstructPar.jl")
include("RedistributePar.jl")
include("LoadBalancing.jl")
include("CLITools.jl")

# Re-export main functionality
using .MeshPartitioning
export PartitionMethod, SimplePartition, MetisPartition, ScotchPartition
export HierarchicalPartition, ManualPartition, partition_mesh
export Partition, PartitionInfo, analyze_partition_quality

using .ProcessorBoundaries
export ProcessorBoundary, ProcessorInterface, BoundaryMapping
export create_processor_boundaries, optimize_communication_pattern
export map_boundary_conditions_to_processors

using .DecomposePar
export DecomposeConfig, decompose_par, decompose_par_parallel
export write_decomposition_info, read_decomposition_info

using .ReconstructPar
export ReconstructConfig, reconstruct_par, reconstruct_par_distributed
export verify_reconstruction

using .RedistributePar
export RedistributeConfig, redistribute_par, redistribute_par_live
export analyze_load_imbalance, suggest_redistribution

using .LoadBalancing
export LoadMonitor, LoadMetrics, LoadBalanceAnalysis
export monitor_load, analyze_load_balance, suggest_rebalancing
export predict_parallel_efficiency, calculate_speedup

using .CLITools
export main_decomposePar, main_reconstructPar, main_redistributePar
export main_checkMesh, main_loadBalance

# ============================================================================
# HIGH-LEVEL INTERFACE
# ============================================================================

"""
    setup_parallel_case(case_dir::String, n_procs::Int; method=:metis)

Quick setup for parallel execution with sensible defaults.
"""
function setup_parallel_case(case_dir::String, n_procs::Int; 
                           method::Symbol=:metis, verbose::Bool=true)
    # Create partition method
    partition_method = if method == :simple
        SimplePartition(n_procs, :xyz)
    elseif method == :metis
        MetisPartition(n_procs, face_weights=true)
    elseif method == :scotch
        ScotchPartition(n_procs)
    else
        error("Unknown method: $method")
    end
    
    # Create config with defaults
    config = DecomposeConfig(
        method=partition_method,
        verbose=verbose
    )
    
    # Run decomposition
    return decompose_par(case_dir, config)
end

"""
    check_load_balance(case_dir::String, n_procs::Int)

Quick check of current load balance.
"""
function check_load_balance(case_dir::String, n_procs::Int)
    info = analyze_load_imbalance(case_dir, n_procs)
    
    println("Load Balance Status:")
    @printf("  Imbalance: %.1f%%\n", (info.imbalance_factor - 1.0) * 100)
    @printf("  Efficiency: %.1f%%\n", 100.0 / info.imbalance_factor)
    
    if info.requires_redistribution
        println("  Status: Redistribution recommended")
        println("  Suggested processors: $(info.suggested_n_procs)")
    else
        println("  Status: Good")
    end
    
    return info
end

"""
    auto_redistribute(case_dir::String, old_procs::Int, new_procs::Int)

Automatically redistribute with optimal settings.
"""
function auto_redistribute(case_dir::String, old_procs::Int, new_procs::Int)
    # Choose method based on processor count
    method = if new_procs <= 8
        SimplePartition(new_procs, :xyz)
    else
        MetisPartition(new_procs, face_weights=true, imbalance=1.03)
    end
    
    config = RedistributeConfig(
        old_n_procs=old_procs,
        new_n_procs=new_procs,
        new_method=method,
        minimize_migration=true
    )
    
    return redistribute_par(case_dir, config)
end

# ============================================================================
# CONVENIENCE FUNCTIONS
# ============================================================================

"""
Get information about current decomposition
"""
function decomposition_info(case_dir::String)
    try
        info = read_decomposition_info(case_dir)
        
        println("Decomposition Information:")
        println("  Processors: $(info.n_processors)")
        println("  Method: $(info.method)")
        println("  Case: $(info.case_dir)")
        
        if haskey(info.mesh_stats, "n_cells")
            println("  Total cells: $(info.mesh_stats["n_cells"])")
            println("  Cells per processor: $(info.mesh_stats["n_cells"] ÷ info.n_processors)")
        end
        
        quality = info.partition_info.quality_metrics
        @printf("  Load imbalance: %.1f%%\n", (quality["load_imbalance"] - 1.0) * 100)
        @printf("  Edge cut: %d\n", Int(quality["edge_cut"]))
        
        return info
    catch e
        println("No decomposition found or error reading: $e")
        return nothing
    end
end

"""
Estimate optimal processor count for a case
"""
function suggest_processor_count(case_dir::String; target_cells_per_proc::Int=25000)
    mesh_path = joinpath(case_dir, "constant", "polyMesh")
    
    # This would read actual mesh stats
    # For now, return a placeholder
    println("Processor count suggestion not yet implemented")
    println("Target cells per processor: $target_cells_per_proc")
    
    return nothing
end

# ============================================================================
# MODULE INITIALIZATION
# ============================================================================

function __init__()
    # Register command-line tools if running as script
    if !isinteractive() && length(ARGS) > 0
        cmd = ARGS[1]
        
        if cmd == "decomposePar"
            exit(main_decomposePar(ARGS[2:end]))
        elseif cmd == "reconstructPar"
            exit(main_reconstructPar(ARGS[2:end]))
        elseif cmd == "redistributePar"
            exit(main_redistributePar(ARGS[2:end]))
        elseif cmd == "checkMesh"
            exit(main_checkMesh(ARGS[2:end]))
        elseif cmd == "loadBalance"
            exit(main_loadBalance(ARGS[2:end]))
        end
    end
end

end # module Parallel