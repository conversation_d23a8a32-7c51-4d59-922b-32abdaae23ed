"""
    DistributedFields.jl

Transparent distributed field abstractions for JuliaFOAM.
Provides automatic halo exchange and parallel operations while maintaining
the same interface as serial fields.

Key features:
- Automatic halo cell synchronization
- Transparent arithmetic operations across processes
- Lazy evaluation for performance
- Compatible with existing Field interface
"""

module DistributedFields

using MPI
using LinearAlgebra
using SparseArrays
using StaticArrays

# Import types from parent modules
import ...JuliaFOAM: Field, Mesh, BoundaryCondition

export DistributedField, sync!, local_values, global_size
export is_distributed, owner_rank, get_halo_info

# ============================================================================
# DISTRIBUTED FIELD TYPE
# ============================================================================

"""
    DistributedField{T}

A distributed field that transparently handles parallel data distribution and communication.
Maintains compatibility with the serial Field interface while adding parallel capabilities.

# Type Parameters
- `T`: The data type of field values (scalar, vector, or tensor)

# Fields
- `local_field::Field{T}`: Local portion of the field including halo cells
- `mesh::Mesh`: Reference to the mesh (can be distributed or serial)
- `comm::MPI.Comm`: MPI communicator
- `rank::Int`: Process rank
- `nprocs::Int`: Number of processes
- `local_cells::Vector{Int}`: Global indices of locally owned cells
- `halo_cells::Vector{Int}`: Global indices of halo cells
- `send_list::Dict{Int, Vector{Int}}`: Map of rank -> cells to send
- `recv_list::Dict{Int, Vector{Int}}`: Map of rank -> cells to receive
- `needs_sync::Bool`: Flag indicating if halo cells need synchronization
- `sync_requests::Vector{MPI.Request}`: Active MPI requests for async operations
"""
mutable struct DistributedField{T}
    local_field::Field{T}
    mesh::Mesh
    comm::MPI.Comm
    rank::Int
    nprocs::Int
    local_cells::Vector{Int}
    halo_cells::Vector{Int}
    send_list::Dict{Int, Vector{Int}}
    recv_list::Dict{Int, Vector{Int}}
    needs_sync::Bool
    sync_requests::Vector{MPI.Request}
    
    # Constructor for creating from existing Field
    function DistributedField{T}(field::Field{T}, mesh::Mesh, comm::MPI.Comm=MPI.COMM_WORLD) where T
        rank = MPI.Comm_rank(comm)
        nprocs = MPI.Comm_size(comm)
        
        # If single process, just wrap the field
        if nprocs == 1
            return new{T}(
                field, mesh, comm, rank, nprocs,
                collect(1:length(field.values)), Int[],
                Dict{Int, Vector{Int}}(), Dict{Int, Vector{Int}}(),
                false, MPI.Request[]
            )
        end
        
        # Extract partition information from mesh
        local_cells = findall(i -> mesh.cell_partition[i] == rank, 1:length(mesh.cells))
        halo_cells = mesh.halo_cells
        
        # Build send/receive lists for halo exchange
        send_list, recv_list = build_communication_lists(mesh, rank)
        
        # Create local field with space for halo cells
        n_local = length(local_cells)
        n_halo = length(halo_cells)
        local_values = Vector{T}(undef, n_local + n_halo)
        
        # Copy local values
        for (i, global_idx) in enumerate(local_cells)
            local_values[i] = field.values[global_idx]
        end
        
        # Initialize halo values (will be synchronized)
        if T <: Number
            local_values[n_local+1:end] .= zero(T)
        else
            for i in (n_local+1):(n_local+n_halo)
                local_values[i] = zero(T)
            end
        end
        
        # Create local field
        local_field = Field{T}(
            field.name,
            mesh,
            local_values,
            field.location,
            field.boundary_values,
            nothing
        )
        
        dist_field = new{T}(
            local_field, mesh, comm, rank, nprocs,
            local_cells, halo_cells, send_list, recv_list,
            true, MPI.Request[]
        )
        
        # Initial synchronization
        sync!(dist_field)
        
        return dist_field
    end
end

# Convenience constructor
function DistributedField(field::Field{T}, mesh::Mesh, comm::MPI.Comm=MPI.COMM_WORLD) where T
    return DistributedField{T}(field, mesh, comm)
end

# Constructor for creating new distributed field
function DistributedField(name::String, mesh::Mesh, initial_value::T, 
                         location::Symbol=:cellCenters, comm::MPI.Comm=MPI.COMM_WORLD) where T
    rank = MPI.Comm_rank(comm)
    nprocs = MPI.Comm_size(comm)
    
    # Create local field
    if nprocs == 1
        # Serial case
        field = Field{T}(name, mesh, initial_value, location)
        return DistributedField{T}(field, mesh, comm)
    else
        # Parallel case
        local_cells = findall(i -> mesh.cell_partition[i] == rank, 1:length(mesh.cells))
        halo_cells = mesh.halo_cells
        
        # Create local field with space for halo cells
        n_local = length(local_cells)
        n_halo = length(halo_cells)
        local_values = fill(initial_value, n_local + n_halo)
        
        # Create boundary values for local patches
        boundary_values = Dict{String, Vector{T}}()
        for (patch_name, face_indices) in mesh.boundary_patches
            # Check if any faces in this patch belong to local cells
            local_patch_faces = filter(face_indices) do face_idx
                face = mesh.faces[face_idx]
                return mesh.cell_partition[face.owner] == rank
            end
            
            if !isempty(local_patch_faces)
                boundary_values[patch_name] = fill(initial_value, length(local_patch_faces))
            end
        end
        
        local_field = Field{T}(name, mesh, local_values, location, boundary_values)
        
        # Build communication lists
        send_list, recv_list = build_communication_lists(mesh, rank)
        
        return new{T}(
            local_field, mesh, comm, rank, nprocs,
            local_cells, halo_cells, send_list, recv_list,
            false, MPI.Request[]
        )
    end
end

# ============================================================================
# COMMUNICATION SETUP
# ============================================================================

"""
    build_communication_lists(mesh::Mesh, rank::Int)

Build send and receive lists for halo cell communication.

# Returns
- `send_list`: Dictionary mapping destination rank to local indices of cells to send
- `recv_list`: Dictionary mapping source rank to local indices of halo cells to receive into
"""
function build_communication_lists(mesh::Mesh, rank::Int)
    send_list = Dict{Int, Vector{Int}}()
    recv_list = Dict{Int, Vector{Int}}()
    
    # Build receive list: which halo cells come from which processors
    halo_to_owner = Dict{Int, Int}()
    for (i, halo_global) in enumerate(mesh.halo_cells)
        owner = mesh.cell_partition[halo_global]
        halo_to_owner[halo_global] = owner
        
        if !haskey(recv_list, owner)
            recv_list[owner] = Int[]
        end
        # Store local index in halo array (offset by number of local cells)
        n_local = count(p -> p == rank, mesh.cell_partition)
        push!(recv_list[owner], n_local + i)
    end
    
    # Build send list: which local cells need to be sent to which processors
    local_cells = findall(i -> mesh.cell_partition[i] == rank, 1:length(mesh.cells))
    
    for (local_idx, global_idx) in enumerate(local_cells)
        cell = mesh.cells[global_idx]
        
        # Check all faces of this cell
        for face_idx in cell.faces
            face = mesh.faces[face_idx]
            neighbor_cell = (face.owner == global_idx) ? face.neighbour : face.owner
            
            # If neighbor is on different processor, we need to send this cell
            if neighbor_cell > 0 && mesh.cell_partition[neighbor_cell] != rank
                neighbor_rank = mesh.cell_partition[neighbor_cell]
                
                if !haskey(send_list, neighbor_rank)
                    send_list[neighbor_rank] = Int[]
                end
                
                # Store local index (not global)
                if !(local_idx in send_list[neighbor_rank])
                    push!(send_list[neighbor_rank], local_idx)
                end
            end
        end
    end
    
    return send_list, recv_list
end

# ============================================================================
# SYNCHRONIZATION
# ============================================================================

"""
    sync!(field::DistributedField{T}) where T

Synchronize halo cells across processes. This ensures that ghost cell values
match their owner cell values on neighboring processes.
"""
function sync!(field::DistributedField{T}) where T
    # Skip if single process or no sync needed
    if field.nprocs == 1 || !field.needs_sync
        return
    end
    
    # Wait for any pending async operations
    if !isempty(field.sync_requests)
        MPI.Waitall!(field.sync_requests)
        empty!(field.sync_requests)
    end
    
    # Prepare send/receive buffers
    send_buffers = Dict{Int, Vector{T}}()
    recv_buffers = Dict{Int, Vector{T}}()
    send_requests = MPI.Request[]
    recv_requests = MPI.Request[]
    
    # Post receives first (to avoid deadlock)
    for (source_rank, recv_indices) in field.recv_list
        recv_buffer = Vector{T}(undef, length(recv_indices))
        recv_buffers[source_rank] = recv_buffer
        
        req = MPI.Irecv!(recv_buffer, source_rank, 0, field.comm)
        push!(recv_requests, req)
    end
    
    # Pack and send data
    for (dest_rank, send_indices) in field.send_list
        send_buffer = field.local_field.values[send_indices]
        send_buffers[dest_rank] = send_buffer
        
        req = MPI.Isend(send_buffer, dest_rank, 0, field.comm)
        push!(send_requests, req)
    end
    
    # Wait for all receives to complete
    MPI.Waitall!(recv_requests)
    
    # Unpack received data into halo cells
    for (source_rank, recv_indices) in field.recv_list
        recv_buffer = recv_buffers[source_rank]
        for (i, local_idx) in enumerate(recv_indices)
            field.local_field.values[local_idx] = recv_buffer[i]
        end
    end
    
    # Wait for sends to complete (cleanup)
    MPI.Waitall!(send_requests)
    
    # Mark as synchronized
    field.needs_sync = false
end

"""
    sync_async!(field::DistributedField{T}) where T

Start asynchronous halo cell synchronization. Returns immediately and allows
computation on interior cells while communication happens in background.
Call `wait_sync!` to complete the synchronization.
"""
function sync_async!(field::DistributedField{T}) where T
    # Skip if single process
    if field.nprocs == 1
        return
    end
    
    # Clear any previous requests
    if !isempty(field.sync_requests)
        MPI.Waitall!(field.sync_requests)
        empty!(field.sync_requests)
    end
    
    # Post receives
    recv_buffers = Dict{Int, Vector{T}}()
    for (source_rank, recv_indices) in field.recv_list
        recv_buffer = Vector{T}(undef, length(recv_indices))
        recv_buffers[source_rank] = recv_buffer
        
        req = MPI.Irecv!(recv_buffer, source_rank, 0, field.comm)
        push!(field.sync_requests, req)
    end
    
    # Post sends
    for (dest_rank, send_indices) in field.send_list
        send_buffer = field.local_field.values[send_indices]
        
        req = MPI.Isend(send_buffer, dest_rank, 0, field.comm)
        push!(field.sync_requests, req)
    end
    
    # Store receive buffers for later unpacking
    field.local_field.old_time_field = recv_buffers  # Temporary storage hack
end

"""
    wait_sync!(field::DistributedField{T}) where T

Wait for asynchronous synchronization to complete and unpack received data.
"""
function wait_sync!(field::DistributedField{T}) where T
    if isempty(field.sync_requests)
        return
    end
    
    # Wait for all operations
    MPI.Waitall!(field.sync_requests)
    empty!(field.sync_requests)
    
    # Unpack received data
    if field.local_field.old_time_field isa Dict
        recv_buffers = field.local_field.old_time_field
        
        for (source_rank, recv_indices) in field.recv_list
            recv_buffer = recv_buffers[source_rank]
            for (i, local_idx) in enumerate(recv_indices)
                field.local_field.values[local_idx] = recv_buffer[i]
            end
        end
        
        field.local_field.old_time_field = nothing
    end
    
    field.needs_sync = false
end

# ============================================================================
# FIELD INTERFACE
# ============================================================================

# Forward most methods to local field
Base.length(field::DistributedField) = length(field.local_cells)
Base.size(field::DistributedField) = (length(field.local_cells),)
Base.eltype(::DistributedField{T}) where T = T

"""
    local_values(field::DistributedField{T}) where T

Get the local values array (including halo cells). This is useful for
operations that need direct access to the data.
"""
local_values(field::DistributedField{T}) where T = field.local_field.values

"""
    global_size(field::DistributedField)

Get the global size of the distributed field across all processes.
"""
function global_size(field::DistributedField)
    local_size = length(field.local_cells)
    return MPI.Allreduce(local_size, +, field.comm)
end

"""
    is_distributed(field::DistributedField)

Check if field is actually distributed (more than one process).
"""
is_distributed(field::DistributedField) = field.nprocs > 1

"""
    owner_rank(field::DistributedField, global_idx::Int)

Get the rank that owns a particular global cell index.
"""
function owner_rank(field::DistributedField, global_idx::Int)
    return field.mesh.cell_partition[global_idx]
end

"""
    get_halo_info(field::DistributedField)

Get information about halo cells for debugging/visualization.
"""
function get_halo_info(field::DistributedField)
    return (
        n_local = length(field.local_cells),
        n_halo = length(field.halo_cells),
        send_neighbors = collect(keys(field.send_list)),
        recv_neighbors = collect(keys(field.recv_list)),
        total_send = sum(length(v) for v in values(field.send_list); init=0),
        total_recv = sum(length(v) for v in values(field.recv_list); init=0)
    )
end

# ============================================================================
# ARRAY INTERFACE
# ============================================================================

# Indexing (local indices only for now)
function Base.getindex(field::DistributedField{T}, i::Int) where T
    if i < 1 || i > length(field.local_cells)
        throw(BoundsError(field, i))
    end
    return field.local_field.values[i]
end

function Base.setindex!(field::DistributedField{T}, v::T, i::Int) where T
    if i < 1 || i > length(field.local_cells)
        throw(BoundsError(field, i))
    end
    field.local_field.values[i] = v
    field.needs_sync = true
    return v
end

# ============================================================================
# ARITHMETIC OPERATIONS
# ============================================================================

# Binary operations that create new distributed fields
for op in (:+, :-, :*, :/)
    @eval begin
        function Base.$op(a::DistributedField{T}, b::DistributedField{T}) where T
            # Ensure fields are synchronized
            sync!(a)
            sync!(b)
            
            # Perform operation on local data
            local_result = $op.(a.local_field.values, b.local_field.values)
            
            # Create new field with result
            result_field = Field{T}(
                "$(a.local_field.name)_$($op)_$(b.local_field.name)",
                a.mesh,
                local_result,
                a.local_field.location,
                Dict{String, Vector{T}}()  # Boundary values need proper handling
            )
            
            # Wrap in distributed field
            result = DistributedField{T}(result_field, a.mesh, a.comm)
            result.local_cells = a.local_cells
            result.halo_cells = a.halo_cells
            result.send_list = a.send_list
            result.recv_list = a.recv_list
            result.needs_sync = false  # Already synchronized
            
            return result
        end
        
        # Scalar operations
        function Base.$op(a::DistributedField{T}, b::Number) where T
            sync!(a)
            
            local_result = $op.(a.local_field.values, b)
            
            result_field = Field{T}(
                "$(a.local_field.name)_$($op)_scalar",
                a.mesh,
                local_result,
                a.local_field.location,
                Dict{String, Vector{T}}()
            )
            
            result = DistributedField{T}(result_field, a.mesh, a.comm)
            result.local_cells = a.local_cells
            result.halo_cells = a.halo_cells
            result.send_list = a.send_list
            result.recv_list = a.recv_list
            result.needs_sync = false
            
            return result
        end
        
        function Base.$op(a::Number, b::DistributedField{T}) where T
            sync!(b)
            
            local_result = $op.(a, b.local_field.values)
            
            result_field = Field{T}(
                "scalar_$($op)_$(b.local_field.name)",
                b.mesh,
                local_result,
                b.local_field.location,
                Dict{String, Vector{T}}()
            )
            
            result = DistributedField{T}(result_field, b.mesh, b.comm)
            result.local_cells = b.local_cells
            result.halo_cells = b.halo_cells
            result.send_list = b.send_list
            result.recv_list = b.recv_list
            result.needs_sync = false
            
            return result
        end
    end
end

# In-place operations
for op in (:+=, :-=, :*=, :/=)
    @eval begin
        function Base.$op(a::DistributedField{T}, b::DistributedField{T}) where T
            sync!(a)
            sync!(b)
            
            # Get the base operation (e.g., + from +=)
            base_op = Symbol(String($op)[1:end-1])
            
            # Apply operation to local values
            a.local_field.values .= $(base_op).(a.local_field.values, b.local_field.values)
            a.needs_sync = true
            
            return a
        end
        
        function Base.$op(a::DistributedField{T}, b::Number) where T
            sync!(a)
            
            base_op = Symbol(String($op)[1:end-1])
            a.local_field.values .= $(base_op).(a.local_field.values, b)
            a.needs_sync = true
            
            return a
        end
    end
end

# ============================================================================
# REDUCTIONS
# ============================================================================

"""
    sum(field::DistributedField{T}) where T

Compute global sum across all processes.
"""
function Base.sum(field::DistributedField{T}) where T
    sync!(field)
    
    # Sum only local cells (not halo)
    local_sum = sum(field.local_field.values[1:length(field.local_cells)])
    
    # Global reduction
    return MPI.Allreduce(local_sum, +, field.comm)
end

"""
    maximum(field::DistributedField{T}) where T

Compute global maximum across all processes.
"""
function Base.maximum(field::DistributedField{T}) where T
    sync!(field)
    
    # Max only local cells
    local_max = maximum(field.local_field.values[1:length(field.local_cells)])
    
    # Global reduction
    return MPI.Allreduce(local_max, max, field.comm)
end

"""
    minimum(field::DistributedField{T}) where T

Compute global minimum across all processes.
"""
function Base.minimum(field::DistributedField{T}) where T
    sync!(field)
    
    # Min only local cells
    local_min = minimum(field.local_field.values[1:length(field.local_cells)])
    
    # Global reduction
    return MPI.Allreduce(local_min, min, field.comm)
end

"""
    norm(field::DistributedField{T}, p::Real=2) where T

Compute global norm across all processes.
"""
function LinearAlgebra.norm(field::DistributedField{T}, p::Real=2) where T
    sync!(field)
    
    local_values = field.local_field.values[1:length(field.local_cells)]
    
    if p == 2
        # L2 norm
        local_sum_sq = sum(abs2, local_values)
        global_sum_sq = MPI.Allreduce(local_sum_sq, +, field.comm)
        return sqrt(global_sum_sq)
    elseif p == Inf
        # L∞ norm
        return maximum(field)
    else
        # General Lp norm
        local_sum_p = sum(x -> abs(x)^p, local_values)
        global_sum_p = MPI.Allreduce(local_sum_p, +, field.comm)
        return global_sum_p^(1/p)
    end
end

# ============================================================================
# BROADCASTING
# ============================================================================

# Custom broadcasting for distributed fields
struct DistributedFieldStyle <: Base.Broadcast.BroadcastStyle end

Base.BroadcastStyle(::Type{<:DistributedField}) = DistributedFieldStyle()

# Promotion rules for broadcasting
Base.BroadcastStyle(::DistributedFieldStyle, ::Base.Broadcast.DefaultArrayStyle{0}) = DistributedFieldStyle()
Base.BroadcastStyle(::DistributedFieldStyle, ::Base.Broadcast.DefaultArrayStyle{1}) = DistributedFieldStyle()

# Broadcasting implementation
function Base.broadcasted(::DistributedFieldStyle, f, args...)
    # Extract distributed fields and ensure they're synchronized
    dist_fields = filter(a -> a isa DistributedField, args)
    for field in dist_fields
        sync!(field)
    end
    
    # Get reference field for structure
    ref_field = first(dist_fields)
    
    # Convert arguments to local arrays
    local_args = map(args) do arg
        if arg isa DistributedField
            arg.local_field.values
        else
            arg
        end
    end
    
    # Broadcast on local data
    local_result = broadcast(f, local_args...)
    
    # Create result field
    T = eltype(local_result)
    result_field = Field{T}(
        "broadcast_result",
        ref_field.mesh,
        local_result,
        ref_field.local_field.location,
        Dict{String, Vector{T}}()
    )
    
    # Wrap in distributed field
    result = DistributedField{T}(result_field, ref_field.mesh, ref_field.comm)
    result.local_cells = ref_field.local_cells
    result.halo_cells = ref_field.halo_cells
    result.send_list = ref_field.send_list
    result.recv_list = ref_field.recv_list
    result.needs_sync = true
    
    return result
end

# ============================================================================
# UTILITIES
# ============================================================================

"""
    gather(field::DistributedField{T}, root::Int=0) where T

Gather distributed field to a single process. Returns the complete field
on the root process and nothing on others.
"""
function gather(field::DistributedField{T}, root::Int=0) where T
    sync!(field)
    
    if field.nprocs == 1
        return field.local_field
    end
    
    # Gather sizes first
    local_size = length(field.local_cells)
    sizes = MPI.Gather(local_size, root, field.comm)
    
    # Gather data
    local_data = field.local_field.values[1:local_size]
    
    if field.rank == root
        # Calculate displacements
        total_size = sum(sizes)
        displacements = cumsum([0; sizes[1:end-1]])
        
        # Receive buffer
        global_data = Vector{T}(undef, total_size)
        
        # Gather
        MPI.Gatherv!(local_data, global_data, sizes, displacements, root, field.comm)
        
        # Create global field
        global_field = Field{T}(
            field.local_field.name,
            field.mesh,
            global_data,
            field.local_field.location
        )
        
        return global_field
    else
        MPI.Gatherv!(local_data, nothing, nothing, nothing, root, field.comm)
        return nothing
    end
end

"""
    scatter!(global_field::Field{T}, dist_field::DistributedField{T}, root::Int=0) where T

Scatter a global field from root process to distributed field.
"""
function scatter!(global_field::Field{T}, dist_field::DistributedField{T}, root::Int=0) where T
    if dist_field.nprocs == 1
        dist_field.local_field.values .= global_field.values
        return
    end
    
    # Gather sizes
    local_size = length(dist_field.local_cells)
    sizes = MPI.Gather(local_size, root, dist_field.comm)
    
    if dist_field.rank == root
        # Calculate displacements
        displacements = cumsum([0; sizes[1:end-1]])
        
        # Scatter
        MPI.Scatterv!(global_field.values, dist_field.local_field.values[1:local_size], 
                      sizes, displacements, root, dist_field.comm)
    else
        MPI.Scatterv!(nothing, dist_field.local_field.values[1:local_size], 
                      nothing, nothing, root, dist_field.comm)
    end
    
    dist_field.needs_sync = true
    sync!(dist_field)
end

end # module DistributedFields