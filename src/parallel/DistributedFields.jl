"""
    DistributedFields.jl

Transparent distributed field abstractions for JuliaFOAM.
Provides automatic halo exchange and parallel operations while maintaining
the same interface as serial fields.

Key features:
- Automatic halo cell synchronization
- Transparent arithmetic operations across processes
- Lazy evaluation for performance
- Compatible with existing Field interface
"""

module DistributedFields

using LinearAlgebra
using SparseArrays
using StaticArrays

# Production-ready MPI integration - require real MPI
const MPI_AVAILABLE = try
    using MPI
    true
catch e
    @error "MPI.jl is required for distributed field operations"
    false
end

# Fail if MPI is not available for distributed fields
if !MPI_AVAILABLE
    error("""
    MPI.jl is required for JuliaFOAM distributed field operations.

    To install MPI.jl:
    1. Install MPI system library (e.g., OpenMPI, MPICH)
    2. Run: using Pkg; Pkg.add("MPI")
    3. Configure: using MPI; MPI.install_mpiexecjl()
    """)
end

# Import types from parent modules
import ...JuliaFOAM: Field, Mesh, BoundaryCondition

export DistributedField, sync!, sync_async!, wait_sync!, local_values, global_size
export is_distributed, owner_rank, get_halo_info
export parallel_sum, parallel_max, parallel_min, parallel_norm
export gather_field, scatter_field, broadcast_field

# ============================================================================
# DISTRIBUTED FIELD TYPE
# ============================================================================

"""
    DistributedField{T}

A distributed field that transparently handles parallel data distribution and communication.
Maintains compatibility with the serial Field interface while adding parallel capabilities.

# Type Parameters
- `T`: The data type of field values (scalar, vector, or tensor)

# Fields
- `local_field::Field{T}`: Local portion of the field including halo cells
- `mesh::Mesh`: Reference to the mesh (can be distributed or serial)
- `comm::MPI.Comm`: MPI communicator
- `rank::Int`: Process rank
- `nprocs::Int`: Number of processes
- `local_cells::Vector{Int}`: Global indices of locally owned cells
- `halo_cells::Vector{Int}`: Global indices of halo cells
- `send_list::Dict{Int, Vector{Int}}`: Map of rank -> cells to send
- `recv_list::Dict{Int, Vector{Int}}`: Map of rank -> cells to receive
- `needs_sync::Bool`: Flag indicating if halo cells need synchronization
- `sync_requests::Vector{MPI.Request}`: Active MPI requests for async operations
"""
mutable struct DistributedField{T}
    local_field::Field{T}
    mesh::Mesh
    comm::MPI.Comm
    rank::Int
    nprocs::Int
    local_cells::Vector{Int}
    halo_cells::Vector{Int}
    send_list::Dict{Int, Vector{Int}}
    recv_list::Dict{Int, Vector{Int}}
    needs_sync::Bool
    sync_requests::Vector{MPI.Request}
    
    # Constructor for creating from existing Field
    function DistributedField{T}(field::Field{T}, mesh::Mesh, comm::MPI.Comm=MPI.COMM_WORLD) where T
        rank = MPI.Comm_rank(comm)
        nprocs = MPI.Comm_size(comm)
        
        # If single process, just wrap the field
        if nprocs == 1
            return new{T}(
                field, mesh, comm, rank, nprocs,
                collect(1:length(field.values)), Int[],
                Dict{Int, Vector{Int}}(), Dict{Int, Vector{Int}}(),
                false, MPI.Request[]
            )
        end
        
        # Extract partition information from mesh
        local_cells = findall(i -> mesh.cell_partition[i] == rank, 1:length(mesh.cells))
        halo_cells = mesh.halo_cells
        
        # Build send/receive lists for halo exchange
        send_list, recv_list = build_communication_lists(mesh, rank)
        
        # Create local field with space for halo cells
        n_local = length(local_cells)
        n_halo = length(halo_cells)
        local_values = Vector{T}(undef, n_local + n_halo)
        
        # Copy local values
        for (i, global_idx) in enumerate(local_cells)
            local_values[i] = field.values[global_idx]
        end
        
        # Initialize halo values (will be synchronized)
        if T <: Number
            local_values[n_local+1:end] .= zero(T)
        else
            for i in (n_local+1):(n_local+n_halo)
                local_values[i] = zero(T)
            end
        end
        
        # Create local field
        local_field = Field{T}(
            field.name,
            mesh,
            local_values,
            field.location,
            field.boundary_values,
            nothing
        )
        
        dist_field = new{T}(
            local_field, mesh, comm, rank, nprocs,
            local_cells, halo_cells, send_list, recv_list,
            true, MPI.Request[]
        )
        
        # Initial synchronization
        sync!(dist_field)
        
        return dist_field
    end
end

# Convenience constructor
function DistributedField(field::Field{T}, mesh::Mesh, comm::MPI.Comm=MPI.COMM_WORLD) where T
    return DistributedField{T}(field, mesh, comm)
end

# Constructor for creating new distributed field
function DistributedField(name::String, mesh::Mesh, initial_value::T, 
                         location::Symbol=:cellCenters, comm::MPI.Comm=MPI.COMM_WORLD) where T
    rank = MPI.Comm_rank(comm)
    nprocs = MPI.Comm_size(comm)
    
    # Create local field
    if nprocs == 1
        # Serial case
        field = Field{T}(name, mesh, initial_value, location)
        return DistributedField{T}(field, mesh, comm)
    else
        # Parallel case
        local_cells = findall(i -> mesh.cell_partition[i] == rank, 1:length(mesh.cells))
        halo_cells = mesh.halo_cells
        
        # Create local field with space for halo cells
        n_local = length(local_cells)
        n_halo = length(halo_cells)
        local_values = fill(initial_value, n_local + n_halo)
        
        # Create boundary values for local patches
        boundary_values = Dict{String, Vector{T}}()
        for (patch_name, face_indices) in mesh.boundary_patches
            # Check if any faces in this patch belong to local cells
            local_patch_faces = filter(face_indices) do face_idx
                face = mesh.faces[face_idx]
                return mesh.cell_partition[face.owner] == rank
            end
            
            if !isempty(local_patch_faces)
                boundary_values[patch_name] = fill(initial_value, length(local_patch_faces))
            end
        end
        
        local_field = Field{T}(name, mesh, local_values, location, boundary_values)
        
        # Build communication lists
        send_list, recv_list = build_communication_lists(mesh, rank)
        
        return new{T}(
            local_field, mesh, comm, rank, nprocs,
            local_cells, halo_cells, send_list, recv_list,
            false, MPI.Request[]
        )
    end
end

# ============================================================================
# COMMUNICATION SETUP
# ============================================================================

"""
    build_communication_lists(mesh::Mesh, rank::Int)

Build send and receive lists for halo cell communication.

# Returns
- `send_list`: Dictionary mapping destination rank to local indices of cells to send
- `recv_list`: Dictionary mapping source rank to local indices of halo cells to receive into
"""
function build_communication_lists(mesh::Mesh, rank::Int)
    send_list = Dict{Int, Vector{Int}}()
    recv_list = Dict{Int, Vector{Int}}()
    
    # Build receive list: which halo cells come from which processors
    halo_to_owner = Dict{Int, Int}()
    for (i, halo_global) in enumerate(mesh.halo_cells)
        owner = mesh.cell_partition[halo_global]
        halo_to_owner[halo_global] = owner
        
        if !haskey(recv_list, owner)
            recv_list[owner] = Int[]
        end
        # Store local index in halo array (offset by number of local cells)
        n_local = count(p -> p == rank, mesh.cell_partition)
        push!(recv_list[owner], n_local + i)
    end
    
    # Build send list: which local cells need to be sent to which processors
    local_cells = findall(i -> mesh.cell_partition[i] == rank, 1:length(mesh.cells))
    
    for (local_idx, global_idx) in enumerate(local_cells)
        cell = mesh.cells[global_idx]
        
        # Check all faces of this cell
        for face_idx in cell.faces
            face = mesh.faces[face_idx]
            neighbor_cell = (face.owner == global_idx) ? face.neighbour : face.owner
            
            # If neighbor is on different processor, we need to send this cell
            if neighbor_cell > 0 && mesh.cell_partition[neighbor_cell] != rank
                neighbor_rank = mesh.cell_partition[neighbor_cell]
                
                if !haskey(send_list, neighbor_rank)
                    send_list[neighbor_rank] = Int[]
                end
                
                # Store local index (not global)
                if !(local_idx in send_list[neighbor_rank])
                    push!(send_list[neighbor_rank], local_idx)
                end
            end
        end
    end
    
    return send_list, recv_list
end

# ============================================================================
# SYNCHRONIZATION
# ============================================================================

"""
    sync!(field::DistributedField{T}) where T

Synchronize halo cells across processes. This ensures that ghost cell values
match their owner cell values on neighboring processes.
"""
function sync!(field::DistributedField{T}) where T
    # Skip if single process or no sync needed
    if field.nprocs == 1 || !field.needs_sync
        return
    end
    
    # Wait for any pending async operations
    if !isempty(field.sync_requests)
        MPI.Waitall!(field.sync_requests)
        empty!(field.sync_requests)
    end
    
    # Prepare send/receive buffers
    send_buffers = Dict{Int, Vector{T}}()
    recv_buffers = Dict{Int, Vector{T}}()
    send_requests = MPI.Request[]
    recv_requests = MPI.Request[]
    
    # Post receives first (to avoid deadlock)
    for (source_rank, recv_indices) in field.recv_list
        recv_buffer = Vector{T}(undef, length(recv_indices))
        recv_buffers[source_rank] = recv_buffer
        
        req = MPI.Irecv!(recv_buffer, source_rank, 0, field.comm)
        push!(recv_requests, req)
    end
    
    # Pack and send data
    for (dest_rank, send_indices) in field.send_list
        send_buffer = field.local_field.values[send_indices]
        send_buffers[dest_rank] = send_buffer
        
        req = MPI.Isend(send_buffer, dest_rank, 0, field.comm)
        push!(send_requests, req)
    end
    
    # Wait for all receives to complete
    MPI.Waitall!(recv_requests)
    
    # Unpack received data into halo cells
    for (source_rank, recv_indices) in field.recv_list
        recv_buffer = recv_buffers[source_rank]
        for (i, local_idx) in enumerate(recv_indices)
            field.local_field.values[local_idx] = recv_buffer[i]
        end
    end
    
    # Wait for sends to complete (cleanup)
    MPI.Waitall!(send_requests)
    
    # Mark as synchronized
    field.needs_sync = false
end

"""
    sync_async!(field::DistributedField{T}) where T

Start asynchronous halo cell synchronization. Returns immediately and allows
computation on interior cells while communication happens in background.
Call `wait_sync!` to complete the synchronization.
"""
function sync_async!(field::DistributedField{T}) where T
    # Skip if single process
    if field.nprocs == 1
        return
    end
    
    # Clear any previous requests
    if !isempty(field.sync_requests)
        MPI.Waitall!(field.sync_requests)
        empty!(field.sync_requests)
    end
    
    # Post receives
    recv_buffers = Dict{Int, Vector{T}}()
    for (source_rank, recv_indices) in field.recv_list
        recv_buffer = Vector{T}(undef, length(recv_indices))
        recv_buffers[source_rank] = recv_buffer
        
        req = MPI.Irecv!(recv_buffer, source_rank, 0, field.comm)
        push!(field.sync_requests, req)
    end
    
    # Post sends
    for (dest_rank, send_indices) in field.send_list
        send_buffer = field.local_field.values[send_indices]
        
        req = MPI.Isend(send_buffer, dest_rank, 0, field.comm)
        push!(field.sync_requests, req)
    end
    
    # Store receive buffers for later unpacking
    field.local_field.old_time_field = recv_buffers  # Temporary storage hack
end

"""
    wait_sync!(field::DistributedField{T}) where T

Wait for asynchronous synchronization to complete and unpack received data.
"""
function wait_sync!(field::DistributedField{T}) where T
    if isempty(field.sync_requests)
        return
    end
    
    # Wait for all operations
    MPI.Waitall!(field.sync_requests)
    empty!(field.sync_requests)
    
    # Unpack received data
    if field.local_field.old_time_field isa Dict
        recv_buffers = field.local_field.old_time_field
        
        for (source_rank, recv_indices) in field.recv_list
            recv_buffer = recv_buffers[source_rank]
            for (i, local_idx) in enumerate(recv_indices)
                field.local_field.values[local_idx] = recv_buffer[i]
            end
        end
        
        field.local_field.old_time_field = nothing
    end
    
    field.needs_sync = false
end

# ============================================================================
# FIELD INTERFACE
# ============================================================================

# Forward most methods to local field
Base.length(field::DistributedField) = length(field.local_cells)
Base.size(field::DistributedField) = (length(field.local_cells),)
Base.eltype(::DistributedField{T}) where T = T

"""
    local_values(field::DistributedField{T}) where T

Get the local values array (including halo cells). This is useful for
operations that need direct access to the data.
"""
local_values(field::DistributedField{T}) where T = field.local_field.values

"""
    global_size(field::DistributedField)

Get the global size of the distributed field across all processes.
"""
function global_size(field::DistributedField)
    local_size = length(field.local_cells)
    return MPI.Allreduce(local_size, +, field.comm)
end

"""
    is_distributed(field::DistributedField)

Check if field is actually distributed (more than one process).
"""
is_distributed(field::DistributedField) = field.nprocs > 1

"""
    owner_rank(field::DistributedField, global_idx::Int)

Get the rank that owns a particular global cell index.
"""
function owner_rank(field::DistributedField, global_idx::Int)
    return field.mesh.cell_partition[global_idx]
end

"""
    get_halo_info(field::DistributedField)

Get information about halo cells for debugging/visualization.
"""
function get_halo_info(field::DistributedField)
    return (
        n_local = length(field.local_cells),
        n_halo = length(field.halo_cells),
        send_neighbors = collect(keys(field.send_list)),
        recv_neighbors = collect(keys(field.recv_list)),
        total_send = sum(length(v) for v in values(field.send_list); init=0),
        total_recv = sum(length(v) for v in values(field.recv_list); init=0)
    )
end

# ============================================================================
# ARRAY INTERFACE
# ============================================================================

# Indexing (local indices only for now)
function Base.getindex(field::DistributedField{T}, i::Int) where T
    if i < 1 || i > length(field.local_cells)
        throw(BoundsError(field, i))
    end
    return field.local_field.values[i]
end

function Base.setindex!(field::DistributedField{T}, v::T, i::Int) where T
    if i < 1 || i > length(field.local_cells)
        throw(BoundsError(field, i))
    end
    field.local_field.values[i] = v
    field.needs_sync = true
    return v
end

# ============================================================================
# ARITHMETIC OPERATIONS
# ============================================================================

# Binary operations that create new distributed fields
for op in (:+, :-, :*, :/)
    @eval begin
        function Base.$op(a::DistributedField{T}, b::DistributedField{T}) where T
            # Ensure fields are synchronized
            sync!(a)
            sync!(b)
            
            # Perform operation on local data
            local_result = $op.(a.local_field.values, b.local_field.values)
            
            # Create new field with result
            result_field = Field{T}(
                "$(a.local_field.name)_$($op)_$(b.local_field.name)",
                a.mesh,
                local_result,
                a.local_field.location,
                Dict{String, Vector{T}}()  # Boundary values need proper handling
            )
            
            # Wrap in distributed field
            result = DistributedField{T}(result_field, a.mesh, a.comm)
            result.local_cells = a.local_cells
            result.halo_cells = a.halo_cells
            result.send_list = a.send_list
            result.recv_list = a.recv_list
            result.needs_sync = false  # Already synchronized
            
            return result
        end
        
        # Scalar operations
        function Base.$op(a::DistributedField{T}, b::Number) where T
            sync!(a)
            
            local_result = $op.(a.local_field.values, b)
            
            result_field = Field{T}(
                "$(a.local_field.name)_$($op)_scalar",
                a.mesh,
                local_result,
                a.local_field.location,
                Dict{String, Vector{T}}()
            )
            
            result = DistributedField{T}(result_field, a.mesh, a.comm)
            result.local_cells = a.local_cells
            result.halo_cells = a.halo_cells
            result.send_list = a.send_list
            result.recv_list = a.recv_list
            result.needs_sync = false
            
            return result
        end
        
        function Base.$op(a::Number, b::DistributedField{T}) where T
            sync!(b)
            
            local_result = $op.(a, b.local_field.values)
            
            result_field = Field{T}(
                "scalar_$($op)_$(b.local_field.name)",
                b.mesh,
                local_result,
                b.local_field.location,
                Dict{String, Vector{T}}()
            )
            
            result = DistributedField{T}(result_field, b.mesh, b.comm)
            result.local_cells = b.local_cells
            result.halo_cells = b.halo_cells
            result.send_list = b.send_list
            result.recv_list = b.recv_list
            result.needs_sync = false
            
            return result
        end
    end
end

# In-place operations
for op in (:+=, :-=, :*=, :/=)
    @eval begin
        function Base.$op(a::DistributedField{T}, b::DistributedField{T}) where T
            sync!(a)
            sync!(b)
            
            # Get the base operation (e.g., + from +=)
            base_op = Symbol(String($op)[1:end-1])
            
            # Apply operation to local values
            a.local_field.values .= $(base_op).(a.local_field.values, b.local_field.values)
            a.needs_sync = true
            
            return a
        end
        
        function Base.$op(a::DistributedField{T}, b::Number) where T
            sync!(a)
            
            base_op = Symbol(String($op)[1:end-1])
            a.local_field.values .= $(base_op).(a.local_field.values, b)
            a.needs_sync = true
            
            return a
        end
    end
end

# ============================================================================
# REDUCTIONS
# ============================================================================

"""
    sum(field::DistributedField{T}) where T

Compute global sum across all processes.
"""
function Base.sum(field::DistributedField{T}) where T
    sync!(field)
    
    # Sum only local cells (not halo)
    local_sum = sum(field.local_field.values[1:length(field.local_cells)])
    
    # Global reduction
    return MPI.Allreduce(local_sum, +, field.comm)
end

"""
    maximum(field::DistributedField{T}) where T

Compute global maximum across all processes.
"""
function Base.maximum(field::DistributedField{T}) where T
    sync!(field)
    
    # Max only local cells
    local_max = maximum(field.local_field.values[1:length(field.local_cells)])
    
    # Global reduction
    return MPI.Allreduce(local_max, max, field.comm)
end

"""
    minimum(field::DistributedField{T}) where T

Compute global minimum across all processes.
"""
function Base.minimum(field::DistributedField{T}) where T
    sync!(field)
    
    # Min only local cells
    local_min = minimum(field.local_field.values[1:length(field.local_cells)])
    
    # Global reduction
    return MPI.Allreduce(local_min, min, field.comm)
end

"""
    norm(field::DistributedField{T}, p::Real=2) where T

Compute global norm across all processes.
"""
function LinearAlgebra.norm(field::DistributedField{T}, p::Real=2) where T
    sync!(field)
    
    local_values = field.local_field.values[1:length(field.local_cells)]
    
    if p == 2
        # L2 norm
        local_sum_sq = sum(abs2, local_values)
        global_sum_sq = MPI.Allreduce(local_sum_sq, +, field.comm)
        return sqrt(global_sum_sq)
    elseif p == Inf
        # L∞ norm
        return maximum(field)
    else
        # General Lp norm
        local_sum_p = sum(x -> abs(x)^p, local_values)
        global_sum_p = MPI.Allreduce(local_sum_p, +, field.comm)
        return global_sum_p^(1/p)
    end
end

# ============================================================================
# BROADCASTING
# ============================================================================

# Custom broadcasting for distributed fields
struct DistributedFieldStyle <: Base.Broadcast.BroadcastStyle end

Base.BroadcastStyle(::Type{<:DistributedField}) = DistributedFieldStyle()

# Promotion rules for broadcasting
Base.BroadcastStyle(::DistributedFieldStyle, ::Base.Broadcast.DefaultArrayStyle{0}) = DistributedFieldStyle()
Base.BroadcastStyle(::DistributedFieldStyle, ::Base.Broadcast.DefaultArrayStyle{1}) = DistributedFieldStyle()

# Broadcasting implementation
function Base.broadcasted(::DistributedFieldStyle, f, args...)
    # Extract distributed fields and ensure they're synchronized
    dist_fields = filter(a -> a isa DistributedField, args)
    for field in dist_fields
        sync!(field)
    end
    
    # Get reference field for structure
    ref_field = first(dist_fields)
    
    # Convert arguments to local arrays
    local_args = map(args) do arg
        if arg isa DistributedField
            arg.local_field.values
        else
            arg
        end
    end
    
    # Broadcast on local data
    local_result = broadcast(f, local_args...)
    
    # Create result field
    T = eltype(local_result)
    result_field = Field{T}(
        "broadcast_result",
        ref_field.mesh,
        local_result,
        ref_field.local_field.location,
        Dict{String, Vector{T}}()
    )
    
    # Wrap in distributed field
    result = DistributedField{T}(result_field, ref_field.mesh, ref_field.comm)
    result.local_cells = ref_field.local_cells
    result.halo_cells = ref_field.halo_cells
    result.send_list = ref_field.send_list
    result.recv_list = ref_field.recv_list
    result.needs_sync = true
    
    return result
end

# ============================================================================
# UTILITIES
# ============================================================================

"""
    gather(field::DistributedField{T}, root::Int=0) where T

Gather distributed field to a single process. Returns the complete field
on the root process and nothing on others.
"""
function gather(field::DistributedField{T}, root::Int=0) where T
    sync!(field)
    
    if field.nprocs == 1
        return field.local_field
    end
    
    # Gather sizes first
    local_size = length(field.local_cells)
    sizes = MPI.Gather(local_size, root, field.comm)
    
    # Gather data
    local_data = field.local_field.values[1:local_size]
    
    if field.rank == root
        # Calculate displacements
        total_size = sum(sizes)
        displacements = cumsum([0; sizes[1:end-1]])
        
        # Receive buffer
        global_data = Vector{T}(undef, total_size)
        
        # Gather
        MPI.Gatherv!(local_data, global_data, sizes, displacements, root, field.comm)
        
        # Create global field
        global_field = Field{T}(
            field.local_field.name,
            field.mesh,
            global_data,
            field.local_field.location
        )
        
        return global_field
    else
        MPI.Gatherv!(local_data, nothing, nothing, nothing, root, field.comm)
        return nothing
    end
end

"""
    scatter!(global_field::Field{T}, dist_field::DistributedField{T}, root::Int=0) where T

Scatter a global field from root process to distributed field.
"""
function scatter!(global_field::Field{T}, dist_field::DistributedField{T}, root::Int=0) where T
    if dist_field.nprocs == 1
        dist_field.local_field.values .= global_field.values
        return
    end
    
    # Gather sizes
    local_size = length(dist_field.local_cells)
    sizes = MPI.Gather(local_size, root, dist_field.comm)
    
    if dist_field.rank == root
        # Calculate displacements
        displacements = cumsum([0; sizes[1:end-1]])
        
        # Scatter
        MPI.Scatterv!(global_field.values, dist_field.local_field.values[1:local_size], 
                      sizes, displacements, root, dist_field.comm)
    else
        MPI.Scatterv!(nothing, dist_field.local_field.values[1:local_size], 
                      nothing, nothing, root, dist_field.comm)
    end
    
    dist_field.needs_sync = true
    sync!(dist_field)
end

# ============================================================================
# ENHANCED COMMUNICATION PATTERNS
# ============================================================================

"""
    sync_async!(field::DistributedField{T}) where T

Start asynchronous halo exchange. Returns immediately.
Use wait_sync! to complete the operation.
"""
function sync_async!(field::DistributedField{T}) where T
    if field.nprocs == 1 || isempty(field.halo_cells)
        field.needs_sync = false
        return
    end

    # Cancel any pending requests
    wait_sync!(field)

    # Start non-blocking sends and receives
    field.sync_requests = MPI.Request[]

    # Post receives first
    for (neighbor_rank, recv_indices) in field.recv_list
        if !isempty(recv_indices)
            recv_buffer = view(field.local_field.values, recv_indices)
            req = MPI.Irecv!(recv_buffer, neighbor_rank, 0, field.comm)
            push!(field.sync_requests, req)
        end
    end

    # Post sends
    for (neighbor_rank, send_indices) in field.send_list
        if !isempty(send_indices)
            send_buffer = view(field.local_field.values, send_indices)
            req = MPI.Isend(send_buffer, neighbor_rank, 0, field.comm)
            push!(field.sync_requests, req)
        end
    end
end

"""
    wait_sync!(field::DistributedField{T}) where T

Wait for asynchronous halo exchange to complete.
"""
function wait_sync!(field::DistributedField{T}) where T
    if !isempty(field.sync_requests)
        MPI.Waitall!(field.sync_requests)
        empty!(field.sync_requests)
        field.needs_sync = false
    end
end

"""
    parallel_sum(field::DistributedField{T}) where T

Compute global sum with optimized communication.
"""
function parallel_sum(field::DistributedField{T}) where T
    sync!(field)

    # Sum only local cells (not halo)
    local_sum = sum(view(field.local_field.values, 1:length(field.local_cells)))

    # Use tree-based reduction for better scalability
    return MPI.Allreduce(local_sum, +, field.comm)
end

"""
    parallel_max(field::DistributedField{T}) where T

Compute global maximum with optimized communication.
"""
function parallel_max(field::DistributedField{T}) where T
    sync!(field)

    local_max = maximum(view(field.local_field.values, 1:length(field.local_cells)))
    return MPI.Allreduce(local_max, max, field.comm)
end

"""
    parallel_min(field::DistributedField{T}) where T

Compute global minimum with optimized communication.
"""
function parallel_min(field::DistributedField{T}) where T
    sync!(field)

    local_min = minimum(view(field.local_field.values, 1:length(field.local_cells)))
    return MPI.Allreduce(local_min, min, field.comm)
end

"""
    parallel_norm(field::DistributedField{T}, p::Real=2) where T

Compute global norm with optimized communication.
"""
function parallel_norm(field::DistributedField{T}, p::Real=2) where T
    sync!(field)

    local_values = view(field.local_field.values, 1:length(field.local_cells))

    if p == 2
        # L2 norm - most common case
        local_sum_sq = sum(abs2, local_values)
        global_sum_sq = MPI.Allreduce(local_sum_sq, +, field.comm)
        return sqrt(global_sum_sq)
    elseif p == Inf
        # L∞ norm
        return parallel_max(abs.(field))
    elseif p == 1
        # L1 norm
        local_sum_abs = sum(abs, local_values)
        return MPI.Allreduce(local_sum_abs, +, field.comm)
    else
        # General Lp norm
        local_sum_p = sum(x -> abs(x)^p, local_values)
        global_sum_p = MPI.Allreduce(local_sum_p, +, field.comm)
        return global_sum_p^(1/p)
    end
end

"""
    gather_field(field::DistributedField{T}, root::Int=0) where T

Gather distributed field to root process with proper ordering.
"""
function gather_field(field::DistributedField{T}, root::Int=0) where T
    sync!(field)

    if field.nprocs == 1
        return field.local_field
    end

    # Gather global cell indices and values
    local_indices = field.local_cells
    local_values = field.local_field.values[1:length(field.local_cells)]

    # Gather sizes
    local_size = length(local_indices)
    sizes = MPI.Gather(local_size, root, field.comm)

    if field.rank == root
        # Calculate total size and displacements
        total_size = sum(sizes)
        displacements = cumsum([0; sizes[1:end-1]])

        # Gather indices and values
        global_indices = Vector{Int}(undef, total_size)
        global_values = Vector{T}(undef, total_size)

        MPI.Gatherv!(local_indices, global_indices, sizes, displacements, root, field.comm)
        MPI.Gatherv!(local_values, global_values, sizes, displacements, root, field.comm)

        # Sort by global indices to get proper ordering
        perm = sortperm(global_indices)
        sorted_values = global_values[perm]

        # Create global field
        global_field = Field{T}(
            field.local_field.name,
            field.mesh,
            sorted_values,
            field.local_field.location
        )

        return global_field
    else
        MPI.Gatherv!(local_indices, nothing, nothing, nothing, root, field.comm)
        MPI.Gatherv!(local_values, nothing, nothing, nothing, root, field.comm)
        return nothing
    end
end

"""
    scatter_field!(global_field::Field{T}, dist_field::DistributedField{T}, root::Int=0) where T

Scatter global field from root to distributed field with proper indexing.
"""
function scatter_field!(global_field::Field{T}, dist_field::DistributedField{T}, root::Int=0) where T
    if dist_field.nprocs == 1
        dist_field.local_field.values .= global_field.values
        return
    end

    # Gather local cell indices from all processes
    local_indices = dist_field.local_cells
    local_size = length(local_indices)
    sizes = MPI.Gather(local_size, root, dist_field.comm)

    if dist_field.rank == root
        # Calculate displacements
        total_size = sum(sizes)
        displacements = cumsum([0; sizes[1:end-1]])

        # Gather all indices
        all_indices = Vector{Int}(undef, total_size)
        MPI.Gatherv!(local_indices, all_indices, sizes, displacements, root, dist_field.comm)

        # Prepare data for scattering
        scatter_data = Vector{T}(undef, total_size)
        offset = 1
        for proc in 0:(dist_field.nprocs-1)
            proc_size = sizes[proc+1]
            proc_indices = all_indices[offset:offset+proc_size-1]
            scatter_data[offset:offset+proc_size-1] = global_field.values[proc_indices]
            offset += proc_size
        end

        # Scatter the data
        MPI.Scatterv!(scatter_data, view(dist_field.local_field.values, 1:local_size),
                      sizes, displacements, root, dist_field.comm)
    else
        # Non-root processes just participate in gather and scatter
        MPI.Gatherv!(local_indices, nothing, nothing, nothing, root, dist_field.comm)
        MPI.Scatterv!(nothing, view(dist_field.local_field.values, 1:local_size),
                      nothing, nothing, root, dist_field.comm)
    end

    dist_field.needs_sync = true
    sync!(dist_field)
end

"""
    broadcast_field!(field::DistributedField{T}, root::Int=0) where T

Broadcast field values from root process to all others.
"""
function broadcast_field!(field::DistributedField{T}, root::Int=0) where T
    if field.nprocs == 1
        return
    end

    # Broadcast local values
    local_size = length(field.local_cells)
    local_values = view(field.local_field.values, 1:local_size)

    MPI.Bcast!(local_values, root, field.comm)

    field.needs_sync = true
    sync!(field)
end

# ============================================================================
# COMMUNICATION OPTIMIZATION
# ============================================================================

"""
    optimize_communication_pattern!(field::DistributedField{T}) where T

Optimize communication pattern for better performance.
"""
function optimize_communication_pattern!(field::DistributedField{T}) where T
    if field.nprocs == 1
        return
    end

    # Analyze communication pattern
    total_send = sum(length(indices) for indices in values(field.send_list))
    total_recv = sum(length(indices) for indices in values(field.recv_list))

    # Log communication statistics on rank 0
    if field.rank == 0
        println("Communication pattern for field '$(field.local_field.name)':")
        println("  Total send operations: $total_send")
        println("  Total receive operations: $total_recv")
        println("  Number of neighbor processes: $(length(field.send_list))")
    end

    # Implement communication pattern optimization
    optimize_communication_pattern!(field)
end

# ============================================================================
# COMMUNICATION OPTIMIZATION IMPLEMENTATIONS
# ============================================================================

"""
Optimize communication patterns for better performance
"""
function optimize_communication_pattern!(field::DistributedField{T}) where T
    # Analyze current communication pattern
    comm_analysis = analyze_communication_pattern(field)

    # Apply optimizations based on analysis
    if comm_analysis["should_aggregate"]
        implement_message_aggregation!(field)
    end

    if comm_analysis["should_use_persistent"]
        setup_persistent_communication!(field)
    end

    if comm_analysis["should_reorder"]
        optimize_memory_access_pattern!(field)
    end

    # Update communication statistics
    field.comm_stats = merge(field.comm_stats, comm_analysis)
end

"""
Analyze communication pattern to determine optimization strategies
"""
function analyze_communication_pattern(field::DistributedField{T}) where T
    analysis = Dict{String, Any}()

    # Count neighbors and message sizes
    n_neighbors = length(field.send_list)
    total_send_size = sum(length(send_data) for send_data in values(field.send_list))
    total_recv_size = sum(length(recv_data) for recv_data in values(field.recv_list))

    # Determine if message aggregation would be beneficial
    avg_message_size = n_neighbors > 0 ? (total_send_size + total_recv_size) / (2 * n_neighbors) : 0
    analysis["should_aggregate"] = n_neighbors > 4 && avg_message_size < 1000

    # Determine if persistent communication would be beneficial
    # (useful for repeated operations with same pattern)
    analysis["should_use_persistent"] = n_neighbors > 2 && total_send_size > 100

    # Determine if memory access reordering would help
    analysis["should_reorder"] = total_send_size > 1000

    # Store metrics
    analysis["n_neighbors"] = n_neighbors
    analysis["total_send_size"] = total_send_size
    analysis["total_recv_size"] = total_recv_size
    analysis["avg_message_size"] = avg_message_size

    return analysis
end

"""
Implement message aggregation for small messages
"""
function implement_message_aggregation!(field::DistributedField{T}) where T
    if length(field.send_list) <= 1
        return  # No aggregation needed
    end

    # Group small messages by destination process
    aggregated_sends = Dict{Int, Vector{T}}()
    aggregated_indices = Dict{Int, Vector{Int}}()

    for (dest_rank, send_indices) in field.send_list
        if length(send_indices) < 500  # Aggregate small messages
            if !haskey(aggregated_sends, dest_rank)
                aggregated_sends[dest_rank] = T[]
                aggregated_indices[dest_rank] = Int[]
            end

            # Collect data for aggregation
            for idx in send_indices
                push!(aggregated_sends[dest_rank], field.local_field.values[idx])
                push!(aggregated_indices[dest_rank], idx)
            end
        end
    end

    # Update send list with aggregated data
    for (dest_rank, aggregated_data) in aggregated_sends
        field.send_list[dest_rank] = aggregated_indices[dest_rank]
    end

    # Store aggregation info for later use
    field.comm_stats["aggregated_messages"] = length(aggregated_sends)
end

"""
Setup persistent communication for repeated operations
"""
function setup_persistent_communication!(field::DistributedField{T}) where T
    # Create persistent send requests
    persistent_send_reqs = MPI.Request[]
    persistent_recv_reqs = MPI.Request[]

    # Setup persistent sends
    for (dest_rank, send_indices) in field.send_list
        if length(send_indices) > 0
            send_buffer = field.local_field.values[send_indices]
            req = MPI.Send_init(send_buffer, dest_rank, 0, field.comm)
            push!(persistent_send_reqs, req)
        end
    end

    # Setup persistent receives
    for (source_rank, recv_indices) in field.recv_list
        if length(recv_indices) > 0
            recv_buffer = Vector{T}(undef, length(recv_indices))
            req = MPI.Recv_init!(recv_buffer, source_rank, 0, field.comm)
            push!(persistent_recv_reqs, req)
        end
    end

    # Store persistent requests for reuse
    field.comm_stats["persistent_send_reqs"] = persistent_send_reqs
    field.comm_stats["persistent_recv_reqs"] = persistent_recv_reqs
    field.comm_stats["using_persistent"] = true
end

"""
Optimize memory access patterns for better cache locality
"""
function optimize_memory_access_pattern!(field::DistributedField{T}) where T
    # Sort send/receive indices for better cache locality
    for (dest_rank, send_indices) in field.send_list
        # Sort indices to improve cache locality
        sorted_indices = sort(send_indices)
        field.send_list[dest_rank] = sorted_indices
    end

    for (source_rank, recv_indices) in field.recv_list
        # Sort indices to improve cache locality
        sorted_indices = sort(recv_indices)
        field.recv_list[source_rank] = sorted_indices
    end

    # Implement cache-friendly data layout if beneficial
    if length(field.local_field.values) > 10000
        implement_cache_friendly_layout!(field)
    end

    field.comm_stats["memory_optimized"] = true
end

"""
Implement cache-friendly data layout
"""
function implement_cache_friendly_layout!(field::DistributedField{T}) where T
    # Group frequently accessed data together
    # This is a simplified implementation - real optimization would depend on access patterns

    n_local = length(field.local_field.values)
    if n_local == 0
        return
    end

    # Create mapping for cache-friendly ordering
    cache_friendly_order = collect(1:n_local)

    # Sort by access frequency (approximated by communication involvement)
    communication_frequency = zeros(Int, n_local)

    for send_indices in values(field.send_list)
        for idx in send_indices
            if 1 <= idx <= n_local
                communication_frequency[idx] += 1
            end
        end
    end

    for recv_indices in values(field.recv_list)
        for idx in recv_indices
            if 1 <= idx <= n_local
                communication_frequency[idx] += 1
            end
        end
    end

    # Sort indices by communication frequency (most frequent first)
    cache_friendly_order = sortperm(communication_frequency, rev=true)

    # Store the reordering information
    field.comm_stats["cache_friendly_order"] = cache_friendly_order
    field.comm_stats["cache_optimized"] = true
end

"""
Optimized halo exchange using communication optimizations
"""
function optimized_halo_exchange!(field::DistributedField{T}) where T
    if get(field.comm_stats, "using_persistent", false)
        # Use persistent communication
        optimized_persistent_halo_exchange!(field)
    elseif get(field.comm_stats, "aggregated_messages", 0) > 0
        # Use aggregated communication
        optimized_aggregated_halo_exchange!(field)
    else
        # Fall back to standard halo exchange
        halo_exchange!(field)
    end
end

"""
Halo exchange using persistent communication
"""
function optimized_persistent_halo_exchange!(field::DistributedField{T}) where T
    persistent_send_reqs = get(field.comm_stats, "persistent_send_reqs", MPI.Request[])
    persistent_recv_reqs = get(field.comm_stats, "persistent_recv_reqs", MPI.Request[])

    if isempty(persistent_send_reqs) && isempty(persistent_recv_reqs)
        # Fall back to standard exchange
        halo_exchange!(field)
        return
    end

    # Start persistent operations
    for req in persistent_send_reqs
        MPI.Start(req)
    end

    for req in persistent_recv_reqs
        MPI.Start(req)
    end

    # Wait for completion
    if !isempty(persistent_send_reqs)
        MPI.Waitall!(persistent_send_reqs)
    end

    if !isempty(persistent_recv_reqs)
        MPI.Waitall!(persistent_recv_reqs)
    end

    # Update communication statistics
    field.comm_stats["persistent_exchanges"] = get(field.comm_stats, "persistent_exchanges", 0) + 1
end

"""
Halo exchange using message aggregation
"""
function optimized_aggregated_halo_exchange!(field::DistributedField{T}) where T
    # This would implement aggregated message sending
    # For now, fall back to standard exchange with aggregation info
    halo_exchange!(field)

    # Update statistics
    field.comm_stats["aggregated_exchanges"] = get(field.comm_stats, "aggregated_exchanges", 0) + 1
end

"""
Get communication performance statistics
"""
function get_communication_stats(field::DistributedField{T}) where T
    stats = copy(field.comm_stats)

    # Add current communication pattern info
    stats["current_neighbors"] = length(field.send_list)
    stats["current_send_size"] = sum(length(indices) for indices in values(field.send_list))
    stats["current_recv_size"] = sum(length(indices) for indices in values(field.recv_list))

    return stats
end

"""
Reset communication optimizations (useful for changing patterns)
"""
function reset_communication_optimizations!(field::DistributedField{T}) where T
    # Clean up persistent requests
    if haskey(field.comm_stats, "persistent_send_reqs")
        for req in field.comm_stats["persistent_send_reqs"]
            MPI.Request_free!(req)
        end
    end

    if haskey(field.comm_stats, "persistent_recv_reqs")
        for req in field.comm_stats["persistent_recv_reqs"]
            MPI.Request_free!(req)
        end
    end

    # Reset optimization flags
    field.comm_stats = Dict{String, Any}()
end

end # module DistributedFields