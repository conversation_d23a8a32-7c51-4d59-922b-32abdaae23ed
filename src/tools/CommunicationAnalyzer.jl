"""
    Communication Pattern Analysis for JuliaFOAM Parallel Infrastructure

This module provides comprehensive analysis of MPI communication patterns,
load balancing efficiency, and communication/computation overlap opportunities.
"""
module CommunicationAnalyzer

using MPI
using StaticArrays
using LinearAlgebra
using Statistics
using Printf
using Dates

# Import JuliaFOAM types
import ..JuliaFOAM: OptimizedMesh, Field

export CommunicationProfile, LoadBalanceMetrics, OverlapAnalysis
export analyze_communication_patterns, profile_halo_exchange, analyze_load_balance
export identify_overlap_opportunities, create_communication_report
export benchmark_communication_patterns, optimize_communication_schedule

"""
    CommunicationProfile

Structure to store communication profiling data.
"""
mutable struct CommunicationProfile
    name::String
    start_time::DateTime
    end_time::Union{DateTime, Nothing}
    
    # Message statistics
    message_counts::Dict{Int, Int}          # neighbor_rank -> message_count
    message_sizes::Dict{Int, Vector{Int}}   # neighbor_rank -> [sizes...]
    message_latencies::Dict{Int, Vector{Float64}}  # neighbor_rank -> [latencies...]
    
    # Communication timing
    send_times::Dict{Int, Vector{Float64}}  # neighbor_rank -> [send_times...]
    recv_times::Dict{Int, Vector{Float64}}  # neighbor_rank -> [recv_times...]
    wait_times::Dict{Int, Vector{Float64}}  # neighbor_rank -> [wait_times...]
    
    # Bandwidth measurements
    effective_bandwidth::Dict{Int, Float64}  # neighbor_rank -> bandwidth (MB/s)
    peak_bandwidth::Float64
    
    # Communication patterns
    communication_volume::Float64           # Total bytes communicated
    communication_time::Float64             # Total communication time
    overlap_potential::Float64              # Potential for computation overlap (0-1)
    
    # Load balance metrics
    load_imbalance::Float64                 # Load imbalance factor
    communication_imbalance::Float64        # Communication imbalance factor
end

function CommunicationProfile(name::String)
    return CommunicationProfile(
        name, now(), nothing,
        Dict{Int, Int}(), Dict{Int, Vector{Int}}(), Dict{Int, Vector{Float64}}(),
        Dict{Int, Vector{Float64}}(), Dict{Int, Vector{Float64}}(), Dict{Int, Vector{Float64}}(),
        Dict{Int, Float64}(), 0.0,
        0.0, 0.0, 0.0,
        0.0, 0.0
    )
end

"""
    LoadBalanceMetrics

Structure to store load balancing analysis results.
"""
struct LoadBalanceMetrics
    profile_name::String
    n_processes::Int
    
    # Work distribution
    work_per_process::Vector{Float64}       # Work units per process
    work_imbalance::Float64                 # Coefficient of variation
    max_work_ratio::Float64                 # max_work / avg_work
    
    # Communication distribution
    comm_volume_per_process::Vector{Float64}  # Communication volume per process
    comm_imbalance::Float64                 # Communication imbalance factor
    
    # Efficiency metrics
    parallel_efficiency::Float64           # Overall parallel efficiency
    load_balance_efficiency::Float64       # Load balance component
    communication_efficiency::Float64      # Communication component
    
    # Recommendations
    recommendations::Vector{String}
end

"""
    OverlapAnalysis

Structure to store computation-communication overlap analysis.
"""
struct OverlapAnalysis
    profile_name::String
    
    # Timing breakdown
    total_time::Float64
    computation_time::Float64
    communication_time::Float64
    overlap_time::Float64
    idle_time::Float64
    
    # Overlap metrics
    overlap_ratio::Float64                  # overlap_time / communication_time
    efficiency_gain::Float64               # Potential speedup from perfect overlap
    current_overlap_efficiency::Float64    # Current overlap effectiveness
    
    # Bottleneck analysis
    communication_bottlenecks::Vector{Tuple{Int, String, Float64}}  # (rank, type, time)
    computation_bottlenecks::Vector{Tuple{String, Float64}}         # (operation, time)
    
    # Optimization opportunities
    overlap_opportunities::Vector{String}
end

"""
    analyze_communication_patterns(mesh::OptimizedMesh, comm::MPI.Comm; 
                                  profile_name="comm_analysis")

Analyze communication patterns in the mesh for the given MPI communicator.
"""
function analyze_communication_patterns(mesh::OptimizedMesh, comm::MPI.Comm; 
                                       profile_name="comm_analysis")
    profile = CommunicationProfile(profile_name)
    rank = MPI.Comm_rank(comm)
    size = MPI.Comm_size(comm)
    
    # Analyze send/receive maps
    total_send_volume = 0
    total_recv_volume = 0
    
    # Analyze send patterns
    for (neighbor_rank, send_indices) in mesh.send_maps
        if neighbor_rank == rank
            continue  # Skip self-communication
        end
        
        message_size = length(send_indices) * sizeof(Float64)  # Assume Float64 fields
        total_send_volume += message_size
        
        # Initialize tracking for this neighbor
        if !haskey(profile.message_counts, neighbor_rank)
            profile.message_counts[neighbor_rank] = 0
            profile.message_sizes[neighbor_rank] = Int[]
        end
        
        profile.message_counts[neighbor_rank] += 1
        push!(profile.message_sizes[neighbor_rank], message_size)
    end
    
    # Analyze receive patterns
    for (neighbor_rank, recv_indices) in mesh.recv_maps
        if neighbor_rank == rank
            continue  # Skip self-communication
        end
        
        message_size = length(recv_indices) * sizeof(Float64)
        total_recv_volume += message_size
    end
    
    # Calculate communication volume and imbalance
    profile.communication_volume = max(total_send_volume, total_recv_volume)
    
    # Gather communication volumes from all processes
    all_comm_volumes = MPI.Allgather(profile.communication_volume, comm)
    
    if length(all_comm_volumes) > 1
        avg_comm_volume = mean(all_comm_volumes)
        profile.communication_imbalance = std(all_comm_volumes) / max(avg_comm_volume, 1e-10)
    else
        profile.communication_imbalance = 0.0
    end
    
    # Analyze mesh load balance
    n_local_cells = length(mesh.cells)
    all_cell_counts = MPI.Allgather(n_local_cells, comm)
    
    if length(all_cell_counts) > 1
        avg_cells = mean(all_cell_counts)
        profile.load_imbalance = std(all_cell_counts) / max(avg_cells, 1e-10)
    else
        profile.load_imbalance = 0.0
    end
    
    profile.end_time = now()
    return profile
end

"""
    profile_halo_exchange(field::Vector{T}, mesh::OptimizedMesh, comm::MPI.Comm;
                         n_iterations=10, profile_name="halo_profile") where T

Profile halo exchange operations to measure communication performance.
"""
function profile_halo_exchange(field::Vector{T}, mesh::OptimizedMesh, comm::MPI.Comm;
                              n_iterations=10, profile_name="halo_profile") where T
    profile = CommunicationProfile(profile_name)
    rank = MPI.Comm_rank(comm)
    
    # Warm-up run
    _perform_halo_exchange(field, mesh, comm)
    
    # Profile multiple iterations
    total_comm_time = 0.0
    
    for iter in 1:n_iterations
        start_time = time()
        _perform_halo_exchange(field, mesh, comm)
        end_time = time()
        
        iteration_time = end_time - start_time
        total_comm_time += iteration_time
        
        # Record timing for each neighbor
        for (neighbor_rank, _) in mesh.send_maps
            if neighbor_rank == rank
                continue
            end
            
            if !haskey(profile.send_times, neighbor_rank)
                profile.send_times[neighbor_rank] = Float64[]
            end
            push!(profile.send_times[neighbor_rank], iteration_time / length(mesh.send_maps))
        end
    end
    
    profile.communication_time = total_comm_time / n_iterations
    
    # Calculate effective bandwidth
    for (neighbor_rank, send_indices) in mesh.send_maps
        if neighbor_rank == rank || !haskey(profile.send_times, neighbor_rank)
            continue
        end
        
        message_size = length(send_indices) * sizeof(T)
        avg_time = mean(profile.send_times[neighbor_rank])
        
        if avg_time > 0
            profile.effective_bandwidth[neighbor_rank] = (message_size / avg_time) / (1024^2)  # MB/s
        end
    end
    
    if !isempty(profile.effective_bandwidth)
        profile.peak_bandwidth = maximum(values(profile.effective_bandwidth))
    end
    
    profile.end_time = now()
    return profile
end

"""
    _perform_halo_exchange(field::Vector{T}, mesh::OptimizedMesh, comm::MPI.Comm) where T

Internal function to perform halo exchange for profiling.
"""
function _perform_halo_exchange(field::Vector{T}, mesh::OptimizedMesh, comm::MPI.Comm) where T
    rank = MPI.Comm_rank(comm)
    
    # Simple halo exchange implementation for profiling
    send_requests = MPI.Request[]
    recv_requests = MPI.Request[]
    recv_buffers = Dict{Int, Vector{T}}()
    
    # Post receives first
    for (neighbor_rank, recv_indices) in mesh.recv_maps
        if neighbor_rank == rank
            continue
        end
        
        recv_buffers[neighbor_rank] = Vector{T}(undef, length(recv_indices))
        request = MPI.Irecv!(recv_buffers[neighbor_rank], neighbor_rank, 0, comm)
        push!(recv_requests, request)
    end
    
    # Post sends
    for (neighbor_rank, send_indices) in mesh.send_maps
        if neighbor_rank == rank
            continue
        end
        
        send_buffer = field[send_indices]
        request = MPI.Isend(send_buffer, neighbor_rank, 0, comm)
        push!(send_requests, request)
    end
    
    # Wait for completion
    if !isempty(recv_requests)
        MPI.Waitall!(recv_requests)
    end
    if !isempty(send_requests)
        MPI.Waitall!(send_requests)
    end
    
    # Update field with received data
    for (neighbor_rank, recv_indices) in mesh.recv_maps
        if neighbor_rank == rank || !haskey(recv_buffers, neighbor_rank)
            continue
        end
        
        field[recv_indices] .= recv_buffers[neighbor_rank]
    end
end

"""
    analyze_load_balance(mesh::OptimizedMesh, comm::MPI.Comm, work_weights::Vector{Float64};
                        profile_name="load_balance_analysis")

Analyze load balancing efficiency across processes.
"""
function analyze_load_balance(mesh::OptimizedMesh, comm::MPI.Comm, work_weights::Vector{Float64};
                             profile_name="load_balance_analysis")
    rank = MPI.Comm_rank(comm)
    size = MPI.Comm_size(comm)
    
    # Calculate local work
    local_work = sum(work_weights)
    n_local_cells = length(mesh.cells)
    
    # Gather work distribution from all processes
    all_work = MPI.Allgather(local_work, comm)
    all_cell_counts = MPI.Allgather(n_local_cells, comm)
    
    # Calculate work imbalance
    avg_work = mean(all_work)
    work_imbalance = std(all_work) / max(avg_work, 1e-10)
    max_work_ratio = maximum(all_work) / max(avg_work, 1e-10)
    
    # Calculate communication volume per process
    comm_volumes = Float64[]
    for p in 0:(size-1)
        if p == rank
            # Calculate local communication volume
            local_comm_volume = 0.0
            for (neighbor_rank, send_indices) in mesh.send_maps
                if neighbor_rank != rank
                    local_comm_volume += length(send_indices) * sizeof(Float64)
                end
            end
            push!(comm_volumes, local_comm_volume)
        else
            push!(comm_volumes, 0.0)  # Will be filled by Allgather
        end
    end
    
    # Gather communication volumes
    all_comm_volumes = MPI.Allgather(comm_volumes[rank+1], comm)
    
    # Calculate communication imbalance
    avg_comm_volume = mean(all_comm_volumes)
    comm_imbalance = std(all_comm_volumes) / max(avg_comm_volume, 1e-10)
    
    # Calculate efficiency metrics
    load_balance_efficiency = 1.0 / max_work_ratio
    communication_efficiency = 1.0 - min(comm_imbalance, 1.0)
    parallel_efficiency = load_balance_efficiency * communication_efficiency
    
    # Generate recommendations
    recommendations = String[]
    
    if work_imbalance > 0.1
        push!(recommendations, "High work imbalance detected ($(round(work_imbalance*100, digits=1))%). Consider mesh repartitioning.")
    end
    
    if comm_imbalance > 0.2
        push!(recommendations, "High communication imbalance detected ($(round(comm_imbalance*100, digits=1))%). Review mesh decomposition strategy.")
    end
    
    if max_work_ratio > 1.5
        push!(recommendations, "Maximum work is $(round(max_work_ratio, digits=2))x average. Load balancing needed.")
    end
    
    if parallel_efficiency < 0.7
        push!(recommendations, "Low parallel efficiency ($(round(parallel_efficiency*100, digits=1))%). Optimize load balance and communication.")
    end
    
    return LoadBalanceMetrics(
        profile_name, size,
        all_work, work_imbalance, max_work_ratio,
        all_comm_volumes, comm_imbalance,
        parallel_efficiency, load_balance_efficiency, communication_efficiency,
        recommendations
    )
end

"""
    identify_overlap_opportunities(mesh::OptimizedMesh, comm::MPI.Comm,
                                 computation_func::Function, field::Vector{T};
                                 profile_name="overlap_analysis") where T

Identify opportunities for computation-communication overlap.
"""
function identify_overlap_opportunities(mesh::OptimizedMesh, comm::MPI.Comm,
                                      computation_func::Function, field::Vector{T};
                                      profile_name="overlap_analysis") where T
    rank = MPI.Comm_rank(comm)

    # Measure pure computation time
    comp_start = time()
    computation_func(field, mesh)
    comp_time = time() - comp_start

    # Measure pure communication time
    comm_start = time()
    _perform_halo_exchange(field, mesh, comm)
    comm_time = time() - comm_start

    # Measure overlapped execution
    overlap_start = time()
    overlap_time_actual = _measure_overlap_execution(field, mesh, comm, computation_func)
    total_overlap_time = time() - overlap_start

    # Calculate overlap metrics
    total_time = comp_time + comm_time
    overlap_ratio = overlap_time_actual / max(comm_time, 1e-10)
    efficiency_gain = (total_time - total_overlap_time) / max(total_time, 1e-10)
    current_overlap_efficiency = overlap_time_actual / max(min(comp_time, comm_time), 1e-10)

    # Identify bottlenecks
    communication_bottlenecks = Tuple{Int, String, Float64}[]
    computation_bottlenecks = Tuple{String, Float64}[]

    # Analyze communication bottlenecks
    for (neighbor_rank, send_indices) in mesh.send_maps
        if neighbor_rank == rank
            continue
        end

        message_size = length(send_indices) * sizeof(T)
        estimated_time = message_size / (100 * 1024^2)  # Assume 100 MB/s baseline

        if estimated_time > comm_time * 0.1  # If this neighbor takes >10% of comm time
            push!(communication_bottlenecks, (neighbor_rank, "large_message", estimated_time))
        end
    end

    # Analyze computation bottlenecks (simplified)
    if comp_time > comm_time * 2
        push!(computation_bottlenecks, ("computation_heavy", comp_time))
    elseif comm_time > comp_time * 2
        push!(computation_bottlenecks, ("communication_heavy", comm_time))
    end

    # Generate optimization opportunities
    overlap_opportunities = String[]

    if overlap_ratio < 0.5
        push!(overlap_opportunities, "Low overlap ratio ($(round(overlap_ratio*100, digits=1))%). Consider non-blocking communication.")
    end

    if efficiency_gain < 0.2
        push!(overlap_opportunities, "Limited efficiency gain from overlap. Focus on reducing communication volume.")
    end

    if current_overlap_efficiency < 0.7
        push!(overlap_opportunities, "Overlap implementation is inefficient. Review computation-communication scheduling.")
    end

    if length(communication_bottlenecks) > 0
        push!(overlap_opportunities, "Large messages detected. Consider message aggregation or compression.")
    end

    idle_time = max(0.0, total_overlap_time - max(comp_time, comm_time))

    return OverlapAnalysis(
        profile_name,
        total_time, comp_time, comm_time, overlap_time_actual, idle_time,
        overlap_ratio, efficiency_gain, current_overlap_efficiency,
        communication_bottlenecks, computation_bottlenecks,
        overlap_opportunities
    )
end

"""
    _measure_overlap_execution(field::Vector{T}, mesh::OptimizedMesh, comm::MPI.Comm,
                              computation_func::Function) where T

Measure actual overlap time in a computation-communication overlap scenario.
"""
function _measure_overlap_execution(field::Vector{T}, mesh::OptimizedMesh, comm::MPI.Comm,
                                   computation_func::Function) where T
    rank = MPI.Comm_rank(comm)

    # Start non-blocking communication
    send_requests = MPI.Request[]
    recv_requests = MPI.Request[]
    recv_buffers = Dict{Int, Vector{T}}()

    comm_start = time()

    # Post receives
    for (neighbor_rank, recv_indices) in mesh.recv_maps
        if neighbor_rank == rank
            continue
        end

        recv_buffers[neighbor_rank] = Vector{T}(undef, length(recv_indices))
        request = MPI.Irecv!(recv_buffers[neighbor_rank], neighbor_rank, 0, comm)
        push!(recv_requests, request)
    end

    # Post sends
    for (neighbor_rank, send_indices) in mesh.send_maps
        if neighbor_rank == rank
            continue
        end

        send_buffer = field[send_indices]
        request = MPI.Isend(send_buffer, neighbor_rank, 0, comm)
        push!(send_requests, request)
    end

    # Perform computation while communication is in progress
    computation_func(field, mesh)

    # Wait for communication to complete
    if !isempty(recv_requests)
        MPI.Waitall!(recv_requests)
    end
    if !isempty(send_requests)
        MPI.Waitall!(send_requests)
    end

    comm_end = time()

    # Update field with received data
    for (neighbor_rank, recv_indices) in mesh.recv_maps
        if neighbor_rank == rank || !haskey(recv_buffers, neighbor_rank)
            continue
        end

        field[recv_indices] .= recv_buffers[neighbor_rank]
    end

    return comm_end - comm_start
end

"""
    create_communication_report(profile::CommunicationProfile,
                               load_metrics::LoadBalanceMetrics,
                               overlap_analysis::OverlapAnalysis,
                               output_file::String="communication_report.md")

Create a comprehensive communication analysis report.
"""
function create_communication_report(profile::CommunicationProfile,
                                   load_metrics::LoadBalanceMetrics,
                                   overlap_analysis::OverlapAnalysis,
                                   output_file::String="communication_report.md")
    open(output_file, "w") do io
        write(io, "# Communication Pattern Analysis Report\n\n")
        write(io, "Generated on: $(now())\n")
        write(io, "Profile: $(profile.name)\n\n")

        # Executive Summary
        write(io, "## Executive Summary\n\n")
        write(io, "- **Processes**: $(load_metrics.n_processes)\n")
        write(io, "- **Load Imbalance**: $(round(load_metrics.work_imbalance*100, digits=2))%\n")
        write(io, "- **Communication Imbalance**: $(round(load_metrics.comm_imbalance*100, digits=2))%\n")
        write(io, "- **Parallel Efficiency**: $(round(load_metrics.parallel_efficiency*100, digits=2))%\n")
        write(io, "- **Overlap Efficiency**: $(round(overlap_analysis.current_overlap_efficiency*100, digits=2))%\n\n")

        # Communication Statistics
        write(io, "## Communication Statistics\n\n")
        write(io, "- **Total Communication Volume**: $(round(profile.communication_volume / 1024^2, digits=2)) MB\n")
        write(io, "- **Communication Time**: $(round(profile.communication_time * 1000, digits=2)) ms\n")
        write(io, "- **Peak Bandwidth**: $(round(profile.peak_bandwidth, digits=2)) MB/s\n")
        write(io, "- **Number of Neighbors**: $(length(profile.message_counts))\n\n")

        # Load Balance Analysis
        write(io, "## Load Balance Analysis\n\n")
        write(io, "| Metric | Value | Status |\n")
        write(io, "|--------|-------|--------|\n")
        write(io, "| Work Imbalance | $(round(load_metrics.work_imbalance*100, digits=2))% | ")
        write(io, load_metrics.work_imbalance < 0.1 ? "✅ Good" : load_metrics.work_imbalance < 0.2 ? "⚠️ Moderate" : "❌ Poor")
        write(io, " |\n")
        write(io, "| Max/Avg Work Ratio | $(round(load_metrics.max_work_ratio, digits=2)) | ")
        write(io, load_metrics.max_work_ratio < 1.2 ? "✅ Good" : load_metrics.max_work_ratio < 1.5 ? "⚠️ Moderate" : "❌ Poor")
        write(io, " |\n")
        write(io, "| Communication Imbalance | $(round(load_metrics.comm_imbalance*100, digits=2))% | ")
        write(io, load_metrics.comm_imbalance < 0.15 ? "✅ Good" : load_metrics.comm_imbalance < 0.3 ? "⚠️ Moderate" : "❌ Poor")
        write(io, " |\n\n")

        # Overlap Analysis
        write(io, "## Computation-Communication Overlap Analysis\n\n")
        write(io, "- **Computation Time**: $(round(overlap_analysis.computation_time * 1000, digits=2)) ms\n")
        write(io, "- **Communication Time**: $(round(overlap_analysis.communication_time * 1000, digits=2)) ms\n")
        write(io, "- **Overlap Time**: $(round(overlap_analysis.overlap_time * 1000, digits=2)) ms\n")
        write(io, "- **Overlap Ratio**: $(round(overlap_analysis.overlap_ratio*100, digits=2))%\n")
        write(io, "- **Efficiency Gain**: $(round(overlap_analysis.efficiency_gain*100, digits=2))%\n\n")

        # Bottlenecks
        if !isempty(overlap_analysis.communication_bottlenecks) || !isempty(overlap_analysis.computation_bottlenecks)
            write(io, "## Performance Bottlenecks\n\n")

            if !isempty(overlap_analysis.communication_bottlenecks)
                write(io, "### Communication Bottlenecks\n")
                for (rank, type, time) in overlap_analysis.communication_bottlenecks
                    write(io, "- Rank $(rank): $(type) ($(round(time*1000, digits=2)) ms)\n")
                end
                write(io, "\n")
            end

            if !isempty(overlap_analysis.computation_bottlenecks)
                write(io, "### Computation Bottlenecks\n")
                for (operation, time) in overlap_analysis.computation_bottlenecks
                    write(io, "- $(operation): $(round(time*1000, digits=2)) ms\n")
                end
                write(io, "\n")
            end
        end

        # Recommendations
        write(io, "## Optimization Recommendations\n\n")
        write(io, "### Load Balancing\n")
        for (i, rec) in enumerate(load_metrics.recommendations)
            write(io, "$(i). $(rec)\n")
        end
        write(io, "\n### Communication Overlap\n")
        for (i, rec) in enumerate(overlap_analysis.overlap_opportunities)
            write(io, "$(i). $(rec)\n")
        end
        write(io, "\n")

        # Performance Assessment
        write(io, "## Overall Performance Assessment\n\n")
        overall_score = (load_metrics.parallel_efficiency + overlap_analysis.current_overlap_efficiency) / 2

        if overall_score > 0.8
            write(io, "🟢 **EXCELLENT**: High performance with good load balance and communication efficiency.\n")
        elseif overall_score > 0.6
            write(io, "🟡 **GOOD**: Reasonable performance with some optimization opportunities.\n")
        elseif overall_score > 0.4
            write(io, "🟠 **MODERATE**: Performance issues detected. Optimization recommended.\n")
        else
            write(io, "🔴 **POOR**: Significant performance issues. Immediate optimization required.\n")
        end
    end

    @printf("Communication analysis report saved to: %s\n", output_file)
end

end # module CommunicationAnalyzer
