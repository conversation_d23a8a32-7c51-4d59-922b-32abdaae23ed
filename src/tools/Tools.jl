"""
    Tools.jl

This module provides various tools for JuliaFOAM.
"""
module Tools

# Include tool modules
include("LinearSystemDiagnostics.jl")
include("MemoryProfiler.jl")
include("CommunicationAnalyzer.jl")

# Export modules
using .LinearSystemDiagnostics
using .MemoryProfiler
using .CommunicationAnalyzer

# Export functions
export diagnose_linear_system, fix_linear_system
export analyze_convergence_history, generate_diagnostic_report
export check_matrix_properties

# Export memory profiling functions
export MemoryProfile, ProfileResult, profile_memory_usage, analyze_allocations
export create_memory_report, benchmark_memory_performance, track_allocation_hotspots
export profile_juliafoam_solver

# Export communication analysis functions
export CommunicationProfile, LoadBalanceMetrics, OverlapAnalysis
export analyze_communication_patterns, profile_halo_exchange, analyze_load_balance
export identify_overlap_opportunities, create_communication_report

end # module Tools
