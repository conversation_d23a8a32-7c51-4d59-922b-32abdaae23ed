#!/usr/bin/env julia

"""
Comprehensive Mesh Diagnostic and Doctor Utilities for JuliaFOAM

This module provides extensive mesh quality analysis, debugging tools, and
automatic issue detection for finite volume meshes.

Features:
- Mesh quality metrics (orthogonality, skewness, aspect ratio)
- Connectivity validation
- Geometric consistency checks
- Boundary condition validation
- Performance analysis
- Automatic issue detection and recommendations
"""

module MeshDiagnostics

using LinearAlgebra
using StaticArrays
using SparseArrays
using Printf

# Add parent directories to path for importing JuliaFOAM
push!(LOAD_PATH, joinpath(@__DIR__, ".."))

include("../core/Types.jl")

export MeshDoctor, diagnose_mesh, check_mesh_quality, analyze_connectivity
export check_geometric_consistency, validate_boundaries, mesh_report

"""
Comprehensive mesh diagnostic results structure
"""
struct MeshDiagnosticResults
    # Quality metrics
    orthogonality_stats::Dict{String, Float64}
    skewness_stats::Dict{String, Float64}
    aspect_ratio_stats::Dict{String, Float64}
    face_area_ratio_stats::Dict{String, Float64}
    
    # Connectivity
    connectivity_issues::Vector{String}
    isolated_cells::Vector{Int}
    dangling_faces::Vector{Int}
    
    # Geometry
    geometric_issues::Vector{String}
    negative_volumes::Vector{Int}
    invalid_face_areas::Vector{Int}
    
    # Boundaries
    boundary_issues::Vector{String}
    unclosed_boundaries::Vector{String}
    
    # Overall assessment
    overall_quality::String  # "EXCELLENT", "GOOD", "ACCEPTABLE", "POOR", "CRITICAL"
    critical_issues::Vector{String}
    recommendations::Vector{String}
    
    # Performance metrics
    cell_count::Int
    face_count::Int
    memory_usage_mb::Float64
    expected_cfl_limit::Float64
end

"""
Main mesh doctor utility
"""
struct MeshDoctor
    mesh::Mesh
    verbose::Bool
    tolerance::Float64
    
    function MeshDoctor(mesh::Mesh; verbose=true, tolerance=1e-12)
        new(mesh, verbose, tolerance)
    end
end

"""
Perform comprehensive mesh diagnosis
"""
function diagnose_mesh(doctor::MeshDoctor)::MeshDiagnosticResults
    if doctor.verbose
        println("🔍 JuliaFOAM Mesh Doctor - Comprehensive Diagnosis")
        println("="^70)
    end
    
    # Initialize results
    orthogonality_stats = Dict{String, Float64}()
    skewness_stats = Dict{String, Float64}()
    aspect_ratio_stats = Dict{String, Float64}()
    face_area_ratio_stats = Dict{String, Float64}()
    
    connectivity_issues = String[]
    isolated_cells = Int[]
    dangling_faces = Int[]
    
    geometric_issues = String[]
    negative_volumes = Int[]
    invalid_face_areas = Int[]
    
    boundary_issues = String[]
    unclosed_boundaries = String[]
    
    critical_issues = String[]
    recommendations = String[]
    
    # 1. Mesh Quality Analysis
    if doctor.verbose
        println("\n📐 Analyzing Mesh Quality...")
    end
    
    orthogonality_values = compute_orthogonality(doctor.mesh)
    skewness_values = compute_skewness(doctor.mesh)
    aspect_ratios = compute_aspect_ratios(doctor.mesh)
    area_ratios = compute_face_area_ratios(doctor.mesh)
    
    # Statistics
    orthogonality_stats["min"] = minimum(orthogonality_values)
    orthogonality_stats["max"] = maximum(orthogonality_values)
    orthogonality_stats["mean"] = sum(orthogonality_values) / length(orthogonality_values)
    orthogonality_stats["std"] = sqrt(sum((orthogonality_values .- orthogonality_stats["mean"]).^2) / length(orthogonality_values))
    
    skewness_stats["min"] = minimum(skewness_values)
    skewness_stats["max"] = maximum(skewness_values)
    skewness_stats["mean"] = sum(skewness_values) / length(skewness_values)
    skewness_stats["std"] = sqrt(sum((skewness_values .- skewness_stats["mean"]).^2) / length(skewness_values))
    
    aspect_ratio_stats["min"] = minimum(aspect_ratios)
    aspect_ratio_stats["max"] = maximum(aspect_ratios)
    aspect_ratio_stats["mean"] = sum(aspect_ratios) / length(aspect_ratios)
    aspect_ratio_stats["std"] = sqrt(sum((aspect_ratios .- aspect_ratio_stats["mean"]).^2) / length(aspect_ratios))
    
    face_area_ratio_stats["min"] = minimum(area_ratios)
    face_area_ratio_stats["max"] = maximum(area_ratios)
    face_area_ratio_stats["mean"] = sum(area_ratios) / length(area_ratios)
    face_area_ratio_stats["std"] = sqrt(sum((area_ratios .- face_area_ratio_stats["mean"]).^2) / length(area_ratios))
    
    # Quality assessment
    if orthogonality_stats["min"] < 0.1
        push!(critical_issues, "Poor orthogonality (min: $(orthogonality_stats["min"]))")
        push!(recommendations, "Improve mesh orthogonality near boundaries")
    end
    
    if skewness_stats["max"] > 0.9
        push!(critical_issues, "High skewness detected (max: $(skewness_stats["max"]))")
        push!(recommendations, "Reduce cell skewness, especially near complex geometries")
    end
    
    if aspect_ratio_stats["max"] > 1000
        push!(critical_issues, "Extreme aspect ratios (max: $(aspect_ratio_stats["max"]))")
        push!(recommendations, "Reduce aspect ratios in high-gradient regions")
    end
    
    # 2. Connectivity Analysis
    if doctor.verbose
        println("\n🔗 Analyzing Connectivity...")
    end
    
    isolated_cells = find_isolated_cells(doctor.mesh)
    dangling_faces = find_dangling_faces(doctor.mesh)
    
    if !isempty(isolated_cells)
        push!(connectivity_issues, "Found $(length(isolated_cells)) isolated cells")
        push!(critical_issues, "Isolated cells detected")
        push!(recommendations, "Remove or connect isolated cells")
    end
    
    if !isempty(dangling_faces)
        push!(connectivity_issues, "Found $(length(dangling_faces)) dangling faces")
        push!(critical_issues, "Dangling faces detected")
        push!(recommendations, "Fix mesh topology - remove dangling faces")
    end
    
    # 3. Geometric Consistency
    if doctor.verbose
        println("\n📏 Checking Geometric Consistency...")
    end
    
    negative_volumes = find_negative_volumes(doctor.mesh, doctor.tolerance)
    invalid_face_areas = find_invalid_face_areas(doctor.mesh, doctor.tolerance)
    
    if !isempty(negative_volumes)
        push!(geometric_issues, "Found $(length(negative_volumes)) cells with negative volumes")
        push!(critical_issues, "Negative cell volumes detected")
        push!(recommendations, "Fix cell ordering or mesh topology")
    end
    
    if !isempty(invalid_face_areas)
        push!(geometric_issues, "Found $(length(invalid_face_areas)) faces with invalid areas")
        push!(critical_issues, "Invalid face areas detected")
        push!(recommendations, "Check face geometry and orientation")
    end
    
    # 4. Boundary Validation
    if doctor.verbose
        println("\n🚪 Validating Boundaries...")
    end
    
    unclosed_boundaries = find_unclosed_boundaries(doctor.mesh)
    
    if !isempty(unclosed_boundaries)
        push!(boundary_issues, "Found unclosed boundaries: $(join(unclosed_boundaries, ", "))")
        push!(critical_issues, "Unclosed boundaries detected")
        push!(recommendations, "Ensure all boundaries are properly closed")
    end
    
    # 5. Performance Analysis
    cell_count = length(doctor.mesh.cells)
    face_count = length(doctor.mesh.faces)
    memory_usage_mb = estimate_memory_usage(doctor.mesh)
    expected_cfl_limit = estimate_cfl_limit(doctor.mesh)
    
    # Overall assessment
    overall_quality = assess_overall_quality(
        orthogonality_stats, skewness_stats, aspect_ratio_stats,
        connectivity_issues, geometric_issues, boundary_issues
    )
    
    if doctor.verbose
        println("\n📊 Overall Assessment: $overall_quality")
        if !isempty(critical_issues)
            println("\n⚠️  Critical Issues:")
            for issue in critical_issues
                println("  • $issue")
            end
        end
        
        if !isempty(recommendations)
            println("\n💡 Recommendations:")
            for rec in recommendations
                println("  • $rec")
            end
        end
    end
    
    return MeshDiagnosticResults(
        orthogonality_stats, skewness_stats, aspect_ratio_stats, face_area_ratio_stats,
        connectivity_issues, isolated_cells, dangling_faces,
        geometric_issues, negative_volumes, invalid_face_areas,
        boundary_issues, unclosed_boundaries,
        overall_quality, critical_issues, recommendations,
        cell_count, face_count, memory_usage_mb, expected_cfl_limit
    )
end

"""
Compute orthogonality for all faces
"""
function compute_orthogonality(mesh::Mesh)::Vector{Float64}
    orthogonality = Float64[]
    
    for face in mesh.faces
        if face.neighbour > 0  # Internal face
            # Vector between cell centers
            owner_center = mesh.cells[face.owner].center
            neighbour_center = mesh.cells[face.neighbour].center
            d_vector = neighbour_center - owner_center
            
            # Face area vector
            face_normal = normalize(face.area)
            
            # Orthogonality = |d · n| / |d|
            orthogonality_val = abs(dot(d_vector, face_normal)) / norm(d_vector)
            push!(orthogonality, orthogonality_val)
        end
    end
    
    return orthogonality
end

"""
Compute skewness for all cells
"""
function compute_skewness(mesh::Mesh)::Vector{Float64}
    skewness = Float64[]
    
    for cell in mesh.cells
        # Simple skewness metric based on cell center vs geometric center
        geometric_center = SVector{3,Float64}(0.0, 0.0, 0.0)
        total_volume = 0.0
        
        # This is a simplified skewness calculation
        # In practice, you'd use face centers and proper geometric calculations
        cell_faces = get_cell_faces(mesh, cell)
        
        if length(cell_faces) > 0
            avg_distance = 0.0
            for face_idx in cell_faces
                face_center = mesh.faces[face_idx].center
                avg_distance += norm(face_center - cell.center)
            end
            avg_distance /= length(cell_faces)
            
            max_distance = maximum([norm(mesh.faces[face_idx].center - cell.center) for face_idx in cell_faces])
            
            skewness_val = max_distance / (avg_distance + 1e-15)
            push!(skewness, min(skewness_val - 1.0, 1.0))  # Normalize to [0,1]
        else
            push!(skewness, 0.0)
        end
    end
    
    return max.(skewness, 0.0)
end

"""
Compute aspect ratios for all cells
"""
function compute_aspect_ratios(mesh::Mesh)::Vector{Float64}
    aspect_ratios = Float64[]
    
    for cell in mesh.cells
        cell_faces = get_cell_faces(mesh, cell)
        
        if length(cell_faces) >= 4  # At least tetrahedral
            # Simple aspect ratio: max distance / min distance between face centers
            distances = Float64[]
            for i in 1:length(cell_faces)
                for j in (i+1):length(cell_faces)
                    face1_center = mesh.faces[cell_faces[i]].center
                    face2_center = mesh.faces[cell_faces[j]].center
                    push!(distances, norm(face2_center - face1_center))
                end
            end
            
            if !isempty(distances)
                aspect_ratio = maximum(distances) / (minimum(distances) + 1e-15)
                push!(aspect_ratios, aspect_ratio)
            else
                push!(aspect_ratios, 1.0)
            end
        else
            push!(aspect_ratios, 1.0)
        end
    end
    
    return aspect_ratios
end

"""
Compute face area ratios for neighboring faces
"""
function compute_face_area_ratios(mesh::Mesh)::Vector{Float64}
    area_ratios = Float64[]
    
    for face in mesh.faces
        if face.neighbour > 0  # Internal face
            face_area = norm(face.area)
            
            # Find neighboring faces and compare areas
            owner_faces = get_cell_faces(mesh, mesh.cells[face.owner])
            
            for other_face_idx in owner_faces
                if other_face_idx != findfirst(f -> f === face, mesh.faces)
                    other_area = norm(mesh.faces[other_face_idx].area)
                    ratio = max(face_area, other_area) / (min(face_area, other_area) + 1e-15)
                    push!(area_ratios, ratio)
                end
            end
        end
    end
    
    return isempty(area_ratios) ? [1.0] : area_ratios
end

"""
Find isolated cells (cells with no proper connectivity)
"""
function find_isolated_cells(mesh::Mesh)::Vector{Int}
    isolated = Int[]
    
    for (cell_idx, cell) in enumerate(mesh.cells)
        connected_faces = get_cell_faces(mesh, cell)
        
        # Check if cell has any internal faces (proper connectivity)
        has_internal_connection = false
        for face_idx in connected_faces
            face = mesh.faces[face_idx]
            if face.neighbour > 0 && face.neighbour != cell_idx
                has_internal_connection = true
                break
            end
        end
        
        if !has_internal_connection && length(connected_faces) > 0
            # Cell only has boundary faces - might be isolated
            push!(isolated, cell_idx)
        end
    end
    
    return isolated
end

"""
Find dangling faces (faces not properly connected)
"""
function find_dangling_faces(mesh::Mesh)::Vector{Int}
    dangling = Int[]
    
    for (face_idx, face) in enumerate(mesh.faces)
        # Check for invalid owner/neighbour indices
        if face.owner <= 0 || face.owner > length(mesh.cells)
            push!(dangling, face_idx)
        elseif face.neighbour > length(mesh.cells)
            push!(dangling, face_idx)
        end
    end
    
    return dangling
end

"""
Find cells with negative volumes
"""
function find_negative_volumes(mesh::Mesh, tolerance::Float64)::Vector{Int}
    negative = Int[]
    
    for (cell_idx, cell) in enumerate(mesh.cells)
        if cell.volume < -tolerance
            push!(negative, cell_idx)
        end
    end
    
    return negative
end

"""
Find faces with invalid areas
"""
function find_invalid_face_areas(mesh::Mesh, tolerance::Float64)::Vector{Int}
    invalid = Int[]
    
    for (face_idx, face) in enumerate(mesh.faces)
        area_magnitude = norm(face.area)
        if area_magnitude < tolerance || !isfinite(area_magnitude)
            push!(invalid, face_idx)
        end
    end
    
    return invalid
end

"""
Find unclosed boundaries
"""
function find_unclosed_boundaries(mesh::Mesh)::Vector{String}
    # This is a simplified check - in practice you'd check boundary topology
    unclosed = String[]
    
    # Group boundary faces by patch
    boundary_patches = Dict{String, Vector{Int}}()
    
    for (face_idx, face) in enumerate(mesh.faces)
        if face.neighbour <= 0  # Boundary face
            # In a real implementation, you'd get the patch name from boundary conditions
            patch_name = "patch_$(abs(face.neighbour))"
            if !haskey(boundary_patches, patch_name)
                boundary_patches[patch_name] = Int[]
            end
            push!(boundary_patches[patch_name], face_idx)
        end
    end
    
    # Check each patch for closure (simplified)
    for (patch_name, face_indices) in boundary_patches
        if length(face_indices) < 3
            push!(unclosed, patch_name)
        end
    end
    
    return unclosed
end

"""
Get faces belonging to a cell
"""
function get_cell_faces(mesh::Mesh, cell::Cell)::Vector{Int}
    faces = Int[]
    
    for (face_idx, face) in enumerate(mesh.faces)
        if face.owner == findfirst(c -> c === cell, mesh.cells) || 
           (face.neighbour > 0 && face.neighbour == findfirst(c -> c === cell, mesh.cells))
            push!(faces, face_idx)
        end
    end
    
    return faces
end

"""
Estimate memory usage of mesh
"""
function estimate_memory_usage(mesh::Mesh)::Float64
    # Rough estimate in MB
    cell_memory = length(mesh.cells) * 100  # bytes per cell (rough estimate)
    face_memory = length(mesh.faces) * 80   # bytes per face (rough estimate)
    
    return (cell_memory + face_memory) / (1024 * 1024)
end

"""
Estimate CFL limit based on mesh characteristics
"""
function estimate_cfl_limit(mesh::Mesh)::Float64
    if isempty(mesh.cells)
        return 1.0
    end
    
    # Find minimum cell size
    min_cell_size = minimum([cbrt(abs(cell.volume)) for cell in mesh.cells])
    
    # Estimate CFL limit (very rough approximation)
    return min_cell_size / 10.0  # Conservative estimate
end

"""
Assess overall mesh quality
"""
function assess_overall_quality(
    orthogonality_stats::Dict{String, Float64},
    skewness_stats::Dict{String, Float64},
    aspect_ratio_stats::Dict{String, Float64},
    connectivity_issues::Vector{String},
    geometric_issues::Vector{String},
    boundary_issues::Vector{String}
)::String
    
    # Critical issues
    if !isempty(geometric_issues) || !isempty(connectivity_issues) || !isempty(boundary_issues)
        return "CRITICAL"
    end
    
    # Poor quality indicators
    if orthogonality_stats["min"] < 0.1 || skewness_stats["max"] > 0.9 || aspect_ratio_stats["max"] > 1000
        return "POOR"
    end
    
    # Acceptable quality
    if orthogonality_stats["min"] < 0.3 || skewness_stats["max"] > 0.7 || aspect_ratio_stats["max"] > 100
        return "ACCEPTABLE"
    end
    
    # Good quality
    if orthogonality_stats["min"] > 0.7 && skewness_stats["max"] < 0.5 && aspect_ratio_stats["max"] < 50
        return "EXCELLENT"
    end
    
    return "GOOD"
end

"""
Generate comprehensive mesh report
"""
function mesh_report(results::MeshDiagnosticResults; output_file::Union{String, Nothing}=nothing)
    report_lines = String[]
    
    push!(report_lines, "")
    push!(report_lines, "📋 JuliaFOAM Mesh Diagnostic Report")
    push!(report_lines, "="^70)
    push!(report_lines, "Generated: $(now())")
    push!(report_lines, "")
    
    # Mesh Statistics
    push!(report_lines, "📊 Mesh Statistics")
    push!(report_lines, "-"^30)
    push!(report_lines, "Cells: $(results.cell_count)")
    push!(report_lines, "Faces: $(results.face_count)")
    push!(report_lines, "Memory Usage: $(round(results.memory_usage_mb, digits=2)) MB")
    push!(report_lines, "Expected CFL Limit: $(round(results.expected_cfl_limit, digits=6))")
    push!(report_lines, "")
    
    # Quality Metrics
    push!(report_lines, "📐 Quality Metrics")
    push!(report_lines, "-"^30)
    push!(report_lines, "Orthogonality:")
    push!(report_lines, "  Min: $(round(results.orthogonality_stats["min"], digits=4))")
    push!(report_lines, "  Max: $(round(results.orthogonality_stats["max"], digits=4))")
    push!(report_lines, "  Mean: $(round(results.orthogonality_stats["mean"], digits=4))")
    push!(report_lines, "")
    push!(report_lines, "Skewness:")
    push!(report_lines, "  Min: $(round(results.skewness_stats["min"], digits=4))")
    push!(report_lines, "  Max: $(round(results.skewness_stats["max"], digits=4))")
    push!(report_lines, "  Mean: $(round(results.skewness_stats["mean"], digits=4))")
    push!(report_lines, "")
    push!(report_lines, "Aspect Ratio:")
    push!(report_lines, "  Min: $(round(results.aspect_ratio_stats["min"], digits=2))")
    push!(report_lines, "  Max: $(round(results.aspect_ratio_stats["max"], digits=2))")
    push!(report_lines, "  Mean: $(round(results.aspect_ratio_stats["mean"], digits=2))")
    push!(report_lines, "")
    
    # Issues
    if !isempty(results.critical_issues)
        push!(report_lines, "⚠️  Critical Issues")
        push!(report_lines, "-"^30)
        for issue in results.critical_issues
            push!(report_lines, "• $issue")
        end
        push!(report_lines, "")
    end
    
    # Recommendations
    if !isempty(results.recommendations)
        push!(report_lines, "💡 Recommendations")
        push!(report_lines, "-"^30)
        for rec in results.recommendations
            push!(report_lines, "• $rec")
        end
        push!(report_lines, "")
    end
    
    # Overall Assessment
    status_emoji = results.overall_quality == "EXCELLENT" ? "🏆" :
                   results.overall_quality == "GOOD" ? "✅" :
                   results.overall_quality == "ACCEPTABLE" ? "⚠️" :
                   results.overall_quality == "POOR" ? "❌" : "🚨"
    
    push!(report_lines, "🎯 Overall Assessment")
    push!(report_lines, "-"^30)
    push!(report_lines, "$status_emoji $(results.overall_quality)")
    push!(report_lines, "")
    
    report = join(report_lines, "\n")
    
    if output_file !== nothing
        open(output_file, "w") do f
            write(f, report)
        end
        println("Report saved to: $output_file")
    else
        println(report)
    end
    
    return report
end

end  # module MeshDiagnostics