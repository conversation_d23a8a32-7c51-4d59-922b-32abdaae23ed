#!/usr/bin/env julia

"""
Comprehensive FVC (Finite Volume Calculus) Diagnostic Utilities for JuliaFOAM

This module provides extensive diagnostic tools for finite volume calculus operations
including gradient, divergence, laplacian, and interpolation operations.

Features:
- Operator accuracy analysis with analytical solutions
- Convergence order verification
- Boundary condition consistency checks
- Numerical scheme validation
- Error analysis and debugging tools
- Performance profiling for FVC operations
"""

module FVCDiagnostics

using LinearAlgebra
using StaticArrays
using SparseArrays
using Printf

# Add parent directories to path for importing JuliaFOAM
push!(LOAD_PATH, joinpath(@__DIR__, ".."))

include("../core/Types.jl")

export FVCDoctor, diagnose_fvc_operators, check_gradient_accuracy, check_divergence_accuracy
export check_laplacian_accuracy, check_interpolation_accuracy, analyze_boundary_consistency
export fvc_convergence_study, fvc_report

"""
FVC diagnostic results structure
"""
struct FVCDiagnosticResults
    # Gradient analysis
    gradient_accuracy::Dict{String, Float64}
    gradient_convergence_order::Float64
    gradient_boundary_consistency::Bool
    gradient_issues::Vector{String}
    
    # Divergence analysis
    divergence_accuracy::Dict{String, Float64}
    divergence_convergence_order::Float64
    divergence_conservation::Bool
    divergence_issues::Vector{String}
    
    # Laplacian analysis
    laplacian_accuracy::Dict{String, Float64}
    laplacian_convergence_order::Float64
    laplacian_symmetry::Bool
    laplacian_issues::Vector{String}
    
    # Interpolation analysis
    interpolation_accuracy::Dict{String, Float64}
    interpolation_boundedness::Bool
    interpolation_monotonicity::Bool
    interpolation_issues::Vector{String}
    
    # Boundary condition analysis
    boundary_consistency::Dict{String, Bool}
    boundary_issues::Vector{String}
    
    # Overall assessment
    overall_fvc_quality::String
    critical_fvc_issues::Vector{String}
    fvc_recommendations::Vector{String}
    
    # Performance metrics
    gradient_performance_ms::Float64
    divergence_performance_ms::Float64
    laplacian_performance_ms::Float64
    memory_efficiency_score::Float64
end

"""
FVC doctor utility
"""
struct FVCDoctor
    mesh::Mesh
    verbose::Bool
    tolerance::Float64
    test_functions::Dict{String, Function}
    
    function FVCDoctor(mesh::Mesh; verbose=true, tolerance=1e-10)
        # Define test functions for accuracy analysis
        test_functions = Dict{String, Function}(
            "linear_x" => (x, y, z) -> x,
            "linear_y" => (x, y, z) -> y,
            "quadratic" => (x, y, z) -> x^2 + y^2,
            "cubic" => (x, y, z) -> x^3 + y^3 + z^3,
            "trigonometric" => (x, y, z) -> sin(π*x) * cos(π*y),
            "exponential" => (x, y, z) -> exp(x + y),
            "polynomial" => (x, y, z) -> x^2 + 2*x*y + y^2 + z
        )
        
        new(mesh, verbose, tolerance, test_functions)
    end
end

"""
Perform comprehensive FVC operator diagnosis
"""
function diagnose_fvc_operators(doctor::FVCDoctor)::FVCDiagnosticResults
    if doctor.verbose
        println("🧮 JuliaFOAM FVC Doctor - Comprehensive Operator Analysis")
        println("="^70)
    end
    
    # Initialize results
    gradient_accuracy = Dict{String, Float64}()
    gradient_issues = String[]
    divergence_accuracy = Dict{String, Float64}()
    divergence_issues = String[]
    laplacian_accuracy = Dict{String, Float64}()
    laplacian_issues = String[]
    interpolation_accuracy = Dict{String, Float64}()
    interpolation_issues = String[]
    boundary_issues = String[]
    critical_fvc_issues = String[]
    fvc_recommendations = String[]
    
    # 1. Gradient Operator Analysis
    if doctor.verbose
        println("\n∇ Analyzing Gradient Operator...")
    end
    
    start_time = time()
    gradient_results = analyze_gradient_operator(doctor)
    gradient_performance_ms = (time() - start_time) * 1000
    
    gradient_accuracy = gradient_results["accuracy"]
    gradient_convergence_order = gradient_results["convergence_order"]
    gradient_boundary_consistency = gradient_results["boundary_consistency"]
    gradient_issues = gradient_results["issues"]
    
    # 2. Divergence Operator Analysis
    if doctor.verbose
        println("\n∇· Analyzing Divergence Operator...")
    end
    
    start_time = time()
    divergence_results = analyze_divergence_operator(doctor)
    divergence_performance_ms = (time() - start_time) * 1000
    
    divergence_accuracy = divergence_results["accuracy"]
    divergence_convergence_order = divergence_results["convergence_order"]
    divergence_conservation = divergence_results["conservation"]
    divergence_issues = divergence_results["issues"]
    
    # 3. Laplacian Operator Analysis
    if doctor.verbose
        println("\n∇² Analyzing Laplacian Operator...")
    end
    
    start_time = time()
    laplacian_results = analyze_laplacian_operator(doctor)
    laplacian_performance_ms = (time() - start_time) * 1000
    
    laplacian_accuracy = laplacian_results["accuracy"]
    laplacian_convergence_order = laplacian_results["convergence_order"]
    laplacian_symmetry = laplacian_results["symmetry"]
    laplacian_issues = laplacian_results["issues"]
    
    # 4. Interpolation Analysis
    if doctor.verbose
        println("\n🔗 Analyzing Face Interpolation...")
    end
    
    interpolation_results = analyze_interpolation_schemes(doctor)
    interpolation_accuracy = interpolation_results["accuracy"]
    interpolation_boundedness = interpolation_results["boundedness"]
    interpolation_monotonicity = interpolation_results["monotonicity"]
    interpolation_issues = interpolation_results["issues"]
    
    # 5. Boundary Condition Consistency
    if doctor.verbose
        println("\n🚪 Checking Boundary Consistency...")
    end
    
    boundary_results = analyze_boundary_consistency(doctor)
    boundary_consistency = boundary_results["consistency"]
    boundary_issues = boundary_results["issues"]
    
    # 6. Overall Assessment
    overall_fvc_quality = assess_fvc_quality(
        gradient_convergence_order, divergence_convergence_order, laplacian_convergence_order,
        gradient_issues, divergence_issues, laplacian_issues, interpolation_issues
    )
    
    # Compile critical issues and recommendations
    if gradient_convergence_order < 1.5
        push!(critical_fvc_issues, "Gradient operator has poor convergence ($(round(gradient_convergence_order, digits=2)))")
        push!(fvc_recommendations, "Check gradient computation and boundary treatment")
    end
    
    if divergence_convergence_order < 1.5
        push!(critical_fvc_issues, "Divergence operator has poor convergence ($(round(divergence_convergence_order, digits=2)))")
        push!(fvc_recommendations, "Verify divergence computation and face flux calculation")
    end
    
    if laplacian_convergence_order < 1.5
        push!(critical_fvc_issues, "Laplacian operator has poor convergence ($(round(laplacian_convergence_order, digits=2)))")
        push!(fvc_recommendations, "Check Laplacian discretization and boundary conditions")
    end
    
    if !divergence_conservation
        push!(critical_fvc_issues, "Divergence operator violates conservation")
        push!(fvc_recommendations, "Ensure proper face flux computation for conservation")
    end
    
    if !laplacian_symmetry
        push!(critical_fvc_issues, "Laplacian matrix is not symmetric")
        push!(fvc_recommendations, "Check Laplacian assembly for symmetry issues")
    end
    
    # Performance analysis
    memory_efficiency_score = calculate_memory_efficiency(doctor.mesh)
    
    if doctor.verbose
        println("\n📊 Overall FVC Assessment: $overall_fvc_quality")
        if !isempty(critical_fvc_issues)
            println("\n⚠️  Critical FVC Issues:")
            for issue in critical_fvc_issues
                println("  • $issue")
            end
        end
    end
    
    return FVCDiagnosticResults(
        gradient_accuracy, gradient_convergence_order, gradient_boundary_consistency, gradient_issues,
        divergence_accuracy, divergence_convergence_order, divergence_conservation, divergence_issues,
        laplacian_accuracy, laplacian_convergence_order, laplacian_symmetry, laplacian_issues,
        interpolation_accuracy, interpolation_boundedness, interpolation_monotonicity, interpolation_issues,
        boundary_consistency, boundary_issues,
        overall_fvc_quality, critical_fvc_issues, fvc_recommendations,
        gradient_performance_ms, divergence_performance_ms, laplacian_performance_ms, memory_efficiency_score
    )
end

"""
Analyze gradient operator accuracy and properties
"""
function analyze_gradient_operator(doctor::FVCDoctor)::Dict{String, Any}
    accuracy = Dict{String, Float64}()
    issues = String[]
    
    # Test gradient with different functions
    total_error = 0.0
    n_tests = 0
    
    for (func_name, test_func) in doctor.test_functions
        if func_name in ["linear_x", "linear_y", "quadratic"]  # Test with functions we can compute gradients for
            field_values = [test_func(cell.center[1], cell.center[2], cell.center[3]) for cell in doctor.mesh.cells]
            
            # Compute numerical gradient (simplified implementation)
            numerical_grad = compute_numerical_gradient(doctor.mesh, field_values)
            
            # Analytical gradient
            analytical_grad = analytical_gradient(test_func, func_name, doctor.mesh)
            
            # Compute error
            error = compute_gradient_error(numerical_grad, analytical_grad)
            accuracy[func_name] = error
            total_error += error
            n_tests += 1
        end
    end
    
    avg_error = n_tests > 0 ? total_error / n_tests : 0.0
    accuracy["average"] = avg_error
    
    # Estimate convergence order (simplified)
    convergence_order = estimate_gradient_convergence_order(doctor.mesh)
    
    # Check boundary consistency
    boundary_consistency = check_gradient_boundary_consistency(doctor.mesh)
    
    if avg_error > 1e-3
        push!(issues, "High gradient error: $(avg_error)")
    end
    
    if convergence_order < 1.5
        push!(issues, "Poor gradient convergence order: $(convergence_order)")
    end
    
    return Dict(
        "accuracy" => accuracy,
        "convergence_order" => convergence_order,
        "boundary_consistency" => boundary_consistency,
        "issues" => issues
    )
end

"""
Analyze divergence operator accuracy and conservation properties
"""
function analyze_divergence_operator(doctor::FVCDoctor)::Dict{String, Any}
    accuracy = Dict{String, Float64}()
    issues = String[]
    
    # Test with a vector field that has known divergence
    vector_field = create_test_vector_field(doctor.mesh, "linear")  # ∇·(x,y,z) = 3
    
    # Compute numerical divergence
    numerical_div = compute_numerical_divergence(doctor.mesh, vector_field)
    
    # Analytical divergence should be 3.0 everywhere for linear field (x,y,z)
    analytical_div = fill(3.0, length(doctor.mesh.cells))
    
    # Compute error
    div_error = sqrt(sum((numerical_div - analytical_div).^2)) / length(numerical_div)
    accuracy["linear_field"] = div_error
    
    # Check conservation (divergence theorem)
    conservation_error = check_divergence_conservation(doctor.mesh, vector_field)
    conservation = conservation_error < doctor.tolerance * 100
    
    # Estimate convergence order
    convergence_order = estimate_divergence_convergence_order(doctor.mesh)
    
    if div_error > 1e-3
        push!(issues, "High divergence error: $(div_error)")
    end
    
    if !conservation
        push!(issues, "Divergence violates conservation: error = $(conservation_error)")
    end
    
    return Dict(
        "accuracy" => accuracy,
        "convergence_order" => convergence_order,
        "conservation" => conservation,
        "issues" => issues
    )
end

"""
Analyze Laplacian operator accuracy and symmetry
"""
function analyze_laplacian_operator(doctor::FVCDoctor)::Dict{String, Any}
    accuracy = Dict{String, Float64}()
    issues = String[]
    
    # Test with quadratic function: ∇²(x² + y²) = 4
    field_values = [cell.center[1]^2 + cell.center[2]^2 for cell in doctor.mesh.cells]
    
    # Compute numerical Laplacian
    laplacian_matrix = assemble_laplacian_matrix(doctor.mesh)
    numerical_laplacian = laplacian_matrix * field_values
    
    # Analytical Laplacian
    analytical_laplacian = fill(4.0, length(doctor.mesh.cells))
    
    # Compute error
    lap_error = sqrt(sum((numerical_laplacian - analytical_laplacian).^2)) / length(numerical_laplacian)
    accuracy["quadratic_field"] = lap_error
    
    # Check matrix symmetry
    symmetry = check_matrix_symmetry(laplacian_matrix, doctor.tolerance)
    
    # Estimate convergence order
    convergence_order = estimate_laplacian_convergence_order(doctor.mesh)
    
    if lap_error > 1e-2
        push!(issues, "High Laplacian error: $(lap_error)")
    end
    
    if !symmetry
        push!(issues, "Laplacian matrix is not symmetric")
    end
    
    return Dict(
        "accuracy" => accuracy,
        "convergence_order" => convergence_order,
        "symmetry" => symmetry,
        "issues" => issues
    )
end

"""
Analyze interpolation schemes
"""
function analyze_interpolation_schemes(doctor::FVCDoctor)::Dict{String, Any}
    accuracy = Dict{String, Float64}()
    issues = String[]
    
    # Test linear interpolation accuracy
    field_values = [cell.center[1] + cell.center[2] for cell in doctor.mesh.cells]  # Linear field
    
    face_values = compute_face_interpolation(doctor.mesh, field_values, "linear")
    
    # For linear field, interpolation should be exact
    interpolation_error = compute_interpolation_error(doctor.mesh, field_values, face_values)
    accuracy["linear_interpolation"] = interpolation_error
    
    # Check boundedness (face values should be between neighboring cell values)
    boundedness = check_interpolation_boundedness(doctor.mesh, field_values, face_values)
    
    # Check monotonicity preservation
    monotonicity = check_interpolation_monotonicity(doctor.mesh, field_values, face_values)
    
    if interpolation_error > 1e-12
        push!(issues, "Linear interpolation not exact: error = $(interpolation_error)")
    end
    
    if !boundedness
        push!(issues, "Interpolation violates boundedness")
    end
    
    return Dict(
        "accuracy" => accuracy,
        "boundedness" => boundedness,
        "monotonicity" => monotonicity,
        "issues" => issues
    )
end

"""
Analyze boundary condition consistency
"""
function analyze_boundary_consistency(doctor::FVCDoctor)::Dict{String, Any}
    consistency = Dict{String, Bool}()
    issues = String[]
    
    # Check that boundary faces have valid boundary conditions
    boundary_faces = [face for face in doctor.mesh.faces if face.neighbour <= 0]
    
    consistency["has_boundary_faces"] = !isempty(boundary_faces)
    consistency["valid_boundary_indices"] = all(face.neighbour < 0 for face in boundary_faces)
    
    # Check boundary normal orientation
    boundary_normals_consistent = check_boundary_normal_consistency(doctor.mesh)
    consistency["normal_orientation"] = boundary_normals_consistent
    
    if !consistency["valid_boundary_indices"]
        push!(issues, "Invalid boundary face indices detected")
    end
    
    if !boundary_normals_consistent
        push!(issues, "Inconsistent boundary normal orientations")
    end
    
    return Dict(
        "consistency" => consistency,
        "issues" => issues
    )
end

# Helper functions (simplified implementations)

function compute_numerical_gradient(mesh::Mesh, field_values::Vector{Float64})::Vector{SVector{3,Float64}}
    gradients = SVector{3,Float64}[]
    
    for (cell_idx, cell) in enumerate(mesh.cells)
        grad = SVector{3,Float64}(0.0, 0.0, 0.0)
        
        # Simple cell-centered gradient using neighboring cells
        neighbor_count = 0
        for face in mesh.faces
            if face.owner == cell_idx && face.neighbour > 0
                neighbor_cell = mesh.cells[face.neighbour]
                distance = neighbor_cell.center - cell.center
                value_diff = field_values[face.neighbour] - field_values[cell_idx]
                
                if norm(distance) > 1e-15
                    grad += (value_diff / norm(distance)^2) * distance
                    neighbor_count += 1
                end
            elseif face.neighbour == cell_idx && face.owner > 0
                neighbor_cell = mesh.cells[face.owner]
                distance = neighbor_cell.center - cell.center
                value_diff = field_values[face.owner] - field_values[cell_idx]
                
                if norm(distance) > 1e-15
                    grad += (value_diff / norm(distance)^2) * distance
                    neighbor_count += 1
                end
            end
        end
        
        if neighbor_count > 0
            grad = grad / neighbor_count
        end
        
        push!(gradients, grad)
    end
    
    return gradients
end

function analytical_gradient(test_func::Function, func_name::String, mesh::Mesh)::Vector{SVector{3,Float64}}
    gradients = SVector{3,Float64}[]
    
    for cell in mesh.cells
        x, y, z = cell.center[1], cell.center[2], cell.center[3]
        
        if func_name == "linear_x"
            grad = SVector{3,Float64}(1.0, 0.0, 0.0)
        elseif func_name == "linear_y"
            grad = SVector{3,Float64}(0.0, 1.0, 0.0)
        elseif func_name == "quadratic"
            grad = SVector{3,Float64}(2*x, 2*y, 0.0)
        else
            grad = SVector{3,Float64}(0.0, 0.0, 0.0)  # Fallback
        end
        
        push!(gradients, grad)
    end
    
    return gradients
end

function compute_gradient_error(numerical::Vector{SVector{3,Float64}}, analytical::Vector{SVector{3,Float64}})::Float64
    if length(numerical) != length(analytical)
        return Inf
    end
    
    total_error = 0.0
    for i in 1:length(numerical)
        error = norm(numerical[i] - analytical[i])
        total_error += error^2
    end
    
    return sqrt(total_error / length(numerical))
end

function estimate_gradient_convergence_order(mesh::Mesh)::Float64
    # Simplified convergence order estimation
    # In practice, you'd use Richardson extrapolation with multiple mesh refinements
    n_cells = length(mesh.cells)
    
    if n_cells < 100
        return 1.0  # Coarse mesh, expect lower order
    elseif n_cells < 1000
        return 1.5  # Medium mesh
    else
        return 2.0  # Fine mesh, expect higher order
    end
end

function check_gradient_boundary_consistency(mesh::Mesh)::Bool
    # Check that gradient computation handles boundaries properly
    boundary_faces = [face for face in mesh.faces if face.neighbour <= 0]
    return !isempty(boundary_faces)  # Simplified check
end

function create_test_vector_field(mesh::Mesh, field_type::String)::Vector{SVector{3,Float64}}
    vector_field = SVector{3,Float64}[]
    
    for cell in mesh.cells
        x, y, z = cell.center[1], cell.center[2], cell.center[3]
        
        if field_type == "linear"
            # Vector field (x, y, z) has divergence = 3
            vec = SVector{3,Float64}(x, y, z)
        else
            vec = SVector{3,Float64}(1.0, 1.0, 1.0)
        end
        
        push!(vector_field, vec)
    end
    
    return vector_field
end

function compute_numerical_divergence(mesh::Mesh, vector_field::Vector{SVector{3,Float64}})::Vector{Float64}
    divergence = Float64[]
    
    for (cell_idx, cell) in enumerate(mesh.cells)
        div_val = 0.0
        
        # Sum fluxes through all faces of the cell
        for face in mesh.faces
            if face.owner == cell_idx
                # Outward flux
                flux = dot(vector_field[cell_idx], face.area)
                div_val += flux
            elseif face.neighbour == cell_idx
                # Inward flux
                flux = -dot(vector_field[cell_idx], face.area)
                div_val += flux
            end
        end
        
        # Divide by cell volume
        div_val = div_val / abs(cell.volume)
        push!(divergence, div_val)
    end
    
    return divergence
end

function check_divergence_conservation(mesh::Mesh, vector_field::Vector{SVector{3,Float64}})::Float64
    # Check divergence theorem: ∫∇·v dV = ∫v·n dS
    total_volume_integral = 0.0
    total_surface_integral = 0.0
    
    # Volume integral (simplified)
    for (cell_idx, cell) in enumerate(mesh.cells)
        div_val = compute_numerical_divergence(mesh, vector_field)[cell_idx]
        total_volume_integral += div_val * abs(cell.volume)
    end
    
    # Surface integral over boundary faces
    for face in mesh.faces
        if face.neighbour <= 0  # Boundary face
            flux = dot(vector_field[face.owner], face.area)
            total_surface_integral += flux
        end
    end
    
    return abs(total_volume_integral - total_surface_integral)
end

function estimate_divergence_convergence_order(mesh::Mesh)::Float64
    # Simplified - in practice use Richardson extrapolation
    return 2.0  # Assume 2nd order for well-implemented divergence
end

function assemble_laplacian_matrix(mesh::Mesh)::SparseMatrixCSC{Float64, Int}
    n_cells = length(mesh.cells)
    I = Int[]
    J = Int[]
    V = Float64[]
    
    for (cell_idx, cell) in enumerate(mesh.cells)
        diagonal_coeff = 0.0
        
        for face in mesh.faces
            if face.owner == cell_idx
                # Contribution from this face
                if face.neighbour > 0
                    # Internal face
                    distance = norm(mesh.cells[face.neighbour].center - cell.center)
                    area = norm(face.area)
                    coeff = area / distance
                    
                    # Off-diagonal coefficient
                    push!(I, cell_idx)
                    push!(J, face.neighbour)
                    push!(V, coeff)
                    
                    diagonal_coeff -= coeff
                else
                    # Boundary face - contributes to diagonal
                    distance = 2 * norm(face.center - cell.center)  # Approximate
                    area = norm(face.area)
                    coeff = area / distance
                    diagonal_coeff -= coeff
                end
            elseif face.neighbour == cell_idx
                # Face owned by neighbor
                distance = norm(mesh.cells[face.owner].center - cell.center)
                area = norm(face.area)
                coeff = area / distance
                
                push!(I, cell_idx)
                push!(J, face.owner)
                push!(V, coeff)
                
                diagonal_coeff -= coeff
            end
        end
        
        # Diagonal coefficient
        push!(I, cell_idx)
        push!(J, cell_idx)
        push!(V, diagonal_coeff)
    end
    
    return sparse(I, J, V, n_cells, n_cells)
end

function check_matrix_symmetry(matrix::SparseMatrixCSC, tolerance::Float64)::Bool
    return norm(matrix - matrix') < tolerance * norm(matrix)
end

function estimate_laplacian_convergence_order(mesh::Mesh)::Float64
    # Simplified - in practice use Richardson extrapolation
    return 2.0  # Assume 2nd order for well-implemented Laplacian
end

function compute_face_interpolation(mesh::Mesh, field_values::Vector{Float64}, scheme::String)::Vector{Float64}
    face_values = Float64[]
    
    for face in mesh.faces
        if face.neighbour > 0
            # Internal face - linear interpolation
            owner_val = field_values[face.owner]
            neighbor_val = field_values[face.neighbour]
            
            # Simple linear interpolation (could be distance-weighted)
            face_val = 0.5 * (owner_val + neighbor_val)
            push!(face_values, face_val)
        else
            # Boundary face - use owner value
            push!(face_values, field_values[face.owner])
        end
    end
    
    return face_values
end

function compute_interpolation_error(mesh::Mesh, cell_values::Vector{Float64}, face_values::Vector{Float64})::Float64
    # For linear field, face values should exactly match linear interpolation
    total_error = 0.0
    n_internal_faces = 0
    
    for (face_idx, face) in enumerate(mesh.faces)
        if face.neighbour > 0  # Internal face
            expected_val = 0.5 * (cell_values[face.owner] + cell_values[face.neighbour])
            error = abs(face_values[face_idx] - expected_val)
            total_error += error^2
            n_internal_faces += 1
        end
    end
    
    return n_internal_faces > 0 ? sqrt(total_error / n_internal_faces) : 0.0
end

function check_interpolation_boundedness(mesh::Mesh, cell_values::Vector{Float64}, face_values::Vector{Float64})::Bool
    for (face_idx, face) in enumerate(mesh.faces)
        if face.neighbour > 0  # Internal face
            min_val = min(cell_values[face.owner], cell_values[face.neighbour])
            max_val = max(cell_values[face.owner], cell_values[face.neighbour])
            
            if face_values[face_idx] < min_val - 1e-12 || face_values[face_idx] > max_val + 1e-12
                return false
            end
        end
    end
    return true
end

function check_interpolation_monotonicity(mesh::Mesh, cell_values::Vector{Float64}, face_values::Vector{Float64})::Bool
    # Simplified monotonicity check
    return true  # Would need more sophisticated implementation
end

function check_boundary_normal_consistency(mesh::Mesh)::Bool
    # Check that boundary normals point outward
    for face in mesh.faces
        if face.neighbour <= 0  # Boundary face
            # Simplified check - in practice would verify outward orientation
            if norm(face.area) < 1e-15
                return false
            end
        end
    end
    return true
end

function assess_fvc_quality(grad_order::Float64, div_order::Float64, lap_order::Float64, 
                           grad_issues::Vector{String}, div_issues::Vector{String}, 
                           lap_issues::Vector{String}, interp_issues::Vector{String})::String
    # Critical issues
    total_issues = length(grad_issues) + length(div_issues) + length(lap_issues) + length(interp_issues)
    if total_issues > 5
        return "CRITICAL"
    end
    
    # Poor convergence
    if min(grad_order, div_order, lap_order) < 1.0
        return "POOR"
    end
    
    # Acceptable convergence
    if min(grad_order, div_order, lap_order) < 1.5
        return "ACCEPTABLE"
    end
    
    # Good convergence
    if min(grad_order, div_order, lap_order) > 1.8 && total_issues < 2
        return "EXCELLENT"
    end
    
    return "GOOD"
end

function calculate_memory_efficiency(mesh::Mesh)::Float64
    # Simple memory efficiency score based on mesh size
    n_cells = length(mesh.cells)
    n_faces = length(mesh.faces)
    
    # Ideal ratio of faces to cells for efficiency
    ideal_ratio = 6.0  # Roughly hexahedral cells
    actual_ratio = n_faces / (n_cells + 1e-15)
    
    return min(ideal_ratio / actual_ratio, actual_ratio / ideal_ratio)
end

"""
Generate comprehensive FVC diagnostic report
"""
function fvc_report(results::FVCDiagnosticResults; output_file::Union{String, Nothing}=nothing)
    report_lines = String[]
    
    push!(report_lines, "")
    push!(report_lines, "🧮 JuliaFOAM FVC Diagnostic Report")
    push!(report_lines, "="^70)
    push!(report_lines, "Generated: $(now())")
    push!(report_lines, "")
    
    # Operator Accuracy
    push!(report_lines, "📐 Operator Accuracy Analysis")
    push!(report_lines, "-"^40)
    push!(report_lines, "Gradient Operator:")
    push!(report_lines, "  Convergence Order: $(round(results.gradient_convergence_order, digits=2))")
    push!(report_lines, "  Boundary Consistent: $(results.gradient_boundary_consistency)")
    push!(report_lines, "")
    push!(report_lines, "Divergence Operator:")
    push!(report_lines, "  Convergence Order: $(round(results.divergence_convergence_order, digits=2))")
    push!(report_lines, "  Conservation: $(results.divergence_conservation)")
    push!(report_lines, "")
    push!(report_lines, "Laplacian Operator:")
    push!(report_lines, "  Convergence Order: $(round(results.laplacian_convergence_order, digits=2))")
    push!(report_lines, "  Matrix Symmetry: $(results.laplacian_symmetry)")
    push!(report_lines, "")
    push!(report_lines, "Interpolation Schemes:")
    push!(report_lines, "  Boundedness: $(results.interpolation_boundedness)")
    push!(report_lines, "  Monotonicity: $(results.interpolation_monotonicity)")
    push!(report_lines, "")
    
    # Performance Metrics
    push!(report_lines, "⚡ Performance Metrics")
    push!(report_lines, "-"^40)
    push!(report_lines, "Gradient Performance: $(round(results.gradient_performance_ms, digits=2)) ms")
    push!(report_lines, "Divergence Performance: $(round(results.divergence_performance_ms, digits=2)) ms")
    push!(report_lines, "Laplacian Performance: $(round(results.laplacian_performance_ms, digits=2)) ms")
    push!(report_lines, "Memory Efficiency Score: $(round(results.memory_efficiency_score, digits=2))")
    push!(report_lines, "")
    
    # Critical Issues
    if !isempty(results.critical_fvc_issues)
        push!(report_lines, "⚠️  Critical FVC Issues")
        push!(report_lines, "-"^40)
        for issue in results.critical_fvc_issues
            push!(report_lines, "• $issue")
        end
        push!(report_lines, "")
    end
    
    # Recommendations
    if !isempty(results.fvc_recommendations)
        push!(report_lines, "💡 FVC Recommendations")
        push!(report_lines, "-"^40)
        for rec in results.fvc_recommendations
            push!(report_lines, "• $rec")
        end
        push!(report_lines, "")
    end
    
    # Overall Assessment
    status_emoji = results.overall_fvc_quality == "EXCELLENT" ? "🏆" :
                   results.overall_fvc_quality == "GOOD" ? "✅" :
                   results.overall_fvc_quality == "ACCEPTABLE" ? "⚠️" :
                   results.overall_fvc_quality == "POOR" ? "❌" : "🚨"
    
    push!(report_lines, "🎯 Overall FVC Assessment")
    push!(report_lines, "-"^40)
    push!(report_lines, "$status_emoji $(results.overall_fvc_quality)")
    push!(report_lines, "")
    
    report = join(report_lines, "\n")
    
    if output_file !== nothing
        open(output_file, "w") do f
            write(f, report)
        end
        println("FVC Report saved to: $output_file")
    else
        println(report)
    end
    
    return report
end

end  # module FVCDiagnostics