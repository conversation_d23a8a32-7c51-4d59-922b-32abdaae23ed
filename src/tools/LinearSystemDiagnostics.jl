"""
    LinearSystemDiagnostics.jl

This module provides diagnostic tools for linear systems in JuliaFOAM.
It helps identify and fix issues that can lead to NaN residuals or poor convergence.
"""
module LinearSystemDiagnostics

using LinearAlgebra
using SparseArrays
using Printf
using Statistics

export diagnose_linear_system, fix_linear_system
export analyze_convergence_history, plot_residual_history
export check_matrix_conditioning, check_boundary_conditions
export generate_diagnostic_report

"""
    diagnose_linear_system(A::SparseMatrixCSC{Float64, Int}, b::Vector{Float64}, x::Vector{Float64}=zeros(length(b)))

Diagnose potential issues with a linear system.

# Arguments
- `A`: System matrix
- `b`: Right-hand side vector
- `x`: Current solution vector (optional)

# Returns
- `Dict`: Dictionary of diagnostic information
"""
function diagnose_linear_system(A::SparseMatrixCSC{Float64, Int}, b::Vector{Float64}, x::Vector{Float64}=zeros(length(b)))
    diagnostics = Dict{Symbol, Any}()
    
    # Check matrix dimensions
    m, n = size(A)
    diagnostics[:matrix_dimensions] = (m, n)
    
    if m != n
        diagnostics[:rectangular_matrix] = true
        diagnostics[:issues] = ["Matrix is rectangular ($(m)×$(n)), not square"]
        return diagnostics
    end
    
    if length(b) != m
        diagnostics[:dimension_mismatch] = true
        diagnostics[:issues] = ["Dimension mismatch: A is $(m)×$(n), b is $(length(b))"]
        return diagnostics
    end
    
    # Check for NaN or Inf in matrix
    if any(isnan.(nonzeros(A))) || any(isinf.(nonzeros(A)))
        diagnostics[:matrix_has_nan_inf] = true
        push!(get!(diagnostics, :issues, String[]), "Matrix contains NaN or Inf values")
    else
        diagnostics[:matrix_has_nan_inf] = false
    end
    
    # Check for NaN or Inf in right-hand side
    if any(isnan.(b)) || any(isinf.(b))
        diagnostics[:rhs_has_nan_inf] = true
        push!(get!(diagnostics, :issues, String[]), "Right-hand side contains NaN or Inf values")
    else
        diagnostics[:rhs_has_nan_inf] = false
    end
    
    # Check for zero right-hand side
    b_norm = norm(b)
    diagnostics[:rhs_norm] = b_norm
    
    if b_norm < 1e-15
        diagnostics[:zero_rhs] = true
        push!(get!(diagnostics, :issues, String[]), "Right-hand side is effectively zero (norm = $(b_norm))")
    else
        diagnostics[:zero_rhs] = false
    end
    
    # Check for zero diagonal entries
    diag_vals = diag(A)
    zero_diags = findall(abs.(diag_vals) .< 1e-15)
    diagnostics[:zero_diagonals] = zero_diags
    
    if !isempty(zero_diags)
        push!(get!(diagnostics, :issues, String[]), "Matrix has $(length(zero_diags)) zero diagonal entries")
    end
    
    # Check for zero rows/columns
    zero_rows = Int[]
    zero_cols = Int[]
    
    for i in 1:n
        if nnz(A[i, :]) == 0
            push!(zero_rows, i)
        end
        
        if nnz(A[:, i]) == 0
            push!(zero_cols, i)
        end
    end
    
    diagnostics[:zero_rows] = zero_rows
    diagnostics[:zero_cols] = zero_cols
    
    if !isempty(zero_rows)
        push!(get!(diagnostics, :issues, String[]), "Matrix has $(length(zero_rows)) zero rows")
    end
    
    if !isempty(zero_cols)
        push!(get!(diagnostics, :issues, String[]), "Matrix has $(length(zero_cols)) zero columns")
    end
    
    # Check for diagonal dominance
    diag_dominant = true
    weak_rows = Int[]
    
    for i in 1:n
        diag_val = abs(A[i, i])
        row_sum = sum(abs.(A[i, :]))
        
        if diag_val < row_sum - diag_val
            diag_dominant = false
            push!(weak_rows, i)
        end
    end
    
    diagnostics[:diag_dominant] = diag_dominant
    diagnostics[:weak_rows] = weak_rows
    
    if !diag_dominant
        push!(get!(diagnostics, :issues, String[]), "Matrix is not diagonally dominant ($(length(weak_rows)) weak rows)")
    end
    
    # Check for symmetry
    is_symmetric = issymmetric(A)
    diagnostics[:symmetric] = is_symmetric
    
    # Check for positive definiteness (for symmetric matrices)
    if is_symmetric && n <= 1000
        try
            # Convert to dense for eigenvalue computation
            A_dense = Array(A)
            eigvals = eigen(A_dense, sortby=abs).values
            min_eigval = minimum(real.(eigvals))
            max_eigval = maximum(real.(eigvals))
            
            diagnostics[:min_eigenvalue] = min_eigval
            diagnostics[:max_eigenvalue] = max_eigval
            
            if min_eigval < 0
                diagnostics[:positive_definite] = false
                push!(get!(diagnostics, :issues, String[]), "Matrix is not positive definite (min eigenvalue = $(min_eigval))")
            else
                diagnostics[:positive_definite] = true
            end
            
            # Estimate condition number
            cond_est = max_eigval / abs(min_eigval)
            diagnostics[:condition_number] = cond_est
            
            if cond_est > 1e8
                diagnostics[:ill_conditioned] = true
                push!(get!(diagnostics, :issues, String[]), "Matrix is ill-conditioned (condition number ≈ $(cond_est))")
            else
                diagnostics[:ill_conditioned] = false
            end
        catch e
            # Eigenvalue computation failed
            diagnostics[:eigenvalue_computation_failed] = true
            push!(get!(diagnostics, :issues, String[]), "Eigenvalue computation failed: $(e)")
        end
    elseif is_symmetric
        # Matrix too large for direct eigenvalue computation
        # Use power iteration to estimate largest eigenvalue
        try
            max_eigval_est = power_iteration(A, 20)
            min_eigval_est = inverse_power_iteration(A, 20)
            
            diagnostics[:min_eigenvalue_est] = min_eigval_est
            diagnostics[:max_eigenvalue_est] = max_eigval_est
            
            if min_eigval_est < 0
                diagnostics[:positive_definite] = false
                push!(get!(diagnostics, :issues, String[]), "Matrix is likely not positive definite (est. min eigenvalue = $(min_eigval_est))")
            else
                diagnostics[:positive_definite] = true
            end
            
            # Estimate condition number
            cond_est = max_eigval_est / abs(min_eigval_est)
            diagnostics[:condition_number_est] = cond_est
            
            if cond_est > 1e8
                diagnostics[:ill_conditioned] = true
                push!(get!(diagnostics, :issues, String[]), "Matrix is likely ill-conditioned (est. condition number ≈ $(cond_est))")
            else
                diagnostics[:ill_conditioned] = false
            end
        catch e
            # Eigenvalue estimation failed
            diagnostics[:eigenvalue_estimation_failed] = true
            push!(get!(diagnostics, :issues, String[]), "Eigenvalue estimation failed: $(e)")
        end
    end
    
    # Check current residual
    r = b - A * x
    r_norm = norm(r)
    rel_residual = r_norm / max(b_norm, 1e-15)
    
    diagnostics[:residual_norm] = r_norm
    diagnostics[:relative_residual] = rel_residual
    
    # Check for NaN or Inf in residual
    if any(isnan.(r)) || any(isinf.(r))
        diagnostics[:residual_has_nan_inf] = true
        push!(get!(diagnostics, :issues, String[]), "Residual contains NaN or Inf values")
    else
        diagnostics[:residual_has_nan_inf] = false
    end
    
    # Generate recommendations
    recommendations = String[]
    
    if !isempty(zero_diags)
        push!(recommendations, "Add regularization to zero diagonal entries (e.g., A[i,i] += 1e-8 for zero diagonals)")
    end
    
    if !isempty(zero_rows) || !isempty(zero_cols)
        push!(recommendations, "Check boundary conditions and discretization for zero rows/columns")
    end
    
    if get(diagnostics, :ill_conditioned, false)
        push!(recommendations, "Use a preconditioner (e.g., diagonal, incomplete Cholesky, or incomplete LU)")
    end
    
    if !diag_dominant
        push!(recommendations, "Consider using a more robust solver (e.g., GMRES instead of CG)")
    end
    
    if get(diagnostics, :matrix_has_nan_inf, false) || get(diagnostics, :rhs_has_nan_inf, false)
        push!(recommendations, "Fix NaN/Inf values in the matrix or right-hand side")
    end
    
    diagnostics[:recommendations] = recommendations
    
    return diagnostics
end

"""
    fix_linear_system(A::SparseMatrixCSC{Float64, Int}, b::Vector{Float64}, diagnostics::Dict)

Fix common issues in a linear system based on diagnostic information.

# Arguments
- `A`: System matrix (will be modified)
- `b`: Right-hand side vector (will be modified)
- `diagnostics`: Diagnostic information from diagnose_linear_system

# Returns
- `Tuple{SparseMatrixCSC{Float64, Int}, Vector{Float64}}`: Fixed matrix and right-hand side
"""
function fix_linear_system(A::SparseMatrixCSC{Float64, Int}, b::Vector{Float64}, diagnostics::Dict)
    # Create copies to avoid modifying the originals
    A_fixed = copy(A)
    b_fixed = copy(b)
    
    # Fix NaN/Inf values in matrix
    if get(diagnostics, :matrix_has_nan_inf, false)
        for i in 1:nnz(A_fixed)
            if isnan(A_fixed.nzval[i]) || isinf(A_fixed.nzval[i])
                A_fixed.nzval[i] = 0.0
            end
        end
    end
    
    # Fix NaN/Inf values in right-hand side
    if get(diagnostics, :rhs_has_nan_inf, false)
        for i in 1:length(b_fixed)
            if isnan(b_fixed[i]) || isinf(b_fixed[i])
                b_fixed[i] = 0.0
            end
        end
    end
    
    # Fix zero diagonal entries
    if !isempty(get(diagnostics, :zero_diagonals, Int[]))
        for i in diagnostics[:zero_diagonals]
            # Add small value to diagonal
            A_fixed[i, i] = 1.0
            
            # Set corresponding right-hand side entry to zero
            b_fixed[i] = 0.0
        end
    end
    
    # Fix zero rows/columns
    zero_rows = get(diagnostics, :zero_rows, Int[])
    zero_cols = get(diagnostics, :zero_cols, Int[])
    
    for i in zero_rows
        # Set diagonal to 1 and right-hand side to 0
        A_fixed[i, i] = 1.0
        b_fixed[i] = 0.0
    end
    
    for i in zero_cols
        # Set diagonal to 1
        A_fixed[i, i] = 1.0
    end
    
    # Apply diagonal scaling for ill-conditioned matrices
    if get(diagnostics, :ill_conditioned, false)
        # Extract diagonal
        d = diag(A_fixed)
        
        # Create scaling factors
        d_inv = similar(d)
        
        for i in 1:length(d)
            if abs(d[i]) > 1e-15
                d_inv[i] = 1.0 / sqrt(abs(d[i]))
            else
                d_inv[i] = 1.0
            end
        end
        
        # Apply scaling: A' = D^(-1/2) * A * D^(-1/2)
        for j in 1:size(A_fixed, 2)
            for i in nzrange(A_fixed, j)
                row = A_fixed.rowval[i]
                A_fixed.nzval[i] *= d_inv[row] * d_inv[j]
            end
        end
        
        # Apply scaling to right-hand side: b' = D^(-1/2) * b
        b_fixed .*= d_inv
    end
    
    return A_fixed, b_fixed
end

"""
    power_iteration(A::SparseMatrixCSC{Float64, Int}, max_iter::Int=20)

Estimate the largest eigenvalue of a matrix using power iteration.

# Arguments
- `A`: Matrix
- `max_iter`: Maximum number of iterations

# Returns
- `Float64`: Estimated largest eigenvalue
"""
function power_iteration(A::SparseMatrixCSC{Float64, Int}, max_iter::Int=20)
    n = size(A, 1)
    x = randn(n)
    x ./= norm(x)
    
    for _ in 1:max_iter
        y = A * x
        x = y ./ norm(y)
    end
    
    return dot(x, A * x) / dot(x, x)
end

"""
    inverse_power_iteration(A::SparseMatrixCSC{Float64, Int}, max_iter::Int=20)

Estimate the smallest eigenvalue of a matrix using inverse power iteration.

# Arguments
- `A`: Matrix
- `max_iter`: Maximum number of iterations

# Returns
- `Float64`: Estimated smallest eigenvalue
"""
function inverse_power_iteration(A::SparseMatrixCSC{Float64, Int}, max_iter::Int=20)
    n = size(A, 1)
    x = randn(n)
    x ./= norm(x)
    
    # Add small regularization to avoid singular matrix
    A_reg = A + 1e-10 * sparse(I, n, n)
    
    for _ in 1:max_iter
        y = A_reg \ x
        x = y ./ norm(y)
    end
    
    return dot(x, A * x) / dot(x, x)
end

"""
    analyze_convergence_history(residuals::Vector{Float64})

Analyze the convergence history of a linear solver.

# Arguments
- `residuals`: Vector of residual norms

# Returns
- `Dict`: Analysis results
"""
function analyze_convergence_history(residuals::Vector{Float64})
    analysis = Dict{Symbol, Any}()
    
    n = length(residuals)
    
    if n < 2
        analysis[:too_few_iterations] = true
        return analysis
    end
    
    # Check for NaN or Inf
    if any(isnan.(residuals)) || any(isinf.(residuals))
        analysis[:has_nan_inf] = true
        nan_inf_indices = findall(isnan.(residuals) .| isinf.(residuals))
        analysis[:nan_inf_indices] = nan_inf_indices
        analysis[:first_nan_inf] = minimum(nan_inf_indices)
    else
        analysis[:has_nan_inf] = false
    end
    
    # Check for convergence
    initial_residual = residuals[1]
    final_residual = residuals[end]
    
    analysis[:initial_residual] = initial_residual
    analysis[:final_residual] = final_residual
    analysis[:reduction_factor] = final_residual / initial_residual
    
    # Calculate convergence rate
    if n >= 3
        # Use last few iterations to estimate convergence rate
        rates = Float64[]
        
        for i in 3:n
            if residuals[i-1] > 0 && residuals[i-2] > 0
                rate = residuals[i] / residuals[i-1]
                push!(rates, rate)
            end
        end
        
        if !isempty(rates)
            analysis[:convergence_rate] = mean(rates)
            
            if analysis[:convergence_rate] >= 1.0
                analysis[:diverging] = true
            elseif analysis[:convergence_rate] > 0.9
                analysis[:slow_convergence] = true
            else
                analysis[:good_convergence] = true
            end
        end
    end
    
    # Check for stagnation
    stagnation_threshold = 1e-3
    stagnation_count = 0
    
    for i in 2:n
        if abs(residuals[i] - residuals[i-1]) / residuals[i-1] < stagnation_threshold
            stagnation_count += 1
        end
    end
    
    analysis[:stagnation_count] = stagnation_count
    
    if stagnation_count > n / 3
        analysis[:stagnating] = true
    else
        analysis[:stagnating] = false
    end
    
    return analysis
end

"""
    generate_diagnostic_report(diagnostics::Dict, convergence_analysis::Dict)

Generate a human-readable diagnostic report.

# Arguments
- `diagnostics`: Diagnostic information from diagnose_linear_system
- `convergence_analysis`: Analysis from analyze_convergence_history

# Returns
- `String`: Diagnostic report
"""
function generate_diagnostic_report(diagnostics::Dict, convergence_analysis::Dict)
    io = IOBuffer()
    
    println(io, "=== Linear System Diagnostic Report ===")
    println(io)
    
    # Matrix properties
    println(io, "Matrix Properties:")
    println(io, "  Dimensions: $(diagnostics[:matrix_dimensions])")
    println(io, "  Symmetric: $(diagnostics[:symmetric])")
    println(io, "  Diagonally Dominant: $(diagnostics[:diag_dominant])")
    
    if haskey(diagnostics, :condition_number)
        println(io, "  Condition Number: $(diagnostics[:condition_number])")
    elseif haskey(diagnostics, :condition_number_est)
        println(io, "  Estimated Condition Number: $(diagnostics[:condition_number_est])")
    end
    
    if haskey(diagnostics, :positive_definite)
        println(io, "  Positive Definite: $(diagnostics[:positive_definite])")
    end
    
    println(io)
    
    # Issues
    if haskey(diagnostics, :issues) && !isempty(diagnostics[:issues])
        println(io, "Issues Detected:")
        for (i, issue) in enumerate(diagnostics[:issues])
            println(io, "  $(i). $(issue)")
        end
        println(io)
    else
        println(io, "No matrix issues detected.")
        println(io)
    end
    
    # Convergence analysis
    println(io, "Convergence Analysis:")
    
    if haskey(convergence_analysis, :has_nan_inf) && convergence_analysis[:has_nan_inf]
        println(io, "  WARNING: NaN or Inf values detected in residuals")
        println(io, "  First occurrence at iteration $(convergence_analysis[:first_nan_inf])")
    end
    
    if haskey(convergence_analysis, :initial_residual)
        println(io, "  Initial Residual: $(convergence_analysis[:initial_residual])")
        println(io, "  Final Residual: $(convergence_analysis[:final_residual])")
        println(io, "  Reduction Factor: $(convergence_analysis[:reduction_factor])")
    end
    
    if haskey(convergence_analysis, :convergence_rate)
        println(io, "  Convergence Rate: $(convergence_analysis[:convergence_rate])")
        
        if get(convergence_analysis, :diverging, false)
            println(io, "  WARNING: Solution is diverging")
        elseif get(convergence_analysis, :slow_convergence, false)
            println(io, "  WARNING: Convergence is very slow")
        elseif get(convergence_analysis, :good_convergence, false)
            println(io, "  Convergence is good")
        end
    end
    
    if get(convergence_analysis, :stagnating, false)
        println(io, "  WARNING: Solution is stagnating")
    end
    
    println(io)
    
    # Recommendations
    if haskey(diagnostics, :recommendations) && !isempty(diagnostics[:recommendations])
        println(io, "Recommendations:")
        for (i, rec) in enumerate(diagnostics[:recommendations])
            println(io, "  $(i). $(rec)")
        end
    end
    
    return String(take!(io))
end

end # module LinearSystemDiagnostics
