#!/usr/bin/env julia

"""
JuliaFOAM Doctor - Master Diagnostic and Health Check Utility

This is the main entry point for comprehensive JuliaFOAM diagnostics.
It coordinates mesh, FVC, and FVM diagnostics to provide a complete
health assessment of your CFD simulation setup.

Features:
- Unified diagnostic interface
- Comprehensive health scoring
- Automated issue detection and prioritization
- Performance optimization recommendations
- Integration with validation framework
- Export capabilities for CI/CD integration
"""

module JuliaFOAMDoctor

using Printf
using Dates
using LinearAlgebra
using SparseArrays

# Add parent directories to path for importing JuliaFOAM
push!(LOAD_PATH, joinpath(@__DIR__, ".."))

include("../core/Types.jl")
include("MeshDiagnostics.jl")
include("FVCDiagnostics.jl") 
include("FVMDiagnostics.jl")

using .MeshDiagnostics
using .FVCDiagnostics
using .FVMDiagnostics

export JuliaFOAMHealthCheck, run_full_diagnostics, quick_health_check
export generate_health_report, get_health_score, doctor_recommendations

"""
Comprehensive health check results
"""
struct JuliaFOAMHealthResults
    # Individual diagnostic results
    mesh_results::MeshDiagnosticResults
    fvc_results::FVCDiagnosticResults
    fvm_results::FVMDiagnosticResults
    
    # Overall health metrics
    overall_health_score::Float64  # 0-100 scale
    health_grade::String  # A+, A, B, C, D, F
    
    # Prioritized issues
    critical_issues::Vector{Tuple{String, String, Int}}  # (category, issue, priority)
    warnings::Vector{Tuple{String, String}}  # (category, warning)
    recommendations::Vector{Tuple{String, String, String}}  # (category, recommendation, urgency)
    
    # Performance metrics
    total_diagnostic_time::Float64
    system_readiness::String  # "PRODUCTION_READY", "DEVELOPMENT_READY", "NEEDS_WORK", "CRITICAL_ISSUES"
    
    # Validation status
    validation_status::Dict{String, Bool}
    validation_coverage::Float64  # Percentage of tests passing
    
    # Summary statistics
    total_cells::Int
    total_faces::Int
    estimated_memory_mb::Float64
    expected_performance_score::Float64
end

"""
Master health check utility
"""
struct JuliaFOAMHealthCheck
    mesh::Mesh
    verbose::Bool
    tolerance::Float64
    run_performance_tests::Bool
    validation_mode::Bool
    
    function JuliaFOAMHealthCheck(mesh::Mesh; verbose=true, tolerance=1e-10, 
                                 run_performance_tests=true, validation_mode=false)
        new(mesh, verbose, tolerance, run_performance_tests, validation_mode)
    end
end

"""
Run comprehensive diagnostics on JuliaFOAM system
"""
function run_full_diagnostics(health_check::JuliaFOAMHealthCheck)::JuliaFOAMHealthResults
    if health_check.verbose
        println("🩺 JuliaFOAM Doctor - Comprehensive Health Check")
        println("="^80)
        println("Started at: $(now())")
        println("Mesh: $(length(health_check.mesh.cells)) cells, $(length(health_check.mesh.faces)) faces")
        println()
    end
    
    start_time = time()
    
    # 1. Mesh Diagnostics
    if health_check.verbose
        println("📐 Running Mesh Diagnostics...")
    end
    
    mesh_doctor = MeshDoctor(health_check.mesh, 
                            verbose=health_check.verbose, 
                            tolerance=health_check.tolerance)
    mesh_results = diagnose_mesh(mesh_doctor)
    
    # 2. FVC Diagnostics  
    if health_check.verbose
        println("\n🧮 Running FVC Operator Diagnostics...")
    end
    
    fvc_doctor = FVCDoctor(health_check.mesh,
                          verbose=health_check.verbose,
                          tolerance=health_check.tolerance)
    fvc_results = diagnose_fvc_operators(fvc_doctor)
    
    # 3. FVM Diagnostics
    if health_check.verbose
        println("\n⚙️  Running FVM System Diagnostics...")
    end
    
    fvm_doctor = FVMDoctor(health_check.mesh,
                          verbose=health_check.verbose, 
                          tolerance=health_check.tolerance)
    fvm_results = diagnose_fvm_system(fvm_doctor)
    
    total_diagnostic_time = time() - start_time
    
    # 4. Comprehensive Analysis
    if health_check.verbose
        println("\n📊 Performing Comprehensive Analysis...")
    end
    
    # Calculate overall health score
    health_score = calculate_health_score(mesh_results, fvc_results, fvm_results)
    health_grade = score_to_grade(health_score)
    
    # Prioritize issues
    critical_issues = prioritize_issues(mesh_results, fvc_results, fvm_results)
    warnings = collect_warnings(mesh_results, fvc_results, fvm_results)
    recommendations = generate_prioritized_recommendations(mesh_results, fvc_results, fvm_results)
    
    # System readiness assessment
    system_readiness = assess_system_readiness(health_score, critical_issues)
    
    # Validation status
    validation_status, validation_coverage = assess_validation_status(health_check, mesh_results, fvc_results, fvm_results)
    
    # Performance estimation
    expected_performance_score = estimate_performance_score(mesh_results, fvc_results, fvm_results)
    
    if health_check.verbose
        println("\n🎯 Health Assessment Complete!")
        println("Overall Health Score: $(round(health_score, digits=1))/100.0 ($(health_grade))")
        println("System Readiness: $(system_readiness)")
        println("Critical Issues: $(length(critical_issues))")
        println("Validation Coverage: $(round(validation_coverage, digits=1))%")
        println("Diagnostic Time: $(round(total_diagnostic_time, digits=2)) seconds")
    end
    
    return JuliaFOAMHealthResults(
        mesh_results, fvc_results, fvm_results,
        health_score, health_grade,
        critical_issues, warnings, recommendations,
        total_diagnostic_time, system_readiness,
        validation_status, validation_coverage,
        length(health_check.mesh.cells), length(health_check.mesh.faces),
        mesh_results.memory_usage_mb + fvm_results.memory_usage_mb,
        expected_performance_score
    )
end

"""
Quick health check for rapid assessment
"""
function quick_health_check(mesh::Mesh; verbose=false)::JuliaFOAMHealthResults
    health_check = JuliaFOAMHealthCheck(mesh, verbose=verbose, run_performance_tests=false)
    return run_full_diagnostics(health_check)
end

"""
Calculate overall health score from individual diagnostics
"""
function calculate_health_score(mesh_results::MeshDiagnosticResults, 
                               fvc_results::FVCDiagnosticResults,
                               fvm_results::FVMDiagnosticResults)::Float64
    
    # Mesh health (30% weight)
    mesh_score = 100.0
    
    # Deduct for mesh issues
    if mesh_results.overall_quality == "CRITICAL"
        mesh_score = 20.0
    elseif mesh_results.overall_quality == "POOR"
        mesh_score = 40.0
    elseif mesh_results.overall_quality == "ACCEPTABLE"
        mesh_score = 60.0
    elseif mesh_results.overall_quality == "GOOD"
        mesh_score = 80.0
    end
    
    # Deduct for specific mesh issues
    mesh_score -= length(mesh_results.critical_issues) * 10
    mesh_score -= length(mesh_results.connectivity_issues) * 5
    mesh_score -= length(mesh_results.geometric_issues) * 5
    
    # FVC health (35% weight)
    fvc_score = 100.0
    
    if fvc_results.overall_fvc_quality == "CRITICAL"
        fvc_score = 20.0
    elseif fvc_results.overall_fvc_quality == "POOR"
        fvc_score = 40.0
    elseif fvc_results.overall_fvc_quality == "ACCEPTABLE"
        fvc_score = 60.0
    elseif fvc_results.overall_fvc_quality == "GOOD"
        fvc_score = 80.0
    end
    
    # Convergence order penalties
    if fvc_results.gradient_convergence_order < 1.5
        fvc_score -= 15
    end
    if fvc_results.divergence_convergence_order < 1.5
        fvc_score -= 15
    end
    if fvc_results.laplacian_convergence_order < 1.5
        fvc_score -= 15
    end
    
    # Conservation penalties
    if !fvc_results.divergence_conservation
        fvc_score -= 20
    end
    if !fvc_results.laplacian_symmetry
        fvc_score -= 10
    end
    
    # FVM health (35% weight)
    fvm_score = 100.0
    
    if fvm_results.overall_fvm_quality == "CRITICAL"
        fvm_score = 20.0
    elseif fvm_results.overall_fvm_quality == "POOR"
        fvm_score = 40.0
    elseif fvm_results.overall_fvm_quality == "ACCEPTABLE"
        fvm_score = 60.0
    elseif fvm_results.overall_fvm_quality == "GOOD"
        fvm_score = 80.0
    end
    
    # Matrix and solver penalties
    fvm_score -= length(fvm_results.matrix_issues) * 8
    fvm_score -= length(fvm_results.solver_issues) * 8
    fvm_score -= length(fvm_results.conservation_issues) * 15
    fvm_score -= length(fvm_results.stability_issues) * 10
    
    # Solution quality penalties
    if !fvm_results.solution_boundedness
        fvm_score -= 20
    end
    if length(fvm_results.unphysical_values) > 0
        fvm_score -= min(length(fvm_results.unphysical_values), 25)
    end
    
    # Weighted average
    overall_score = 0.30 * max(mesh_score, 0) + 0.35 * max(fvc_score, 0) + 0.35 * max(fvm_score, 0)
    
    return min(max(overall_score, 0.0), 100.0)
end

"""
Convert numerical score to letter grade
"""
function score_to_grade(score::Float64)::String
    if score >= 95
        return "A+"
    elseif score >= 90
        return "A"
    elseif score >= 85
        return "A-"
    elseif score >= 80
        return "B+"
    elseif score >= 75
        return "B"
    elseif score >= 70
        return "B-"
    elseif score >= 65
        return "C+"
    elseif score >= 60
        return "C"
    elseif score >= 55
        return "C-"
    elseif score >= 50
        return "D+"
    elseif score >= 45
        return "D"
    elseif score >= 40
        return "D-"
    else
        return "F"
    end
end

"""
Prioritize issues across all diagnostic categories
"""
function prioritize_issues(mesh_results::MeshDiagnosticResults,
                          fvc_results::FVCDiagnosticResults, 
                          fvm_results::FVMDiagnosticResults)::Vector{Tuple{String, String, Int}}
    
    issues = Tuple{String, String, Int}[]
    
    # Critical mesh issues (priority 1-3)
    for issue in mesh_results.critical_issues
        if occursin("negative volume", lowercase(issue))
            push!(issues, ("Mesh", issue, 1))
        elseif occursin("isolated", lowercase(issue))
            push!(issues, ("Mesh", issue, 2))
        else
            push!(issues, ("Mesh", issue, 3))
        end
    end
    
    # Critical FVC issues (priority 2-4)
    for issue in fvc_results.critical_fvc_issues
        if occursin("conservation", lowercase(issue))
            push!(issues, ("FVC", issue, 2))
        elseif occursin("convergence", lowercase(issue))
            push!(issues, ("FVC", issue, 3))
        else
            push!(issues, ("FVC", issue, 4))
        end
    end
    
    # Critical FVM issues (priority 1-4)
    for issue in fvm_results.critical_fvm_issues
        if occursin("conservation", lowercase(issue))
            push!(issues, ("FVM", issue, 1))
        elseif occursin("matrix", lowercase(issue))
            push!(issues, ("FVM", issue, 2))
        elseif occursin("solver", lowercase(issue))
            push!(issues, ("FVM", issue, 3))
        else
            push!(issues, ("FVM", issue, 4))
        end
    end
    
    # Sort by priority
    sort!(issues, by=x->x[3])
    
    return issues
end

"""
Collect warnings from all diagnostic categories
"""
function collect_warnings(mesh_results::MeshDiagnosticResults,
                         fvc_results::FVCDiagnosticResults,
                         fvm_results::FVMDiagnosticResults)::Vector{Tuple{String, String}}
    
    warnings = Tuple{String, String}[]
    
    # Mesh warnings
    if mesh_results.orthogonality_stats["min"] < 0.3
        push!(warnings, ("Mesh", "Low orthogonality detected - may affect accuracy"))
    end
    
    if mesh_results.skewness_stats["max"] > 0.7
        push!(warnings, ("Mesh", "High skewness detected - consider mesh improvement"))
    end
    
    if mesh_results.aspect_ratio_stats["max"] > 100
        push!(warnings, ("Mesh", "High aspect ratios - may cause convergence issues"))
    end
    
    # FVC warnings
    if fvc_results.gradient_convergence_order < 2.0 && fvc_results.gradient_convergence_order >= 1.5
        push!(warnings, ("FVC", "Gradient convergence order below optimal"))
    end
    
    if !fvc_results.interpolation_boundedness
        push!(warnings, ("FVC", "Interpolation violates boundedness principle"))
    end
    
    # FVM warnings
    if fvm_results.solve_performance_ms > 500
        push!(warnings, ("FVM", "Slow solver performance detected"))
    end
    
    if fvm_results.solution_smoothness > 5.0
        push!(warnings, ("FVM", "Solution lacks smoothness - check discretization"))
    end
    
    return warnings
end

"""
Generate prioritized recommendations
"""
function generate_prioritized_recommendations(mesh_results::MeshDiagnosticResults,
                                            fvc_results::FVCDiagnosticResults,
                                            fvm_results::FVMDiagnosticResults)::Vector{Tuple{String, String, String}}
    
    recommendations = Tuple{String, String, String}[]
    
    # High urgency recommendations
    if !isempty(mesh_results.critical_issues)
        push!(recommendations, ("Mesh", "Fix critical mesh issues before proceeding", "HIGH"))
    end
    
    if !isempty(fvm_results.conservation_issues)
        push!(recommendations, ("FVM", "Address conservation violations immediately", "HIGH"))
    end
    
    # Medium urgency recommendations  
    if mesh_results.overall_quality == "POOR"
        push!(recommendations, ("Mesh", "Improve mesh quality for better accuracy", "MEDIUM"))
    end
    
    if fvc_results.gradient_convergence_order < 1.5
        push!(recommendations, ("FVC", "Fix gradient operator implementation", "MEDIUM"))
    end
    
    if fvc_results.laplacian_convergence_order < 1.5
        push!(recommendations, ("FVC", "Fix Laplacian operator implementation", "MEDIUM"))
    end
    
    # Low urgency recommendations
    if mesh_results.orthogonality_stats["min"] < 0.5
        push!(recommendations, ("Mesh", "Consider improving mesh orthogonality", "LOW"))
    end
    
    if fvm_results.solve_performance_ms > 1000
        push!(recommendations, ("FVM", "Optimize solver performance", "LOW"))
    end
    
    return recommendations
end

"""
Assess system readiness for different use cases
"""
function assess_system_readiness(health_score::Float64, critical_issues::Vector{Tuple{String, String, Int}})::String
    
    # Critical issues block production use
    high_priority_issues = filter(issue -> issue[3] <= 2, critical_issues)
    
    if !isempty(high_priority_issues)
        return "CRITICAL_ISSUES"
    end
    
    if health_score >= 85
        return "PRODUCTION_READY"
    elseif health_score >= 70
        return "DEVELOPMENT_READY"
    else
        return "NEEDS_WORK"
    end
end

"""
Assess validation status
"""
function assess_validation_status(health_check::JuliaFOAMHealthCheck,
                                 mesh_results::MeshDiagnosticResults,
                                 fvc_results::FVCDiagnosticResults, 
                                 fvm_results::FVMDiagnosticResults)::Tuple{Dict{String, Bool}, Float64}
    
    status = Dict{String, Bool}()
    
    # Mesh validation
    status["mesh_quality"] = mesh_results.overall_quality in ["GOOD", "EXCELLENT"]
    status["mesh_connectivity"] = isempty(mesh_results.connectivity_issues)
    status["mesh_geometry"] = isempty(mesh_results.geometric_issues)
    
    # FVC validation
    status["gradient_accuracy"] = fvc_results.gradient_convergence_order >= 1.5
    status["divergence_conservation"] = fvc_results.divergence_conservation
    status["laplacian_accuracy"] = fvc_results.laplacian_convergence_order >= 1.5
    status["laplacian_symmetry"] = fvc_results.laplacian_symmetry
    
    # FVM validation
    status["matrix_properties"] = isempty(fvm_results.matrix_issues)
    status["solver_convergence"] = isempty(fvm_results.solver_issues)
    status["boundary_implementation"] = isempty(fvm_results.boundary_issues)
    status["conservation_laws"] = isempty(fvm_results.conservation_issues)
    status["solution_quality"] = fvm_results.solution_boundedness && isempty(fvm_results.unphysical_values)
    
    # Calculate coverage
    total_tests = length(status)
    passed_tests = count(values(status))
    coverage = (passed_tests / total_tests) * 100.0
    
    return status, coverage
end

"""
Estimate performance score
"""
function estimate_performance_score(mesh_results::MeshDiagnosticResults,
                                   fvc_results::FVCDiagnosticResults,
                                   fvm_results::FVMDiagnosticResults)::Float64
    
    base_score = 100.0
    
    # Mesh performance factors
    if mesh_results.cell_count > 1e6
        base_score -= 10  # Large meshes are slower
    end
    
    # FVC performance factors
    base_score -= (fvc_results.gradient_performance_ms + fvc_results.divergence_performance_ms + 
                   fvc_results.laplacian_performance_ms) / 100  # Penalty for slow operators
    
    # FVM performance factors
    base_score -= fvm_results.solve_performance_ms / 50  # Penalty for slow solving
    base_score -= fvm_results.assembly_performance_ms / 100  # Penalty for slow assembly
    
    # Memory efficiency factor
    if fvm_results.memory_usage_mb > 1000
        base_score -= 15
    end
    
    return max(base_score, 0.0)
end

"""
Generate comprehensive health report
"""
function generate_health_report(results::JuliaFOAMHealthResults; 
                               output_file::Union{String, Nothing}=nothing,
                               format::String="text")::String
    
    report_lines = String[]
    
    # Header
    push!(report_lines, "")
    push!(report_lines, "🩺 JuliaFOAM Comprehensive Health Report")
    push!(report_lines, "="^80)
    push!(report_lines, "Generated: $(now())")
    push!(report_lines, "Diagnostic Time: $(round(results.total_diagnostic_time, digits=2)) seconds")
    push!(report_lines, "")
    
    # Executive Summary
    push!(report_lines, "📋 Executive Summary")
    push!(report_lines, "-"^40)
    
    health_emoji = results.health_grade in ["A+", "A", "A-"] ? "🏆" :
                   results.health_grade in ["B+", "B", "B-"] ? "✅" :
                   results.health_grade in ["C+", "C", "C-"] ? "⚠️" :
                   results.health_grade in ["D+", "D", "D-"] ? "❌" : "🚨"
    
    push!(report_lines, "Overall Health Score: $(round(results.overall_health_score, digits=1))/100 $(health_emoji)")
    push!(report_lines, "Health Grade: $(results.health_grade)")
    push!(report_lines, "System Readiness: $(results.system_readiness)")
    push!(report_lines, "Validation Coverage: $(round(results.validation_coverage, digits=1))%")
    push!(report_lines, "")
    
    # System Specifications
    push!(report_lines, "🔧 System Specifications")
    push!(report_lines, "-"^40)
    push!(report_lines, "Mesh Size: $(results.total_cells) cells, $(results.total_faces) faces")
    push!(report_lines, "Estimated Memory: $(round(results.estimated_memory_mb, digits=1)) MB")
    push!(report_lines, "Performance Score: $(round(results.expected_performance_score, digits=1))/100")
    push!(report_lines, "")
    
    # Critical Issues
    if !isempty(results.critical_issues)
        push!(report_lines, "🚨 Critical Issues (Immediate Action Required)")
        push!(report_lines, "-"^50)
        for (category, issue, priority) in results.critical_issues
            priority_label = priority <= 2 ? "HIGH" : priority <= 4 ? "MEDIUM" : "LOW"
            push!(report_lines, "[$priority_label] $category: $issue")
        end
        push!(report_lines, "")
    end
    
    # Warnings
    if !isempty(results.warnings)
        push!(report_lines, "⚠️  Warnings")
        push!(report_lines, "-"^20)
        for (category, warning) in results.warnings
            push!(report_lines, "$category: $warning")
        end
        push!(report_lines, "")
    end
    
    # Recommendations
    if !isempty(results.recommendations)
        push!(report_lines, "💡 Recommendations")
        push!(report_lines, "-"^30)
        
        # Group by urgency
        high_recs = filter(r -> r[3] == "HIGH", results.recommendations)
        medium_recs = filter(r -> r[3] == "MEDIUM", results.recommendations)
        low_recs = filter(r -> r[3] == "LOW", results.recommendations)
        
        if !isempty(high_recs)
            push!(report_lines, "High Priority:")
            for (cat, rec, _) in high_recs
                push!(report_lines, "  • $cat: $rec")
            end
        end
        
        if !isempty(medium_recs)
            push!(report_lines, "Medium Priority:")
            for (cat, rec, _) in medium_recs
                push!(report_lines, "  • $cat: $rec")
            end
        end
        
        if !isempty(low_recs)
            push!(report_lines, "Low Priority:")
            for (cat, rec, _) in low_recs
                push!(report_lines, "  • $cat: $rec")
            end
        end
        push!(report_lines, "")
    end
    
    # Detailed Component Analysis
    push!(report_lines, "📊 Detailed Component Analysis")
    push!(report_lines, "-"^40)
    push!(report_lines, "Mesh Quality: $(results.mesh_results.overall_quality)")
    push!(report_lines, "FVC Operators: $(results.fvc_results.overall_fvc_quality)")
    push!(report_lines, "FVM System: $(results.fvm_results.overall_fvm_quality)")
    push!(report_lines, "")
    
    # Validation Status
    push!(report_lines, "✅ Validation Status")
    push!(report_lines, "-"^25)
    for (test_name, passed) in results.validation_status
        status_symbol = passed ? "✅" : "❌"
        push!(report_lines, "$status_symbol $(replace(test_name, "_" => " "))")
    end
    push!(report_lines, "")
    
    # Next Steps
    push!(report_lines, "🎯 Next Steps")
    push!(report_lines, "-"^20)
    
    if results.system_readiness == "PRODUCTION_READY"
        push!(report_lines, "✅ System is ready for production CFD simulations")
        push!(report_lines, "• Run validation tests to confirm specific use cases")
        push!(report_lines, "• Monitor performance during production runs")
    elseif results.system_readiness == "DEVELOPMENT_READY"
        push!(report_lines, "⚠️  System is suitable for development and testing")
        push!(report_lines, "• Address remaining issues before production use")
        push!(report_lines, "• Run comprehensive validation tests")
    elseif results.system_readiness == "NEEDS_WORK"
        push!(report_lines, "❌ System requires significant improvements")
        push!(report_lines, "• Address critical and high-priority issues")
        push!(report_lines, "• Re-run diagnostics after improvements")
    else  # CRITICAL_ISSUES
        push!(report_lines, "🚨 System has critical issues - DO NOT USE")
        push!(report_lines, "• Fix all critical issues immediately")
        push!(report_lines, "• Re-run full diagnostics after fixes")
    end
    push!(report_lines, "")
    
    # Footer
    push!(report_lines, "="^80)
    push!(report_lines, "Report generated by JuliaFOAM Doctor")
    push!(report_lines, "For support, see: https://github.com/JuliaFOAM/JuliaFOAM.jl")
    push!(report_lines, "")
    
    report = join(report_lines, "\n")
    
    if output_file !== nothing
        open(output_file, "w") do f
            write(f, report)
        end
        println("Health report saved to: $output_file")
    else
        println(report)
    end
    
    return report
end

"""
Get simplified health score for automated systems
"""
function get_health_score(mesh::Mesh)::Float64
    health_check = JuliaFOAMHealthCheck(mesh, verbose=false)
    results = run_full_diagnostics(health_check)
    return results.overall_health_score
end

"""
Get top recommendations for a given mesh
"""
function doctor_recommendations(mesh::Mesh; max_recommendations::Int=5)::Vector{String}
    health_check = JuliaFOAMHealthCheck(mesh, verbose=false)
    results = run_full_diagnostics(health_check)
    
    recs = String[]
    
    # Add critical issues as recommendations
    for (category, issue, priority) in results.critical_issues[1:min(end, max_recommendations)]
        push!(recs, "$category: $issue")
    end
    
    # Add prioritized recommendations
    remaining_slots = max_recommendations - length(recs)
    if remaining_slots > 0
        for (category, rec, urgency) in results.recommendations[1:min(end, remaining_slots)]
            push!(recs, "[$urgency] $category: $rec")
        end
    end
    
    return recs
end

end  # module JuliaFOAMDoctor