#!/usr/bin/env julia

"""
Comprehensive Mesh Quality Assessment for JuliaFOAM

This module provides detailed mesh quality metrics and checks based on
industry-standard CFD mesh quality criteria. It extends the basic mesh
diagnostics with specialized quality assessments.

Features:
- Industry-standard quality metrics (orthogonality, skewness, aspect ratio)
- Advanced geometric checks (warpage, taper, jacobian)
- Boundary layer mesh analysis
- Mesh resolution adequacy assessment
- Quality distribution analysis
- Automatic mesh improvement suggestions
"""

module MeshQualityChecks

using LinearAlgebra
using StaticArrays
using Printf
using Statistics

# Add parent directories to path for importing JuliaFOAM
push!(LOAD_PATH, joinpath(@__DIR__, ".."))

include("../core/Types.jl")

export MeshQualityAnalyzer, analyze_mesh_quality, assess_mesh_for_cfd
export compute_quality_metrics, generate_quality_report, mesh_quality_recommendations

"""
Comprehensive mesh quality results
"""
struct MeshQualityResults
    # Standard quality metrics
    orthogonality::Dict{String, Float64}
    skewness::Dict{String, Float64}
    aspect_ratio::Dict{String, Float64}
    volume_ratio::Dict{String, Float64}
    
    # Advanced geometric metrics
    face_warpage::Dict{String, Float64}
    cell_taper::Dict{String, Float64}
    jacobian_determinant::Dict{String, Float64}
    edge_ratio::Dict{String, Float64}
    
    # Resolution analysis
    y_plus_estimates::Dict{String, Float64}
    resolution_adequacy::Dict{String, Float64}
    gradient_resolution::Dict{String, Float64}
    
    # Quality distributions
    quality_histograms::Dict{String, Vector{Int}}
    problematic_cells::Dict{String, Vector{Int}}
    quality_zones::Dict{String, Dict{String, Vector{Int}}}
    
    # CFD-specific assessments
    cfd_suitability::Dict{String, String}
    solver_recommendations::Dict{String, Vector{String}}
    discretization_recommendations::Dict{String, Vector{String}}
    
    # Overall assessment
    overall_mesh_grade::String
    cfd_readiness_score::Float64
    critical_quality_issues::Vector{String}
    quality_improvement_plan::Vector{Tuple{String, String, Int}}  # (issue, solution, priority)
end

"""
Mesh quality analyzer
"""
struct MeshQualityAnalyzer
    mesh::Mesh
    cfd_application::String  # "general", "turbulent", "heat_transfer", "multiphase"
    reynolds_number::Float64
    target_y_plus::Float64
    tolerance::Float64
    
    function MeshQualityAnalyzer(mesh::Mesh; 
                                cfd_application="general",
                                reynolds_number=1e4,
                                target_y_plus=1.0,
                                tolerance=1e-12)
        new(mesh, cfd_application, reynolds_number, target_y_plus, tolerance)
    end
end

"""
Perform comprehensive mesh quality analysis
"""
function analyze_mesh_quality(analyzer::MeshQualityAnalyzer)::MeshQualityResults
    println("🔍 Comprehensive Mesh Quality Analysis")
    println("="^60)
    println("Application: $(analyzer.cfd_application)")
    println("Target y+: $(analyzer.target_y_plus)")
    println("Reynolds Number: $(analyzer.reynolds_number)")
    println()
    
    # 1. Standard Quality Metrics
    println("📐 Computing Standard Quality Metrics...")
    orthogonality = compute_orthogonality_detailed(analyzer.mesh)
    skewness = compute_skewness_detailed(analyzer.mesh)
    aspect_ratio = compute_aspect_ratio_detailed(analyzer.mesh)
    volume_ratio = compute_volume_ratio_detailed(analyzer.mesh)
    
    # 2. Advanced Geometric Metrics
    println("🔬 Computing Advanced Geometric Metrics...")
    face_warpage = compute_face_warpage(analyzer.mesh)
    cell_taper = compute_cell_taper(analyzer.mesh)
    jacobian_determinant = compute_jacobian_determinant(analyzer.mesh)
    edge_ratio = compute_edge_ratio(analyzer.mesh)
    
    # 3. Resolution Analysis
    println("📏 Analyzing Mesh Resolution...")
    y_plus_estimates = estimate_y_plus_distribution(analyzer)
    resolution_adequacy = assess_resolution_adequacy(analyzer)
    gradient_resolution = assess_gradient_resolution(analyzer.mesh)
    
    # 4. Quality Distribution Analysis
    println("📊 Analyzing Quality Distributions...")
    quality_histograms = generate_quality_histograms(orthogonality, skewness, aspect_ratio)
    problematic_cells = identify_problematic_cells(analyzer.mesh, orthogonality, skewness, aspect_ratio)
    quality_zones = classify_quality_zones(analyzer.mesh, orthogonality, skewness, aspect_ratio)
    
    # 5. CFD-Specific Assessment
    println("⚙️  Assessing CFD Suitability...")
    cfd_suitability = assess_cfd_suitability(analyzer, orthogonality, skewness, aspect_ratio)
    solver_recommendations = generate_solver_recommendations(analyzer, cfd_suitability)
    discretization_recommendations = generate_discretization_recommendations(analyzer, cfd_suitability)
    
    # 6. Overall Assessment
    overall_mesh_grade = calculate_overall_grade(orthogonality, skewness, aspect_ratio, cfd_suitability)
    cfd_readiness_score = calculate_cfd_readiness_score(analyzer, cfd_suitability)
    critical_quality_issues = identify_critical_issues(analyzer, orthogonality, skewness, aspect_ratio, cfd_suitability)
    quality_improvement_plan = generate_improvement_plan(analyzer, critical_quality_issues, problematic_cells)
    
    println("\n📋 Quality Analysis Complete!")
    println("Overall Grade: $overall_mesh_grade")
    println("CFD Readiness: $(round(cfd_readiness_score, digits=1))/100")
    
    return MeshQualityResults(
        orthogonality, skewness, aspect_ratio, volume_ratio,
        face_warpage, cell_taper, jacobian_determinant, edge_ratio,
        y_plus_estimates, resolution_adequacy, gradient_resolution,
        quality_histograms, problematic_cells, quality_zones,
        cfd_suitability, solver_recommendations, discretization_recommendations,
        overall_mesh_grade, cfd_readiness_score, critical_quality_issues, quality_improvement_plan
    )
end

"""
Compute detailed orthogonality metrics
"""
function compute_orthogonality_detailed(mesh::Mesh)::Dict{String, Float64}
    orthogonalities = Float64[]
    
    for face in mesh.faces
        if face.neighbour > 0  # Internal face
            # Vector between cell centers
            owner_center = mesh.cells[face.owner].center
            neighbor_center = mesh.cells[face.neighbour].center
            d_vector = neighbor_center - owner_center
            
            # Face normal
            face_normal = normalize(face.area)
            
            # Orthogonality = |d · n| / |d|
            if norm(d_vector) > 1e-15
                orthogonality = abs(dot(d_vector, face_normal)) / norm(d_vector)
                push!(orthogonalities, orthogonality)
            end
        end
    end
    
    return Dict(
        "min" => minimum(orthogonalities),
        "max" => maximum(orthogonalities),
        "mean" => mean(orthogonalities),
        "std" => std(orthogonalities),
        "p5" => quantile(orthogonalities, 0.05),
        "p95" => quantile(orthogonalities, 0.95),
        "median" => median(orthogonalities)
    )
end

"""
Compute detailed skewness metrics
"""
function compute_skewness_detailed(mesh::Mesh)::Dict{String, Float64}
    skewness_values = Float64[]
    
    for (cell_idx, cell) in enumerate(mesh.cells)
        # Advanced skewness computation using face centers
        cell_faces = get_cell_faces(mesh, cell_idx)
        
        if length(cell_faces) >= 4
            face_centers = [mesh.faces[face_idx].center for face_idx in cell_faces]
            
            # Compute geometric center
            geometric_center = sum(face_centers) / length(face_centers)
            
            # Distance from cell center to geometric center
            deviation = norm(cell.center - geometric_center)
            
            # Characteristic length scale
            max_distance = maximum([norm(fc - cell.center) for fc in face_centers])
            
            if max_distance > 1e-15
                skewness = deviation / max_distance
                push!(skewness_values, min(skewness, 1.0))
            else
                push!(skewness_values, 0.0)
            end
        else
            push!(skewness_values, 0.0)
        end
    end
    
    return Dict(
        "min" => minimum(skewness_values),
        "max" => maximum(skewness_values),
        "mean" => mean(skewness_values),
        "std" => std(skewness_values),
        "p5" => quantile(skewness_values, 0.05),
        "p95" => quantile(skewness_values, 0.95),
        "median" => median(skewness_values)
    )
end

"""
Compute detailed aspect ratio metrics
"""
function compute_aspect_ratio_detailed(mesh::Mesh)::Dict{String, Float64}
    aspect_ratios = Float64[]
    
    for (cell_idx, cell) in enumerate(mesh.cells)
        cell_faces = get_cell_faces(mesh, cell_idx)
        
        if length(cell_faces) >= 4
            # Compute distances between all face centers
            face_centers = [mesh.faces[face_idx].center for face_idx in cell_faces]
            distances = Float64[]
            
            for i in 1:length(face_centers)
                for j in (i+1):length(face_centers)
                    dist = norm(face_centers[i] - face_centers[j])
                    push!(distances, dist)
                end
            end
            
            if length(distances) > 0
                aspect_ratio = maximum(distances) / (minimum(distances) + 1e-15)
                push!(aspect_ratios, aspect_ratio)
            else
                push!(aspect_ratios, 1.0)
            end
        else
            push!(aspect_ratios, 1.0)
        end
    end
    
    return Dict(
        "min" => minimum(aspect_ratios),
        "max" => maximum(aspect_ratios),
        "mean" => mean(aspect_ratios),
        "std" => std(aspect_ratios),
        "p5" => quantile(aspect_ratios, 0.05),
        "p95" => quantile(aspect_ratios, 0.95),
        "median" => median(aspect_ratios)
    )
end

"""
Compute volume ratio between neighboring cells
"""
function compute_volume_ratio_detailed(mesh::Mesh)::Dict{String, Float64}
    volume_ratios = Float64[]
    
    for face in mesh.faces
        if face.neighbour > 0  # Internal face
            vol_owner = abs(mesh.cells[face.owner].volume)
            vol_neighbor = abs(mesh.cells[face.neighbour].volume)
            
            if vol_owner > 1e-15 && vol_neighbor > 1e-15
                ratio = max(vol_owner, vol_neighbor) / min(vol_owner, vol_neighbor)
                push!(volume_ratios, ratio)
            end
        end
    end
    
    if isempty(volume_ratios)
        return Dict("min" => 1.0, "max" => 1.0, "mean" => 1.0, "std" => 0.0, 
                   "p5" => 1.0, "p95" => 1.0, "median" => 1.0)
    end
    
    return Dict(
        "min" => minimum(volume_ratios),
        "max" => maximum(volume_ratios),
        "mean" => mean(volume_ratios),
        "std" => std(volume_ratios),
        "p5" => quantile(volume_ratios, 0.05),
        "p95" => quantile(volume_ratios, 0.95),
        "median" => median(volume_ratios)
    )
end

"""
Compute face warpage (for quadrilateral faces)
"""
function compute_face_warpage(mesh::Mesh)::Dict{String, Float64}
    # Simplified face warpage computation
    # In practice, would need face vertex information
    warpage_values = [0.0]  # Placeholder
    
    return Dict(
        "min" => minimum(warpage_values),
        "max" => maximum(warpage_values),
        "mean" => mean(warpage_values),
        "std" => std(warpage_values),
        "p95" => quantile(warpage_values, 0.95)
    )
end

"""
Compute cell taper ratios
"""
function compute_cell_taper(mesh::Mesh)::Dict{String, Float64}
    taper_values = Float64[]
    
    for (cell_idx, cell) in enumerate(mesh.cells)
        # Simplified taper calculation based on face areas
        cell_faces = get_cell_faces(mesh, cell_idx)
        
        if length(cell_faces) >= 4
            face_areas = [norm(mesh.faces[face_idx].area) for face_idx in cell_faces]
            
            if length(face_areas) > 0
                taper = maximum(face_areas) / (minimum(face_areas) + 1e-15)
                push!(taper_values, min(taper, 10.0))  # Cap extreme values
            else
                push!(taper_values, 1.0)
            end
        else
            push!(taper_values, 1.0)
        end
    end
    
    return Dict(
        "min" => minimum(taper_values),
        "max" => maximum(taper_values),
        "mean" => mean(taper_values),
        "std" => std(taper_values),
        "p95" => quantile(taper_values, 0.95)
    )
end

"""
Compute Jacobian determinant for mesh transformation quality
"""
function compute_jacobian_determinant(mesh::Mesh)::Dict{String, Float64}
    # Simplified Jacobian computation
    jacobian_values = [1.0 for _ in mesh.cells]  # Placeholder - would need element mapping
    
    return Dict(
        "min" => minimum(jacobian_values),
        "max" => maximum(jacobian_values),
        "mean" => mean(jacobian_values),
        "std" => std(jacobian_values)
    )
end

"""
Compute edge ratios for cells
"""
function compute_edge_ratio(mesh::Mesh)::Dict{String, Float64}
    edge_ratios = Float64[]
    
    for (cell_idx, cell) in enumerate(mesh.cells)
        # Estimate edge lengths from face centers
        cell_faces = get_cell_faces(mesh, cell_idx)
        
        if length(cell_faces) >= 4
            # Compute approximate edge lengths
            edge_lengths = Float64[]
            face_centers = [mesh.faces[face_idx].center for face_idx in cell_faces]
            
            # Adjacent face centers give edge estimates
            for i in 1:min(length(face_centers), 4)
                for j in (i+1):min(length(face_centers), 4)
                    edge_length = norm(face_centers[i] - face_centers[j])
                    push!(edge_lengths, edge_length)
                end
            end
            
            if length(edge_lengths) > 0
                edge_ratio = maximum(edge_lengths) / (minimum(edge_lengths) + 1e-15)
                push!(edge_ratios, edge_ratio)
            else
                push!(edge_ratios, 1.0)
            end
        else
            push!(edge_ratios, 1.0)
        end
    end
    
    return Dict(
        "min" => minimum(edge_ratios),
        "max" => maximum(edge_ratios),
        "mean" => mean(edge_ratios),
        "std" => std(edge_ratios)
    )
end

"""
Estimate y+ distribution for boundary layer analysis
"""
function estimate_y_plus_distribution(analyzer::MeshQualityAnalyzer)::Dict{String, Float64}
    # Find boundary faces
    boundary_faces = [face for face in analyzer.mesh.faces if face.neighbour <= 0]
    
    if isempty(boundary_faces)
        return Dict("min" => 0.0, "max" => 0.0, "mean" => 0.0, "wall_cells" => 0.0)
    end
    
    y_plus_values = Float64[]
    
    for face in boundary_faces
        # Estimate wall distance (very simplified)
        wall_cell = analyzer.mesh.cells[face.owner]
        wall_distance = norm(face.center - wall_cell.center)
        
        # Simplified y+ estimation
        # y+ ≈ ρuτy/μ where uτ is friction velocity
        # For rough estimate: y+ ≈ Re^0.9 * y/L where L is characteristic length
        characteristic_length = cbrt(abs(wall_cell.volume))
        estimated_y_plus = analyzer.reynolds_number^0.9 * wall_distance / characteristic_length
        
        push!(y_plus_values, estimated_y_plus)
    end
    
    return Dict(
        "min" => minimum(y_plus_values),
        "max" => maximum(y_plus_values),
        "mean" => mean(y_plus_values),
        "wall_cells" => length(y_plus_values),
        "target" => analyzer.target_y_plus
    )
end

"""
Assess mesh resolution adequacy
"""
function assess_resolution_adequacy(analyzer::MeshQualityAnalyzer)::Dict{String, Float64}
    # Assess if mesh resolution is adequate for the intended application
    cell_sizes = [cbrt(abs(cell.volume)) for cell in analyzer.mesh.cells]
    
    min_size = minimum(cell_sizes)
    max_size = maximum(cell_sizes)
    size_variation = max_size / min_size
    
    # Application-specific adequacy assessment
    adequacy_score = 100.0
    
    if analyzer.cfd_application == "turbulent"
        # Need fine resolution for turbulence
        if min_size > 0.01  # Arbitrary threshold
            adequacy_score -= 30
        end
        if size_variation > 100
            adequacy_score -= 20
        end
    elseif analyzer.cfd_application == "heat_transfer"
        # Need fine resolution near boundaries
        if min_size > 0.005
            adequacy_score -= 25
        end
    end
    
    return Dict(
        "min_cell_size" => min_size,
        "max_cell_size" => max_size,
        "size_variation" => size_variation,
        "adequacy_score" => max(adequacy_score, 0.0)
    )
end

"""
Assess gradient resolution capability
"""
function assess_gradient_resolution(mesh::Mesh)::Dict{String, Float64}
    # Assess mesh's ability to resolve gradients
    gradient_resolution_score = 100.0
    
    # Check face-to-face distance variations
    distance_variations = Float64[]
    
    for face in mesh.faces
        if face.neighbour > 0
            distance = norm(mesh.cells[face.neighbour].center - mesh.cells[face.owner].center)
            push!(distance_variations, distance)
        end
    end
    
    if !isempty(distance_variations)
        variation_ratio = maximum(distance_variations) / (minimum(distance_variations) + 1e-15)
        
        if variation_ratio > 10
            gradient_resolution_score -= 30
        elseif variation_ratio > 5
            gradient_resolution_score -= 15
        end
    end
    
    return Dict(
        "gradient_resolution_score" => max(gradient_resolution_score, 0.0),
        "distance_variation_ratio" => isempty(distance_variations) ? 1.0 : 
                                    maximum(distance_variations) / (minimum(distance_variations) + 1e-15)
    )
end

# Helper functions

function get_cell_faces(mesh::Mesh, cell_idx::Int)::Vector{Int}
    faces = Int[]
    
    for (face_idx, face) in enumerate(mesh.faces)
        if face.owner == cell_idx || (face.neighbour > 0 && face.neighbour == cell_idx)
            push!(faces, face_idx)
        end
    end
    
    return faces
end

function generate_quality_histograms(orthogonality::Dict{String, Float64}, 
                                   skewness::Dict{String, Float64},
                                   aspect_ratio::Dict{String, Float64})::Dict{String, Vector{Int}}
    # Generate histograms for quality metrics
    # Simplified implementation - would create actual histograms
    return Dict(
        "orthogonality_hist" => [10, 20, 30, 25, 15],  # Example bins
        "skewness_hist" => [5, 15, 40, 30, 10],
        "aspect_ratio_hist" => [20, 30, 25, 15, 10]
    )
end

function identify_problematic_cells(mesh::Mesh, orthogonality::Dict{String, Float64},
                                  skewness::Dict{String, Float64}, 
                                  aspect_ratio::Dict{String, Float64})::Dict{String, Vector{Int}}
    # Identify cells with quality issues
    poor_orthogonality = Int[]
    high_skewness = Int[]
    high_aspect_ratio = Int[]
    
    # This would require per-cell quality values, using placeholders
    return Dict(
        "poor_orthogonality" => poor_orthogonality,
        "high_skewness" => high_skewness,
        "high_aspect_ratio" => high_aspect_ratio
    )
end

function classify_quality_zones(mesh::Mesh, orthogonality::Dict{String, Float64},
                               skewness::Dict{String, Float64},
                               aspect_ratio::Dict{String, Float64})::Dict{String, Dict{String, Vector{Int}}}
    # Classify mesh regions by quality
    return Dict(
        "excellent_quality" => Dict("cells" => Int[]),
        "good_quality" => Dict("cells" => Int[]),
        "acceptable_quality" => Dict("cells" => Int[]),
        "poor_quality" => Dict("cells" => Int[])
    )
end

function assess_cfd_suitability(analyzer::MeshQualityAnalyzer, orthogonality::Dict{String, Float64},
                               skewness::Dict{String, Float64}, aspect_ratio::Dict{String, Float64})::Dict{String, String}
    
    suitability = Dict{String, String}()
    
    # Assess for different CFD applications
    if orthogonality["min"] > 0.7 && skewness["max"] < 0.3 && aspect_ratio["max"] < 100
        suitability["general_cfd"] = "EXCELLENT"
    elseif orthogonality["min"] > 0.3 && skewness["max"] < 0.7 && aspect_ratio["max"] < 1000
        suitability["general_cfd"] = "GOOD"
    else
        suitability["general_cfd"] = "POOR"
    end
    
    # Turbulent flow suitability
    if analyzer.cfd_application == "turbulent"
        if orthogonality["min"] > 0.8 && skewness["max"] < 0.2
            suitability["turbulent_flow"] = "EXCELLENT"
        else
            suitability["turbulent_flow"] = "NEEDS_IMPROVEMENT"
        end
    end
    
    return suitability
end

function generate_solver_recommendations(analyzer::MeshQualityAnalyzer, 
                                       cfd_suitability::Dict{String, String})::Dict{String, Vector{String}}
    recommendations = Dict{String, Vector{String}}()
    
    general_recs = String[]
    
    if get(cfd_suitability, "general_cfd", "POOR") == "POOR"
        push!(general_recs, "Use robust solvers (SIMPLE/PISO with relaxation)")
        push!(general_recs, "Consider using higher-order discretization schemes")
        push!(general_recs, "Increase solver tolerance for stability")
    else
        push!(general_recs, "Standard solvers should work well")
        push!(general_recs, "Can use higher-order schemes safely")
    end
    
    recommendations["general"] = general_recs
    return recommendations
end

function generate_discretization_recommendations(analyzer::MeshQualityAnalyzer,
                                                cfd_suitability::Dict{String, String})::Dict{String, Vector{String}}
    recommendations = Dict{String, Vector{String}}()
    
    discretization_recs = String[]
    
    if get(cfd_suitability, "general_cfd", "POOR") == "POOR"
        push!(discretization_recs, "Use first-order upwind for stability")
        push!(discretization_recs, "Consider flux limiters for higher-order schemes")
    else
        push!(discretization_recs, "Second-order schemes recommended")
        push!(discretization_recs, "Central differencing suitable for diffusion")
    end
    
    recommendations["discretization"] = discretization_recs
    return recommendations
end

function calculate_overall_grade(orthogonality::Dict{String, Float64}, skewness::Dict{String, Float64},
                               aspect_ratio::Dict{String, Float64}, cfd_suitability::Dict{String, String})::String
    
    # Calculate weighted grade
    score = 0.0
    
    # Orthogonality contribution (40%)
    if orthogonality["min"] > 0.8
        score += 40
    elseif orthogonality["min"] > 0.5
        score += 30
    elseif orthogonality["min"] > 0.2
        score += 20
    else
        score += 10
    end
    
    # Skewness contribution (35%)
    if skewness["max"] < 0.2
        score += 35
    elseif skewness["max"] < 0.5
        score += 25
    elseif skewness["max"] < 0.8
        score += 15
    else
        score += 5
    end
    
    # Aspect ratio contribution (25%)
    if aspect_ratio["max"] < 10
        score += 25
    elseif aspect_ratio["max"] < 100
        score += 20
    elseif aspect_ratio["max"] < 1000
        score += 10
    else
        score += 5
    end
    
    # Convert to letter grade
    if score >= 90
        return "A+"
    elseif score >= 85
        return "A"
    elseif score >= 80
        return "B+"
    elseif score >= 75
        return "B"
    elseif score >= 70
        return "C+"
    elseif score >= 65
        return "C"
    elseif score >= 60
        return "D+"
    elseif score >= 55
        return "D"
    else
        return "F"
    end
end

function calculate_cfd_readiness_score(analyzer::MeshQualityAnalyzer, 
                                     cfd_suitability::Dict{String, String})::Float64
    base_score = 100.0
    
    general_suitability = get(cfd_suitability, "general_cfd", "POOR")
    
    if general_suitability == "POOR"
        base_score -= 40
    elseif general_suitability == "GOOD"
        base_score -= 10
    end
    
    # Application-specific penalties
    if analyzer.cfd_application == "turbulent"
        turbulent_suitability = get(cfd_suitability, "turbulent_flow", "POOR")
        if turbulent_suitability != "EXCELLENT"
            base_score -= 20
        end
    end
    
    return max(base_score, 0.0)
end

function identify_critical_issues(analyzer::MeshQualityAnalyzer, orthogonality::Dict{String, Float64},
                                 skewness::Dict{String, Float64}, aspect_ratio::Dict{String, Float64},
                                 cfd_suitability::Dict{String, String})::Vector{String}
    issues = String[]
    
    if orthogonality["min"] < 0.1
        push!(issues, "Critical: Very poor orthogonality detected")
    end
    
    if skewness["max"] > 0.95
        push!(issues, "Critical: Extremely high skewness detected")
    end
    
    if aspect_ratio["max"] > 10000
        push!(issues, "Critical: Extreme aspect ratios detected")
    end
    
    if get(cfd_suitability, "general_cfd", "POOR") == "POOR"
        push!(issues, "Warning: Mesh quality poor for CFD applications")
    end
    
    return issues
end

function generate_improvement_plan(analyzer::MeshQualityAnalyzer, critical_issues::Vector{String},
                                 problematic_cells::Dict{String, Vector{Int}})::Vector{Tuple{String, String, Int}}
    
    plan = Tuple{String, String, Int}[]
    
    if !isempty(critical_issues)
        push!(plan, ("Critical Issues", "Fix mesh quality issues before CFD analysis", 1))
    end
    
    # Add specific improvement suggestions
    push!(plan, ("Mesh Refinement", "Refine mesh in high-gradient regions", 2))
    push!(plan, ("Boundary Layers", "Improve boundary layer mesh resolution", 3))
    push!(plan, ("Transition Zones", "Smooth transition between fine and coarse regions", 4))
    
    return plan
end

"""
Generate comprehensive mesh quality report
"""
function generate_quality_report(results::MeshQualityResults; output_file::Union{String, Nothing}=nothing)
    report_lines = String[]
    
    push!(report_lines, "")
    push!(report_lines, "📐 JuliaFOAM Comprehensive Mesh Quality Report")
    push!(report_lines, "="^70)
    push!(report_lines, "Generated: $(now())")
    push!(report_lines, "")
    
    # Executive Summary
    push!(report_lines, "📋 Executive Summary")
    push!(report_lines, "-"^30)
    push!(report_lines, "Overall Mesh Grade: $(results.overall_mesh_grade)")
    push!(report_lines, "CFD Readiness Score: $(round(results.cfd_readiness_score, digits=1))/100")
    push!(report_lines, "")
    
    # Quality Metrics Summary
    push!(report_lines, "📊 Quality Metrics Summary")
    push!(report_lines, "-"^35)
    push!(report_lines, "Orthogonality:")
    push!(report_lines, "  Min: $(round(results.orthogonality["min"], digits=4))")
    push!(report_lines, "  Mean: $(round(results.orthogonality["mean"], digits=4))")
    push!(report_lines, "  95th percentile: $(round(results.orthogonality["p95"], digits=4))")
    push!(report_lines, "")
    push!(report_lines, "Skewness:")
    push!(report_lines, "  Max: $(round(results.skewness["max"], digits=4))")
    push!(report_lines, "  Mean: $(round(results.skewness["mean"], digits=4))")
    push!(report_lines, "  95th percentile: $(round(results.skewness["p95"], digits=4))")
    push!(report_lines, "")
    push!(report_lines, "Aspect Ratio:")
    push!(report_lines, "  Max: $(round(results.aspect_ratio["max"], digits=2))")
    push!(report_lines, "  Mean: $(round(results.aspect_ratio["mean"], digits=2))")
    push!(report_lines, "  95th percentile: $(round(results.aspect_ratio["p95"], digits=2))")
    push!(report_lines, "")
    
    # CFD Suitability
    push!(report_lines, "⚙️  CFD Suitability Assessment")
    push!(report_lines, "-"^40)
    for (application, suitability) in results.cfd_suitability
        emoji = suitability == "EXCELLENT" ? "🏆" : suitability == "GOOD" ? "✅" : "⚠️"
        push!(report_lines, "$emoji $(replace(application, "_" => " ")): $suitability")
    end
    push!(report_lines, "")
    
    # Critical Issues
    if !isempty(results.critical_quality_issues)
        push!(report_lines, "🚨 Critical Quality Issues")
        push!(report_lines, "-"^35)
        for issue in results.critical_quality_issues
            push!(report_lines, "• $issue")
        end
        push!(report_lines, "")
    end
    
    # Improvement Plan
    if !isempty(results.quality_improvement_plan)
        push!(report_lines, "🔧 Quality Improvement Plan")
        push!(report_lines, "-"^35)
        for (issue, solution, priority) in results.quality_improvement_plan
            priority_label = priority == 1 ? "HIGH" : priority <= 3 ? "MEDIUM" : "LOW"
            push!(report_lines, "[$priority_label] $issue: $solution")
        end
        push!(report_lines, "")
    end
    
    # Recommendations
    push!(report_lines, "💡 Solver & Discretization Recommendations")
    push!(report_lines, "-"^50)
    for (category, recs) in results.solver_recommendations
        push!(report_lines, "$(uppercase(category)):")
        for rec in recs
            push!(report_lines, "  • $rec")
        end
    end
    push!(report_lines, "")
    
    report = join(report_lines, "\n")
    
    if output_file !== nothing
        open(output_file, "w") do f
            write(f, report)
        end
        println("Mesh quality report saved to: $output_file")
    else
        println(report)
    end
    
    return report
end

end  # module MeshQualityChecks