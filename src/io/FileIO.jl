using StaticArrays
const HAVE_WRITEVTK = try
    using WriteVTK
    true
catch
    @warn "WriteVTK package not available. Output to VTK files will be disabled."
    false
end

"""
    read_case_configuration(case_dir::String)

Read case configuration from OpenFOAM controlDict file.

# Arguments
- `case_dir`: Path to the case directory

# Returns
- `CaseConfiguration`: The case configuration
"""
function read_case_configuration(case_dir::String)
    control_dict_path = joinpath(case_dir, "system", "controlDict")
    
    # Default values
    transient = true
    start_time = 0.0
    end_time = 1.0
    delta_t = 0.001
    write_interval = 0.1
    
    # Read controlDict if it exists
    if isfile(control_dict_path)
        open(control_dict_path, "r") do file
            for line in eachline(file)
                if occursin("application", line) && occursin("steady", lowercase(line))
                    transient = false
                elseif occursin("startTime", line)
                    start_time = parse(Float64, split(line, ' ')[end-1])
                elseif occursin("endTime", line)
                    end_time = parse(Float64, split(line, ' ')[end-1])
                elseif occursin("deltaT", line)
                    delta_t = parse(Float64, split(line, ' ')[end-1])
                elseif occursin("writeInterval", line)
                    write_interval = parse(Float64, split(line, ' ')[end-1])
                end
            end
        end
    end
    
    return CaseConfiguration(
        transient = transient,
        start_time = start_time,
        end_time = end_time,
        delta_t = delta_t,
        write_interval = write_interval
    )
end

"""
    read_transport_properties(filename::String)

Read fluid properties from OpenFOAM transportProperties file.

# Arguments
- `filename`: Path to the transportProperties file

# Returns
- `FluidProperties`: The fluid properties
"""
function read_transport_properties(filename::String)
    # Default values
    density = 1.0
    kinematic_viscosity = 1e-5
    specific_heat = 1000.0
    thermal_conductivity = 0.6
    
    # Read transportProperties if it exists
    if isfile(filename)
        open(filename, "r") do file
            for line in eachline(file)
                if occursin("rho", line)
                    density = parse(Float64, split(line, ' ')[end-1])
                elseif occursin("nu", line)
                    kinematic_viscosity = parse(Float64, split(line, ' ')[end-1])
                elseif occursin("Cp", line)
                    specific_heat = parse(Float64, split(line, ' ')[end-1])
                elseif occursin("k", line) && !occursin("turbulent", line)
                    thermal_conductivity = parse(Float64, split(line, ' ')[end-1])
                end
            end
        end
    end
    
    return FluidProperties(
        density = density,
        kinematic_viscosity = kinematic_viscosity,
        specific_heat = specific_heat,
        thermal_conductivity = thermal_conductivity
    )
end

"""
    read_numerical_schemes(filename::String)

Read numerical schemes from OpenFOAM fvSchemes file.

# Arguments
- `filename`: Path to the fvSchemes file

# Returns
- `FvScheme`: The numerical schemes
"""
function read_numerical_schemes(filename::String)
    # Default values
    grad_scheme = :gauss
    div_scheme = :upwind
    laplacian_scheme = :gauss
    interpolation_scheme = :linear
    
    # Read fvSchemes if it exists
    if isfile(filename)
        open(filename, "r") do file
            section = ""
            for line in eachline(file)
                if occursin("gradSchemes", line)
                    section = "grad"
                elseif occursin("divSchemes", line)
                    section = "div"
                elseif occursin("laplacianSchemes", line)
                    section = "laplacian"
                elseif occursin("interpolationSchemes", line)
                    section = "interpolation"
                elseif occursin("}", line)
                    section = ""
                elseif section == "grad" && occursin("default", line)
                    if occursin("leastSquares", line)
                        grad_scheme = :leastSquares
                    else
                        grad_scheme = :gauss
                    end
                elseif section == "div" && occursin("default", line)
                    if occursin("upwind", line)
                        div_scheme = :upwind
                    elseif occursin("linear", line)
                        div_scheme = :linear
                    elseif occursin("TVD", line) || occursin("limitedLinear", line)
                        div_scheme = :tvd
                    end
                elseif section == "laplacian" && occursin("default", line)
                    laplacian_scheme = :gauss
                elseif section == "interpolation" && occursin("default", line)
                    if occursin("upwind", line)
                        interpolation_scheme = :upwind
                    else
                        interpolation_scheme = :linear
                    end
                end
            end
        end
    end
    
    return FvScheme(
        grad_scheme,
        div_scheme,
        laplacian_scheme,
        interpolation_scheme
    )
end

"""
    read_solver_settings(filename::String)

Read solver settings from OpenFOAM fvSolution file.

# Arguments
- `filename`: Path to the fvSolution file

# Returns
- `Dict`: The solver settings
"""
function read_solver_settings(filename::String)
    # Default values
    settings = Dict{String,Any}(
        "under_relaxation_U" => 0.7,
        "under_relaxation_p" => 0.3,
        "n_correctors" => 3,
        "n_non_orthogonal_correctors" => 1,
        "max_iterations" => 1000,
        "tolerance" => 1e-6
    )
    
    # Read fvSolution if it exists
    if isfile(filename)
        open(filename, "r") do file
            section = ""
            for line in eachline(file)
                if occursin("SIMPLE", line)
                    section = "SIMPLE"
                elseif occursin("PISO", line)
                    section = "PISO"
                elseif occursin("relaxationFactors", line)
                    section = "relaxation"
                elseif occursin("}", line)
                    section = ""
                elseif section == "SIMPLE" && occursin("nNonOrthogonalCorrectors", line)
                    settings["n_non_orthogonal_correctors"] = parse(Int, split(line, ' ')[end-1])
                elseif section == "PISO" && occursin("nCorrectors", line)
                    settings["n_correctors"] = parse(Int, split(line, ' ')[end-1])
                elseif section == "PISO" && occursin("nNonOrthogonalCorrectors", line)
                    settings["n_non_orthogonal_correctors"] = parse(Int, split(line, ' ')[end-1])
                elseif section == "relaxation" && occursin("U", line)
                    settings["under_relaxation_U"] = parse(Float64, split(line, ' ')[end-1])
                elseif section == "relaxation" && occursin("p", line)
                    settings["under_relaxation_p"] = parse(Float64, split(line, ' ')[end-1])
                end
            end
        end
    end
    
    return settings
end

"""
    initialize_field(::Type{T}, mesh::Mesh, filename::String) where T

Initialize a field from an OpenFOAM field file.

# Arguments
- `T`: Type of the field values
- `mesh`: The mesh
- `filename`: Path to the field file

# Returns
- `Field{T}`: The initialized field
"""
function initialize_field(::Type{T}, mesh::Mesh, filename::String) where T
    # Create empty field with zero values
    field = Field{T}(mesh, zero(T))
    
    # Read field file if it exists
    if isfile(filename)
        open(filename, "r") do file
            # Skip header
            section = ""
            for line in eachline(file)
                if occursin("internalField", line)
                    section = "internal"
                    
                    # Check if uniform or nonuniform
                    if occursin("uniform", line)
                        # Parse uniform value
                        if T <: SVector
                            # Vector field
                            value_str = match(r"\((.*?)\)", line).captures[1]
                            values = parse.(Float64, split(value_str, ' '))
                            value = SVector{3,Float64}(values...)
                            field.internal_field .= value
                        else
                            # Scalar field
                            value = parse(Float64, split(line, ' ')[end-1])
                            field.internal_field .= value
                        end
                    else
                        # Nonuniform field - read values
                        # Skip to opening parenthesis
                        while !occursin("(", line)
                            line = readline(file)
                        end
                        
                        # Read values
                        i = 1
                        while !occursin(")", line) && i <= length(field.internal_field)
                            line = readline(file)
                            if occursin(")", line)
                                break
                            end
                            
                            if T <: SVector
                                # Vector field
                                value_str = match(r"\((.*?)\)", line).captures[1]
                                values = parse.(Float64, split(value_str, ' '))
                                field.internal_field[i] = SVector{3,Float64}(values...)
                            else
                                # Scalar field
                                field.internal_field[i] = parse(Float64, line)
                            end
                            
                            i += 1
                        end
                    end
                elseif occursin("boundaryField", line)
                    section = "boundary"
                elseif section == "boundary" && occursin("{", line)
                    # Parse patch name
                    patch_name = strip(readline(file))
                    
                    # Skip to type
                    while !occursin("type", line)
                        line = readline(file)
                    end
                    
                    # Parse boundary condition type
                    bc_type = split(line, ' ')[end-1]
                    
                    if bc_type == "fixedValue"
                        # Read value
                        while !occursin("value", line)
                            line = readline(file)
                        end
                        
                        if T <: SVector
                            # Vector field
                            value_str = match(r"\((.*?)\)", line).captures[1]
                            values = parse.(Float64, split(value_str, ' '))
                            value = SVector{3,Float64}(values...)
                            field.boundary_field[patch_name] .= value
                            
                            # Update boundary condition
                            mesh.boundary_conditions[patch_name] = FixedValueBC(value)
                        else
                            # Scalar field
                            value = parse(Float64, split(line, ' ')[end-1])
                            field.boundary_field[patch_name] .= value
                            
                            # Update boundary condition
                            mesh.boundary_conditions[patch_name] = FixedValueBC(value)
                        end
                    elseif bc_type == "fixedGradient"
                        # Read gradient
                        while !occursin("gradient", line)
                            line = readline(file)
                        end
                        
                        if T <: SVector
                            # Vector field
                            value_str = match(r"\((.*?)\)", line).captures[1]
                            values = parse.(Float64, split(value_str, ' '))
                            gradient = SVector{3,Float64}(values...)
                            
                            # Update boundary condition
                            mesh.boundary_conditions[patch_name] = FixedGradientBC(gradient)
                        else
                            # Scalar field
                            gradient = parse(Float64, split(line, ' ')[end-1])
                            
                            # Update boundary condition
                            mesh.boundary_conditions[patch_name] = FixedGradientBC(gradient)
                        end
                    elseif bc_type == "zeroGradient"
                        # Update boundary condition
                        mesh.boundary_conditions[patch_name] = ZeroGradientBC()
                    elseif bc_type == "cyclic"
                        # Find matching patch
                        while !occursin("neighbourPatch", line)
                            line = readline(file)
                            if occursin("}", line)
                                break
                            end
                        end
                        
                        if occursin("neighbourPatch", line)
                            matching_patch = split(line, ' ')[end-1]
                            
                            # Update boundary condition
                            mesh.boundary_conditions[patch_name] = CyclicBC(matching_patch)
                        end
                    end
                end
            end
        end
    end
    
    # Initialize old time field
    field.old_time_field .= field.internal_field
    
    return field
end

"""
    write_fields(case_dir::String, time::Union{Float64,String}, fields::Vector{Field}, mesh::Mesh)

Write fields to OpenFOAM format files and VTK format for visualization.

# Arguments
- `case_dir`: Path to the case directory
- `time`: Current time or "steady" for steady-state
- `fields`: List of fields to write
- `mesh`: The mesh
"""
function write_fields(case_dir::String, time::Union{Float64,String}, fields::Vector{Field}, mesh::Mesh)
    # Create time directory
    time_dir = joinpath(case_dir, string(time))
    mkpath(time_dir)
    
    # Write fields in OpenFOAM format
    for field in fields
        write_field(time_dir, field, mesh)
    end
    
    # Write VTK file for visualization
    write_vtk(case_dir, time, fields, mesh)
end

"""
    write_field(time_dir::String, field::Field, mesh::Mesh)

Write a field to OpenFOAM format file.
"""
function write_field(time_dir::String, field::Field{T}, mesh::Mesh) where T
    # Determine field name
    field_name = if T <: SVector{3,Float64}
        "U"
    elseif T <: Float64
        "p"
    else
        "field"
    end
    
    # Open file
    open(joinpath(time_dir, field_name), "w") do file
        # Write header
        write(file, """
        /*--------------------------------*- C++ -*----------------------------------*\\
        | =========                 |                                                 |
        | \\\\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
        |  \\\\    /   O peration     | Version:  v2012                                |
        |   \\\\  /    A nd           | Website:  www.openfoam.com                      |
        |    \\\\/     M anipulation  |                                                 |
        \\*---------------------------------------------------------------------------*/
        FoamFile
        {
            version     2.0;
            format      ascii;
            class       $(T <: SVector ? "volVectorField" : "volScalarField");
            location    "$time_dir";
            object      $field_name;
        }
        // * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //
        
        dimensions      [0 0 0 0 0 0 0];
        
        internalField   nonuniform List<$(T <: SVector ? "vector" : "scalar")> 
        $(length(field.internal_field))
        (
        """)
        
        # Write internal field values
        for value in field.internal_field
            if T <: SVector
                write(file, "($(value[1]) $(value[2]) $(value[3]))\n")
            else
                write(file, "$value\n")
            end
        end
        
        write(file, ")\n;\n\nboundaryField\n{\n")
        
        # Write boundary field values
        for (patch_name, bc) in mesh.boundary_conditions
            write(file, "    $patch_name\n    {\n")
            
            if bc isa FixedValueBC
                write(file, "        type            fixedValue;\n")
                write(file, "        value           ")
                
                if T <: SVector
                    write(file, "uniform ($(bc.value[1]) $(bc.value[2]) $(bc.value[3]));\n")
                else
                    write(file, "uniform $(bc.value);\n")
                end
            elseif bc isa FixedGradientBC
                write(file, "        type            fixedGradient;\n")
                write(file, "        gradient        ")
                
                if T <: SVector
                    write(file, "uniform ($(bc.gradient[1]) $(bc.gradient[2]) $(bc.gradient[3]));\n")
                else
                    write(file, "uniform $(bc.gradient);\n")
                end
            elseif bc isa ZeroGradientBC
                write(file, "        type            zeroGradient;\n")
            elseif bc isa CyclicBC
                write(file, "        type            cyclic;\n")
                write(file, "        neighbourPatch  $(bc.matching_patch);\n")
            end
            
            write(file, "    }\n")
        end
        
        write(file, "}\n\n// ************************************************************************* //\n")
    end
end

"""
    write_vtk(case_dir::String, time::Union{Float64,String}, fields::Vector{Field}, mesh::Mesh)

Write fields to VTK format for visualization.
"""
function write_vtk(case_dir::String, time::Union{Float64,String}, fields::Vector{Field}, mesh::Mesh)
    # Create VTK directory
    vtk_dir = joinpath(case_dir, "VTK")
    mkpath(vtk_dir)
    
    # Extract points from mesh
    n_cells = length(mesh.cells)
    points = Vector{SVector{3,Float64}}(undef, n_cells)
    
    for i in 1:n_cells
        points[i] = mesh.cells[i].center
    end
    
    # Create VTK file
    vtk_filename = joinpath(vtk_dir, "$(typeof(time) == String ? time : string(round(time, digits=4)))")
    
    # Create VTK grid
    vtkfile = vtk_grid(vtk_filename, [p[1] for p in points], [p[2] for p in points], [p[3] for p in points])
    
    # Add fields to VTK file
    for field in fields
        if eltype(field.internal_field) <: SVector{3,Float64}
            # Vector field
            vtk_point_data(vtkfile, ([v[1] for v in field.internal_field], 
                                     [v[2] for v in field.internal_field], 
                                     [v[3] for v in field.internal_field]), "U")
        else
            # Scalar field
            vtk_point_data(vtkfile, field.internal_field, "p")
        end
    end
    
    # Save VTK file
    vtk_save(vtkfile)
end

"""
    should_write_output(current_time::Float64, config::CaseConfiguration)

Determine if output should be written at the current time.
"""
function should_write_output(current_time::Float64, config::CaseConfiguration)
    # Check if current time is a multiple of write interval
    time_step_ratio = current_time / config.write_interval
    return isapprox(round(time_step_ratio), time_step_ratio, atol=1e-10) || 
           isapprox(current_time, config.end_time, atol=1e-10)
end
