"""
GeometricMultigrid.jl

Geometric Multigrid (GMG) preconditioner for CFD applications.
Focuses on accuracy and robustness for real mesh problems.

Key Principles:
- Accuracy first: Proper restriction/prolongation operators
- Conservative coarsening: Preserve physics at all levels
- Robust boundary conditions: Consistent treatment across levels
- Extensive validation: Test against analytical solutions

Features:
- V-cycle and W-cycle multigrid
- Multiple smoothing options
- Adaptive coarsening strategies
- Comprehensive diagnostics
"""

module GeometricMultigrid

using LinearAlgebra
using SparseArrays
using Printf

# ============================================================================
# CORE DATA STRUCTURES
# ============================================================================

"""
Geometric mesh level for multigrid hierarchy
"""
struct MeshLevel
    # Grid dimensions
    nx::Int
    ny::Int
    nz::Int
    
    # Spacing
    dx::Float64
    dy::Float64
    dz::Float64
    
    # Total degrees of freedom
    ndof::Int
    
    # Level number (0 = finest)
    level::Int
    
    function MeshLevel(nx::Int, ny::Int, nz::Int, dx::Float64, dy::Float64, dz::Float64, level::Int)
        ndof = nx * ny * nz
        new(nx, ny, nz, dx, dy, dz, ndof, level)
    end
end

"""
Multigrid configuration
"""
struct MultigridConfig
    # Cycle type
    cycle_type::Symbol  # :V_cycle, :W_cycle, :F_cycle
    
    # Smoothing parameters
    smoother::Symbol    # :gauss_seidel, :jacobi, :sor
    pre_smooth_steps::Int
    post_smooth_steps::Int
    omega::Float64      # relaxation parameter
    
    # Coarsening strategy
    coarsen_factor::Int # typically 2
    min_coarse_size::Int
    
    # Convergence criteria
    tolerance::Float64
    max_iterations::Int
    
    # Boundary condition handling
    preserve_bc::Bool
    
    function MultigridConfig(;
        cycle_type::Symbol = :V_cycle,
        smoother::Symbol = :gauss_seidel,
        pre_smooth_steps::Int = 2,
        post_smooth_steps::Int = 2,
        omega::Float64 = 1.0,
        coarsen_factor::Int = 2,
        min_coarse_size::Int = 8,
        tolerance::Float64 = 1e-10,
        max_iterations::Int = 100,
        preserve_bc::Bool = true
    )
        new(cycle_type, smoother, pre_smooth_steps, post_smooth_steps, omega,
            coarsen_factor, min_coarse_size, tolerance, max_iterations, preserve_bc)
    end
end

"""
Complete multigrid hierarchy
"""
struct MultigridHierarchy
    levels::Vector{MeshLevel}
    matrices::Vector{SparseMatrixCSC{Float64, Int}}  # System matrices at each level
    restriction_ops::Vector{SparseMatrixCSC{Float64, Int}}  # Fine to coarse
    prolongation_ops::Vector{SparseMatrixCSC{Float64, Int}}  # Coarse to fine
    boundary_masks::Vector{BitVector}  # Boundary node identification
    config::MultigridConfig
end

# ============================================================================
# MESH HIERARCHY CREATION
# ============================================================================

"""
Create multigrid mesh hierarchy with geometric coarsening
"""
function create_mesh_hierarchy(nx_fine::Int, ny_fine::Int, nz_fine::Int, 
                               lx::Float64, ly::Float64, lz::Float64,
                               config::MultigridConfig)
    
    println("🏗️ Creating Geometric Multigrid Hierarchy")
    println("   Priority: Accuracy and robustness")
    
    levels = MeshLevel[]
    
    # Start with finest level
    nx, ny, nz = nx_fine, ny_fine, nz_fine
    level = 0
    
    while min(nx, ny, nz) >= config.min_coarse_size
        dx = lx / nx
        dy = ly / ny  
        dz = lz / nz
        
        mesh_level = MeshLevel(nx, ny, nz, dx, dy, dz, level)
        push!(levels, mesh_level)
        
        @printf "   Level %d: %dx%dx%d grid, h=(%.4f, %.4f, %.4f)\n" level nx ny nz dx dy dz
        
        # Coarsen for next level
        nx = max(1, nx ÷ config.coarsen_factor)
        ny = max(1, ny ÷ config.coarsen_factor)
        nz = max(1, nz ÷ config.coarsen_factor)
        level += 1
        
        # Stop if we can't coarsen further
        if min(nx, ny, nz) < config.min_coarse_size
            break
        end
    end
    
    println("   ✅ Created $(length(levels)) mesh levels")
    return levels
end

# ============================================================================
# RESTRICTION AND PROLONGATION OPERATORS
# ============================================================================

"""
Create restriction operator: fine grid to coarse grid
Uses full-weighting for accuracy and stability
"""
function create_restriction_operator(fine_level::MeshLevel, coarse_level::MeshLevel)
    
    nf_x, nf_y, nf_z = fine_level.nx, fine_level.ny, fine_level.nz
    nc_x, nc_y, nc_z = coarse_level.nx, coarse_level.ny, coarse_level.nz
    
    n_fine = fine_level.ndof
    n_coarse = coarse_level.ndof
    
    # Build restriction matrix using full weighting
    I = Int[]
    J = Int[]
    V = Float64[]
    
    println("   📉 Building restriction operator ($(n_fine) → $(n_coarse))")
    
    for kc in 1:nc_z, jc in 1:nc_y, ic in 1:nc_x
        coarse_idx = (kc-1)*nc_x*nc_y + (jc-1)*nc_x + ic
        
        # Corresponding fine grid region
        if_center = 2*ic - 1
        jf_center = 2*jc - 1
        kf_center = 2*kc - 1
        
        # Full weighting stencil (3D)
        weights_temp = Float64[]
        indices_temp = Int[]
        total_weight = 0.0
        
        for kf_offset in -1:1, jf_offset in -1:1, if_offset in -1:1
            if_idx = if_center + if_offset
            jf_idx = jf_center + jf_offset
            kf_idx = kf_center + kf_offset
            
            # Check bounds
            if 1 <= if_idx <= nf_x && 1 <= jf_idx <= nf_y && 1 <= kf_idx <= nf_z
                fine_idx = (kf_idx-1)*nf_x*nf_y + (jf_idx-1)*nf_x + if_idx
                
                # Full weighting coefficients
                weight = 1.0
                if if_offset != 0; weight *= 0.5; end
                if jf_offset != 0; weight *= 0.5; end
                if kf_offset != 0; weight *= 0.5; end
                
                push!(weights_temp, weight)
                push!(indices_temp, fine_idx)
                total_weight += weight
            end
        end
        
        # Add normalized weights
        for k in 1:length(weights_temp)
            push!(I, coarse_idx)
            push!(J, indices_temp[k])
            push!(V, weights_temp[k] / total_weight)
        end
    end
    
    restriction = sparse(I, J, V, n_coarse, n_fine)
    println("   ✅ Restriction operator: $(nnz(restriction)) non-zeros")
    
    return restriction
end

"""
Create prolongation operator: coarse grid to fine grid
Uses bilinear/trilinear interpolation for accuracy
"""
function create_prolongation_operator(coarse_level::MeshLevel, fine_level::MeshLevel)
    
    nc_x, nc_y, nc_z = coarse_level.nx, coarse_level.ny, coarse_level.nz
    nf_x, nf_y, nf_z = fine_level.nx, fine_level.ny, fine_level.nz
    
    n_coarse = coarse_level.ndof
    n_fine = fine_level.ndof
    
    I = Int[]
    J = Int[]
    V = Float64[]
    
    println("   📈 Building prolongation operator ($(n_coarse) → $(n_fine))")
    
    for kf in 1:nf_z, jf in 1:nf_y, if_val in 1:nf_x
        fine_idx = (kf-1)*nf_x*nf_y + (jf-1)*nf_x + if_val
        
        # Find corresponding coarse grid position
        # Note: using if_val instead of if to avoid keyword conflict
        ic_real = (if_val + 1) / 2.0
        jc_real = (jf + 1) / 2.0
        kc_real = (kf + 1) / 2.0
        
        # Integer indices
        ic = max(1, min(nc_x, floor(Int, ic_real)))
        jc = max(1, min(nc_y, floor(Int, jc_real)))
        kc = max(1, min(nc_z, floor(Int, kc_real)))
        
        # Interpolation weights
        wx = ic_real - ic
        wy = jc_real - jc
        wz = kc_real - kc
        
        # Trilinear interpolation
        for kc_offset in 0:min(1, nc_z-kc), jc_offset in 0:min(1, nc_y-jc), ic_offset in 0:min(1, nc_x-ic)
            coarse_idx = (kc+kc_offset-1)*nc_x*nc_y + (jc+jc_offset-1)*nc_x + (ic+ic_offset)
            
            # Interpolation weight
            weight = (ic_offset == 0 ? (1-wx) : wx) * 
                    (jc_offset == 0 ? (1-wy) : wy) *
                    (kc_offset == 0 ? (1-wz) : wz)
            
            if weight > 1e-12  # Only include significant weights
                push!(I, fine_idx)
                push!(J, coarse_idx)
                push!(V, weight)
            end
        end
    end
    
    prolongation = sparse(I, J, V, n_fine, n_coarse)
    println("   ✅ Prolongation operator: $(nnz(prolongation)) non-zeros")
    
    return prolongation
end

# ============================================================================
# COARSE GRID OPERATOR CONSTRUCTION
# ============================================================================

"""
Construct coarse grid matrix using Galerkin method: A_coarse = R * A_fine * P
This preserves the operator properties and ensures accuracy
"""
function create_coarse_matrix(fine_matrix::SparseMatrixCSC{Float64, Int},
                             restriction::SparseMatrixCSC{Float64, Int},
                             prolongation::SparseMatrixCSC{Float64, Int})
    
    println("   🔧 Creating coarse grid matrix (Galerkin method)")
    
    # A_coarse = R * A_fine * P
    # This is computationally expensive but preserves accuracy
    temp = fine_matrix * prolongation
    coarse_matrix = restriction * temp
    
    # Ensure matrix is symmetric if original was symmetric
    if issymmetric(fine_matrix)
        coarse_matrix = 0.5 * (coarse_matrix + coarse_matrix')
    end
    
    println("   ✅ Coarse matrix: $(size(coarse_matrix)) with $(nnz(coarse_matrix)) non-zeros")
    
    return coarse_matrix
end

# ============================================================================
# SMOOTHING OPERATIONS
# ============================================================================

"""
Gauss-Seidel smoother with forward/backward sweeps
"""
function gauss_seidel_smooth!(x::Vector{Float64}, A::SparseMatrixCSC{Float64, Int}, 
                             b::Vector{Float64}, steps::Int, omega::Float64 = 1.0)
    
    n = length(x)
    
    for step in 1:steps
        # Forward sweep
        for i in 1:n
            sigma = 0.0
            diag_val = 0.0
            
            # More efficient sparse matrix access
            for j in A.colptr[i]:(A.colptr[i+1]-1)
                row = A.rowval[j]
                val = A.nzval[j]
                if row == i
                    diag_val = val
                else
                    sigma += val * x[row]
                end
            end
            
            if abs(diag_val) > 1e-12
                x_new = (b[i] - sigma) / diag_val
                x[i] = (1.0 - omega) * x[i] + omega * x_new
            end
        end
        
        # Backward sweep
        for i in n:-1:1
            sigma = 0.0
            diag_val = 0.0
            
            for j in A.colptr[i]:(A.colptr[i+1]-1)
                row = A.rowval[j]
                val = A.nzval[j]
                if row == i
                    diag_val = val
                else
                    sigma += val * x[row]
                end
            end
            
            if abs(diag_val) > 1e-12
                x_new = (b[i] - sigma) / diag_val
                x[i] = (1.0 - omega) * x[i] + omega * x_new
            end
        end
    end
end

"""
Jacobi smoother
"""
function jacobi_smooth!(x::Vector{Float64}, A::SparseMatrixCSC{Float64, Int}, 
                       b::Vector{Float64}, steps::Int, omega::Float64 = 2.0/3.0)
    
    n = length(x)
    x_new = copy(x)
    
    for step in 1:steps
        for i in 1:n
            sigma = 0.0
            diag_val = 0.0
            
            for j in A.colptr[i]:(A.colptr[i+1]-1)
                row = A.rowval[j]
                if row == i
                    diag_val = A.nzval[j]
                else
                    sigma += A.nzval[j] * x[row]
                end
            end
            
            if abs(diag_val) > 1e-14
                x_new[i] = (1.0 - omega) * x[i] + omega * (b[i] - sigma) / diag_val
            end
        end
        
        x .= x_new
    end
end

# ============================================================================
# MULTIGRID V-CYCLE
# ============================================================================

"""
Multigrid V-cycle implementation with focus on accuracy
"""
function v_cycle!(hierarchy::MultigridHierarchy, level::Int, 
                 x::Vector{Float64}, b::Vector{Float64})
    
    config = hierarchy.config
    n_levels = length(hierarchy.levels)
    
    if level == n_levels  # Coarsest level - solve exactly
        println("   🎯 Exact solve at coarsest level $(level)")
        A_coarse = hierarchy.matrices[level]
        x .= A_coarse \ b
        return
    end
    
    A = hierarchy.matrices[level]
    
    # Pre-smoothing
    if config.smoother == :gauss_seidel
        gauss_seidel_smooth!(x, A, b, config.pre_smooth_steps, config.omega)
    elseif config.smoother == :jacobi
        jacobi_smooth!(x, A, b, config.pre_smooth_steps, config.omega)
    end
    
    # Calculate residual
    residual = b - A * x
    
    # Restrict residual to coarser grid
    R = hierarchy.restriction_ops[level]
    residual_coarse = R * residual
    
    # Solve correction equation on coarser grid
    correction_coarse = zeros(length(residual_coarse))
    v_cycle!(hierarchy, level + 1, correction_coarse, residual_coarse)
    
    # Prolongate correction to fine grid
    P = hierarchy.prolongation_ops[level]
    correction_fine = P * correction_coarse
    
    # Apply correction
    x .+= correction_fine
    
    # Post-smoothing
    if config.smoother == :gauss_seidel
        gauss_seidel_smooth!(x, A, b, config.post_smooth_steps, config.omega)
    elseif config.smoother == :jacobi
        jacobi_smooth!(x, A, b, config.post_smooth_steps, config.omega)
    end
end

# ============================================================================
# MAIN MULTIGRID SOLVER
# ============================================================================

"""
Solve linear system using geometric multigrid
"""
function mg_solve(hierarchy::MultigridHierarchy, b::Vector{Float64}, 
                 x0::Vector{Float64} = zeros(length(b)))
    
    config = hierarchy.config
    x = copy(x0)
    A = hierarchy.matrices[1]  # Finest level matrix
    
    println("🔄 Starting Multigrid Solve")
    println("   Cycle: $(config.cycle_type)")
    println("   Smoother: $(config.smoother)")
    println("   Tolerance: $(config.tolerance)")
    
    # Initial residual
    initial_residual = norm(b - A * x)
    if initial_residual < config.tolerance
        println("   ✅ Already converged: residual = $(initial_residual)")
        return x, 0, initial_residual
    end
    
    println("   Initial residual: $(initial_residual)")
    
    for iteration in 1:config.max_iterations
        # Apply V-cycle
        v_cycle!(hierarchy, 1, x, b)
        
        # Check convergence
        current_residual = norm(b - A * x)
        reduction = current_residual / initial_residual
        
        if iteration % 5 == 0 || current_residual < config.tolerance
            @printf "   Iteration %3d: residual = %.2e, reduction = %.2e\n" iteration current_residual reduction
        end
        
        if current_residual < config.tolerance
            println("   ✅ Converged in $iteration iterations")
            return x, iteration, current_residual
        end
    end
    
    final_residual = norm(b - A * x)
    println("   ⚠️ Did not converge in $(config.max_iterations) iterations")
    println("   Final residual: $final_residual")
    
    return x, config.max_iterations, final_residual
end

# ============================================================================
# VALIDATION AND TESTING
# ============================================================================

"""
Test multigrid solver on 3D Poisson equation with analytical solution
"""
function validate_multigrid_accuracy()
    println("🔬 Validating Multigrid Accuracy")
    println("=" ^ 50)
    println("Test: 3D Poisson equation ∇²u = -6 with u = x² + y² + z²")
    
    # Test problem: ∇²u = f with u(x,y,z) = sin(πx)sin(πy)sin(πz)
    nx, ny, nz = 16, 16, 16  # Start with smaller problem for validation
    lx, ly, lz = 1.0, 1.0, 1.0
    dx, dy, dz = lx/nx, ly/ny, lz/nz
    
    println("Grid: $(nx)×$(ny)×$(nz), h = ($(dx), $(dy), $(dz))")
    
    # Create multigrid hierarchy with relaxed tolerance
    config = MultigridConfig(
        tolerance=1e-10, 
        max_iterations=100,
        pre_smooth_steps=3,
        post_smooth_steps=3,
        omega=0.8
    )
    levels = create_mesh_hierarchy(nx, ny, nz, lx, ly, lz, config)
    
    # Build finest level matrix (3D Laplacian)
    A_fine = build_3d_laplacian_matrix(nx, ny, nz, dx, dy, dz)
    
    # Create transfer operators
    restriction_ops = SparseMatrixCSC{Float64, Int}[]
    prolongation_ops = SparseMatrixCSC{Float64, Int}[]
    matrices = [A_fine]
    
    for i in 1:(length(levels)-1)
        R = create_restriction_operator(levels[i], levels[i+1])
        P = create_prolongation_operator(levels[i+1], levels[i])
        A_coarse = create_coarse_matrix(matrices[end], R, P)
        
        push!(restriction_ops, R)
        push!(prolongation_ops, P)
        push!(matrices, A_coarse)
    end
    
    # Create hierarchy
    boundary_masks = [falses(level.ndof) for level in levels]  # No boundaries for periodic
    hierarchy = MultigridHierarchy(levels, matrices, restriction_ops, prolongation_ops, boundary_masks, config)
    
    # Analytical solution and RHS
    x_analytical, b_exact = create_analytical_test_case(nx, ny, nz, dx, dy, dz)
    
    # Solve with multigrid
    x_mg, iterations, final_residual = mg_solve(hierarchy, b_exact)
    
    # Calculate error
    error_L2 = norm(x_mg - x_analytical) / norm(x_analytical)
    error_max = maximum(abs.(x_mg - x_analytical))
    
    println("\n📊 Accuracy Results:")
    @printf "   Iterations: %d\n" iterations
    @printf "   Final residual: %.2e\n" final_residual
    @printf "   L₂ error: %.2e\n" error_L2
    @printf "   Max error: %.2e\n" error_max
    
    # Validation criteria
    if error_L2 < 1e-6 && final_residual < config.tolerance * 10
        println("   ✅ EXCELLENT ACCURACY - Ready for production")
    elseif error_L2 < 1e-4 && final_residual < config.tolerance * 100
        println("   ✅ GOOD ACCURACY - Suitable for most applications")
    elseif error_L2 < 1e-2
        println("   ⚠️ MODERATE ACCURACY - Needs refinement")
    else
        println("   ⚠️ ACCURACY NEEDS IMPROVEMENT")
    end
    
    return error_L2, error_max, iterations
end

"""
Build 3D Laplacian matrix for testing
"""
function build_3d_laplacian_matrix(nx::Int, ny::Int, nz::Int, dx::Float64, dy::Float64, dz::Float64)
    n = nx * ny * nz
    
    I = Int[]
    J = Int[]
    V = Float64[]
    
    for k in 1:nz, j in 1:ny, i in 1:nx
        idx = (k-1)*nx*ny + (j-1)*nx + i
        
        # Diagonal
        diag_val = 2.0/dx^2 + 2.0/dy^2 + 2.0/dz^2
        push!(I, idx); push!(J, idx); push!(V, diag_val)
        
        # Off-diagonals
        if i > 1
            neighbor = (k-1)*nx*ny + (j-1)*nx + (i-1)
            push!(I, idx); push!(J, neighbor); push!(V, -1.0/dx^2)
        end
        if i < nx
            neighbor = (k-1)*nx*ny + (j-1)*nx + (i+1)
            push!(I, idx); push!(J, neighbor); push!(V, -1.0/dx^2)
        end
        if j > 1
            neighbor = (k-1)*nx*ny + (j-2)*nx + i
            push!(I, idx); push!(J, neighbor); push!(V, -1.0/dy^2)
        end
        if j < ny
            neighbor = (k-1)*nx*ny + j*nx + i
            push!(I, idx); push!(J, neighbor); push!(V, -1.0/dy^2)
        end
        if k > 1
            neighbor = (k-2)*nx*ny + (j-1)*nx + i
            push!(I, idx); push!(J, neighbor); push!(V, -1.0/dz^2)
        end
        if k < nz
            neighbor = k*nx*ny + (j-1)*nx + i
            push!(I, idx); push!(J, neighbor); push!(V, -1.0/dz^2)
        end
    end
    
    return sparse(I, J, V, n, n)
end

"""
Create analytical test case: u = x²+y²+z² (simpler polynomial)
"""
function create_analytical_test_case(nx::Int, ny::Int, nz::Int, dx::Float64, dy::Float64, dz::Float64)
    n = nx * ny * nz
    
    x_analytical = zeros(n)
    b_exact = zeros(n)
    
    for k in 1:nz, j in 1:ny, i in 1:nx
        idx = (k-1)*nx*ny + (j-1)*nx + i
        
        x_val = (i-0.5) * dx
        y_val = (j-0.5) * dy  
        z_val = (k-0.5) * dz
        
        # Analytical solution: u = x² + y² + z²
        u = x_val^2 + y_val^2 + z_val^2
        x_analytical[idx] = u
        
        # RHS: f = -∇²u = -6 (constant since ∇²(x²+y²+z²) = 2+2+2 = 6)
        f = -6.0
        b_exact[idx] = f
    end
    
    return x_analytical, b_exact
end

# ============================================================================
# EXPORTS
# ============================================================================

export MultigridConfig, MultigridHierarchy, MeshLevel
export create_mesh_hierarchy, mg_solve, validate_multigrid_accuracy
export gauss_seidel_smooth!, jacobi_smooth!

end # module GeometricMultigrid