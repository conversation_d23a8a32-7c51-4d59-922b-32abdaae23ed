"""
SolverDiagnostics.jl

Comprehensive linear solver diagnostics for CFD applications.
Essential for production monitoring, validation, and performance optimization.

Key Features:
- Real-time convergence monitoring with multiple metrics
- Matrix condition analysis and eigenvalue estimation
- Solution quality assessment (conservation, stability)
- Performance profiling and resource usage tracking
- Automatic anomaly detection and warnings
- Detailed reporting with visualization-ready output

Accuracy Focus:
- Multiple convergence criteria for robust assessment
- Conservative stability thresholds
- Comprehensive error detection and reporting
- Validated against analytical solutions
"""

module SolverDiagnostics

using LinearAlgebra
using SparseArrays
using Printf
using Statistics
using Dates

# ============================================================================
# CONVERGENCE MONITORING STRUCTURES
# ============================================================================

"""
Convergence history and metrics
"""
mutable struct ConvergenceHistory
    # Residual tracking
    absolute_residuals::Vector{Float64}       # ||r|| at each iteration
    relative_residuals::Vector{Float64}       # ||r|| / ||r0|| 
    normalized_residuals::Vector{Float64}     # ||r|| / ||b||
    
    # Solution tracking
    solution_norms::Vector{Float64}           # ||x|| at each iteration
    solution_changes::Vector{Float64}         # ||x^k - x^{k-1}|| / ||x^k||
    
    # Convergence rates
    convergence_rates::Vector{Float64}        # r^k / r^{k-1}
    average_convergence_rate::Float64         # Geometric mean of rates
    
    # Iteration timing
    iteration_times::Vector{Float64}          # Time per iteration
    cumulative_times::Vector{Float64}         # Total time to iteration k
    
    # Quality metrics
    stagnation_count::Int                     # Consecutive stagnant iterations
    oscillation_count::Int                   # Detected oscillations
    divergence_warnings::Int                 # Number of divergence warnings
    
    # Final convergence assessment
    converged::Bool                           # Did the solver converge?
    convergence_reason::String                # Why did it converge/diverge?
    final_iteration::Int                      # Final iteration number
    final_time::Float64                       # Total solution time
    
    function ConvergenceHistory()
        new(Float64[], Float64[], Float64[],
            Float64[], Float64[], Float64[], 0.0,
            Float64[], Float64[],
            0, 0, 0,
            false, "not_converged", 0, 0.0)
    end
end

"""
Matrix diagnostics and properties
"""
struct MatrixDiagnostics
    # Basic properties
    size::Tuple{Int, Int}                     # Matrix dimensions
    nnz::Int                                  # Number of non-zeros
    density::Float64                          # Sparsity pattern density
    
    # Numerical properties
    condition_number_estimate::Float64        # Estimated κ(A)
    largest_eigenvalue_estimate::Float64      # Estimated λ_max
    smallest_eigenvalue_estimate::Float64     # Estimated λ_min
    spectral_radius_estimate::Float64         # Estimated ρ(A)
    
    # Matrix structure
    is_symmetric::Bool                        # Symmetry check
    is_positive_definite::Bool                # Positive definiteness
    diagonal_dominance::Float64               # Measure of diagonal dominance
    
    # Conditioning warnings
    is_well_conditioned::Bool                 # κ(A) < threshold
    has_zero_diagonal::Bool                   # Any zero diagonal elements
    has_negative_diagonal::Bool               # Any negative diagonal elements
    
    # Quality assessment
    quality_level::Symbol                     # :excellent, :good, :poor, :singular
    quality_warnings::Vector{String}         # Specific warnings
end

"""
Solution quality metrics
"""
struct SolutionQuality
    # Physical conservation
    mass_conservation_error::Float64          # Global mass conservation
    momentum_conservation_error::Vector{Float64} # Momentum conservation (3D)
    energy_conservation_error::Float64        # Energy conservation
    
    # Mathematical properties
    solution_boundedness::Bool                # Are values physically reasonable?
    max_solution_value::Float64               # Maximum solution value
    min_solution_value::Float64               # Minimum solution value
    solution_smoothness::Float64              # Measure of solution smoothness
    
    # Stability indicators
    has_oscillations::Bool                    # Oscillatory behavior detected
    has_overshoots::Bool                      # Unphysical overshoots
    has_undershoots::Bool                     # Unphysical undershoots
    
    # Accuracy metrics (when analytical solution available)
    l1_error::Union{Float64, Nothing}         # L1 norm error
    l2_error::Union{Float64, Nothing}         # L2 norm error
    max_error::Union{Float64, Nothing}        # Maximum pointwise error
    
    # Overall assessment
    quality_score::Float64                    # Composite quality score [0,1]
    quality_level::Symbol                     # :excellent, :good, :acceptable, :poor
    quality_issues::Vector{String}           # Specific quality issues identified
end

"""
Performance metrics and profiling
"""
struct PerformanceMetrics
    # Timing
    setup_time::Float64                       # Time for matrix setup/factorization
    solve_time::Float64                       # Total solution time
    average_iteration_time::Float64           # Average time per iteration
    time_per_dof::Float64                     # Solution time per degree of freedom
    
    # Iteration statistics
    total_iterations::Int                     # Total iterations required
    iterations_per_second::Float64            # Iteration rate
    
    # Memory usage
    peak_memory_usage::Float64                # Peak memory usage (MB)
    memory_per_dof::Float64                   # Memory per degree of freedom
    
    # Convergence efficiency
    convergence_rate::Float64                 # Final convergence rate
    efficiency_score::Float64                # Composite efficiency score [0,1]
    
    # Comparative metrics
    flops_estimate::Float64                   # Estimated floating point operations
    flops_per_second::Float64                 # Computational rate
    
    function PerformanceMetrics(setup_time, solve_time, total_iterations, 
                               peak_memory, matrix_size, convergence_rate)
        avg_iter_time = solve_time / max(total_iterations, 1)
        time_per_dof = solve_time / matrix_size
        iter_per_sec = total_iterations / max(solve_time, 1e-10)
        memory_per_dof = peak_memory / matrix_size
        
        # Efficiency score based on convergence rate and iteration count
        efficiency = exp(-total_iterations / 100.0) * max(0.0, 1.0 - convergence_rate)
        
        # Rough FLOPS estimate for iterative solver
        flops = total_iterations * matrix_size * 10.0  # Rough estimate
        flops_per_sec = flops / max(solve_time, 1e-10)
        
        new(setup_time, solve_time, avg_iter_time, time_per_dof,
            total_iterations, iter_per_sec,
            peak_memory, memory_per_dof,
            convergence_rate, efficiency,
            flops, flops_per_sec)
    end
end

# ============================================================================
# COMPREHENSIVE SOLVER DIAGNOSTICS
# ============================================================================

"""
Complete diagnostic suite for linear solver analysis
"""
struct SolverDiagnosticsSuite
    convergence::ConvergenceHistory
    matrix::MatrixDiagnostics
    solution::SolutionQuality
    performance::PerformanceMetrics
    
    # Diagnostic metadata
    solver_name::String
    problem_name::String
    timestamp::String
    julia_version::String
    
    # Summary assessment
    overall_success::Bool
    overall_quality::Symbol                   # :excellent, :good, :acceptable, :poor, :failure
    critical_issues::Vector{String}
    recommendations::Vector{String}
end

# ============================================================================
# CONVERGENCE MONITORING FUNCTIONS
# ============================================================================

"""
Initialize convergence monitoring for a linear solve
"""
function initialize_convergence_monitoring(
    A::SparseMatrixCSC{Float64, Int},
    b::Vector{Float64},
    x0::Vector{Float64}
)
    
    history = ConvergenceHistory()
    
    # Calculate initial residual
    r0 = b - A * x0
    initial_residual = norm(r0)
    b_norm = norm(b)
    
    # Store initial values
    push!(history.absolute_residuals, initial_residual)
    push!(history.relative_residuals, 1.0)  # r0/r0 = 1
    push!(history.normalized_residuals, initial_residual / max(b_norm, 1e-15))
    push!(history.solution_norms, norm(x0))
    push!(history.solution_changes, 0.0)
    push!(history.convergence_rates, 1.0)
    push!(history.iteration_times, 0.0)
    push!(history.cumulative_times, 0.0)
    
    return history
end

"""
Update convergence monitoring during iteration
"""
function update_convergence_monitoring!(
    history::ConvergenceHistory,
    A::SparseMatrixCSC{Float64, Int},
    b::Vector{Float64},
    x_current::Vector{Float64},
    x_previous::Vector{Float64},
    iteration::Int,
    iteration_time::Float64
)
    
    # Calculate current residual
    r_current = b - A * x_current
    abs_residual = norm(r_current)
    rel_residual = abs_residual / max(history.absolute_residuals[1], 1e-15)
    norm_residual = abs_residual / max(norm(b), 1e-15)
    
    # Solution metrics
    sol_norm = norm(x_current)
    sol_change = norm(x_current - x_previous) / max(sol_norm, 1e-15)
    
    # Convergence rate
    if length(history.absolute_residuals) > 1
        conv_rate = abs_residual / max(history.absolute_residuals[end], 1e-15)
    else
        conv_rate = 1.0
    end
    
    # Timing
    total_time = length(history.cumulative_times) > 0 ? 
                 history.cumulative_times[end] + iteration_time : iteration_time
    
    # Store all metrics
    push!(history.absolute_residuals, abs_residual)
    push!(history.relative_residuals, rel_residual)
    push!(history.normalized_residuals, norm_residual)
    push!(history.solution_norms, sol_norm)
    push!(history.solution_changes, sol_change)
    push!(history.convergence_rates, conv_rate)
    push!(history.iteration_times, iteration_time)
    push!(history.cumulative_times, total_time)
    
    # Quality checks
    check_stagnation!(history)
    check_oscillations!(history)
    check_divergence!(history)
    
    return abs_residual, rel_residual
end

"""
Check for solution stagnation
"""
function check_stagnation!(history::ConvergenceHistory)
    if length(history.convergence_rates) >= 3
        recent_rates = history.convergence_rates[end-2:end]
        if all(r -> r > 0.99, recent_rates)  # Very slow convergence
            history.stagnation_count += 1
        else
            history.stagnation_count = 0
        end
    end
end

"""
Check for oscillatory convergence behavior
"""
function check_oscillations!(history::ConvergenceHistory)
    if length(history.absolute_residuals) >= 4
        residuals = history.absolute_residuals[end-3:end]
        # Check for alternating increase/decrease pattern
        diffs = diff(residuals)
        if length(diffs) >= 3
            signs = sign.(diffs)
            if signs[1] != signs[2] && signs[2] != signs[3]
                history.oscillation_count += 1
            end
        end
    end
end

"""
Check for solution divergence
"""
function check_divergence!(history::ConvergenceHistory)
    if length(history.absolute_residuals) >= 2
        current = history.absolute_residuals[end]
        previous = history.absolute_residuals[end-1]
        
        # Check for rapid increase in residual
        if current > 10.0 * previous && current > 1e-3
            history.divergence_warnings += 1
        end
    end
end

"""
Finalize convergence assessment
"""
function finalize_convergence_analysis!(
    history::ConvergenceHistory,
    tolerance::Float64,
    max_iterations::Int
)
    
    if length(history.absolute_residuals) == 0
        history.convergence_reason = "no_iterations"
        return
    end
    
    final_residual = history.absolute_residuals[end]
    final_iteration = length(history.absolute_residuals) - 1
    history.final_iteration = final_iteration
    history.final_time = length(history.cumulative_times) > 0 ? 
                        history.cumulative_times[end] : 0.0
    
    # Calculate average convergence rate
    if length(history.convergence_rates) > 1
        valid_rates = filter(r -> 0 < r < 1, history.convergence_rates[2:end])
        if !isempty(valid_rates)
            history.average_convergence_rate = exp(mean(log.(valid_rates)))
        end
    end
    
    # Determine convergence status
    if final_residual <= tolerance
        history.converged = true
        history.convergence_reason = "tolerance_achieved"
    elseif final_iteration >= max_iterations
        history.converged = false
        history.convergence_reason = "max_iterations_reached"
    elseif history.divergence_warnings > 3
        history.converged = false
        history.convergence_reason = "divergence_detected"
    elseif history.stagnation_count > 10
        history.converged = false
        history.convergence_reason = "stagnation_detected"
    else
        history.converged = false
        history.convergence_reason = "unknown_failure"
    end
end

# ============================================================================
# MATRIX ANALYSIS FUNCTIONS
# ============================================================================

"""
Perform comprehensive matrix diagnostics
"""
function analyze_matrix(A::SparseMatrixCSC{Float64, Int})
    
    m, n = size(A)
    if m != n
        error("Matrix analysis currently supports only square matrices")
    end
    
    println("🔍 Analyzing Matrix Properties")
    
    # Basic properties
    nnz_count = nnz(A)
    density = nnz_count / (m * n)
    
    # Symmetry check
    is_symmetric = issymmetric(A)
    
    # Diagonal properties
    diag_A = diag(A)
    has_zero_diag = any(abs.(diag_A) .< 1e-14)
    has_neg_diag = any(diag_A .< 0)
    
    # Diagonal dominance
    diag_dominance = calculate_diagonal_dominance(A)
    
    # Condition number estimation (expensive for large matrices)
    cond_est = estimate_condition_number(A)
    
    # Eigenvalue estimates
    lambda_max_est, lambda_min_est = estimate_extreme_eigenvalues(A)
    spectral_radius = lambda_max_est
    
    # Positive definiteness check (for symmetric matrices)
    is_pos_def = is_symmetric && lambda_min_est > 0 && !has_neg_diag
    
    # Quality assessment
    quality_warnings = String[]
    
    if cond_est > 1e12
        push!(quality_warnings, "Very high condition number (κ ≈ $(cond_est:.1e))")
    elseif cond_est > 1e8
        push!(quality_warnings, "High condition number (κ ≈ $(cond_est:.1e))")
    end
    
    if has_zero_diag
        push!(quality_warnings, "Zero diagonal elements detected")
    end
    
    if !is_symmetric && abs(lambda_min_est) < 1e-12
        push!(quality_warnings, "Near-singular matrix detected")
    end
    
    if diag_dominance < 0.1
        push!(quality_warnings, "Weak diagonal dominance")
    end
    
    # Overall quality level
    quality_level = if cond_est > 1e12 || has_zero_diag || abs(lambda_min_est) < 1e-12
        :singular
    elseif cond_est > 1e8 || diag_dominance < 0.1
        :poor
    elseif cond_est > 1e4 || diag_dominance < 0.5
        :good
    else
        :excellent
    end
    
    is_well_conditioned = cond_est < 1e8
    
    println("   Matrix size: $(m)×$(n)")
    @printf "   Non-zeros: %d (density: %.1f%%)\n" nnz_count (density*100)
    @printf "   Condition number: %.1e\n" cond_est
    println("   Quality: $quality_level")
    
    return MatrixDiagnostics(
        (m, n), nnz_count, density,
        cond_est, lambda_max_est, lambda_min_est, spectral_radius,
        is_symmetric, is_pos_def, diag_dominance,
        is_well_conditioned, has_zero_diag, has_neg_diag,
        quality_level, quality_warnings
    )
end

"""
Calculate diagonal dominance measure
"""
function calculate_diagonal_dominance(A::SparseMatrixCSC{Float64, Int})
    n = size(A, 1)
    dominance_ratios = zeros(n)
    
    for i in 1:n
        diag_val = abs(A[i, i])
        off_diag_sum = 0.0
        
        for j in A.colptr[i]:(A.colptr[i+1]-1)
            row = A.rowval[j]
            if row != i
                off_diag_sum += abs(A.nzval[j])
            end
        end
        
        if diag_val > 1e-14
            dominance_ratios[i] = diag_val / (diag_val + off_diag_sum)
        end
    end
    
    return mean(dominance_ratios)
end

"""
Estimate condition number using power iteration
"""
function estimate_condition_number(A::SparseMatrixCSC{Float64, Int}; max_iter::Int = 20)
    n = size(A, 1)
    
    # Estimate largest eigenvalue
    v = randn(n)
    v = v / norm(v)
    
    for _ in 1:max_iter
        v = A * v
        v = v / norm(v)
    end
    lambda_max = dot(v, A * v)
    
    # Estimate smallest eigenvalue (approximate)
    # For better estimates, would need more sophisticated methods
    diag_A = diag(A)
    lambda_min_approx = minimum(abs.(diag_A[abs.(diag_A) .> 1e-14]))
    
    return abs(lambda_max / lambda_min_approx)
end

"""
Estimate extreme eigenvalues using power iteration
"""
function estimate_extreme_eigenvalues(A::SparseMatrixCSC{Float64, Int}; max_iter::Int = 10)
    n = size(A, 1)
    
    # Power iteration for largest eigenvalue
    v = randn(n)
    v = v / norm(v)
    
    for _ in 1:max_iter
        v_new = A * v
        lambda_est = dot(v, v_new)
        v = v_new / norm(v_new)
    end
    lambda_max = dot(v, A * v)
    
    # Approximate smallest eigenvalue from diagonal
    diag_vals = diag(A)
    nonzero_diag = diag_vals[abs.(diag_vals) .> 1e-14]
    lambda_min = isempty(nonzero_diag) ? 0.0 : minimum(abs.(nonzero_diag))
    
    return lambda_max, lambda_min
end

# ============================================================================
# SOLUTION QUALITY ASSESSMENT
# ============================================================================

"""
Assess solution quality comprehensively
"""
function assess_solution_quality(
    x::Vector{Float64},
    A::SparseMatrixCSC{Float64, Int},
    b::Vector{Float64},
    x_analytical::Union{Vector{Float64}, Nothing} = nothing,
    physical_bounds::Union{Tuple{Float64, Float64}, Nothing} = nothing
)
    
    println("🔍 Assessing Solution Quality")
    
    # Basic solution properties
    max_val = maximum(x)
    min_val = minimum(x)
    
    # Residual analysis
    residual = b - A * x
    residual_norm = norm(residual)
    
    # Conservation checks (simplified - would need physics-specific implementation)
    mass_conservation = calculate_mass_conservation_error(x, A, b)
    
    # Boundedness check
    bounded = if physical_bounds !== nothing
        lb, ub = physical_bounds
        all(lb .<= x .<= ub)
    else
        true  # No bounds specified
    end
    
    # Smoothness measure (variation between adjacent points)
    smoothness = calculate_solution_smoothness(x)
    
    # Oscillation detection
    has_oscillations = detect_oscillations(x)
    has_overshoots = physical_bounds !== nothing && any(x .> physical_bounds[2])
    has_undershoots = physical_bounds !== nothing && any(x .< physical_bounds[1])
    
    # Accuracy metrics (if analytical solution provided)
    l1_error = nothing
    l2_error = nothing
    max_error = nothing
    
    if x_analytical !== nothing
        error_vec = x - x_analytical
        l1_error = norm(error_vec, 1) / length(x)
        l2_error = norm(error_vec, 2) / norm(x_analytical)
        max_error = maximum(abs.(error_vec))
    end
    
    # Quality score calculation
    quality_score = calculate_quality_score(
        residual_norm, bounded, has_oscillations, l2_error
    )
    
    # Quality level determination
    quality_level = if quality_score > 0.9 && residual_norm < 1e-10
        :excellent
    elseif quality_score > 0.7 && residual_norm < 1e-6
        :good
    elseif quality_score > 0.5 && residual_norm < 1e-3
        :acceptable
    else
        :poor
    end
    
    # Identify specific issues
    quality_issues = String[]
    if residual_norm > 1e-6
        push!(quality_issues, @sprintf("High residual norm: %.2e", residual_norm))
    end
    if has_oscillations
        push!(quality_issues, "Oscillatory behavior detected")
    end
    if !bounded
        push!(quality_issues, "Solution violates physical bounds")
    end
    if l2_error !== nothing && l2_error > 0.1
        push!(quality_issues, @sprintf("High error vs analytical solution: %.2e", l2_error))
    end
    
    @printf "   Solution range: [%.3e, %.3e]\n" min_val max_val
    @printf "   Residual norm: %.2e\n" residual_norm
    println("   Quality level: $quality_level")
    
    return SolutionQuality(
        mass_conservation, zeros(3), 0.0,  # Conservation errors
        bounded, max_val, min_val, smoothness,  # Boundedness and smoothness
        has_oscillations, has_overshoots, has_undershoots,  # Stability
        l1_error, l2_error, max_error,  # Accuracy
        quality_score, quality_level, quality_issues  # Overall assessment
    )
end

"""
Calculate mass conservation error (simplified)
"""
function calculate_mass_conservation_error(
    x::Vector{Float64},
    A::SparseMatrixCSC{Float64, Int},
    b::Vector{Float64}
)
    # Simplified mass conservation check
    # In practice, this would be physics-specific
    residual = b - A * x
    return norm(residual) / max(norm(b), 1e-15)
end

"""
Calculate solution smoothness measure
"""
function calculate_solution_smoothness(x::Vector{Float64})
    if length(x) < 2
        return 1.0
    end
    
    # Calculate variation between adjacent points
    variations = abs.(diff(x))
    max_variation = maximum(variations)
    avg_value = mean(abs.(x))
    
    # Smoothness score (higher is smoother)
    if avg_value > 1e-15
        smoothness = 1.0 / (1.0 + max_variation / avg_value)
    else
        smoothness = 1.0
    end
    
    return smoothness
end

"""
Detect oscillatory behavior in solution
"""
function detect_oscillations(x::Vector{Float64})
    if length(x) < 4
        return false
    end
    
    # Look for alternating signs in second differences
    second_diffs = diff(diff(x))
    if length(second_diffs) < 3
        return false
    end
    
    # Count sign changes
    sign_changes = sum(diff(sign.(second_diffs)) .!= 0)
    oscillation_ratio = sign_changes / length(second_diffs)
    
    return oscillation_ratio > 0.5  # More than half are sign changes
end

"""
Calculate composite quality score
"""
function calculate_quality_score(
    residual_norm::Float64,
    is_bounded::Bool,
    has_oscillations::Bool,
    l2_error::Union{Float64, Nothing}
)
    
    score = 1.0
    
    # Penalize high residual
    if residual_norm > 1e-12
        score *= exp(-log10(max(residual_norm, 1e-15)) / 5.0)
    end
    
    # Penalize bound violations
    if !is_bounded
        score *= 0.5
    end
    
    # Penalize oscillations
    if has_oscillations
        score *= 0.7
    end
    
    # Penalize high analytical error
    if l2_error !== nothing && l2_error > 1e-12
        score *= exp(-log10(max(l2_error, 1e-15)) / 3.0)
    end
    
    return clamp(score, 0.0, 1.0)
end

# ============================================================================
# MAIN DIAGNOSTIC FUNCTION
# ============================================================================

"""
Run comprehensive solver diagnostics
"""
function run_comprehensive_diagnostics(
    A::SparseMatrixCSC{Float64, Int},
    b::Vector{Float64},
    x::Vector{Float64},
    convergence_history::ConvergenceHistory,
    solver_name::String = "Unknown",
    problem_name::String = "Unknown";
    x_analytical::Union{Vector{Float64}, Nothing} = nothing,
    physical_bounds::Union{Tuple{Float64, Float64}, Nothing} = nothing
)
    
    println("🔬 Running Comprehensive Solver Diagnostics")
    println("=" ^ 60)
    println("Solver: $solver_name")
    println("Problem: $problem_name")
    
    # Matrix analysis
    matrix_diag = analyze_matrix(A)
    
    # Solution quality assessment
    solution_quality = assess_solution_quality(x, A, b, x_analytical, physical_bounds)
    
    # Performance metrics
    performance = PerformanceMetrics(
        0.0,  # Setup time (would need to be measured externally)
        convergence_history.final_time,
        convergence_history.final_iteration,
        0.0,  # Peak memory (would need external measurement)
        length(x),
        convergence_history.average_convergence_rate
    )
    
    # Overall assessment
    overall_success = convergence_history.converged && 
                     matrix_diag.quality_level != :singular &&
                     solution_quality.quality_level != :poor
    
    overall_quality = if overall_success && 
                         matrix_diag.quality_level == :excellent && 
                         solution_quality.quality_level == :excellent
        :excellent
    elseif overall_success && solution_quality.quality_level in [:good, :excellent]
        :good
    elseif convergence_history.converged
        :acceptable
    else
        :failure
    end
    
    # Critical issues
    critical_issues = String[]
    if !convergence_history.converged
        push!(critical_issues, "Solver failed to converge: $(convergence_history.convergence_reason)")
    end
    if matrix_diag.quality_level == :singular
        push!(critical_issues, "Matrix is singular or near-singular")
    end
    if solution_quality.quality_level == :poor
        push!(critical_issues, "Solution quality is poor")
    end
    
    # Recommendations
    recommendations = generate_recommendations(matrix_diag, convergence_history, solution_quality)
    
    # Create comprehensive diagnostics suite
    suite = SolverDiagnosticsSuite(
        convergence_history, matrix_diag, solution_quality, performance,
        solver_name, problem_name, string(now()), string(VERSION),
        overall_success, overall_quality, critical_issues, recommendations
    )
    
    # Print summary
    print_diagnostic_summary(suite)
    
    return suite
end

"""
Generate recommendations based on diagnostics
"""
function generate_recommendations(
    matrix_diag::MatrixDiagnostics,
    conv_history::ConvergenceHistory,
    sol_quality::SolutionQuality
)
    
    recommendations = String[]
    
    # Matrix-based recommendations
    if matrix_diag.condition_number_estimate > 1e8
        push!(recommendations, "Consider preconditioning to improve matrix conditioning")
    end
    
    if !matrix_diag.is_well_conditioned && matrix_diag.diagonal_dominance < 0.5
        push!(recommendations, "Matrix is poorly conditioned; consider regularization")
    end
    
    # Convergence-based recommendations
    if conv_history.stagnation_count > 5
        push!(recommendations, "Slow convergence detected; try different solver or preconditioning")
    end
    
    if conv_history.oscillation_count > 3
        push!(recommendations, "Oscillatory convergence; consider damping or different method")
    end
    
    if !conv_history.converged && conv_history.convergence_reason == "max_iterations_reached"
        push!(recommendations, "Increase maximum iterations or improve preconditioning")
    end
    
    # Solution quality recommendations
    if sol_quality.has_oscillations
        push!(recommendations, "Solution oscillations detected; check mesh quality or add stabilization")
    end
    
    if sol_quality.quality_level == :poor
        push!(recommendations, "Poor solution quality; verify problem setup and boundary conditions")
    end
    
    return recommendations
end

"""
Print comprehensive diagnostic summary
"""
function print_diagnostic_summary(suite::SolverDiagnosticsSuite)
    println("\\n📊 DIAGNOSTIC SUMMARY")
    println("=" ^ 40)
    
    # Overall status
    status_icon = suite.overall_success ? "✅" : "❌"
    quality_icon = if suite.overall_quality == :excellent
        "🟢"
    elseif suite.overall_quality == :good
        "🟡"
    elseif suite.overall_quality == :acceptable
        "🟠"
    else
        "🔴"
    end
    
    println("$status_icon Overall Success: $(suite.overall_success)")
    println("$quality_icon Overall Quality: $(suite.overall_quality)")
    
    # Convergence summary
    conv = suite.convergence
    @printf "🔄 Convergence: %s in %d iterations (%.3f s)\\n" conv.converged conv.final_iteration conv.final_time
    @printf "   Final residual: %.2e (rate: %.3f)\\n" (length(conv.absolute_residuals) > 0 ? conv.absolute_residuals[end] : NaN) conv.average_convergence_rate
    
    # Matrix summary
    mat = suite.matrix
    @printf "🔢 Matrix: %dx%d, κ ≈ %.1e, quality: %s\\n" mat.size[1] mat.size[2] mat.condition_number_estimate mat.quality_level
    
    # Solution summary
    sol = suite.solution
    @printf "🎯 Solution: quality %s, range [%.2e, %.2e]\\n" sol.quality_level sol.min_solution_value sol.max_solution_value
    
    # Critical issues
    if !isempty(suite.critical_issues)
        println("\\n⚠️  Critical Issues:")
        for issue in suite.critical_issues
            println("   • $issue")
        end
    end
    
    # Recommendations
    if !isempty(suite.recommendations)
        println("\\n💡 Recommendations:")
        for rec in suite.recommendations
            println("   • $rec")
        end
    end
    
    println("\\n" * "=" ^ 40)
end

# ============================================================================
# VALIDATION AND TESTING
# ============================================================================

"""
Test comprehensive diagnostics on a known problem
"""
function validate_solver_diagnostics()
    println("🔬 Validating Solver Diagnostics")
    println("=" ^ 50)
    
    # Create test problem: 2D Poisson equation
    n = 20
    h = 1.0 / (n + 1)
    
    # Build 2D Laplacian matrix
    N = n * n
    I = Int[]
    J = Int[]
    V = Float64[]
    
    for i in 1:n, j in 1:n
        idx = (j-1)*n + i
        
        # Diagonal
        push!(I, idx); push!(J, idx); push!(V, 4.0/h^2)
        
        # Off-diagonals
        if i > 1
            push!(I, idx); push!(J, idx-1); push!(V, -1.0/h^2)
        end
        if i < n
            push!(I, idx); push!(J, idx+1); push!(V, -1.0/h^2)
        end
        if j > 1
            push!(I, idx); push!(J, idx-n); push!(V, -1.0/h^2)
        end
        if j < n
            push!(I, idx); push!(J, idx+n); push!(V, -1.0/h^2)
        end
    end
    
    A = sparse(I, J, V, N, N)
    
    # Right-hand side: f = 1
    b = ones(N)
    
    # Analytical solution (approximate)
    x_analytical = zeros(N)
    for i in 1:n, j in 1:n
        idx = (j-1)*n + i
        x_val = i * h
        y_val = j * h
        x_analytical[idx] = x_val * (1 - x_val) * y_val * (1 - y_val) / 4
    end
    
    println("Test problem: 2D Poisson equation, $(n)×$(n) grid")
    
    # Solve using simple iteration for demonstration
    x = zeros(N)
    history = initialize_convergence_monitoring(A, b, x)
    
    tolerance = 1e-8
    max_iter = 1000
    
    for iter in 1:max_iter
        x_old = copy(x)
        
        # Simple Jacobi iteration
        D_inv = spdiagm(1.0 ./ diag(A))
        L_U = A - spdiagm(diag(A))
        x = D_inv * (b - L_U * x_old)
        
        # Update monitoring
        iter_time = 0.001  # Simulated iteration time
        update_convergence_monitoring!(history, A, b, x, x_old, iter, iter_time)
        
        # Check convergence
        residual = norm(b - A * x)
        if residual < tolerance
            break
        end
    end
    
    # Finalize convergence analysis
    finalize_convergence_analysis!(history, tolerance, max_iter)
    
    # Run comprehensive diagnostics
    diagnostics = run_comprehensive_diagnostics(
        A, b, x, history, "Jacobi", "2D_Poisson_Test",
        x_analytical = x_analytical,
        physical_bounds = (0.0, 1.0)
    )
    
    # Validation checks
    println("\\n📊 Validation Results:")
    
    checks_passed = 0
    total_checks = 6
    
    # Check 1: Convergence
    if diagnostics.convergence.converged
        println("   ✅ Convergence: PASS")
        checks_passed += 1
    else
        println("   ❌ Convergence: FAIL")
    end
    
    # Check 2: Matrix conditioning
    if diagnostics.matrix.quality_level in [:excellent, :good]
        println("   ✅ Matrix quality: PASS")
        checks_passed += 1
    else
        println("   ❌ Matrix quality: FAIL")
    end
    
    # Check 3: Solution boundedness
    if diagnostics.solution.solution_boundedness
        println("   ✅ Solution bounds: PASS")
        checks_passed += 1
    else
        println("   ❌ Solution bounds: FAIL")
    end
    
    # Check 4: Accuracy
    if diagnostics.solution.l2_error !== nothing && diagnostics.solution.l2_error < 0.1
        println("   ✅ Solution accuracy: PASS")
        checks_passed += 1
    else
        println("   ❌ Solution accuracy: FAIL")
    end
    
    # Check 5: No oscillations
    if !diagnostics.solution.has_oscillations
        println("   ✅ Solution stability: PASS")
        checks_passed += 1
    else
        println("   ❌ Solution stability: FAIL")
    end
    
    # Check 6: Overall quality
    if diagnostics.overall_quality in [:excellent, :good, :acceptable]
        println("   ✅ Overall quality: PASS")
        checks_passed += 1
    else
        println("   ❌ Overall quality: FAIL")
    end
    
    success_rate = checks_passed / total_checks
    @printf "\\n📈 Validation Summary: %d/%d checks passed (%.1f%%)\n" checks_passed total_checks (success_rate*100)
    
    if success_rate >= 0.8
        println("   ✅ Solver diagnostics validation SUCCESSFUL")
    else
        println("   ⚠️ Solver diagnostics validation needs improvement")
    end
    
    return diagnostics, success_rate
end

# ============================================================================
# EXPORTS
# ============================================================================

export ConvergenceHistory, MatrixDiagnostics, SolutionQuality, PerformanceMetrics
export SolverDiagnosticsSuite
export initialize_convergence_monitoring, update_convergence_monitoring!
export finalize_convergence_analysis!, analyze_matrix, assess_solution_quality
export run_comprehensive_diagnostics, validate_solver_diagnostics

end # module SolverDiagnostics