# =========================================================================
# Core Types Module - Foundation types for JuliaFOAM
# =========================================================================

# External dependencies
using StaticArrays      # For small fixed-size vectors and matrices
using LinearAlgebra     # For vector operations

# Core mesh structures
"""
    Face

Represents a face in the finite volume mesh.

# Fields
- `owner::Int32`: Index of the cell that owns this face
- `neighbour::Int32`: Index of the neighboring cell (or -1 for boundary faces)
- `area::SVector{3,Float64}`: Face area vector (magnitude equals area, direction is face normal)
- `center::SVector{3,Float64}`: Face center coordinates
"""
struct Face
    owner::Int32
    neighbour::Int32
    area::SVector{3,Float64}  # Using StaticArrays for small fixed-size vectors
    center::SVector{3,Float64}
end

"""
    Cell

Represents a cell in the finite volume mesh.

# Fields
- `faces::Vector{Int32}`: Indices of faces that make up this cell
- `center::SVector{3,Float64}`: Cell center coordinates
- `volume::Float64`: Cell volume
"""
struct Cell
    faces::Vector{Int32}
    center::SVector{3,Float64}
    volume::Float64
end

"""
    BoundaryCondition

Abstract type for all boundary conditions.
"""
abstract type BoundaryCondition end

"""
    FixedValueBC <: BoundaryCondition

Fixed value (Dirichlet) boundary condition.

# Fields
- `value`: The fixed value at the boundary
"""
struct FixedValueBC{T} <: BoundaryCondition
    value::T
end

"""
    FixedGradientBC <: BoundaryCondition

Fixed gradient (Neumann) boundary condition.

# Fields
- `gradient`: The fixed gradient at the boundary
"""
struct FixedGradientBC{T} <: BoundaryCondition
    gradient::T
end

"""
    ZeroGradientBC <: BoundaryCondition

Zero gradient boundary condition (special case of FixedGradientBC).
"""
struct ZeroGradientBC <: BoundaryCondition end

"""
    CyclicBC <: BoundaryCondition

Cyclic (periodic) boundary condition.

# Fields
- `matching_patch::String`: Name of the matching boundary patch
"""
struct CyclicBC <: BoundaryCondition
    matching_patch::String
end

"""
    FluidProperties

Physical properties of the fluid or system.
"""
struct FluidProperties
    nu::Float64      # Kinematic viscosity
    rho::Float64     # Density
    # Add other properties as needed (e.g., thermal conductivity, specific heat)
end

# Default constructor for FluidProperties
function FluidProperties(;
    nu::Float64 = 1e-5,
    rho::Float64 = 1.0
)
    return FluidProperties(nu, rho)
end

"""
    SolverSettings

Solver settings for the simulation.
"""
struct SolverSettings
    solver_type::Symbol
    tolerance::Float64
    max_iterations::Int
    preconditioner::Symbol
    # Other solver-specific settings
end

# Default constructor for SolverSettings
function SolverSettings(;
    solver_type::Symbol = :GMRES,
    tolerance::Float64 = 1e-6,
    max_iterations::Int = 100,
    preconditioner::Symbol = :ILU0
)
    return SolverSettings(solver_type, tolerance, max_iterations, preconditioner)
end

"""
    CaseConfiguration

Stores case-specific configuration parameters.
"""
struct CaseConfiguration
    case_path::String
    start_time::Float64
    end_time::Float64
    delta_t::Float64
    write_interval::Float64
    # Add other configuration parameters as needed
end

# Default constructor for CaseConfiguration
function CaseConfiguration(;
    case_path::String = ".",
    start_time::Float64 = 0.0,
    end_time::Float64 = 1.0,
    delta_t::Float64 = 0.001,
    write_interval::Float64 = 0.1
)
    return CaseConfiguration(case_path, start_time, end_time, delta_t, write_interval)
end

"""
    Mesh

Main mesh container.

# Fields
- `cells::Vector{Cell}`: All cells in the mesh
- `faces::Vector{Face}`: All faces in the mesh
- `boundary_faces::Vector{Int32}`: Indices of boundary faces
- `boundary_patches::Dict{String,Vector{Int32}}`: Mapping from patch names to face indices
- `boundary_conditions::Dict{String,BoundaryCondition}`: Boundary conditions for each patch
- `cell_partition::Vector{Int32}`: Which process owns each cell (for parallel)
- `halo_cells::Vector{Int32}`: Ghost cells from other processes (for parallel)
"""
struct Mesh
    cells::Vector{Cell}
    faces::Vector{Face}
    boundary_faces::Vector{Int32}
    boundary_patches::Dict{String,Vector{Int32}}
    boundary_conditions::Dict{String,BoundaryCondition}
    
    # Parallel information
    cell_partition::Vector{Int32}  # Which process owns each cell
    halo_cells::Vector{Int32}      # Ghost cells from other processes
    
    # Constructor for serial mesh
    function Mesh(cells, faces, boundary_faces, boundary_patches, boundary_conditions)
        n_cells = length(cells)
        return new(
            cells, 
            faces, 
            boundary_faces, 
            boundary_patches,
            boundary_conditions,
            fill(Int32(0), n_cells),  # Default: all cells on process 0
            Int32[]                   # No halo cells in serial
        )
    end
    
    # Constructor for parallel mesh
    function Mesh(cells, faces, boundary_faces, boundary_patches, boundary_conditions, 
                 cell_partition, halo_cells)
        return new(
            cells, 
            faces, 
            boundary_faces, 
            boundary_patches,
            boundary_conditions,
            cell_partition,
            halo_cells
        )
    end
end

"""
    Field{T}

Represents a field variable (e.g., velocity, pressure) on the mesh.

# Type Parameters
- `T`: The data type of the field values. Can be scalar (Float64), vector (SVector{3,Float64}), or tensor.

# Fields
- `name::String`: Name of the field (e.g., "U" for velocity, "p" for pressure)
- `mesh::Mesh`: Reference to the mesh on which the field is defined
- `values::Vector{T}`: The field values at each location (cells, faces, or nodes)
- `location::Symbol`: Where the field values are stored (e.g., :cellCenters, :faceCenters)
- `boundary_values::Dict{String,Vector{T}}`: Values at the boundary patches
- `old_time_field::Union{Vector{T}, Nothing}`: Values from previous time step (for time-dependent simulations)

# OpenFOAM Compatibility
This struct is designed to be compatible with OpenFOAM's field representation for interoperability.
"""
mutable struct Field{T}
    name::String
    mesh::Mesh
    values::Vector{T} # Can be scalar, vector, or tensor depending on T
    location::Symbol # :cellCenters, :faceCenters, :nodes etc.
    boundary_values::Dict{String,Vector{T}} # Boundary values for each boundary patch
    old_time_field::Union{Vector{T}, Nothing} # Values from previous time step (for time-dependent simulations)

    # Unified constructor for Field - handles both scalar and vector inputs
    function Field{T}(name::String, mesh::Mesh, values_or_initial_value, location::Symbol = :cellCenters, boundary_values = nothing) where {T}
        num_elements = 0
        if location == :cellCenters
            num_elements = length(mesh.cells)
        elseif location == :faceCenters
            num_elements = length(mesh.faces)
        # Add other locations like :nodes if necessary
        else
            error("Unknown field location: $location")
        end
        
        # Handle different input types
        values = if isa(values_or_initial_value, Vector)
            # Pre-existing vector of values
            if length(values_or_initial_value) != num_elements
                error("Number of values ($(length(values_or_initial_value))) does not match number of elements ($num_elements) for location $location")
            end
            values_or_initial_value
        else
            # Scalar initial value - create a filled vector
            fill(values_or_initial_value, num_elements)
        end
        
        # Initialize boundary values if not provided
        if boundary_values === nothing
            boundary_values = Dict{String,Vector{T}}()
            # Initialize boundary values for each patch with the initial value
            for (patch_name, patch) in mesh.boundary_patches
                boundary_values[patch_name] = fill(isa(values_or_initial_value, Vector) ? values_or_initial_value[1] : values_or_initial_value, length(patch))
            end
        end
        
        # Create and return the Field object with old_time_field initialized to nothing
        return new{T}(name, mesh, values, location, boundary_values, nothing)
    end
end

# Convenience constructor for scalar value initialization
function Field(name::String, mesh::Mesh, initial_value::T, location::Symbol = :cellCenters, boundary_values = nothing) where {T}
    return Field{T}(name, mesh, initial_value, location, boundary_values)
end

# Legacy constructor for compatibility with existing code
function Field{T}(name::String, values::Vector{T}, boundary_values::Dict{String,Vector{T}}) where {T}
    # This constructor is used in test files; create a minimal dummy mesh matching values
    dummy_cells = [Cell(Int32[], SVector{3,Float64}(0.0,0.0,0.0), 1.0) for _ in 1:length(values)]
    dummy_faces = Face[]
    dummy_mesh = Mesh(dummy_cells, dummy_faces, Int32[], Dict{String,Vector{Int32}}(), Dict{String,BoundaryCondition}())
    # Delegate to main Field constructor
    return Field{T}(name, dummy_mesh, values, :cellCenters, boundary_values)
end

# Convenience constructor for vector initialization
function Field(name::String, mesh::Mesh, values::Vector{T}, location::Symbol = :cellCenters) where {T}
    return Field{T}(name, mesh, values, location)
end

# Property accessors for backward compatibility
function Base.getproperty(field::Field, name::Symbol)
    if name === :internal_field
        return getfield(field, :values)
    else
        return getfield(field, name)
    end
end

function Base.setproperty!(field::Field, name::Symbol, value)
    if name === :internal_field
        setfield!(field, :values, value)
    else
        setfield!(field, name, value)
    end
end
