"""
    EnhancedMemoryPool.jl

This module provides advanced memory pooling functionality to reduce allocations
and improve integration with Julia's garbage collector.
"""

using Base.Threads

"""
    EnhancedMemoryPool{T}

An enhanced memory pool for objects of type T with thread-safety and GC integration.

# Fields
- `chunks::Vector{Vector{T}}`: Storage for pooled objects
- `free_indices::Vector{Tuple{Int, Int}}`: Indices of free objects (chunk_idx, element_idx)
- `chunk_size::Int`: Size of each chunk
- `lock::ReentrantLock`: Lock for thread safety
- `gc_protected::Bool`: Whether objects in this pool are protected from GC
"""
struct EnhancedMemoryPool{T}
    chunks::Vector{Vector{T}}
    free_indices::Vector{Tuple{Int, Int}}  # (chunk_idx, element_idx)
    chunk_size::Int
    lock::ReentrantLock
    gc_protected::Bool
    
    """
        EnhancedMemoryPool{T}(chunk_size::Int = 1024; gc_protected::Bool = false) where T

    Create a new enhanced memory pool for objects of type T with the given chunk size.
    
    # Arguments
    - `chunk_size::Int`: Size of each chunk (default: 1024)
    - `gc_protected::Bool`: Whether objects in this pool are protected from GC (default: false)
    """
    function EnhancedMemoryPool{T}(chunk_size::Int = 1024; gc_protected::Bool = false) where T
        chunks = Vector{Vector{T}}()
        free_indices = Vector{Tuple{Int, Int}}()
        lock = ReentrantLock()
        return new{T}(chunks, free_indices, chunk_size, lock, gc_protected)
    end
end

"""
    allocate(pool::EnhancedMemoryPool{T}) where T

Allocate an object from the memory pool. If the pool is empty, a new chunk is created.
Thread-safe implementation.
"""
function allocate(pool::EnhancedMemoryPool{T}) where T
    lock(pool.lock) do
        if isempty(pool.free_indices)
            # Create new chunk
            chunk_idx = length(pool.chunks) + 1
            
            # Adaptive chunk sizing: increase chunk size as more chunks are created
            # This reduces fragmentation for large pools
            actual_chunk_size = if chunk_idx > 10
                pool.chunk_size * 4
            elseif chunk_idx > 5
                pool.chunk_size * 2
            else
                pool.chunk_size
            end
            
            # Pre-allocate the chunk
            chunk = Vector{T}(undef, actual_chunk_size)
            push!(pool.chunks, chunk)
            
            # Add all elements to free list except the first one
            for i in 2:actual_chunk_size
                push!(pool.free_indices, (chunk_idx, i))
            end
            
            # Return the first element
            return (chunk_idx, 1)
        else
            # Reuse from free list
            return pop!(pool.free_indices)
        end
    end
end

"""
    deallocate!(pool::EnhancedMemoryPool{T}, index::Tuple{Int, Int}) where T

Return an object to the memory pool. Thread-safe implementation.
"""
function deallocate!(pool::EnhancedMemoryPool{T}, index::Tuple{Int, Int}) where T
    lock(pool.lock) do
        push!(pool.free_indices, index)
    end
end

"""
    get_object(pool::EnhancedMemoryPool{T}, index::Tuple{Int, Int}) where T

Get the object at the given index in the memory pool.
"""
function get_object(pool::EnhancedMemoryPool{T}, index::Tuple{Int, Int}) where T
    chunk_idx, element_idx = index
    return pool.chunks[chunk_idx][element_idx]
end

"""
    set_object!(pool::EnhancedMemoryPool{T}, index::Tuple{Int, Int}, value::T) where T

Set the object at the given index in the memory pool.
"""
function set_object!(pool::EnhancedMemoryPool{T}, index::Tuple{Int, Int}, value::T) where T
    chunk_idx, element_idx = index
    pool.chunks[chunk_idx][element_idx] = value
end

"""
    clear_object!(pool::EnhancedMemoryPool{T}, index::Tuple{Int, Int}) where T

Clear the object at the given index in the memory pool.
"""
function clear_object!(pool::EnhancedMemoryPool{T}, index::Tuple{Int, Int}) where T
    chunk_idx, element_idx = index
    pool.chunks[chunk_idx][element_idx] = zero(T)
end

"""
    EnhancedVectorPool{T}

A specialized memory pool for vectors with thread-safety and size-based pooling.

# Fields
- `pools::Dict{Int, EnhancedMemoryPool{Vector{T}}}`: Size-based pools
- `lock::ReentrantLock`: Lock for thread safety
"""
struct EnhancedVectorPool{T}
    pools::Dict{Int, EnhancedMemoryPool{Vector{T}}}
    lock::ReentrantLock
    
    """
        EnhancedVectorPool{T}(chunk_size::Int = 1024) where T

    Create a new enhanced vector pool for vectors of type T with the given chunk size.
    """
    function EnhancedVectorPool{T}(chunk_size::Int = 1024) where T
        pools = Dict{Int, EnhancedMemoryPool{Vector{T}}}()
        lock = ReentrantLock()
        return new{T}(pools, lock)
    end
end

"""
    get_size_bucket(size::Int)

Get the size bucket for a vector of the given size.
"""
function get_size_bucket(size::Int)
    if size <= 64
        return 64
    elseif size <= 256
        return 256
    elseif size <= 1024
        return 1024
    elseif size <= 4096
        return 4096
    elseif size <= 16384
        return 16384
    else
        return nextpow(2, size)
    end
end

"""
    allocate_vector(pool::EnhancedVectorPool{T}, size::Int) where T

Allocate a vector from the vector pool with the given size.
Uses size-based pooling for better memory efficiency.
"""
function allocate_vector(pool::EnhancedVectorPool{T}, size::Int) where T
    # Optimize allocation strategy based on vector size
    if size > 100_000
        # For very large vectors, bypass the pool to avoid memory fragmentation
        # Just allocate directly and track for deallocation
        vector = zeros(T, size)
        # Use a special index to indicate direct allocation
        return ((-1, -1), vector)
    end
    
    # Use a size-based pooling strategy for better performance with different sizes
    target_size = get_size_bucket(size)
    
    # Get or create the appropriate pool for this size
    lock(pool.lock) do
        if !haskey(pool.pools, target_size)
            # Create a new pool for this size
            pool.pools[target_size] = EnhancedMemoryPool{Vector{T}}()
        end
    end
    
    size_pool = pool.pools[target_size]
    index = allocate(size_pool)
    vector = get_object(size_pool, index)
    
    # Initialize vector if needed
    if vector === nothing || length(vector) != target_size
        vector = zeros(T, target_size)
        set_object!(size_pool, index, vector)
    else
        # Clear existing vector
        fill!(vector, zero(T))
    end
    
    return (index, vector, target_size)
end

"""
    deallocate_vector!(pool::EnhancedVectorPool{T}, allocation_data) where T

Return a vector to the vector pool.
"""
function deallocate_vector!(pool::EnhancedVectorPool{T}, allocation_data) where T
    index, _, target_size = allocation_data
    
    # Check if this was a direct allocation (special index)
    if index == (-1, -1)
        # Nothing to do for direct allocations - they'll be garbage collected
        return
    end
    
    # Return to the appropriate size pool
    size_pool = pool.pools[target_size]
    deallocate!(size_pool, index)
end

"""
    EnhancedMatrixPool{T}

A specialized memory pool for matrices with thread-safety and dimension-based pooling.

# Fields
- `pools::Dict{Tuple{Int,Int}, EnhancedMemoryPool{Matrix{T}}}`: Dimension-based pools
- `lock::ReentrantLock`: Lock for thread safety
"""
struct EnhancedMatrixPool{T}
    pools::Dict{Tuple{Int,Int}, EnhancedMemoryPool{Matrix{T}}}
    lock::ReentrantLock
    
    """
        EnhancedMatrixPool{T}(chunk_size::Int = 1024) where T

    Create a new enhanced matrix pool for matrices of type T with the given chunk size.
    """
    function EnhancedMatrixPool{T}(chunk_size::Int = 1024) where T
        pools = Dict{Tuple{Int,Int}, EnhancedMemoryPool{Matrix{T}}}()
        lock = ReentrantLock()
        return new{T}(pools, lock)
    end
end

"""
    get_dimension_bucket(rows::Int, cols::Int)

Get the dimension bucket for a matrix of the given dimensions.
"""
function get_dimension_bucket(rows::Int, cols::Int)
    row_bucket = get_size_bucket(rows)
    col_bucket = get_size_bucket(cols)
    return (row_bucket, col_bucket)
end

"""
    allocate_matrix(pool::EnhancedMatrixPool{T}, rows::Int, cols::Int) where T

Allocate a matrix from the matrix pool with the given dimensions.
Uses dimension-based pooling for better memory efficiency.
"""
function allocate_matrix(pool::EnhancedMatrixPool{T}, rows::Int, cols::Int) where T
    # Optimize allocation strategy based on matrix size
    if rows * cols > 1_000_000
        # For very large matrices, bypass the pool to avoid memory fragmentation
        matrix = zeros(T, rows, cols)
        # Use a special index to indicate direct allocation
        return ((-1, -1), matrix)
    end
    
    # Use a dimension-based pooling strategy
    target_dims = get_dimension_bucket(rows, cols)
    
    # Get or create the appropriate pool for these dimensions
    lock(pool.lock) do
        if !haskey(pool.pools, target_dims)
            # Create a new pool for these dimensions
            pool.pools[target_dims] = EnhancedMemoryPool{Matrix{T}}()
        end
    end
    
    dim_pool = pool.pools[target_dims]
    index = allocate(dim_pool)
    matrix = get_object(dim_pool, index)
    
    # Initialize matrix if needed
    if matrix === nothing || size(matrix) != target_dims
        matrix = zeros(T, target_dims[1], target_dims[2])
        set_object!(dim_pool, index, matrix)
    else
        # Clear existing matrix
        fill!(matrix, zero(T))
    end
    
    return (index, matrix, target_dims)
end

"""
    deallocate_matrix!(pool::EnhancedMatrixPool{T}, allocation_data) where T

Return a matrix to the matrix pool.
"""
function deallocate_matrix!(pool::EnhancedMatrixPool{T}, allocation_data) where T
    index, _, target_dims = allocation_data
    
    # Check if this was a direct allocation (special index)
    if index == (-1, -1)
        # Nothing to do for direct allocations - they'll be garbage collected
        return
    end
    
    # Return to the appropriate dimension pool
    dim_pool = pool.pools[target_dims]
    deallocate!(dim_pool, index)
end

"""
    pool_gc_callback(pool::EnhancedMemoryPool)

Callback function for Julia's GC to clean up unused pools.
"""
function pool_gc_callback(pool::EnhancedMemoryPool)
    # Only perform cleanup if the pool is not GC-protected
    if !pool.gc_protected
        # Check if all objects are in the free list
        if length(pool.free_indices) == sum(length(chunk) for chunk in pool.chunks)
            # All objects are free, so we can clear the chunks
            empty!(pool.chunks)
            empty!(pool.free_indices)
        end
    end
end

# Export functions and types
export EnhancedMemoryPool, allocate, deallocate!, get_object, set_object!, clear_object!
export EnhancedVectorPool, allocate_vector, deallocate_vector!
export EnhancedMatrixPool, allocate_matrix, deallocate_matrix!
export pool_gc_callback
