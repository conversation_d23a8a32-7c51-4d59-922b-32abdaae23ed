module PostProcessing

using StaticArrays
using LinearAlgebra
const HAVE_WRITEVTK = try
    using WriteVTK
    true
catch
    @warn "WriteVTK package not available. Output to VTK files will be disabled."
    false
end
using ..JuliaFOAM

export calculate_vorticity, calculate_q_criterion, calculate_wall_shear_stress
export calculate_pressure_coefficient, calculate_courant_number
export extract_line_data, extract_plane_data, calculate_forces
export write_vtk_visualization, create_residual_plot, find_cell_containing_point, find_closest_cell

"""
    calculate_vorticity(U::Field{SVector{3,Float64}}, mesh::Mesh)

Calculate vorticity field (curl of velocity).

# Arguments
- `U`: Velocity field
- `mesh`: The mesh

# Returns
- `Field{SVector{3,Float64}}`: Vorticity field
"""
function calculate_vorticity(U::Field{SVector{3,Float64}}, mesh::Mesh)
    n_cells = length(mesh.cells)
    vorticity = Field{SVector{3,Float64}}(mesh, SVector{3,Float64}(0.0, 0.0, 0.0))
    
    # Calculate velocity gradient
    grad_U = zeros(SVector{3,Float64}, 3, n_cells)
    
    for i in 1:3
        # Extract component
        U_comp = [U.internal_field[j][i] for j in 1:n_cells]
        
        # Calculate gradient
        grad_comp = zeros(SVector{3,Float64}, n_cells)
        grad_gauss_linear!(grad_comp, U_comp, mesh)
        
        # Store in gradient tensor
        for j in 1:n_cells
            grad_U[i, j] = grad_comp[j]
        end
    end
    
    # Calculate vorticity: ω = ∇ × U
    for i in 1:n_cells
        vorticity.internal_field[i] = SVector{3,Float64}(
            grad_U[3, i][2] - grad_U[2, i][3],  # ∂w/∂y - ∂v/∂z
            grad_U[1, i][3] - grad_U[3, i][1],  # ∂u/∂z - ∂w/∂x
            grad_U[2, i][1] - grad_U[1, i][2]   # ∂v/∂x - ∂u/∂y
        )
    end
    
    return vorticity
end

"""
    calculate_q_criterion(U::Field{SVector{3,Float64}}, mesh::Mesh)

Calculate Q-criterion for vortex identification.

# Arguments
- `U`: Velocity field
- `mesh`: The mesh

# Returns
- `Field{Float64}`: Q-criterion field
"""
function calculate_q_criterion(U::Field{SVector{3,Float64}}, mesh::Mesh)
    n_cells = length(mesh.cells)
    Q = Field{Float64}(mesh, 0.0)
    
    # Calculate velocity gradient
    grad_U = zeros(SVector{3,Float64}, 3, n_cells)
    
    for i in 1:3
        # Extract component
        U_comp = [U.internal_field[j][i] for j in 1:n_cells]
        
        # Calculate gradient
        grad_comp = zeros(SVector{3,Float64}, n_cells)
        grad_gauss_linear!(grad_comp, U_comp, mesh)
        
        # Store in gradient tensor
        for j in 1:n_cells
            grad_U[i, j] = grad_comp[j]
        end
    end
    
    # Calculate Q-criterion: Q = 0.5(|Ω|² - |S|²)
    # Where Ω is the vorticity tensor and S is the strain rate tensor
    for i in 1:n_cells
        # Construct velocity gradient tensor
        grad_tensor = [
            grad_U[1, i][1] grad_U[1, i][2] grad_U[1, i][3];
            grad_U[2, i][1] grad_U[2, i][2] grad_U[2, i][3];
            grad_U[3, i][1] grad_U[3, i][2] grad_U[3, i][3]
        ]
        
        # Decompose into symmetric and antisymmetric parts
        S = 0.5 * (grad_tensor + grad_tensor')  # Strain rate tensor
        Ω = 0.5 * (grad_tensor - grad_tensor')  # Vorticity tensor
        
        # Calculate Q-criterion
        Q.internal_field[i] = 0.5 * (norm(Ω)^2 - norm(S)^2)
    end
    
    return Q
end

"""
    calculate_wall_shear_stress(U::Field{SVector{3,Float64}}, mesh::Mesh, 
                               properties::FluidProperties, patch_names::Vector{String})

Calculate wall shear stress on specified boundary patches.

# Arguments
- `U`: Velocity field
- `mesh`: The mesh
- `properties`: Fluid properties
- `patch_names`: Names of boundary patches to calculate wall shear stress on

# Returns
- `Dict{String,Vector{SVector{3,Float64}}}`: Wall shear stress on each patch
"""
function calculate_wall_shear_stress(
    U::Field{SVector{3,Float64}}, 
    mesh::Mesh, 
    properties::FluidProperties, 
    patch_names::Vector{String}
)
    wall_shear_stress = Dict{String,Vector{SVector{3,Float64}}}()
    
    for patch_name in patch_names
        if !haskey(mesh.boundary_patches, patch_name)
            @warn "Boundary patch $patch_name not found"
            continue
        end
        
        face_indices = mesh.boundary_patches[patch_name]
        n_faces = length(face_indices)
        
        # Initialize wall shear stress for this patch
        wall_shear_stress[patch_name] = Vector{SVector{3,Float64}}(undef, n_faces)
        
        for (i, face_idx) in enumerate(face_indices)
            face = mesh.faces[face_idx]
            owner_cell = face.owner
            
            # Get face normal
            face_normal = normalize(face.area)
            
            # Get cell center velocity
            U_cell = U.internal_field[owner_cell]
            
            # Calculate distance to wall
            cell_center = mesh.cells[owner_cell].center
            face_center = face.center
            distance = norm(face_center - cell_center)
            
            # Calculate velocity gradient at wall
            # This is a simplified approach - in reality we would use wall functions
            # for high Reynolds number flows
            dUdn = U_cell / distance  # Velocity gradient normal to wall
            
            # Calculate tangential velocity component
            U_tangential = U_cell - dot(U_cell, face_normal) * face_normal
            
            # Calculate wall shear stress: τ_w = μ * dU_t/dn
            # For incompressible flow: μ = ρ * ν
            mu = properties.density * properties.kinematic_viscosity
            tau_w = mu * norm(U_tangential) / distance * normalize(U_tangential)
            
            wall_shear_stress[patch_name][i] = tau_w
        end
    end
    
    return wall_shear_stress
end

"""
    calculate_pressure_coefficient(p::Field{Float64}, U_ref::Float64, 
                                  rho::Float64, p_ref::Float64, mesh::Mesh)

Calculate pressure coefficient: Cp = (p - p_ref) / (0.5 * rho * U_ref²).

# Arguments
- `p`: Pressure field
- `U_ref`: Reference velocity
- `rho`: Fluid density
- `p_ref`: Reference pressure
- `mesh`: The mesh

# Returns
- `Field{Float64}`: Pressure coefficient field
"""
function calculate_pressure_coefficient(
    p::Field{Float64}, 
    U_ref::Float64, 
    rho::Float64, 
    p_ref::Float64,
    mesh::Mesh
)
    n_cells = length(p.internal_field)
    Cp = Field{Float64}(mesh, 0.0)
    
    # Calculate dynamic pressure
    q = 0.5 * rho * U_ref^2
    
    # Calculate pressure coefficient
    for i in 1:n_cells
        Cp.internal_field[i] = (p.internal_field[i] - p_ref) / q
    end
    
    return Cp
end

"""
    calculate_courant_number(U::Field{SVector{3,Float64}}, mesh::Mesh, dt::Float64)

Calculate Courant number: Co = |U| * dt / dx.

# Arguments
- `U`: Velocity field
- `mesh`: The mesh
- `dt`: Time step

# Returns
- `Field{Float64}`: Courant number field
"""
function calculate_courant_number(U::Field{SVector{3,Float64}}, mesh::Mesh, dt::Float64)
    n_cells = length(mesh.cells)
    Co = Field{Float64}(mesh, 0.0)
    
    for i in 1:n_cells
        # Calculate characteristic cell size
        # This is a simplified approach - in reality we would use the minimum face area
        cell_volume = mesh.cells[i].volume
        dx = cbrt(cell_volume)  # Cube root of volume as characteristic length
        
        # Calculate Courant number
        velocity_magnitude = norm(U.internal_field[i])
        Co.internal_field[i] = velocity_magnitude * dt / dx
    end
    
    return Co
end

"""
    extract_line_data(field::Field{T}, mesh::Mesh, start::SVector{3,Float64}, 
                     end_::SVector{3,Float64}, n_points::Int) where T

Extract field data along a line.

# Arguments
- `field`: Field to extract data from
- `mesh`: The mesh
- `start`: Start point of the line
- `end_`: End point of the line
- `n_points`: Number of points along the line

# Returns
- `Tuple{Vector{Float64},Vector{T}}`: Distance along the line and field values
"""
function extract_line_data(
    field::Field{T}, 
    mesh::Mesh, 
    start::SVector{3,Float64}, 
    end_::SVector{3,Float64}, 
    n_points::Int
) where T
    # Calculate points along the line
    line_points = Vector{SVector{3,Float64}}(undef, n_points)
    distances = Vector{Float64}(undef, n_points)
    
    for i in 1:n_points
        t = (i - 1) / (n_points - 1)
        line_points[i] = start + t * (end_ - start)
        distances[i] = t * norm(end_ - start)
    end
    
    # Find cells containing the points
    values = Vector{T}(undef, n_points)
    
    for i in 1:n_points
        point = line_points[i]
        
        # Find the cell containing the point
        # This is a simplified approach - in reality we would use a more efficient search
        cell_idx = find_cell_containing_point(mesh, point)
        
        if cell_idx !== nothing
            values[i] = field.internal_field[cell_idx]
        else
            # If point is outside the domain, use the closest cell
            closest_cell = find_closest_cell(mesh, point)
            values[i] = field.internal_field[closest_cell]
        end
    end
    
    return distances, values
end

"""
    find_cell_containing_point(mesh::Mesh, point::SVector{3,Float64})

Find the cell containing a point.

# Arguments
- `mesh`: The mesh
- `point`: The point

# Returns
- `Union{Int,Nothing}`: Index of the cell containing the point, or nothing if not found
"""
function find_cell_containing_point(mesh::Mesh, point::SVector{3,Float64})
    # Improved point-in-cell test using ray casting
    best_cell = -1
    min_distance = Inf
    
    for (i, cell) in enumerate(mesh.cells)
        # First do a quick bounding box check
        dist_to_center = norm(point - cell.center)
        
        # Estimate cell size from volume
        cell_size = cbrt(cell.volume)
        
        if dist_to_center < 2.0 * cell_size  # Potential candidate
            # More accurate test: check if point is on correct side of all faces
            inside = true
            
            for face_id in cell.faces
                face = mesh.faces[face_id]
                
                # Vector from face center to point
                to_point = point - face.center
                
                # Check which side of the face the point is on
                # For owner cell, point should be on positive normal side
                # For neighbor cell, point should be on negative normal side
                if face.owner == i
                    if dot(to_point, face.normal) < -1e-10
                        inside = false
                        break
                    end
                else
                    if dot(to_point, face.normal) > 1e-10
                        inside = false
                        break
                    end
                end
            end
            
            if inside
                # Found the cell containing the point
                return i
            elseif dist_to_center < min_distance
                # Keep track of closest cell as fallback
                min_distance = dist_to_center
                best_cell = i
            end
        end
    end
    
    # If no cell found (point outside domain), return closest
    return best_cell > 0 ? best_cell : nothing
end

"""
    find_closest_cell(mesh::Mesh, point::SVector{3,Float64})

Find the closest cell to a point.

# Arguments
- `mesh`: The mesh
- `point`: The point

# Returns
- `Int`: Index of the closest cell
"""
function find_closest_cell(mesh::Mesh, point::SVector{3,Float64})
    closest_cell = 1
    min_distance = norm(point - mesh.cells[1].center)
    
    for i in 2:length(mesh.cells)
        distance = norm(point - mesh.cells[i].center)
        
        if distance < min_distance
            min_distance = distance
            closest_cell = i
        end
    end
    
    return closest_cell
end

"""
    extract_plane_data(field::Field{T}, mesh::Mesh, origin::SVector{3,Float64}, 
                      normal::SVector{3,Float64}, n_points_x::Int, n_points_y::Int) where T

Extract field data on a plane.

# Arguments
- `field`: Field to extract data from
- `mesh`: The mesh
- `origin`: Origin of the plane
- `normal`: Normal vector of the plane
- `n_points_x`: Number of points in the first direction
- `n_points_y`: Number of points in the second direction

# Returns
- `Tuple{Matrix{SVector{3,Float64}},Matrix{T}}`: Coordinates and field values on the plane
"""
function extract_plane_data(
    field::Field{T}, 
    mesh::Mesh, 
    origin::SVector{3,Float64}, 
    normal::SVector{3,Float64}, 
    n_points_x::Int, 
    n_points_y::Int
) where T
    # Calculate orthogonal basis for the plane
    normal_unit = normalize(normal)
    
    # Find a vector not parallel to normal
    if abs(normal_unit[1]) < 0.9
        v1 = SVector{3,Float64}(1.0, 0.0, 0.0)
    else
        v1 = SVector{3,Float64}(0.0, 1.0, 0.0)
    end
    
    # Calculate orthogonal basis
    e1 = normalize(v1 - dot(v1, normal_unit) * normal_unit)
    e2 = cross(normal_unit, e1)
    
    # Calculate points on the plane
    plane_points = Matrix{SVector{3,Float64}}(undef, n_points_x, n_points_y)
    values = Matrix{T}(undef, n_points_x, n_points_y)
    
    # Calculate bounding box of the domain
    x_min, x_max = extrema([cell.center[1] for cell in mesh.cells])
    y_min, y_max = extrema([cell.center[2] for cell in mesh.cells])
    z_min, z_max = extrema([cell.center[3] for cell in mesh.cells])
    
    # Calculate plane size based on domain size
    plane_size_x = max(x_max - x_min, y_max - y_min, z_max - z_min)
    plane_size_y = plane_size_x
    
    for i in 1:n_points_x
        for j in 1:n_points_y
            # Calculate point on the plane
            x = (i - 1) / (n_points_x - 1) - 0.5
            y = (j - 1) / (n_points_y - 1) - 0.5
            
            plane_points[i, j] = origin + plane_size_x * x * e1 + plane_size_y * y * e2
            
            # Find the cell containing the point
            cell_idx = find_cell_containing_point(mesh, plane_points[i, j])
            
            if cell_idx !== nothing
                values[i, j] = field.internal_field[cell_idx]
            else
                # If point is outside the domain, use the closest cell
                closest_cell = find_closest_cell(mesh, plane_points[i, j])
                values[i, j] = field.internal_field[closest_cell]
            end
        end
    end
    
    return plane_points, values
end

"""
    calculate_forces(p::Field{Float64}, U::Field{SVector{3,Float64}}, mesh::Mesh, 
                    properties::FluidProperties, patch_names::Vector{String})

Calculate forces on specified boundary patches.

# Arguments
- `p`: Pressure field
- `U`: Velocity field
- `mesh`: The mesh
- `properties`: Fluid properties
- `patch_names`: Names of boundary patches to calculate forces on

# Returns
- `Dict{String,Tuple{SVector{3,Float64},SVector{3,Float64}}}`: Pressure and viscous forces on each patch
"""
function calculate_forces(
    p::Field{Float64}, 
    U::Field{SVector{3,Float64}}, 
    mesh::Mesh, 
    properties::FluidProperties, 
    patch_names::Vector{String}
)
    forces = Dict{String,Tuple{SVector{3,Float64},SVector{3,Float64}}}()
    
    # Calculate wall shear stress
    wall_shear = calculate_wall_shear_stress(U, mesh, properties, patch_names)
    
    for patch_name in patch_names
        if !haskey(mesh.boundary_patches, patch_name)
            @warn "Boundary patch $patch_name not found"
            continue
        end
        
        face_indices = mesh.boundary_patches[patch_name]
        
        # Initialize forces
        pressure_force = SVector{3,Float64}(0.0, 0.0, 0.0)
        viscous_force = SVector{3,Float64}(0.0, 0.0, 0.0)
        
        for (i, face_idx) in enumerate(face_indices)
            face = mesh.faces[face_idx]
            
            # Get face area vector
            area_vector = face.area
            
            # Get pressure at face
            face_pressure = get_boundary_face_value(p, Int32(face_idx), mesh)
            
            # Calculate pressure force: F_p = p * A
            pressure_force += face_pressure * area_vector
            
            # Get wall shear stress
            tau_w = wall_shear[patch_name][i]
            
            # Calculate viscous force: F_v = tau_w * A
            # This is simplified - in reality we would need to project tau_w onto the face
            viscous_force += tau_w * norm(area_vector)
        end
        
        forces[patch_name] = (pressure_force, viscous_force)
    end
    
    return forces
end

"""
    write_vtk_visualization(case_dir::String, mesh::Mesh, fields::Dict, time::Union{Float64,String})

Write fields to VTK format for visualization.

# Arguments
- `case_dir`: Path to the case directory
- `mesh`: The mesh
- `fields`: Dictionary of fields
- `time`: Current time or directory name
"""
function write_vtk_visualization(
    case_dir::String, 
    mesh::Mesh, 
    fields::Dict, 
    time::Union{Float64,String}
)
    # Create VTK directory
    vtk_dir = joinpath(case_dir, "VTK")
    mkpath(vtk_dir)
    
    # Extract points from mesh
    n_cells = length(mesh.cells)
    points = Vector{SVector{3,Float64}}(undef, n_cells)
    
    for i in 1:n_cells
        points[i] = mesh.cells[i].center
    end
    
    # Create VTK file
    vtk_filename = joinpath(vtk_dir, string(time))
    
    # Create VTK grid
    vtkfile = vtk_grid(vtk_filename, [p[1] for p in points], [p[2] for p in points], [p[3] for p in points])
    
    # Add fields to VTK file
    for (field_name, field) in fields
        if eltype(field.internal_field) <: SVector{3,Float64}
            # Vector field
            vtk_point_data(vtkfile, ([v[1] for v in field.internal_field], 
                                     [v[2] for v in field.internal_field], 
                                     [v[3] for v in field.internal_field]), field_name)
        else
            # Scalar field
            vtk_point_data(vtkfile, field.internal_field, field_name)
        end
    end
    
    # Save VTK file
    vtk_save(vtkfile)
end

"""
    create_residual_plot(residuals::Dict{String,Vector{Float64}}, output_file::String)

Create a plot of residuals.

# Arguments
- `residuals`: Dictionary of residuals for each field
- `output_file`: Output file name
"""
function create_residual_plot(residuals::Dict{String,Vector{Float64}}, output_file::String)
    # This function would use a plotting library like Plots.jl or PyPlot.jl
    # Since we don't have these libraries loaded, we'll just write the data to a CSV file
    
    open(output_file, "w") do file
        # Write header
        write(file, "Iteration")
        
        for field_name in keys(residuals)
            write(file, ",$field_name")
        end
        
        write(file, "\n")
        
        # Write data
        n_iterations = maximum(length(res) for res in values(residuals))
        
        for i in 1:n_iterations
            write(file, "$i")
            
            for (field_name, res) in residuals
                if i <= length(res)
                    write(file, ",$(res[i])")
                else
                    write(file, ",")
                end
            end
            
            write(file, "\n")
        end
    end
end

end # module PostProcessing
