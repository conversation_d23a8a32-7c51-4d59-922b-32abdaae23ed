"""
    MemoryPool.jl

This module provides memory pooling functionality to reduce allocations in inner loops.
"""

"""
    MemoryPool{T}

A memory pool for objects of type T to reduce allocations.

# Fields
- `chunks::Vector{Vector{T}}`: Storage for pooled objects
- `free_indices::Vector{Tuple{Int, Int}}`: Indices of free objects (chunk_idx, element_idx)
- `chunk_size::Int`: Size of each chunk
"""
struct MemoryPool{T}
    chunks::Vector{Vector{T}}
    free_indices::Vector{Tuple{Int, Int}}  # (chunk_idx, element_idx)
    chunk_size::Int
    
    """
        MemoryPool{T}(chunk_size::Int = 1024) where T

    Create a new memory pool for objects of type T with the given chunk size.
    """
    function MemoryPool{T}(chunk_size::Int = 1024) where T
        chunks = Vector{Vector{T}}()
        free_indices = Vector{Tuple{Int, Int}}()
        return new{T}(chunks, free_indices, chunk_size)
    end
end

"""
    allocate(pool::MemoryPool{T}) where T

Allocate an object from the memory pool. If the pool is empty, a new chunk is created.
"""
function allocate(pool::MemoryPool{T}) where T
    if isempty(pool.free_indices)
        # Create new chunk
        chunk_idx = length(pool.chunks) + 1
        
        # For large pools, use a larger chunk size to reduce overhead
        actual_chunk_size = chunk_idx > 5 ? pool.chunk_size * 2 : pool.chunk_size
        
        # Pre-allocate the chunk
        chunk = Vector{T}(undef, actual_chunk_size)
        push!(pool.chunks, chunk)
        
        # Add all elements to free list except the first one
        for i in 2:actual_chunk_size
            push!(pool.free_indices, (chunk_idx, i))
        end
        
        # Return the first element
        return (chunk_idx, 1)
    else
        # Reuse from free list
        return pop!(pool.free_indices)
    end
end

"""
    deallocate!(pool::MemoryPool{T}, index::Tuple{Int, Int}) where T

Return an object to the memory pool.
"""
function deallocate!(pool::MemoryPool{T}, index::Tuple{Int, Int}) where T
    push!(pool.free_indices, index)
end

"""
    get_object(pool::MemoryPool{T}, index::Tuple{Int, Int}) where T

Get the object at the given index in the memory pool.
"""
function get_object(pool::MemoryPool{T}, index::Tuple{Int, Int}) where T
    chunk_idx, element_idx = index
    return pool.chunks[chunk_idx][element_idx]
end

"""
    set_object!(pool::MemoryPool{T}, index::Tuple{Int, Int}, value::T) where T

Set the object at the given index in the memory pool.
"""
function set_object!(pool::MemoryPool{T}, index::Tuple{Int, Int}, value::T) where T
    chunk_idx, element_idx = index
    pool.chunks[chunk_idx][element_idx] = value
end

"""
    VectorPool{T}

A specialized memory pool for vectors to reduce allocations in inner loops.

# Fields
- `pool::MemoryPool{Vector{T}}`: The underlying memory pool
"""
struct VectorPool{T}
    pool::MemoryPool{Vector{T}}
    
    """
        VectorPool{T}(chunk_size::Int = 1024) where T

    Create a new vector pool for vectors of type T with the given chunk size.
    """
    function VectorPool{T}(chunk_size::Int = 1024) where T
        pool = MemoryPool{Vector{T}}(chunk_size)
        return new{T}(pool)
    end
end

"""
    allocate_vector(pool::VectorPool{T}, size::Int) where T

Allocate a vector from the vector pool with the given size.
"""
function allocate_vector(pool::VectorPool{T}, size::Int) where T
    # Optimize allocation strategy based on vector size
    if size > 100_000
        # For very large vectors, bypass the pool to avoid memory fragmentation
        # Just allocate directly and track for deallocation
        vector = zeros(T, size)
        # Use a special index to indicate direct allocation
        return ((-1, -1), vector)
    end
    
    # Use a size-based pooling strategy for better performance with different sizes
    # Group vectors into size buckets to reduce fragmentation while maintaining efficiency
    if size <= 64
        target_size = 64
    elseif size <= 256
        target_size = 256
    elseif size <= 1024
        target_size = 1024
    elseif size <= 4096
        target_size = 4096
    elseif size <= 16384
        target_size = 16384
    else
        target_size = nextpow(2, size)
    end
    
    index = allocate(pool.pool)
    vector = get_object(pool.pool, index)
    
    # Resize vector if needed, but only grow, never shrink
    # This avoids expensive reallocations when vectors are reused
    if length(vector) < target_size
        resize!(vector, target_size)
    end
    
    # Use optimized clearing strategy based on size
    if size <= 1024
        # For small vectors, clear the whole thing
        fill!(vector, zero(T))
    elseif size <= 16384
        # For medium vectors, use SIMD-optimized loop
        @turbo for i in 1:size
            vector[i] = zero(T)
        end
    else
        # For large vectors, use multi-threaded clearing if available
        if Threads.nthreads() > 1 && size > 100_000
            chunk_size = size ÷ Threads.nthreads()
            Threads.@threads for t in 1:Threads.nthreads()
                start_idx = (t-1) * chunk_size + 1
                end_idx = t == Threads.nthreads() ? size : t * chunk_size
                for i in start_idx:end_idx
                    vector[i] = zero(T)
                end
            end
        else
            # Fall back to simple loop for large vectors
            for i in 1:size
                vector[i] = zero(T)
            end
        end
    end
    
    return (index, vector)
end

"""
    deallocate_vector!(pool::VectorPool{T}, index::Tuple{Int, Int}) where T

Return a vector to the vector pool.
"""
function deallocate_vector!(pool::VectorPool{T}, index::Tuple{Int, Int}) where T
    # Check if this was a direct allocation (special index)
    if index == (-1, -1)
        # Nothing to do for direct allocations - they'll be garbage collected
        return
    end
    
    # Return to pool
    deallocate!(pool.pool, index)
end

"""
    MatrixPool{T}

A specialized memory pool for matrices to reduce allocations in inner loops.

# Fields
- `pool::MemoryPool{Matrix{T}}`: The underlying memory pool
"""
struct MatrixPool{T}
    pool::MemoryPool{Matrix{T}}
    
    """
        MatrixPool{T}(chunk_size::Int = 1024) where T

    Create a new matrix pool for matrices of type T with the given chunk size.
    """
    function MatrixPool{T}(chunk_size::Int = 1024) where T
        pool = MemoryPool{Matrix{T}}(chunk_size)
        return new{T}(pool)
    end
end

"""
    allocate_matrix(pool::MatrixPool{T}, rows::Int, cols::Int) where T

Allocate a matrix from the matrix pool with the given dimensions.
"""
function allocate_matrix(pool::MatrixPool{T}, rows::Int, cols::Int) where T
    index = allocate(pool.pool)
    matrix = get_object(pool.pool, index)
    
    # Resize matrix if needed
    if size(matrix, 1) != rows || size(matrix, 2) != cols
        matrix = zeros(T, rows, cols)
        set_object!(pool.pool, index, matrix)
    else
        # Clear matrix
        fill!(matrix, zero(T))
    end
    
    return (index, matrix)
end

"""
    deallocate_matrix!(pool::MatrixPool{T}, index::Tuple{Int, Int}) where T

Return a matrix to the matrix pool.
"""
function deallocate_matrix!(pool::MatrixPool{T}, index::Tuple{Int, Int}) where T
    deallocate!(pool.pool, index)
end

# Export functions and types
export MemoryPool, allocate, deallocate!, get_object, set_object!
export VectorPool, allocate_vector, deallocate_vector!
export MatrixPool, allocate_matrix, deallocate_matrix!
