"""
    k-ε turbulence model implementation.
    This module provides the standard k-ε two-equation turbulence model.
"""
module KEpsilonModel

using LinearAlgebra
using StaticArrays
using SparseArrays
using MPI
using Base.Threads
using ..JuliaFOAM
# LinearSolvers functions are re-exported by JuliaFOAM
# FiniteVolume functions are re-exported by JuliaFOAM
using ..Turbulence

export KETurbulenceModel, create_ke_model, compute_turbulent_viscosity!
export update_turbulence_fields!, apply_ke_boundary_conditions!
export compute_turbulent_viscosity_parallel!, update_turbulence_fields_parallel!
export apply_ke_boundary_conditions_parallel!

"""
    KETurbulenceModel <: TurbulenceModel

k-ε turbulence model.

# Fields
- `k::Field{Float64}`: Turbulent kinetic energy
- `epsilon::Field{Float64}`: Turbulent dissipation rate
- `nu_t::Vector{Float64}`: Turbulent viscosity
- `C_mu::Float64`: Model constant
- `C_1::Float64`: Model constant
- `C_2::Float64`: Model constant
- `sigma_k::Float64`: Model constant (Prandtl number for k)
- `sigma_epsilon::Float64`: Model constant (Prandtl number for ε)
"""
struct KETurbulenceModel <: TurbulenceModel
    k::Field{Float64}
    epsilon::Field{Float64}
    nu_t::Vector{Float64}
    C_mu::Float64
    C_1::Float64
    C_2::Float64
    sigma_k::Float64
    sigma_epsilon::Float64
end

"""
    create_ke_model(mesh::Mesh; k_init=1e-4, epsilon_init=1e-5, C_mu=0.09, C_1=1.44, C_2=1.92, sigma_k=1.0, sigma_epsilon=1.3)

Create a k-ε turbulence model.

# Arguments
- `mesh`: The mesh

# Keyword Arguments
- `k_init`: Initial value for turbulent kinetic energy
- `epsilon_init`: Initial value for turbulent dissipation rate
- `C_mu`: Model constant
- `C_1`: Model constant
- `C_2`: Model constant
- `sigma_k`: Model constant (Prandtl number for k)
- `sigma_epsilon`: Model constant (Prandtl number for ε)

# Returns
- `KETurbulenceModel`: k-ε turbulence model
"""
function create_ke_model(
    mesh::Mesh;
    k_init=1e-4,
    epsilon_init=1e-5,
    C_mu=0.09,
    C_1=1.44,
    C_2=1.92,
    sigma_k=1.0,
    sigma_epsilon=1.3
)
    # Create fields
    n_cells = length(mesh.cells)

    # Initialize k field
    k = Field{Float64}("k",
        fill(k_init, n_cells),
        Dict{String,Vector{Float64}}()
    )

    # Initialize epsilon field
    epsilon = Field{Float64}("epsilon",
        fill(epsilon_init, n_cells),
        Dict{String,Vector{Float64}}()
    )

    # Initialize turbulent viscosity
    nu_t = zeros(n_cells)

    # Initialize boundary fields
    for (patch_name, patch_faces) in mesh.boundary_patches
        k.boundary_values[patch_name] = fill(k_init, length(patch_faces))
        epsilon.boundary_values[patch_name] = fill(epsilon_init, length(patch_faces))
    end

    # Create model
    return KETurbulenceModel(k, epsilon, nu_t, C_mu, C_1, C_2, sigma_k, sigma_epsilon)
end

"""
    compute_turbulent_viscosity!(nu_t::Vector{Float64}, model::KETurbulenceModel, mesh::Mesh)

Compute turbulent viscosity field for k-ε model.

# Arguments
- `nu_t`: Turbulent viscosity field
- `model`: k-ε turbulence model
- `mesh`: The mesh
"""
function compute_turbulent_viscosity!(nu_t::Vector{Float64}, model::KETurbulenceModel, mesh::Mesh)
    # Get fields
    k = model.k.internal_field
    epsilon = model.epsilon.internal_field

    # Compute turbulent viscosity: nu_t = C_mu * k^2 / epsilon
    for i in 1:length(nu_t)
        # Ensure positive values and avoid division by zero
        k_pos = max(k[i], 1e-10)
        epsilon_pos = max(epsilon[i], 1e-10)

        nu_t[i] = model.C_mu * k_pos^2 / epsilon_pos
    end
end

"""
    update_turbulence_fields!(model::KETurbulenceModel, mesh::Mesh, U::Field{SVector{3,Float64}},
                            dt::Float64, properties::FluidProperties)

Update k and ε fields for k-ε model.

# Arguments
- `model`: k-ε turbulence model
- `mesh`: The mesh
- `U`: Velocity field
- `dt`: Time step
- `properties`: Fluid properties
"""
function update_turbulence_fields!(
    model::KETurbulenceModel,
    mesh::Mesh,
    U::Field{SVector{3,Float64}},
    dt::Float64,
    properties::FluidProperties
)
    # Get fields
    k = model.k.internal_field
    epsilon = model.epsilon.internal_field
    nu_t = model.nu_t

    # Update turbulent viscosity
    compute_turbulent_viscosity!(nu_t, model, mesh)

    # Solve k equation
    solve_k_equation!(model, mesh, U, dt, properties)

    # Solve epsilon equation
    solve_epsilon_equation!(model, mesh, U, dt, properties)

    # Apply boundary conditions
    apply_ke_boundary_conditions!(model, mesh)
end

"""
    solve_k_equation!(model::KETurbulenceModel, mesh::Mesh, U::Field{SVector{3,Float64}},
                    dt::Float64, properties::FluidProperties)

Solve transport equation for turbulent kinetic energy (k).

# Arguments
- `model`: k-ε turbulence model
- `mesh`: The mesh
- `U`: Velocity field
- `dt`: Time step
- `properties`: Fluid properties
"""
function solve_k_equation!(
    model::KETurbulenceModel,
    mesh::Mesh,
    U::Field{SVector{3,Float64}},
    dt::Float64,
    properties::FluidProperties
)
    # Get fields
    k = model.k.internal_field
    epsilon = model.epsilon.internal_field
    nu_t = model.nu_t

    # Build k equation
    A, b = build_k_equation(model, mesh, U, dt, properties)

    # Apply boundary conditions
    apply_ke_boundary_conditions!(model, mesh)

    # Solve k equation
    solver_settings = SolverSettings(
        :bicgstab,
        :ilu,
        1e-6,
        1000
    )

    # Create preconditioner
    precond = ILUPreconditioner(incomplete_lu(A))

    # Wrap matrix
    A_wrapper = MatrixWrapper(A)

    # Solve
    solve!(A_wrapper, k, b, solver_settings, precond)

    # Ensure positive values
    k .= max.(k, 1e-10)
end

"""
    solve_epsilon_equation!(model::KETurbulenceModel, mesh::Mesh, U::Field{SVector{3,Float64}},
                          dt::Float64, properties::FluidProperties)

Solve transport equation for turbulent dissipation rate (ε).

# Arguments
- `model`: k-ε turbulence model
- `mesh`: The mesh
- `U`: Velocity field
- `dt`: Time step
- `properties`: Fluid properties
"""
function solve_epsilon_equation!(
    model::KETurbulenceModel,
    mesh::Mesh,
    U::Field{SVector{3,Float64}},
    dt::Float64,
    properties::FluidProperties
)
    # Get fields
    k = model.k.internal_field
    epsilon = model.epsilon.internal_field
    nu_t = model.nu_t

    # Build epsilon equation
    A, b = build_epsilon_equation(model, mesh, U, dt, properties)

    # Apply boundary conditions
    apply_ke_boundary_conditions!(model, mesh)

    # Solve epsilon equation
    solver_settings = SolverSettings(
        :bicgstab,
        :ilu,
        1e-6,
        1000
    )

    # Create preconditioner
    precond = ILUPreconditioner(incomplete_lu(A))

    # Wrap matrix
    A_wrapper = MatrixWrapper(A)

    # Solve
    solve!(A_wrapper, epsilon, b, solver_settings, precond)

    # Ensure positive values
    epsilon .= max.(epsilon, 1e-10)
end

"""
    build_k_equation(model::KETurbulenceModel, mesh::Mesh, U::Field{SVector{3,Float64}},
                   dt::Float64, properties::FluidProperties)

Build transport equation for turbulent kinetic energy (k).

# Arguments
- `model`: k-ε turbulence model
- `mesh`: The mesh
- `U`: Velocity field
- `dt`: Time step
- `properties`: Fluid properties

# Returns
- `Tuple{SparseMatrixCSC,Vector{Float64}}`: System matrix and right-hand side
"""
function build_k_equation(
    model::KETurbulenceModel,
    mesh::Mesh,
    U::Field{SVector{3,Float64}},
    dt::Float64,
    properties::FluidProperties
)
    # Get fields
    k = model.k.internal_field
    epsilon = model.epsilon.internal_field
    nu_t = model.nu_t

    # Number of cells
    n_cells = length(mesh.cells)

    # Initialize matrix and right-hand side
    I = Int[]
    J = Int[]
    V = Float64[]
    b = zeros(n_cells)

    # Build equation for each cell
    for cell_idx in 1:n_cells
        cell = mesh.cells[cell_idx]

        # Cell volume
        volume = cell.volume

        # Diagonal coefficient
        a_p = volume / dt

        # Source terms
        # Production term: P_k = 2*nu_t*S:S
        P_k = nu_t[cell_idx] * compute_production_term(U, mesh, cell_idx)

        # Dissipation term: rho*epsilon
        D_k = properties.rho * epsilon[cell_idx]

        # Add to right-hand side
        b[cell_idx] += volume * (P_k - D_k)

        # Add old-time contribution to right-hand side
        b[cell_idx] += volume * k[cell_idx] / dt

        # Add to matrix
        push!(I, cell_idx)
        push!(J, cell_idx)
        push!(V, a_p)

        # Add diffusion terms
        for face_idx in cell.faces
            face = mesh.faces[face_idx]

            # Face area magnitude
            area_mag = norm(face.area)

            # Face normal
            normal = normalize(face.area)

            if face.owner == cell_idx
                # This cell is the owner
                if face.neighbour > 0
                    # Internal face
                    neighbour_idx = face.neighbour

                    # Distance between cell centers
                    delta = mesh.cells[neighbour_idx].center - cell.center

                    # Effective viscosity
                    nu_eff = properties.nu + nu_t[cell_idx] / model.sigma_k

                    # Diffusion coefficient
                    d_f = nu_eff * area_mag / norm(delta)

                    # Add to matrix
                    push!(I, cell_idx)
                    push!(J, neighbour_idx)
                    push!(V, -d_f)

                    push!(I, cell_idx)
                    push!(J, cell_idx)
                    push!(V, d_f)
                else
                    # Boundary face
                    # Handled separately through boundary conditions
                end
            else
                # This cell is the neighbour
                # Already handled when processing the owner cell
            end
        end
    end

    # Create sparse matrix
    A = sparse(I, J, V, n_cells, n_cells)

    return A, b
end

"""
    build_epsilon_equation(model::KETurbulenceModel, mesh::Mesh, U::Field{SVector{3,Float64}},
                         dt::Float64, properties::FluidProperties)

Build transport equation for turbulent dissipation rate (ε).

# Arguments
- `model`: k-ε turbulence model
- `mesh`: The mesh
- `U`: Velocity field
- `dt`: Time step
- `properties`: Fluid properties

# Returns
- `Tuple{SparseMatrixCSC,Vector{Float64}}`: System matrix and right-hand side
"""
function build_epsilon_equation(
    model::KETurbulenceModel,
    mesh::Mesh,
    U::Field{SVector{3,Float64}},
    dt::Float64,
    properties::FluidProperties
)
    # Get fields
    k = model.k.internal_field
    epsilon = model.epsilon.internal_field
    nu_t = model.nu_t

    # Number of cells
    n_cells = length(mesh.cells)

    # Initialize matrix and right-hand side
    I = Int[]
    J = Int[]
    V = Float64[]
    b = zeros(n_cells)

    # Build equation for each cell
    for cell_idx in 1:n_cells
        cell = mesh.cells[cell_idx]

        # Cell volume
        volume = cell.volume

        # Diagonal coefficient
        a_p = volume / dt

        # Source terms
        # Production term: C_1 * epsilon/k * P_k
        P_k = nu_t[cell_idx] * compute_production_term(U, mesh, cell_idx)
        P_epsilon = model.C_1 * epsilon[cell_idx] / max(k[cell_idx], 1e-10) * P_k

        # Dissipation term: C_2 * rho * epsilon^2 / k
        D_epsilon = model.C_2 * properties.rho * epsilon[cell_idx]^2 / max(k[cell_idx], 1e-10)

        # Add to right-hand side
        b[cell_idx] += volume * (P_epsilon - D_epsilon)

        # Add old-time contribution to right-hand side
        b[cell_idx] += volume * epsilon[cell_idx] / dt

        # Add to matrix
        push!(I, cell_idx)
        push!(J, cell_idx)
        push!(V, a_p)

        # Add diffusion terms
        for face_idx in cell.faces
            face = mesh.faces[face_idx]

            # Face area magnitude
            area_mag = norm(face.area)

            # Face normal
            normal = normalize(face.area)

            if face.owner == cell_idx
                # This cell is the owner
                if face.neighbour > 0
                    # Internal face
                    neighbour_idx = face.neighbour

                    # Distance between cell centers
                    delta = mesh.cells[neighbour_idx].center - cell.center

                    # Effective viscosity
                    nu_eff = properties.nu + nu_t[cell_idx] / model.sigma_epsilon

                    # Diffusion coefficient
                    d_f = nu_eff * area_mag / norm(delta)

                    # Add to matrix
                    push!(I, cell_idx)
                    push!(J, neighbour_idx)
                    push!(V, -d_f)

                    push!(I, cell_idx)
                    push!(J, cell_idx)
                    push!(V, d_f)
                else
                    # Boundary face
                    # Handled separately through boundary conditions
                end
            else
                # This cell is the neighbour
                # Already handled when processing the owner cell
            end
        end
    end

    # Create sparse matrix
    A = sparse(I, J, V, n_cells, n_cells)

    return A, b
end

"""
    apply_ke_boundary_conditions!(model::KETurbulenceModel, mesh::Mesh)

Apply boundary conditions to k and ε fields.

# Arguments
- `model`: k-ε turbulence model
- `mesh`: The mesh
"""
function apply_ke_boundary_conditions!(model::KETurbulenceModel, mesh::Mesh)
    # Apply boundary conditions to k field
    apply_boundary_conditions!(model.k, mesh)

    # Apply boundary conditions to epsilon field
    apply_boundary_conditions!(model.epsilon, mesh)

    # Apply specific boundary conditions for walls
    for (patch_name, bc) in mesh.boundary_conditions
        if isa(bc, WallFunctionBC)
            # Apply wall functions
            apply_wall_functions!(model, mesh, patch_name)
        end
    end
end

"""
    apply_wall_functions!(model::KETurbulenceModel, mesh::Mesh, patch_name::String)

Apply wall functions for k-ε model.

# Arguments
- `model`: k-ε turbulence model
- `mesh`: The mesh
- `patch_name`: Boundary patch name
"""
function apply_wall_functions!(model::KETurbulenceModel, mesh::Mesh, patch_name::String)
    # Get boundary patch
    patch_faces = mesh.boundary_patches[patch_name]

    # Get boundary condition
    bc = mesh.boundary_conditions[patch_name]

    # Apply wall functions for each face
    for (i, face_idx) in enumerate(patch_faces)
        face = mesh.faces[face_idx]
        cell_idx = face.owner

        # Cell center
        cell_center = mesh.cells[cell_idx].center

        # Face center
        face_center = face.center

        # Distance to wall
        delta = norm(face_center - cell_center)

        # Get k value at cell
        k_cell = model.k.internal_field[cell_idx]

        # Set k at wall to zero
        model.k.boundary_field[patch_name][i] = 0.0

        # Calculate epsilon at wall using wall function
        # For high-Re k-epsilon, epsilon at wall is:
        # epsilon_wall = C_mu^0.75 * k^1.5 / (kappa * y)
        # where kappa is von Karman constant (0.41)
        # and y is distance to wall
        kappa = 0.41
        epsilon_wall = model.C_mu^0.75 * k_cell^1.5 / (kappa * delta)

        # Set epsilon at wall
        model.epsilon.boundary_field[patch_name][i] = epsilon_wall
    end
end

"""
    compute_turbulent_viscosity_parallel!(nu_t::Vector{Float64}, model::KETurbulenceModel, mesh::Any, thread_cell_ranges)

Compute turbulent viscosity field for k-ε model in parallel.

# Arguments
- `nu_t`: Turbulent viscosity field
- `model`: k-ε turbulence model
- `mesh`: The optimized mesh
- `thread_cell_ranges`: Cell ranges for each thread
"""
function compute_turbulent_viscosity_parallel!(nu_t::Vector{Float64}, model::KETurbulenceModel, mesh::Any, thread_cell_ranges)
    # Get fields
    k = model.k.internal_field
    epsilon = model.epsilon.internal_field

    # Compute turbulent viscosity in parallel: nu_t = C_mu * k^2 / epsilon
    Threads.@threads for thread_id in 1:length(thread_cell_ranges)
        cell_range = thread_cell_ranges[thread_id]

        for i in cell_range
            # Ensure positive values and avoid division by zero
            k_pos = max(k[i], 1e-10)
            epsilon_pos = max(epsilon[i], 1e-10)

            nu_t[i] = model.C_mu * k_pos^2 / epsilon_pos
        end
    end
end

"""
    update_turbulence_fields_parallel!(model::KETurbulenceModel, mesh::Any, U::Field{SVector{3,Float64}},
                                    dt::Float64, properties::FluidProperties, thread_cell_ranges)

Update k and ε fields for k-ε model in parallel.

# Arguments
- `model`: k-ε turbulence model
- `mesh`: The optimized mesh
- `U`: Velocity field
- `dt`: Time step
- `properties`: Fluid properties
- `thread_cell_ranges`: Cell ranges for each thread
"""
function update_turbulence_fields_parallel!(
    model::KETurbulenceModel,
    mesh::Any,
    U::Field{SVector{3,Float64}},
    dt::Float64,
    properties::FluidProperties,
    thread_cell_ranges
)
    # Get fields
    k = model.k.internal_field
    epsilon = model.epsilon.internal_field
    nu_t = model.nu_t

    # Update turbulent viscosity in parallel
    compute_turbulent_viscosity_parallel!(nu_t, model, mesh, thread_cell_ranges)

    # Solve k equation in parallel
    solve_k_equation_parallel!(model, mesh, U, dt, properties, thread_cell_ranges)

    # Solve epsilon equation in parallel
    solve_epsilon_equation_parallel!(model, mesh, U, dt, properties, thread_cell_ranges)

    # Apply boundary conditions
    apply_ke_boundary_conditions_parallel!(model, mesh)

    # Synchronize fields across processes
    if MPI.Initialized() && hasfield(typeof(mesh), :comm)
        comm = mesh.comm
        nprocs = MPI.Comm_size(comm)

        if nprocs > 1
            # Synchronize k field
            requests_k = exchange_halo_data_nonblocking!(k, mesh)
            wait_for_halo_exchange!(requests_k, k, mesh)

            # Synchronize epsilon field
            requests_epsilon = exchange_halo_data_nonblocking!(epsilon, mesh)
            wait_for_halo_exchange!(requests_epsilon, epsilon, mesh)
        end
    end
end

"""
    solve_k_equation_parallel!(model::KETurbulenceModel, mesh::Any, U::Field{SVector{3,Float64}},
                            dt::Float64, properties::FluidProperties, thread_cell_ranges)

Solve transport equation for turbulent kinetic energy (k) in parallel.

# Arguments
- `model`: k-ε turbulence model
- `mesh`: The optimized mesh
- `U`: Velocity field
- `dt`: Time step
- `properties`: Fluid properties
- `thread_cell_ranges`: Cell ranges for each thread
"""
function solve_k_equation_parallel!(
    model::KETurbulenceModel,
    mesh::Any,
    U::Field{SVector{3,Float64}},
    dt::Float64,
    properties::FluidProperties,
    thread_cell_ranges
)
    # Get fields
    k = model.k.internal_field
    epsilon = model.epsilon.internal_field
    nu_t = model.nu_t

    # Build k equation in parallel
    A, b = build_k_equation_parallel(model, mesh, U, dt, properties, thread_cell_ranges)

    # Apply boundary conditions
    apply_ke_boundary_conditions_parallel!(model, mesh)

    # Solve k equation
    solver_settings = SolverSettings(
        :bicgstab,
        :ilu,
        1e-6,
        1000
    )

    # Create preconditioner
    precond = ILUPreconditioner(incomplete_lu(A))

    # Wrap matrix
    A_wrapper = MatrixWrapper(A)

    # Solve
    solve!(A_wrapper, k, b, solver_settings, precond)

    # Ensure positive values
    k .= max.(k, 1e-10)
end

"""
    solve_epsilon_equation_parallel!(model::KETurbulenceModel, mesh::Any, U::Field{SVector{3,Float64}},
                                  dt::Float64, properties::FluidProperties, thread_cell_ranges)

Solve transport equation for turbulent dissipation rate (ε) in parallel.

# Arguments
- `model`: k-ε turbulence model
- `mesh`: The optimized mesh
- `U`: Velocity field
- `dt`: Time step
- `properties`: Fluid properties
- `thread_cell_ranges`: Cell ranges for each thread
"""
function solve_epsilon_equation_parallel!(
    model::KETurbulenceModel,
    mesh::Any,
    U::Field{SVector{3,Float64}},
    dt::Float64,
    properties::FluidProperties,
    thread_cell_ranges
)
    # Get fields
    k = model.k.internal_field
    epsilon = model.epsilon.internal_field
    nu_t = model.nu_t

    # Build epsilon equation in parallel
    A, b = build_epsilon_equation_parallel(model, mesh, U, dt, properties, thread_cell_ranges)

    # Apply boundary conditions
    apply_ke_boundary_conditions_parallel!(model, mesh)

    # Solve epsilon equation
    solver_settings = SolverSettings(
        :bicgstab,
        :ilu,
        1e-6,
        1000
    )

    # Create preconditioner
    precond = ILUPreconditioner(incomplete_lu(A))

    # Wrap matrix
    A_wrapper = MatrixWrapper(A)

    # Solve
    solve!(A_wrapper, epsilon, b, solver_settings, precond)

    # Ensure positive values
    epsilon .= max.(epsilon, 1e-10)
end

"""
    build_k_equation_parallel(model::KETurbulenceModel, mesh::Any, U::Field{SVector{3,Float64}},
                           dt::Float64, properties::FluidProperties, thread_cell_ranges)

Build transport equation for turbulent kinetic energy (k) in parallel.

# Arguments
- `model`: k-ε turbulence model
- `mesh`: The optimized mesh
- `U`: Velocity field
- `dt`: Time step
- `properties`: Fluid properties
- `thread_cell_ranges`: Cell ranges for each thread

# Returns
- `Tuple{SparseMatrixCSC,Vector{Float64}}`: System matrix and right-hand side
"""
function build_k_equation_parallel(
    model::KETurbulenceModel,
    mesh::Any,
    U::Field{SVector{3,Float64}},
    dt::Float64,
    properties::FluidProperties,
    thread_cell_ranges
)
    # Get fields
    k = model.k.internal_field
    epsilon = model.epsilon.internal_field
    nu_t = model.nu_t

    # Number of cells
    n_cells = length(mesh.cells)

    # Initialize matrix and right-hand side
    I = Int[]
    J = Int[]
    V = Float64[]
    b = zeros(n_cells)

    # Build equation for each cell in parallel
    # We'll use thread-local storage for I, J, V to avoid race conditions
    thread_I = [Int[] for _ in 1:length(thread_cell_ranges)]
    thread_J = [Int[] for _ in 1:length(thread_cell_ranges)]
    thread_V = [Float64[] for _ in 1:length(thread_cell_ranges)]

    # Process cells in parallel
    Threads.@threads for thread_id in 1:length(thread_cell_ranges)
        cell_range = thread_cell_ranges[thread_id]

        for cell_idx in cell_range
            cell = mesh.cells[cell_idx]

            # Cell volume
            volume = cell.volume

            # Diagonal coefficient
            a_p = volume / dt

            # Source terms
            # Production term: P_k = 2*nu_t*S:S
            P_k = nu_t[cell_idx] * compute_production_term_parallel(U, mesh, cell_idx, thread_cell_ranges)

            # Dissipation term: rho*epsilon
            D_k = properties.rho * epsilon[cell_idx]

            # Add to right-hand side
            b[cell_idx] += volume * (P_k - D_k)

            # Add old-time contribution to right-hand side
            b[cell_idx] += volume * k[cell_idx] / dt

            # Add to matrix
            push!(thread_I[thread_id], cell_idx)
            push!(thread_J[thread_id], cell_idx)
            push!(thread_V[thread_id], a_p)

            # Add diffusion terms
            for face_idx in mesh.cell_faces[cell_idx]
                face = mesh.faces[face_idx]

                # Face area magnitude
                area_mag = norm(face.area)

                # Face normal
                normal = normalize(face.area)

                if face.owner == cell_idx
                    # This cell is the owner
                    if face.neighbour > 0
                        # Internal face
                        neighbour_idx = face.neighbour

                        # Distance between cell centers
                        delta = mesh.cells[neighbour_idx].center - cell.center

                        # Effective viscosity
                        nu_eff = properties.nu + nu_t[cell_idx] / model.sigma_k

                        # Diffusion coefficient
                        d_f = nu_eff * area_mag / norm(delta)

                        # Add to matrix
                        push!(thread_I[thread_id], cell_idx)
                        push!(thread_J[thread_id], neighbour_idx)
                        push!(thread_V[thread_id], -d_f)

                        push!(thread_I[thread_id], cell_idx)
                        push!(thread_J[thread_id], cell_idx)
                        push!(thread_V[thread_id], d_f)
                    else
                        # Boundary face
                        # Handled separately through boundary conditions
                    end
                else
                    # This cell is the neighbour
                    # Already handled when processing the owner cell
                end
            end
        end
    end

    # Combine thread-local storage
    for thread_id in 1:length(thread_cell_ranges)
        append!(I, thread_I[thread_id])
        append!(J, thread_J[thread_id])
        append!(V, thread_V[thread_id])
    end

    # Create sparse matrix
    A = sparse(I, J, V, n_cells, n_cells)

    return A, b
end

"""
    build_epsilon_equation_parallel(model::KETurbulenceModel, mesh::Any, U::Field{SVector{3,Float64}},
                                 dt::Float64, properties::FluidProperties, thread_cell_ranges)

Build transport equation for turbulent dissipation rate (ε) in parallel.

# Arguments
- `model`: k-ε turbulence model
- `mesh`: The optimized mesh
- `U`: Velocity field
- `dt`: Time step
- `properties`: Fluid properties
- `thread_cell_ranges`: Cell ranges for each thread

# Returns
- `Tuple{SparseMatrixCSC,Vector{Float64}}`: System matrix and right-hand side
"""
function build_epsilon_equation_parallel(
    model::KETurbulenceModel,
    mesh::Any,
    U::Field{SVector{3,Float64}},
    dt::Float64,
    properties::FluidProperties,
    thread_cell_ranges
)
    # Get fields
    k = model.k.internal_field
    epsilon = model.epsilon.internal_field
    nu_t = model.nu_t

    # Number of cells
    n_cells = length(mesh.cells)

    # Initialize matrix and right-hand side
    I = Int[]
    J = Int[]
    V = Float64[]
    b = zeros(n_cells)

    # Build equation for each cell in parallel
    # We'll use thread-local storage for I, J, V to avoid race conditions
    thread_I = [Int[] for _ in 1:length(thread_cell_ranges)]
    thread_J = [Int[] for _ in 1:length(thread_cell_ranges)]
    thread_V = [Float64[] for _ in 1:length(thread_cell_ranges)]

    # Process cells in parallel
    Threads.@threads for thread_id in 1:length(thread_cell_ranges)
        cell_range = thread_cell_ranges[thread_id]

        for cell_idx in cell_range
            cell = mesh.cells[cell_idx]

            # Cell volume
            volume = cell.volume

            # Diagonal coefficient
            a_p = volume / dt

            # Source terms
            # Production term: C_1 * epsilon/k * P_k
            P_k = nu_t[cell_idx] * compute_production_term_parallel(U, mesh, cell_idx, thread_cell_ranges)
            P_epsilon = model.C_1 * epsilon[cell_idx] / max(k[cell_idx], 1e-10) * P_k

            # Dissipation term: C_2 * rho * epsilon^2 / k
            D_epsilon = model.C_2 * properties.rho * epsilon[cell_idx]^2 / max(k[cell_idx], 1e-10)

            # Add to right-hand side
            b[cell_idx] += volume * (P_epsilon - D_epsilon)

            # Add old-time contribution to right-hand side
            b[cell_idx] += volume * epsilon[cell_idx] / dt

            # Add to matrix
            push!(thread_I[thread_id], cell_idx)
            push!(thread_J[thread_id], cell_idx)
            push!(thread_V[thread_id], a_p)

            # Add diffusion terms
            for face_idx in mesh.cell_faces[cell_idx]
                face = mesh.faces[face_idx]

                # Face area magnitude
                area_mag = norm(face.area)

                # Face normal
                normal = normalize(face.area)

                if face.owner == cell_idx
                    # This cell is the owner
                    if face.neighbour > 0
                        # Internal face
                        neighbour_idx = face.neighbour

                        # Distance between cell centers
                        delta = mesh.cells[neighbour_idx].center - cell.center

                        # Effective viscosity
                        nu_eff = properties.nu + nu_t[cell_idx] / model.sigma_epsilon

                        # Diffusion coefficient
                        d_f = nu_eff * area_mag / norm(delta)

                        # Add to matrix
                        push!(thread_I[thread_id], cell_idx)
                        push!(thread_J[thread_id], neighbour_idx)
                        push!(thread_V[thread_id], -d_f)

                        push!(thread_I[thread_id], cell_idx)
                        push!(thread_J[thread_id], cell_idx)
                        push!(thread_V[thread_id], d_f)
                    else
                        # Boundary face
                        # Handled separately through boundary conditions
                    end
                else
                    # This cell is the neighbour
                    # Already handled when processing the owner cell
                end
            end
        end
    end

    # Combine thread-local storage
    for thread_id in 1:length(thread_cell_ranges)
        append!(I, thread_I[thread_id])
        append!(J, thread_J[thread_id])
        append!(V, thread_V[thread_id])
    end

    # Create sparse matrix
    A = sparse(I, J, V, n_cells, n_cells)

    return A, b
end

"""
    apply_ke_boundary_conditions_parallel!(model::KETurbulenceModel, mesh::Any)

Apply boundary conditions to k and ε fields in parallel.

# Arguments
- `model`: k-ε turbulence model
- `mesh`: The optimized mesh
"""
function apply_ke_boundary_conditions_parallel!(model::KETurbulenceModel, mesh::Any)
    # Apply boundary conditions for k and epsilon
    # For now, just use zero gradient for all boundaries
    for (patch_name, patch_faces) in mesh.boundary_patches
        n_faces = length(patch_faces)

        # Get boundary cells
        boundary_cells = Int[]
        for face_idx in patch_faces
            face = mesh.faces[face_idx]
            push!(boundary_cells, face.owner)
        end

        # Apply zero gradient for k
        model.k.boundary_values[patch_name] = model.k.internal_field[boundary_cells]

        # Apply zero gradient for epsilon
        model.epsilon.boundary_values[patch_name] = model.epsilon.internal_field[boundary_cells]
    end
end

# Implement parallel Turbulence interface
Turbulence.compute_turbulent_viscosity_parallel!(nu_t::Vector{Float64}, model::KETurbulenceModel, mesh::Any, thread_cell_ranges) = compute_turbulent_viscosity_parallel!(nu_t, model, mesh, thread_cell_ranges)
Turbulence.update_turbulence_fields_parallel!(model::KETurbulenceModel, mesh::Any, U::Field{SVector{3,Float64}}, dt::Float64, properties::FluidProperties, thread_cell_ranges) = update_turbulence_fields_parallel!(model, mesh, U, dt, properties, thread_cell_ranges)
Turbulence.apply_turbulence_boundary_conditions_parallel!(model::KETurbulenceModel, mesh::Any) = apply_ke_boundary_conditions_parallel!(model, mesh)
Turbulence.compute_dissipation_term_parallel(model::KETurbulenceModel, mesh::Any, cell_idx::Int, thread_cell_ranges) = properties.rho * model.epsilon.internal_field[cell_idx]

end # module KEpsilonModel
