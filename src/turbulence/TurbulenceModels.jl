"""
    Main turbulence module that exports all turbulence models.
"""
module TurbulenceModels

using ..JuliaFOAM

# Include base turbulence module
include("Turbulence.jl")

# Include specific turbulence models
include("KEpsilonModel.jl")

# Re-export from Turbulence module
using .Turbulence
export TurbulenceModel, compute_turbulent_viscosity!, update_turbulence_fields!
export compute_production_term, compute_dissipation_term, apply_turbulence_boundary_conditions!

# Re-export from KEpsilonModel module
using .KEpsilonModel
export KETurbulenceModel, create_ke_model

end # module TurbulenceModels
