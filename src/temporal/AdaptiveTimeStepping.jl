"""
AdaptiveTimeStepping.jl

Adaptive time stepping algorithms for CFD simulations.
Essential for maintaining stability, accuracy, and computational efficiency.

Key Features:
- Multiple time integration schemes (Euler, RK2, RK4, BDF)
- Automatic time step adaptation based on multiple criteria
- CFL-based stability control
- Error estimation and temporal accuracy control
- Solution monitoring and divergence detection
- Convergence-based time step optimization

Accuracy Focus:
- Conservative time step selection prioritizing stability
- Multiple error estimation techniques
- Robust handling of stiff equations
- Extensive validation against analytical solutions
"""

module AdaptiveTimeStepping

using LinearAlgebra
using Printf

# ============================================================================
# TIME INTEGRATION SCHEMES
# ============================================================================

"""
Abstract type for time integration schemes
"""
abstract type TimeIntegrationScheme end

"""
Forward Euler scheme (1st order)
"""
struct ForwardEuler <: TimeIntegrationScheme end

"""
Runge-Kutta 2nd order scheme
"""
struct RungeKutta2 <: TimeIntegrationScheme end

"""
Runge-Kutta 4th order scheme
"""
struct RungeKutta4 <: TimeIntegrationScheme end

"""
Backward Euler scheme (1st order, implicit)
"""
struct BackwardEuler <: TimeIntegrationScheme end

"""
Crank-Nicolson scheme (2nd order, implicit)
"""
struct CrankNicolson <: TimeIntegrationScheme end

# ============================================================================
# ADAPTIVE TIME STEPPING CONFIGURATION
# ============================================================================

"""
Configuration for adaptive time stepping
"""
struct AdaptiveTimeConfig
    # Time integration scheme
    integration_scheme::TimeIntegrationScheme
    
    # Time step control parameters
    initial_dt::Float64                        # Initial time step
    min_dt::Float64                           # Minimum allowed time step
    max_dt::Float64                           # Maximum allowed time step
    dt_safety_factor::Float64                 # Safety factor for time step adaptation
    
    # CFL-based adaptation
    target_cfl::Float64                       # Target CFL number
    max_cfl::Float64                          # Maximum allowed CFL number
    cfl_safety_margin::Float64                # Safety margin for CFL calculation
    
    # Error-based adaptation
    use_error_control::Bool                   # Enable error-based time step control
    target_error::Float64                     # Target temporal discretization error
    error_tolerance::Float64                  # Error tolerance for time step adjustment
    
    # Solution monitoring
    max_solution_change::Float64              # Maximum allowed solution change per step
    divergence_threshold::Float64             # Threshold for detecting solution divergence
    
    # Adaptation aggressiveness
    dt_increase_factor::Float64               # Factor for increasing time step
    dt_decrease_factor::Float64               # Factor for decreasing time step
    min_steps_before_increase::Int            # Minimum steps before allowing increase
    
    # Output and monitoring
    print_dt_changes::Bool                    # Print time step changes
    monitor_stability::Bool                   # Monitor solution stability
    
    function AdaptiveTimeConfig(;
        integration_scheme::TimeIntegrationScheme = RungeKutta2(),
        initial_dt::Float64 = 1e-3,
        min_dt::Float64 = 1e-8,
        max_dt::Float64 = 1.0,
        dt_safety_factor::Float64 = 0.8,
        target_cfl::Float64 = 0.5,
        max_cfl::Float64 = 1.0,
        cfl_safety_margin::Float64 = 0.1,
        use_error_control::Bool = true,
        target_error::Float64 = 1e-5,
        error_tolerance::Float64 = 2.0,
        max_solution_change::Float64 = 0.1,
        divergence_threshold::Float64 = 1e6,
        dt_increase_factor::Float64 = 1.2,
        dt_decrease_factor::Float64 = 0.5,
        min_steps_before_increase::Int = 5,
        print_dt_changes::Bool = true,
        monitor_stability::Bool = true
    )
        new(integration_scheme, initial_dt, min_dt, max_dt, dt_safety_factor,
            target_cfl, max_cfl, cfl_safety_margin,
            use_error_control, target_error, error_tolerance,
            max_solution_change, divergence_threshold,
            dt_increase_factor, dt_decrease_factor, min_steps_before_increase,
            print_dt_changes, monitor_stability)
    end
end

"""
Time stepping state and history
"""
mutable struct TimeSteppingState
    current_time::Float64                     # Current simulation time
    current_dt::Float64                       # Current time step
    step_number::Int                          # Current step number
    
    # Adaptation history
    dt_history::Vector{Float64}               # Time step history
    cfl_history::Vector{Float64}              # CFL number history
    error_history::Vector{Float64}            # Error estimate history
    
    # Solution monitoring
    solution_norm_history::Vector{Float64}    # Solution norm history
    max_change_history::Vector{Float64}       # Maximum solution change history
    
    # Adaptation statistics
    dt_increases::Int                         # Number of time step increases
    dt_decreases::Int                         # Number of time step decreases
    rejected_steps::Int                       # Number of rejected time steps
    steps_since_last_increase::Int            # Steps since last time step increase
    
    # Stability monitoring
    is_stable::Bool                           # Current stability status
    consecutive_stable_steps::Int             # Number of consecutive stable steps
    
    function TimeSteppingState(initial_dt::Float64)
        new(0.0, initial_dt, 0,
            Float64[], Float64[], Float64[],
            Float64[], Float64[],
            0, 0, 0, 0,
            true, 0)
    end
end

# ============================================================================
# TIME INTEGRATION IMPLEMENTATIONS
# ============================================================================

"""
Apply single time step using the specified integration scheme
"""
function apply_time_step!(
    u::Vector{Float64},                       # Solution vector (modified in-place)
    rhs_function::Function,                   # RHS evaluation function
    dt::Float64,                              # Time step size
    scheme::TimeIntegrationScheme,            # Integration scheme
    time::Float64 = 0.0                       # Current time
)
    
    if scheme isa ForwardEuler
        return apply_forward_euler!(u, rhs_function, dt, time)
    elseif scheme isa RungeKutta2
        return apply_runge_kutta_2!(u, rhs_function, dt, time)
    elseif scheme isa RungeKutta4
        return apply_runge_kutta_4!(u, rhs_function, dt, time)
    elseif scheme isa BackwardEuler
        return apply_backward_euler!(u, rhs_function, dt, time)
    elseif scheme isa CrankNicolson
        return apply_crank_nicolson!(u, rhs_function, dt, time)
    else
        error("Unknown time integration scheme: $(typeof(scheme))")
    end
end

"""
Forward Euler: u^{n+1} = u^n + dt * f(u^n, t^n)
"""
function apply_forward_euler!(
    u::Vector{Float64},
    rhs_function::Function,
    dt::Float64,
    time::Float64
)
    dudt = rhs_function(u, time)
    u .+= dt * dudt
    return dudt  # Return RHS for error estimation
end

"""
Runge-Kutta 2nd order (Heun's method)
"""
function apply_runge_kutta_2!(
    u::Vector{Float64},
    rhs_function::Function,
    dt::Float64,
    time::Float64
)
    u_original = copy(u)
    
    # Stage 1
    k1 = rhs_function(u, time)
    
    # Stage 2
    u_temp = u_original + dt * k1
    k2 = rhs_function(u_temp, time + dt)
    
    # Final update
    u .= u_original + 0.5 * dt * (k1 + k2)
    
    return k1  # Return first stage for error estimation
end

"""
Runge-Kutta 4th order (classical RK4)
"""
function apply_runge_kutta_4!(
    u::Vector{Float64},
    rhs_function::Function,
    dt::Float64,
    time::Float64
)
    u_original = copy(u)
    
    # Stage 1
    k1 = rhs_function(u_original, time)
    
    # Stage 2
    u_temp = u_original + 0.5 * dt * k1
    k2 = rhs_function(u_temp, time + 0.5 * dt)
    
    # Stage 3
    u_temp = u_original + 0.5 * dt * k2
    k3 = rhs_function(u_temp, time + 0.5 * dt)
    
    # Stage 4
    u_temp = u_original + dt * k3
    k4 = rhs_function(u_temp, time + dt)
    
    # Final update
    u .= u_original + (dt / 6.0) * (k1 + 2*k2 + 2*k3 + k4)
    
    return k1  # Return first stage for error estimation
end

"""
Backward Euler (requires linear solve)
"""
function apply_backward_euler!(
    u::Vector{Float64},
    rhs_function::Function,
    dt::Float64,
    time::Float64
)
    # Simplified implementation - assumes linear RHS
    # In practice, this would require Newton iteration for nonlinear problems
    
    u_old = copy(u)
    dudt = rhs_function(u, time + dt)  # Evaluate at next time level
    u .= u_old + dt * dudt
    
    return dudt
end

"""
Crank-Nicolson (trapezoidal rule)
"""
function apply_crank_nicolson!(
    u::Vector{Float64},
    rhs_function::Function,
    dt::Float64,
    time::Float64
)
    u_old = copy(u)
    
    # Predictor step (explicit)
    dudt_old = rhs_function(u_old, time)
    u_pred = u_old + dt * dudt_old
    
    # Corrector step (implicit approximation)
    dudt_new = rhs_function(u_pred, time + dt)
    u .= u_old + 0.5 * dt * (dudt_old + dudt_new)
    
    return dudt_old
end

# ============================================================================
# CFL NUMBER CALCULATION
# ============================================================================

"""
Calculate CFL number for the current solution and time step
"""
function calculate_cfl_number(
    u::Vector{Float64},                       # Solution vector
    velocity_function::Function,             # Function to extract velocity
    mesh_spacing::Union{Float64, Vector{Float64}}, # Mesh spacing (uniform or per-cell)
    dt::Float64                               # Current time step
)
    
    # Extract velocity field
    velocities = velocity_function(u)
    
    # Calculate maximum velocity magnitude
    max_velocity = maximum(norm(v) for v in velocities)
    
    # Get minimum mesh spacing
    min_spacing = isa(mesh_spacing, Float64) ? mesh_spacing : minimum(mesh_spacing)
    
    # CFL number
    cfl = max_velocity * dt / min_spacing
    
    return cfl, max_velocity
end

# ============================================================================
# ERROR ESTIMATION
# ============================================================================

"""
Estimate temporal discretization error using Richardson extrapolation
"""
function estimate_temporal_error(
    u_current::Vector{Float64},               # Current solution
    rhs_function::Function,                   # RHS function
    dt::Float64,                              # Current time step
    scheme::TimeIntegrationScheme,            # Integration scheme
    time::Float64                             # Current time
)
    
    # Take one step with current time step
    u_single_step = copy(u_current)
    apply_time_step!(u_single_step, rhs_function, dt, scheme, time)
    
    # Take two steps with half time step
    u_double_step = copy(u_current)
    apply_time_step!(u_double_step, rhs_function, dt/2, scheme, time)
    apply_time_step!(u_double_step, rhs_function, dt/2, scheme, time + dt/2)
    
    # Error estimate (Richardson extrapolation)
    if scheme isa ForwardEuler || scheme isa BackwardEuler
        # First-order schemes
        error_estimate = norm(u_double_step - u_single_step)
    else
        # Second-order schemes
        error_estimate = norm(u_double_step - u_single_step) / 3.0
    end
    
    return error_estimate
end

# ============================================================================
# ADAPTIVE TIME STEP CONTROL
# ============================================================================

"""
Determine new time step based on multiple criteria
"""
function adapt_time_step(
    u_current::Vector{Float64},               # Current solution
    u_previous::Vector{Float64},              # Previous solution
    rhs_function::Function,                   # RHS function
    velocity_function::Function,             # Velocity extraction function
    mesh_spacing::Union{Float64, Vector{Float64}}, # Mesh spacing
    state::TimeSteppingState,                 # Time stepping state
    config::AdaptiveTimeConfig                # Configuration
)
    
    current_dt = state.current_dt
    new_dt = current_dt
    adaptation_reason = "no_change"
    
    # 1. CFL-based adaptation
    cfl, max_velocity = calculate_cfl_number(u_current, velocity_function, mesh_spacing, current_dt)
    
    if cfl > config.max_cfl
        # Reduce time step for stability
        dt_cfl = config.target_cfl * current_dt / cfl
        new_dt = min(new_dt, dt_cfl)
        adaptation_reason = "cfl_stability"
    elseif cfl < config.target_cfl * 0.5 && state.steps_since_last_increase >= config.min_steps_before_increase
        # Increase time step for efficiency
        dt_cfl = config.target_cfl * current_dt / cfl
        new_dt = max(new_dt, min(dt_cfl, current_dt * config.dt_increase_factor))
        adaptation_reason = "cfl_efficiency"
    end
    
    # 2. Error-based adaptation
    if config.use_error_control
        error_estimate = estimate_temporal_error(
            u_current, rhs_function, current_dt, config.integration_scheme, state.current_time
        )
        
        if error_estimate > config.target_error * config.error_tolerance
            # Reduce time step for accuracy
            error_factor = sqrt(config.target_error / (error_estimate + 1e-15))
            dt_error = current_dt * min(error_factor, config.dt_decrease_factor)
            new_dt = min(new_dt, dt_error)
            adaptation_reason = "error_control"
        end
        
        # Store error in history
        push!(state.error_history, error_estimate)
    end
    
    # 3. Solution change monitoring
    solution_change = norm(u_current - u_previous) / (norm(u_current) + 1e-15)
    
    if solution_change > config.max_solution_change
        # Large solution change - reduce time step
        change_factor = config.max_solution_change / (solution_change + 1e-15)
        dt_change = current_dt * min(change_factor, config.dt_decrease_factor)
        new_dt = min(new_dt, dt_change)
        adaptation_reason = "solution_change"
    end
    
    # 4. Divergence detection
    solution_norm = norm(u_current)
    if solution_norm > config.divergence_threshold
        new_dt = current_dt * config.dt_decrease_factor
        adaptation_reason = "divergence_prevention"
        state.is_stable = false
    else
        state.is_stable = true
        state.consecutive_stable_steps += 1
    end
    
    # 5. Apply safety factor and limits
    new_dt *= config.dt_safety_factor
    new_dt = max(config.min_dt, min(config.max_dt, new_dt))
    
    # Update statistics
    if new_dt > current_dt * 1.01  # Increase
        state.dt_increases += 1
        state.steps_since_last_increase = 0
    elseif new_dt < current_dt * 0.99  # Decrease
        state.dt_decreases += 1
        state.steps_since_last_increase += 1
    else
        state.steps_since_last_increase += 1
    end
    
    # Update history
    push!(state.dt_history, new_dt)
    push!(state.cfl_history, cfl)
    push!(state.solution_norm_history, solution_norm)
    push!(state.max_change_history, solution_change)
    
    # Print adaptation information
    if config.print_dt_changes && abs(new_dt - current_dt) / current_dt > 0.01
        @printf "   🕒 Time step: %.2e → %.2e (%.1fx) [%s]\\n" current_dt new_dt (new_dt/current_dt) adaptation_reason
        @printf "      CFL: %.3f, Error: %.2e, Change: %.2e\\n" cfl (length(state.error_history) > 0 ? state.error_history[end] : 0.0) solution_change
    end
    
    return new_dt, cfl, adaptation_reason
end

# ============================================================================
# MAIN ADAPTIVE TIME STEPPING FUNCTION
# ============================================================================

"""
Perform adaptive time stepping for one time step
"""
function adaptive_time_step!(
    u::Vector{Float64},                       # Solution vector (modified in-place)
    rhs_function::Function,                   # RHS evaluation function
    velocity_function::Function,             # Velocity extraction function
    mesh_spacing::Union{Float64, Vector{Float64}}, # Mesh spacing
    state::TimeSteppingState,                 # Time stepping state
    config::AdaptiveTimeConfig                # Configuration
)
    
    u_previous = copy(u)
    
    # Determine new time step
    new_dt, cfl, reason = adapt_time_step(
        u, u_previous, rhs_function, velocity_function, mesh_spacing, state, config
    )
    
    # Update time step
    state.current_dt = new_dt
    
    # Apply time integration
    dudt = apply_time_step!(u, rhs_function, new_dt, config.integration_scheme, state.current_time)
    
    # Update time and step number
    state.current_time += new_dt
    state.step_number += 1
    
    return new_dt, cfl, reason
end

# ============================================================================
# VALIDATION AND TESTING
# ============================================================================

"""
Test adaptive time stepping on the 1D advection equation
"""
function validate_adaptive_time_stepping()
    println("🔬 Validating Adaptive Time Stepping")
    println("=" ^ 50)
    println("Test: 1D linear advection equation")
    
    # Test problem: ∂u/∂t + c*∂u/∂x = 0 with periodic BC
    n = 100
    L = 1.0
    dx = L / n
    c = 1.0  # Advection velocity
    
    # Initial condition: Gaussian pulse
    x = range(0, L, length=n+1)[1:end-1]  # Periodic, exclude last point
    u0 = exp.(-50 * (x .- 0.3).^2)
    
    println("   Grid: $n points, dx = $(dx), c = $c")
    
    # RHS function for advection equation (upwind scheme)
    function advection_rhs(u::Vector{Float64}, t::Float64)
        dudt = zeros(length(u))
        for i in 1:length(u)
            i_upstream = i == 1 ? length(u) : i - 1  # Periodic BC
            dudt[i] = -c * (u[i] - u[i_upstream]) / dx
        end
        return dudt
    end
    
    # Velocity function (constant for this test)
    velocity_function(u) = [[c, 0.0, 0.0] for _ in 1:length(u)]
    
    # Test different configurations
    test_configs = [
        ("Conservative", AdaptiveTimeConfig(
            integration_scheme=RungeKutta2(),
            target_cfl=0.3,
            max_cfl=0.8,
            dt_safety_factor=0.8
        )),
        ("Efficient", AdaptiveTimeConfig(
            integration_scheme=RungeKutta4(),
            target_cfl=0.7,
            max_cfl=0.9,
            dt_safety_factor=0.9,
            dt_increase_factor=1.5
        )),
        ("Error-controlled", AdaptiveTimeConfig(
            integration_scheme=RungeKutta2(),
            use_error_control=true,
            target_error=1e-6,
            target_cfl=0.5
        ))
    ]
    
    final_time = 0.5
    results = Dict()
    
    for (name, config) in test_configs
        println("\\n📊 Testing: $name configuration")
        
        # Initialize
        u = copy(u0)
        state = TimeSteppingState(config.initial_dt)
        
        step_count = 0
        max_steps = 10000
        
        # Time integration loop
        while state.current_time < final_time && step_count < max_steps
            adaptive_time_step!(u, advection_rhs, velocity_function, dx, state, config)
            step_count += 1
            
            # Print progress
            if step_count % 50 == 0
                @printf "      Step %d: t = %.3f, dt = %.2e, CFL = %.3f\\n" step_count state.current_time state.current_dt state.cfl_history[end]
            end
        end
        
        # Calculate final error (analytical solution is the shifted initial condition)
        shift = mod(c * state.current_time, L)
        n_shift = round(Int, shift / dx)
        u_exact = circshift(u0, -n_shift)
        
        error_L2 = sqrt(sum((u - u_exact).^2) * dx)
        error_max = maximum(abs.(u - u_exact))
        
        results[name] = (
            steps=step_count,
            final_time=state.current_time,
            error_L2=error_L2,
            error_max=error_max,
            dt_changes=state.dt_increases + state.dt_decreases,
            avg_dt=sum(state.dt_history) / length(state.dt_history),
            min_dt=minimum(state.dt_history),
            max_dt=maximum(state.dt_history)
        )
        
        println("      ✅ Completed: $(step_count) steps, final time = $(state.current_time)")
        @printf "         L₂ error: %.2e, Max error: %.2e\\n" error_L2 error_max
        @printf "         Time step changes: %d, Average dt: %.2e\\n" (state.dt_increases + state.dt_decreases) (sum(state.dt_history) / length(state.dt_history))
    end
    
    # Summary
    println("\\n📊 Validation Summary:")
    for (name, result) in results
        println("   $name:")
        @printf "      Steps: %d, L₂ error: %.2e\\n" result.steps result.error_L2
        @printf "      Time step range: [%.2e, %.2e]\\n" result.min_dt result.max_dt
    end
    
    # Check if all tests passed reasonable criteria
    all_passed = all(r.error_L2 < 0.1 for r in values(results))
    
    if all_passed
        println("   ✅ All adaptive time stepping tests passed")
    else
        println("   ⚠️ Some tests show higher than expected errors")
    end
    
    return results
end

# ============================================================================
# EXPORTS
# ============================================================================

export TimeIntegrationScheme, ForwardEuler, RungeKutta2, RungeKutta4, BackwardEuler, CrankNicolson
export AdaptiveTimeConfig, TimeSteppingState
export apply_time_step!, adaptive_time_step!
export calculate_cfl_number, estimate_temporal_error
export validate_adaptive_time_stepping

end # module AdaptiveTimeStepping