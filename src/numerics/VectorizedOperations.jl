# =========================================================================
# VectorizedOperations Module - SIMD-optimized numerical operations
# =========================================================================

"""
    VectorizedOperations

This module provides SIMD-optimized implementations of key numerical operations.
These optimized operations are critical for achieving high performance in
computational fluid dynamics simulations, particularly for OpenFOAM interoperability.

# Performance Notes
These implementations use LoopVectorization.jl to achieve near-optimal SIMD utilization
on modern CPUs. They are designed to be drop-in replacements for standard operations
with significantly better performance.

# OpenFOAM Compatibility
These operations are designed to match the behavior of equivalent OpenFOAM operations
while providing <PERSON>'s performance advantages.
"""
module VectorizedOperations

# External dependencies
using LoopVectorization  # For SIMD operations with @turbo macro
using SparseArrays       # For sparse matrix operations
using LinearAlgebra      # For vector operations
using StaticArrays       # For small fixed-size vectors
using Base.Threads       # For multi-threading

# Export key functions
export vectorized_matrix_vector_product!
export vectorized_dot_product
export vectorized_axpy!
export vectorized_interpolate_to_faces!
export vectorized_divergence_calculation!

"""
    vectorized_matrix_vector_product!(y::Vector{Float64}, A::SparseMatrixCSC{Float64, Int}, x::Vector{Float64})

Compute y = A*x using SIMD vectorization for better performance.
"""
function vectorized_matrix_vector_product!(y::Vector{Float64}, A::SparseMatrixCSC{Float64, Int}, x::Vector{Float64})
    rows, cols, vals = findnz(A)
    
    @turbo for i in eachindex(y)
        y[i] = 0.0
    end
    
    @turbo for k in eachindex(vals)
        i = rows[k]
        j = cols[k]
        y[i] += vals[k] * x[j]
    end
end

"""
    vectorized_dot_product(a::Vector{Float64}, b::Vector{Float64})

Compute the dot product of two vectors using SIMD vectorization.
"""
function vectorized_dot_product(a::Vector{Float64}, b::Vector{Float64})
    result = 0.0
    @turbo for i in eachindex(a, b)
        result += a[i] * b[i]
    end
    return result
end

"""
    vectorized_axpy!(y::Vector{Float64}, a::Float64, x::Vector{Float64})

Compute y = a*x + y using SIMD vectorization.
"""
function vectorized_axpy!(y::Vector{Float64}, a::Float64, x::Vector{Float64})
    @turbo for i in eachindex(x, y)
        y[i] += a * x[i]
    end
end

"""
    vectorized_interpolate_to_faces!(face_values::Vector{Float64}, cell_values::Vector{Float64}, 
                                   face_owners::Vector{Int}, face_neighbors::Vector{Int})

Interpolate cell values to faces using SIMD vectorization.
"""
function vectorized_interpolate_to_faces!(face_values::Vector{Float64}, cell_values::Vector{Float64}, 
                                        face_owners::Vector{Int}, face_neighbors::Vector{Int})
    n_faces = length(face_values)
    
    # Process internal faces (with neighbors)
    internal_faces = findall(x -> x > 0, face_neighbors)
    @turbo for i in internal_faces
        owner = face_owners[i]
        neighbor = face_neighbors[i]
        face_values[i] = 0.5 * (cell_values[owner] + cell_values[neighbor])
    end
    
    # Process boundary faces (without neighbors)
    boundary_faces = findall(x -> x <= 0, face_neighbors)
    @turbo for i in boundary_faces
        owner = face_owners[i]
        face_values[i] = cell_values[owner]
    end
end

"""
    vectorized_gradient_calculation!(gradients::Matrix{Float64}, face_values::Vector{Float64}, 
                                   face_normals::Matrix{Float64}, face_owners::Vector{Int},
                                   face_neighbors::Vector{Int}, cell_faces::Vector{Vector{Int}},
                                   cell_volumes::Vector{Float64})

Calculate gradients using SIMD vectorization where possible.
"""
function vectorized_gradient_calculation!(gradients::Matrix{Float64}, face_values::Vector{Float64}, 
                                        face_normals::Matrix{Float64}, face_owners::Vector{Int},
                                        face_neighbors::Vector{Int}, cell_faces::Vector{Vector{Int}},
                                        cell_volumes::Vector{Float64})
    n_cells = length(cell_volumes)
    
    # Initialize gradients to zero
    @turbo for i in 1:n_cells
        gradients[1, i] = 0.0
        gradients[2, i] = 0.0
        gradients[3, i] = 0.0
    end
    
    # Adaptive strategy based on mesh size
    if n_cells < 1000  # Very small meshes
        # Use a simpler approach for very small meshes
        # This avoids the overhead of the more complex vectorization
        for cell_idx in 1:n_cells
            for face_idx in cell_faces[cell_idx]
                # Check if cell is owner or neighbor
                sign = (face_owners[face_idx] == cell_idx) ? 1.0 : -1.0
                
                # Add contribution to gradient (simple loop is faster for small meshes)
                face_value = face_values[face_idx]
                gradients[1, cell_idx] += sign * face_value * face_normals[1, face_idx]
                gradients[2, cell_idx] += sign * face_value * face_normals[2, face_idx]
                gradients[3, cell_idx] += sign * face_value * face_normals[3, face_idx]
            end
            
            # Divide by cell volume
            inv_vol = 1.0 / cell_volumes[cell_idx]
            gradients[1, cell_idx] *= inv_vol
            gradients[2, cell_idx] *= inv_vol
            gradients[3, cell_idx] *= inv_vol
        end
    elseif n_cells < 50000  # Small to medium meshes - optimized for this range
        # Use a hybrid approach for medium meshes that's optimized for cache usage
        # Pre-compute face signs for faster lookup
        face_signs = zeros(length(face_values))
        
        # First pass: compute face signs
        for cell_idx in 1:n_cells
            for face_idx in cell_faces[cell_idx]
                face_signs[face_idx] = (face_owners[face_idx] == cell_idx) ? 1.0 : -1.0
            end
        end
        
        # Second pass: compute gradients with better cache locality
        # Process cells in blocks for better cache performance
        block_size = 64  # Typical L1 cache line size in elements
        for block_start in 1:block_size:n_cells
            block_end = min(block_start + block_size - 1, n_cells)
            
            # Process all faces for cells in this block
            for cell_idx in block_start:block_end
                # Get all faces for this cell
                faces = cell_faces[cell_idx]
                
                # Process faces in a vectorized fashion
                cell_grad_x = 0.0
                cell_grad_y = 0.0
                cell_grad_z = 0.0
                
                for face_idx in faces
                    sign = face_signs[face_idx]
                    val = face_values[face_idx]
                    
                    cell_grad_x += sign * val * face_normals[1, face_idx]
                    cell_grad_y += sign * val * face_normals[2, face_idx]
                    cell_grad_z += sign * val * face_normals[3, face_idx]
                end
                
                # Divide by cell volume
                inv_vol = 1.0 / cell_volumes[cell_idx]
                gradients[1, cell_idx] = cell_grad_x * inv_vol
                gradients[2, cell_idx] = cell_grad_y * inv_vol
                gradients[3, cell_idx] = cell_grad_z * inv_vol
            end
        end
    else  # Large meshes
        # For large meshes, use more aggressive vectorization
        # Pre-compute face signs for faster lookup
        face_signs = zeros(length(face_values))
        for cell_idx in 1:n_cells
            for face_idx in cell_faces[cell_idx]
                face_signs[face_idx] = (face_owners[face_idx] == cell_idx) ? 1.0 : -1.0
            end
        end
        
        # Calculate gradients using Gauss theorem with vectorization
        Threads.@threads for cell_idx in 1:n_cells
            # Process faces in chunks for better cache locality
            faces = cell_faces[cell_idx]
            n_faces = length(faces)
            
            # Process 4 faces at a time if possible
            i = 1
            while i <= n_faces - 3
                for d in 1:3
                    gradients[d, cell_idx] += face_signs[faces[i]] * face_values[faces[i]] * face_normals[d, faces[i]]
                    gradients[d, cell_idx] += face_signs[faces[i+1]] * face_values[faces[i+1]] * face_normals[d, faces[i+1]]
                    gradients[d, cell_idx] += face_signs[faces[i+2]] * face_values[faces[i+2]] * face_normals[d, faces[i+2]]
                    gradients[d, cell_idx] += face_signs[faces[i+3]] * face_values[faces[i+3]] * face_normals[d, faces[i+3]]
                end
                i += 4
            end
            
            # Process remaining faces
            while i <= n_faces
                face_idx = faces[i]
                sign = face_signs[face_idx]
                
                @turbo for d in 1:3
                    gradients[d, cell_idx] += sign * face_values[face_idx] * face_normals[d, face_idx]
                end
                i += 1
            end
            
            # Divide by cell volume
            @turbo for d in 1:3
                gradients[d, cell_idx] /= cell_volumes[cell_idx]
            end
        end
    end
end

"""
    vectorized_divergence_calculation!(divergence::Vector{Float64}, vector_field::Matrix{Float64}, 
                                     face_normals::Matrix{Float64}, face_owners::Vector{Int},
                                     face_neighbors::Vector{Int}, cell_faces::Vector{Vector{Int}},
                                     cell_volumes::Vector{Float64})

Calculate divergence using SIMD vectorization where possible.
"""
function vectorized_divergence_calculation!(divergence::Vector{Float64}, vector_field::Matrix{Float64}, 
                                          face_normals::Matrix{Float64}, face_owners::Vector{Int},
                                          face_neighbors::Vector{Int}, cell_faces::Vector{Vector{Int}},
                                          cell_volumes::Vector{Float64})
    n_cells = length(cell_volumes)
    
    # Initialize divergence to zero
    @turbo for i in 1:n_cells
        divergence[i] = 0.0
    end
    
    # Calculate divergence using Gauss theorem
    for cell_idx in 1:n_cells
        for face_idx in cell_faces[cell_idx]
            # Check if cell is owner or neighbor
            sign = (face_owners[face_idx] == cell_idx) ? 1.0 : -1.0
            
            # Add contribution to divergence
            flux = 0.0
            @turbo for d in 1:3
                flux += vector_field[d, face_idx] * face_normals[d, face_idx]
            end
            
            divergence[cell_idx] += sign * flux
        end
        
        # Divide by cell volume
        divergence[cell_idx] /= cell_volumes[cell_idx]
    end
end

# These exports are redundant since we already have them at the top of the module
# But we'll keep them for backward compatibility
export vectorized_matrix_vector_product!, vectorized_dot_product, vectorized_axpy!
export vectorized_interpolate_to_faces!, vectorized_gradient_calculation!
export vectorized_divergence_calculation!

end # module VectorizedOperations
