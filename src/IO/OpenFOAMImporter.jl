module OpenFOAMImporter

using ..UnstructuredMesh: Mesh
using ..BoundaryConditions
using SparseArrays

export import_openfoam_case, OpenFOAMCase, parse_openfoam_dict
export import_decomposed_case, read_processor_case, reconstruct_case
export read_openfoam_field_file, write_openfoam_field_file
export detect_case_type, list_processor_directories

struct OpenFOAMCase
    case_path::String
    mesh_data::Dict{String, Any}
    fields::Dict{String, Any}
    boundary_conditions::Dict{String, Any}
    control_dict::Dict{String, Any}
    fv_schemes::Dict{String, Any}
    fv_solution::Dict{String, Any}
end

"""
Import an OpenFOAM case directory into JuliaFOAM format
"""
function import_openfoam_case(case_path::String)
    println("📁 Importing OpenFOAM case from: $case_path")
    
    if !isdir(case_path)
        error("Case directory not found: $case_path")
    end
    
    # Read mesh
    mesh_data = read_openfoam_mesh(case_path)
    
    # Read fields (0 directory)
    fields = read_initial_fields(case_path)
    
    # Read boundary conditions
    boundary_conditions = extract_boundary_conditions(case_path, fields)
    
    # Read control settings
    control_dict = read_control_dict(case_path)
    
    # Read numerical schemes
    fv_schemes = read_fv_schemes(case_path)
    
    # Read solver settings
    fv_solution = read_fv_solution(case_path)
    
    return OpenFOAMCase(
        case_path,
        mesh_data,
        fields,
        boundary_conditions,
        control_dict,
        fv_schemes,
        fv_solution
    )
end

"""
Read OpenFOAM mesh from constant/polyMesh
"""
function read_openfoam_mesh(case_path::String)
    mesh_path = joinpath(case_path, "constant", "polyMesh")
    
    if !isdir(mesh_path)
        # Try blockMeshDict instead
        block_mesh_dict = joinpath(case_path, "system", "blockMeshDict")
        if isfile(block_mesh_dict)
            return read_block_mesh_dict(block_mesh_dict)
        else
            error("No mesh found in $case_path")
        end
    end
    
    mesh_data = Dict{String, Any}()
    
    # Read points
    points_file = joinpath(mesh_path, "points")
    if isfile(points_file)
        mesh_data["points"] = read_openfoam_points(points_file)
    end
    
    # Read faces
    faces_file = joinpath(mesh_path, "faces")
    if isfile(faces_file)
        mesh_data["faces"] = read_openfoam_faces(faces_file)
    end
    
    # Read cells (owner/neighbour)
    owner_file = joinpath(mesh_path, "owner")
    neighbour_file = joinpath(mesh_path, "neighbour")
    if isfile(owner_file) && isfile(neighbour_file)
        mesh_data["owner"] = read_openfoam_list(owner_file)
        mesh_data["neighbour"] = read_openfoam_list(neighbour_file)
    end
    
    # Read boundary
    boundary_file = joinpath(mesh_path, "boundary")
    if isfile(boundary_file)
        mesh_data["boundary"] = read_openfoam_boundary(boundary_file)
    end
    
    return mesh_data
end

"""
Read blockMeshDict and interpret the mesh specification
"""
function read_block_mesh_dict(dict_file::String)
    content = read(dict_file, String)
    dict_data = parse_openfoam_dict(content)
    
    mesh_data = Dict{String, Any}()
    
    # Extract vertices
    if haskey(dict_data, "vertices")
        vertices = parse_vertex_list(dict_data["vertices"])
        mesh_data["vertices"] = vertices
    end
    
    # Extract blocks
    if haskey(dict_data, "blocks")
        blocks = parse_block_list(dict_data["blocks"])
        mesh_data["blocks"] = blocks
        
        # Generate structured mesh from blocks
        if length(blocks) == 1 && blocks[1]["type"] == "hex"
            mesh_data["structured_mesh"] = generate_structured_mesh(blocks[1], vertices)
        end
    end
    
    # Extract boundary specifications
    if haskey(dict_data, "boundary")
        mesh_data["boundary_specs"] = parse_boundary_specs(dict_data["boundary"])
    end
    
    return mesh_data
end

"""
Parse OpenFOAM dictionary format
"""
function parse_openfoam_dict(content::String)
    dict_data = Dict{String, Any}()
    
    # Remove comments
    content = replace(content, r"//.*$"m => "")
    content = replace(content, r"/\*.*?\*/"s => "")
    
    # Simple parser for key-value pairs
    lines = split(content, '\n')
    current_key = ""
    bracket_level = 0
    current_value = String[]
    
    for line in lines
        line = strip(line)
        if isempty(line)
            continue
        end
        
        # Count brackets
        open_brackets = count('{', line)
        close_brackets = count('}', line)
        bracket_level += open_brackets - close_brackets
        
        # Check for key-value start
        if bracket_level == 0 && contains(line, r"^\w+\s+")
            if !isempty(current_key) && !isempty(current_value)
                dict_data[current_key] = join(current_value, '\n')
            end
            parts = split(line, r"\s+", limit=2)
            current_key = parts[1]
            current_value = length(parts) > 1 ? [parts[2]] : String[]
        elseif !isempty(current_key)
            push!(current_value, line)
        end
    end
    
    # Store last key-value pair
    if !isempty(current_key) && !isempty(current_value)
        dict_data[current_key] = join(current_value, '\n')
    end
    
    return dict_data
end

"""
Parse vertex list from blockMeshDict
"""
function parse_vertex_list(vertex_string::String)
    vertices = []
    
    # Extract vertex coordinates
    vertex_pattern = r"\(\s*([-\d.]+)\s+([-\d.]+)\s+([-\d.]+)\s*\)"
    for match in eachmatch(vertex_pattern, vertex_string)
        x = parse(Float64, match.captures[1])
        y = parse(Float64, match.captures[2])
        z = parse(Float64, match.captures[3])
        push!(vertices, [x, y, z])
    end
    
    return vertices
end

"""
Parse block list from blockMeshDict
"""
function parse_block_list(block_string::String)
    blocks = []
    
    # Extract hex blocks
    hex_pattern = r"hex\s*\(([^)]+)\)\s*\(([^)]+)\)\s*simpleGrading\s*\(([^)]+)\)"
    for match in eachmatch(hex_pattern, block_string)
        vertex_indices = parse.(Int, split(match.captures[1]))
        cell_counts = parse.(Int, split(match.captures[2]))
        grading = parse.(Float64, split(match.captures[3]))
        
        push!(blocks, Dict(
            "type" => "hex",
            "vertices" => vertex_indices,
            "cells" => cell_counts,
            "grading" => grading
        ))
    end
    
    return blocks
end

"""
Generate structured mesh from hex block specification
"""
function generate_structured_mesh(block::Dict, vertices::Vector)
    nx, ny, nz = block["cells"]
    vertex_ids = block["vertices"] .+ 1  # Convert to 1-based indexing
    
    # Get corner vertices
    corners = [vertices[id] for id in vertex_ids]
    
    # For 2D cavity case (assuming z-direction is thin)
    if nz == 1
        # Generate 2D mesh
        x_min, x_max = minimum(c[1] for c in corners), maximum(c[1] for c in corners)
        y_min, y_max = minimum(c[2] for c in corners), maximum(c[2] for c in corners)
        
        x = range(x_min, x_max, length=nx+1)
        y = range(y_min, y_max, length=ny+1)
        
        points = []
        for j in 1:ny+1, i in 1:nx+1
            push!(points, [x[i], y[j], 0.0])
        end
        
        # Generate cell connectivity
        cells = []
        for j in 1:ny, i in 1:nx
            # Quad cell vertices (counter-clockwise)
            v1 = (j-1)*(nx+1) + i
            v2 = v1 + 1
            v3 = v1 + nx + 2
            v4 = v1 + nx + 1
            push!(cells, [v1, v2, v3, v4])
        end
        
        return Dict(
            "points" => points,
            "cells" => cells,
            "nx" => nx,
            "ny" => ny,
            "nz" => nz,
            "cell_type" => "quad"
        )
    else
        # 3D mesh generation would go here
        error("3D mesh generation not yet implemented")
    end
end

"""
Parse boundary specifications from blockMeshDict
"""
function parse_boundary_specs(boundary_string::String)
    boundaries = Dict{String, Any}()
    
    # Simple pattern matching for boundary definitions
    boundary_pattern = r"(\w+)\s*\{\s*type\s+(\w+);\s*faces\s*\((.*?)\);\s*\}"s
    
    for match in eachmatch(boundary_pattern, boundary_string)
        name = match.captures[1]
        bc_type = match.captures[2]
        faces_spec = match.captures[3]
        
        boundaries[name] = Dict(
            "type" => bc_type,
            "faces" => faces_spec
        )
    end
    
    return boundaries
end

"""
Read OpenFOAM points file
"""
function read_openfoam_points(file_path::String)
    content = read(file_path, String)
    
    # Extract point data between parentheses
    points_match = match(r"\((.*)\)"s, content)
    if points_match === nothing
        error("Could not parse points file")
    end
    
    points_data = points_match.captures[1]
    points = []
    
    # Parse individual points
    point_pattern = r"\(([-\d.e]+)\s+([-\d.e]+)\s+([-\d.e]+)\)"
    for match in eachmatch(point_pattern, points_data)
        x = parse(Float64, match.captures[1])
        y = parse(Float64, match.captures[2])
        z = parse(Float64, match.captures[3])
        push!(points, [x, y, z])
    end
    
    return points
end

"""
Read OpenFOAM faces file
"""
function read_openfoam_faces(file_path::String)
    content = read(file_path, String)
    
    # Extract face data
    faces_match = match(r"\((.*)\)"s, content)
    if faces_match === nothing
        error("Could not parse faces file")
    end
    
    faces_data = faces_match.captures[1]
    faces = []
    
    # Parse individual faces
    face_pattern = r"\d+\(([\d\s]+)\)"
    for match in eachmatch(face_pattern, faces_data)
        vertices = parse.(Int, split(match.captures[1]))
        push!(faces, vertices)
    end
    
    return faces
end

"""
Read OpenFOAM list file (owner/neighbour)
"""
function read_openfoam_list(file_path::String)
    content = read(file_path, String)
    
    # Extract list data
    list_match = match(r"\((.*)\)"s, content)
    if list_match === nothing
        error("Could not parse list file")
    end
    
    list_data = list_match.captures[1]
    values = parse.(Int, split(list_data))
    
    return values
end

"""
Read OpenFOAM boundary file
"""
function read_openfoam_boundary(file_path::String)
    content = read(file_path, String)
    boundaries = Dict{String, Any}()
    
    # Parse boundary patches
    patch_pattern = r"(\w+)\s*\{\s*type\s+(\w+);\s*(?:physicalType\s+(\w+);\s*)?nFaces\s+(\d+);\s*startFace\s+(\d+);\s*\}"s
    
    for match in eachmatch(patch_pattern, content)
        name = match.captures[1]
        bc_type = match.captures[2]
        physical_type = match.captures[3] !== nothing ? match.captures[3] : bc_type
        n_faces = parse(Int, match.captures[4])
        start_face = parse(Int, match.captures[5])
        
        boundaries[name] = Dict(
            "type" => bc_type,
            "physicalType" => physical_type,
            "nFaces" => n_faces,
            "startFace" => start_face
        )
    end
    
    return boundaries
end

"""
Read initial fields from 0 directory
"""
function read_initial_fields(case_path::String)
    fields = Dict{String, Any}()
    zero_dir = joinpath(case_path, "0")
    
    if !isdir(zero_dir)
        println("⚠️  No initial conditions directory (0) found")
        return fields
    end
    
    # Read velocity field
    u_file = joinpath(zero_dir, "U")
    if isfile(u_file)
        fields["U"] = read_field_file(u_file)
    end
    
    # Read pressure field
    p_file = joinpath(zero_dir, "p")
    if isfile(p_file)
        fields["p"] = read_field_file(p_file)
    end
    
    # Read other fields
    for file in readdir(zero_dir)
        if file ∉ ["U", "p"] && isfile(joinpath(zero_dir, file))
            fields[file] = read_field_file(joinpath(zero_dir, file))
        end
    end
    
    return fields
end

"""
Read OpenFOAM field file
"""
function read_field_file(file_path::String)
    content = read(file_path, String)
    field_data = Dict{String, Any}()
    
    # Extract dimensions
    dim_match = match(r"dimensions\s*\[([\d\s-]+)\];", content)
    if dim_match !== nothing
        field_data["dimensions"] = parse.(Int, split(dim_match.captures[1]))
    end
    
    # Extract internal field
    internal_match = match(r"internalField\s+(\w+)\s*(.*?);", content)
    if internal_match !== nothing
        field_type = internal_match.captures[1]
        field_value = internal_match.captures[2]
        
        if field_type == "uniform"
            # Parse uniform value
            if contains(field_value, "(")
                # Vector value
                vec_match = match(r"\(([-\d.e\s]+)\)", field_value)
                if vec_match !== nothing
                    field_data["internalField"] = parse.(Float64, split(vec_match.captures[1]))
                end
            else
                # Scalar value
                field_data["internalField"] = parse(Float64, strip(field_value))
            end
        elseif field_type == "nonuniform"
            # Would need to parse list of values
            field_data["internalField"] = "nonuniform"
        end
    end
    
    # Extract boundary conditions
    boundary_match = match(r"boundaryField\s*\{(.*?)\}"s, content)
    if boundary_match !== nothing
        boundary_content = boundary_match.captures[1]
        field_data["boundaryField"] = parse_boundary_field(boundary_content)
    end
    
    return field_data
end

"""
Parse boundary field specifications
"""
function parse_boundary_field(content::String)
    boundaries = Dict{String, Any}()
    
    # Parse each boundary patch
    patch_pattern = r"(\w+)\s*\{([^}]*)\}"s
    
    for match in eachmatch(patch_pattern, content)
        patch_name = match.captures[1]
        patch_content = match.captures[2]
        
        patch_data = Dict{String, Any}()
        
        # Extract type
        type_match = match(r"type\s+(\w+);", patch_content)
        if type_match !== nothing
            patch_data["type"] = type_match.captures[1]
        end
        
        # Extract value (if present)
        value_match = match(r"value\s+uniform\s+(.*?);", patch_content)
        if value_match !== nothing
            value_str = value_match.captures[1]
            if contains(value_str, "(")
                # Vector value
                vec_match = match(r"\(([-\d.e\s]+)\)", value_str)
                if vec_match !== nothing
                    patch_data["value"] = parse.(Float64, split(vec_match.captures[1]))
                end
            else
                # Scalar value
                patch_data["value"] = parse(Float64, strip(value_str))
            end
        end
        
        boundaries[patch_name] = patch_data
    end
    
    return boundaries
end

"""
Extract boundary conditions from fields
"""
function extract_boundary_conditions(case_path::String, fields::Dict{String, Any})
    boundary_conditions = Dict{String, Any}()
    
    for (field_name, field_data) in fields
        if haskey(field_data, "boundaryField")
            boundary_conditions[field_name] = field_data["boundaryField"]
        end
    end
    
    return boundary_conditions
end

"""
Read controlDict
"""
function read_control_dict(case_path::String)
    control_file = joinpath(case_path, "system", "controlDict")
    
    if !isfile(control_file)
        println("⚠️  No controlDict found")
        return Dict{String, Any}()
    end
    
    content = read(control_file, String)
    control_dict = parse_openfoam_dict(content)
    
    # Parse specific entries
    parsed_dict = Dict{String, Any}()
    
    # Common entries
    for key in ["application", "startFrom", "startTime", "stopAt", "endTime", 
                "deltaT", "writeControl", "writeInterval", "purgeWrite", 
                "writeFormat", "writePrecision", "writeCompression", 
                "timeFormat", "timePrecision", "runTimeModifiable"]
        if haskey(control_dict, key)
            value_str = strip(control_dict[key], ';')
            # Try to parse as number
            num_val = tryparse(Float64, value_str)
            if num_val !== nothing
                parsed_dict[key] = num_val
            else
                parsed_dict[key] = value_str
            end
        end
    end
    
    return parsed_dict
end

"""
Read fvSchemes
"""
function read_fv_schemes(case_path::String)
    schemes_file = joinpath(case_path, "system", "fvSchemes")
    
    if !isfile(schemes_file)
        println("⚠️  No fvSchemes found")
        return Dict{String, Any}()
    end
    
    content = read(schemes_file, String)
    # Simplified parsing - would need more sophisticated parser for full support
    
    schemes = Dict{String, Any}()
    
    # Extract main scheme categories
    for category in ["ddtSchemes", "gradSchemes", "divSchemes", "laplacianSchemes", 
                     "interpolationSchemes", "snGradSchemes"]
        pattern = Regex("$category\\s*\\{([^}]*)\\}", "s")
        match_result = match(pattern, content)
        if match_result !== nothing
            schemes[category] = parse_scheme_entries(match_result.captures[1])
        end
    end
    
    return schemes
end

"""
Parse scheme entries
"""
function parse_scheme_entries(content::String)
    entries = Dict{String, String}()
    
    lines = split(content, '\n')
    for line in lines
        line = strip(line)
        if !isempty(line) && !startswith(line, "//")
            # Remove semicolon and split
            line = strip(line, ';')
            parts = split(line, r"\s+", limit=2)
            if length(parts) >= 2
                entries[parts[1]] = parts[2]
            elseif length(parts) == 1 && contains(line, "default")
                entries["default"] = parts[1]
            end
        end
    end
    
    return entries
end

"""
Read fvSolution
"""
function read_fv_solution(case_path::String)
    solution_file = joinpath(case_path, "system", "fvSolution")
    
    if !isfile(solution_file)
        println("⚠️  No fvSolution found")
        return Dict{String, Any}()
    end
    
    content = read(solution_file, String)
    solution_dict = Dict{String, Any}()
    
    # Extract solvers
    solvers_match = match(r"solvers\s*\{(.*?)\}"s, content)
    if solvers_match !== nothing
        solution_dict["solvers"] = parse_solvers(solvers_match.captures[1])
    end
    
    # Extract SIMPLE/PISO/PIMPLE settings
    for algorithm in ["SIMPLE", "PISO", "PIMPLE"]
        algo_match = match(Regex("$algorithm\\s*\\{([^}]*)\\}", "s"), content)
        if algo_match !== nothing
            solution_dict[algorithm] = parse_algorithm_settings(algo_match.captures[1])
        end
    end
    
    return solution_dict
end

"""
Parse solver specifications
"""
function parse_solvers(content::String)
    solvers = Dict{String, Any}()
    
    # Match solver blocks
    solver_pattern = r"(\w+)\s*\{([^}]*)\}"s
    
    for match in eachmatch(solver_pattern, content)
        field_name = match.captures[1]
        solver_content = match.captures[2]
        
        solver_settings = Dict{String, Any}()
        
        # Parse solver settings
        lines = split(solver_content, '\n')
        for line in lines
            line = strip(line)
            if !isempty(line) && contains(line, r"^\w+\s+")
                parts = split(line, r"\s+", limit=2)
                if length(parts) >= 2
                    key = parts[1]
                    value = strip(parts[2], ';')
                    # Try to parse as number
                    num_val = tryparse(Float64, value)
                    if num_val !== nothing
                        solver_settings[key] = num_val
                    else
                        solver_settings[key] = value
                    end
                end
            end
        end
        
        solvers[field_name] = solver_settings
    end
    
    return solvers
end

"""
Parse algorithm settings (SIMPLE/PISO/PIMPLE)
"""
function parse_algorithm_settings(content::String)
    settings = Dict{String, Any}()
    
    lines = split(content, '\n')
    for line in lines
        line = strip(line)
        if !isempty(line) && contains(line, r"^\w+\s+")
            parts = split(line, r"\s+", limit=2)
            if length(parts) >= 2
                key = parts[1]
                value = strip(parts[2], ';')
                # Try to parse as number or boolean
                if value == "yes" || value == "true"
                    settings[key] = true
                elseif value == "no" || value == "false"
                    settings[key] = false
                else
                    num_val = tryparse(Float64, value)
                    if num_val !== nothing
                        settings[key] = num_val
                    else
                        settings[key] = value
                    end
                end
            end
        end
    end
    
    return settings
end

"""
Convert OpenFOAM case to JuliaFOAM mesh
"""
function convert_to_juliafoam_mesh(openfoam_case::OpenFOAMCase)
    mesh_data = openfoam_case.mesh_data
    
    if haskey(mesh_data, "structured_mesh")
        # Convert structured mesh
        structured = mesh_data["structured_mesh"]
        nx, ny = structured["nx"], structured["ny"]
        
        # Create Mesh2D
        points_2d = [[p[1], p[2]] for p in structured["points"]]
        
        # Generate faces from cells
        faces = []
        face_to_cell = []
        boundary_faces = Dict{String, Vector{Int}}()
        
        # Add boundary face groups
        if haskey(mesh_data, "boundary_specs")
            for (name, spec) in mesh_data["boundary_specs"]
                boundary_faces[name] = Int[]
            end
        end
        
        # Generate faces (simplified for structured mesh)
        face_id = 1
        
        # Bottom boundary
        for i in 1:nx
            push!(faces, [i, i+1])
            push!(face_to_cell, [1, -1])  # Cell 1, no neighbor
            if haskey(boundary_faces, "movingWall")
                # This would need proper mapping
            end
            face_id += 1
        end
        
        # Internal and boundary faces would be generated here...
        
        return Mesh2D(
            points_2d,
            structured["cells"],
            faces,
            face_to_cell,
            boundary_faces
        )
    else
        error("Unstructured mesh conversion not yet implemented")
    end
end

"""
Convert OpenFOAM boundary conditions to JuliaFOAM format
"""
function convert_boundary_conditions(openfoam_case::OpenFOAMCase, mesh::Mesh)
    bc_dict = Dict{String, Dict{String, BoundaryCondition}}()
    
    for (field_name, field_bcs) in openfoam_case.boundary_conditions
        bc_dict[field_name] = Dict{String, BoundaryCondition}()
        
        for (patch_name, patch_bc) in field_bcs
            bc_type = get(patch_bc, "type", "zeroGradient")
            
            if bc_type == "fixedValue"
                value = get(patch_bc, "value", 0.0)
                bc_dict[field_name][patch_name] = DirichletBC(value)
            elseif bc_type == "zeroGradient"
                bc_dict[field_name][patch_name] = NeumannBC(0.0)
            elseif bc_type == "empty"
                # Empty BC for 2D cases
                bc_dict[field_name][patch_name] = DirichletBC(0.0)  # Placeholder
            else
                println("⚠️  Unknown BC type: $bc_type, using zero gradient")
                bc_dict[field_name][patch_name] = NeumannBC(0.0)
            end
        end
    end
    
    return bc_dict
end

# ============================================================================
# DECOMPOSED CASE HANDLING
# ============================================================================

"""
Detect if a case is decomposed (has processor directories) or serial
"""
function detect_case_type(case_path::String)
    if !isdir(case_path)
        error("Case directory not found: $case_path")
    end

    # Check for processor directories
    processor_dirs = list_processor_directories(case_path)

    if !isempty(processor_dirs)
        return :decomposed, length(processor_dirs)
    elseif isdir(joinpath(case_path, "constant", "polyMesh"))
        return :serial, 1
    else
        error("Cannot determine case type - no mesh found in $case_path")
    end
end

"""
List all processor directories in a case
"""
function list_processor_directories(case_path::String)
    processor_dirs = String[]

    for item in readdir(case_path)
        if occursin(r"^processor\d+$", item)
            proc_path = joinpath(case_path, item)
            if isdir(proc_path) && isdir(joinpath(proc_path, "constant", "polyMesh"))
                push!(processor_dirs, item)
            end
        end
    end

    # Sort by processor number
    sort!(processor_dirs, by = x -> parse(Int, match(r"processor(\d+)", x).captures[1]))

    return processor_dirs
end

"""
Import a decomposed OpenFOAM case from processor directories
"""
function import_decomposed_case(case_path::String)
    println("📁 Importing decomposed OpenFOAM case from: $case_path")

    case_type, n_processors = detect_case_type(case_path)

    if case_type != :decomposed
        error("Case is not decomposed. Use import_openfoam_case() for serial cases.")
    end

    processor_dirs = list_processor_directories(case_path)

    if length(processor_dirs) != n_processors
        @warn "Expected $n_processors processors, found $(length(processor_dirs))"
    end

    # Read processor cases
    processor_cases = Dict{String, OpenFOAMCase}()

    for proc_dir in processor_dirs
        proc_path = joinpath(case_path, proc_dir)
        println("   📂 Reading $proc_dir...")

        try
            processor_cases[proc_dir] = read_processor_case(proc_path)
        catch e
            @warn "Failed to read $proc_dir: $e"
        end
    end

    # Read global case settings (system directory)
    control_dict = read_control_dict(case_path)
    fv_schemes = read_fv_schemes(case_path)
    fv_solution = read_fv_solution(case_path)

    return Dict(
        "case_path" => case_path,
        "case_type" => :decomposed,
        "n_processors" => n_processors,
        "processor_cases" => processor_cases,
        "control_dict" => control_dict,
        "fv_schemes" => fv_schemes,
        "fv_solution" => fv_solution
    )
end

"""
Read a single processor case directory
"""
function read_processor_case(proc_path::String)
    if !isdir(proc_path)
        error("Processor directory not found: $proc_path")
    end

    # Read mesh
    mesh_data = read_openfoam_mesh(proc_path)

    # Read fields from time directories
    fields = read_processor_fields(proc_path)

    # Read addressing files for reconstruction
    addressing = read_processor_addressing(proc_path)

    # Extract boundary conditions from fields
    boundary_conditions = extract_boundary_conditions(proc_path, fields)

    return OpenFOAMCase(
        proc_path,
        mesh_data,
        fields,
        boundary_conditions,
        Dict{String, Any}(),  # control_dict (read globally)
        Dict{String, Any}(),  # fv_schemes (read globally)
        Dict{String, Any}()   # fv_solution (read globally)
    )
end

"""
Read fields from all time directories in a processor case
"""
function read_processor_fields(proc_path::String)
    fields = Dict{String, Any}()

    # Find time directories
    time_dirs = String[]
    for item in readdir(proc_path)
        item_path = joinpath(proc_path, item)
        if isdir(item_path)
            # Check if it's a time directory (numeric name or "0")
            if occursin(r"^\d+(\.\d+)?$", item) || item == "0"
                push!(time_dirs, item)
            end
        end
    end

    if isempty(time_dirs)
        @warn "No time directories found in $proc_path"
        return fields
    end

    # Read fields from each time directory
    for time_dir in time_dirs
        time_path = joinpath(proc_path, time_dir)
        time_fields = Dict{String, Any}()

        for field_file in readdir(time_path)
            field_path = joinpath(time_path, field_file)
            if isfile(field_path) && !startswith(field_file, ".")
                try
                    time_fields[field_file] = read_openfoam_field_file(field_path)
                catch e
                    @warn "Failed to read field $field_file in $time_dir: $e"
                end
            end
        end

        fields[time_dir] = time_fields
    end

    return fields
end

"""
Read processor addressing files for reconstruction
"""
function read_processor_addressing(proc_path::String)
    addressing = Dict{String, Any}()

    mesh_dir = joinpath(proc_path, "constant", "polyMesh")

    # Read cell addressing
    cell_addr_file = joinpath(mesh_dir, "cellProcAddressing")
    if isfile(cell_addr_file)
        addressing["cellProcAddressing"] = read_addressing_file(cell_addr_file)
    end

    # Read face addressing
    face_addr_file = joinpath(mesh_dir, "faceProcAddressing")
    if isfile(face_addr_file)
        addressing["faceProcAddressing"] = read_addressing_file(face_addr_file)
    end

    # Read point addressing
    point_addr_file = joinpath(mesh_dir, "pointProcAddressing")
    if isfile(point_addr_file)
        addressing["pointProcAddressing"] = read_addressing_file(point_addr_file)
    end

    # Read boundary addressing
    boundary_addr_file = joinpath(mesh_dir, "boundaryProcAddressing")
    if isfile(boundary_addr_file)
        addressing["boundaryProcAddressing"] = read_addressing_file(boundary_addr_file)
    end

    return addressing
end

"""
Read an OpenFOAM addressing file (list of integers)
"""
function read_addressing_file(file_path::String)
    content = read(file_path, String)
    lines = split(content, '\n')

    addresses = Int[]
    in_data = false

    for line in lines
        line = strip(line)
        if occursin(r"^\d+$", line)
            if in_data
                # OpenFOAM uses 0-based indexing, convert to 1-based
                push!(addresses, parse(Int, line) + 1)
            else
                # First number is the list size
                in_data = true
            end
        end
    end

    return addresses
end

# ============================================================================
# ENHANCED FIELD I/O
# ============================================================================

"""
Read an OpenFOAM field file with comprehensive parsing
"""
function read_openfoam_field_file(field_path::String)
    if !isfile(field_path)
        error("Field file not found: $field_path")
    end

    content = read(field_path, String)
    field_data = Dict{String, Any}()

    # Parse header information
    field_data["class"] = extract_foam_header_value(content, "class")
    field_data["object"] = extract_foam_header_value(content, "object")

    # Parse dimensions
    dim_match = match(r"dimensions\s+\[(.*?)\]", content)
    if dim_match !== nothing
        field_data["dimensions"] = "[$(dim_match.captures[1])]"
    else
        field_data["dimensions"] = "[0 0 0 0 0 0 0]"
    end

    # Parse internal field
    field_data["internal_field"] = parse_internal_field(content)

    # Parse boundary field
    field_data["boundary_field"] = parse_boundary_field_section(content)

    return field_data
end

"""
Write an OpenFOAM field file
"""
function write_openfoam_field_file(field_path::String, field_data::Dict{String, Any})
    open(field_path, "w") do f
        # Write header
        class = get(field_data, "class", "volScalarField")
        object = get(field_data, "object", basename(field_path))
        write_foam_header(f, class, object)

        # Write dimensions
        dimensions = get(field_data, "dimensions", "[0 0 0 0 0 0 0]")
        println(f, "dimensions      $dimensions;")
        println(f)

        # Write internal field
        write_internal_field(f, field_data["internal_field"])

        # Write boundary field
        write_boundary_field_section(f, field_data["boundary_field"])
    end
end

"""
Extract value from OpenFOAM header
"""
function extract_foam_header_value(content::String, key::String)
    pattern = Regex("$key\\s+(\\w+);")
    match_result = match(pattern, content)
    return match_result !== nothing ? match_result.captures[1] : ""
end

"""
Parse internal field from OpenFOAM content
"""
function parse_internal_field(content::String)
    # Look for internalField
    internal_match = match(r"internalField\s+(.*?);", content, 1)
    if internal_match === nothing
        return Dict("type" => "uniform", "value" => 0.0)
    end

    internal_str = strip(internal_match.captures[1])

    if occursin("uniform", internal_str)
        # Uniform field
        if occursin(r"\(.*\)", internal_str)
            # Vector uniform field
            vector_match = match(r"uniform\s+\(\s*([-+]?[0-9]*\.?[0-9]+(?:[eE][-+]?[0-9]+)?)\s+([-+]?[0-9]*\.?[0-9]+(?:[eE][-+]?[0-9]+)?)\s+([-+]?[0-9]*\.?[0-9]+(?:[eE][-+]?[0-9]+)?)\s*\)", internal_str)
            if vector_match !== nothing
                x = parse(Float64, vector_match.captures[1])
                y = parse(Float64, vector_match.captures[2])
                z = parse(Float64, vector_match.captures[3])
                return Dict("type" => "uniform", "value" => [x, y, z])
            end
        else
            # Scalar uniform field
            value_match = match(r"uniform\s+([-+]?[0-9]*\.?[0-9]+(?:[eE][-+]?[0-9]+)?)", internal_str)
            if value_match !== nothing
                return Dict("type" => "uniform", "value" => parse(Float64, value_match.captures[1]))
            end
        end
    elseif occursin("nonuniform", internal_str)
        # Non-uniform field - would need more complex parsing
        return Dict("type" => "nonuniform", "value" => "complex_data")
    end

    return Dict("type" => "unknown", "value" => internal_str)
end

"""
Parse boundary field section
"""
function parse_boundary_field_section(content::String)
    boundary_field = Dict{String, Any}()

    # Find boundaryField section
    boundary_match = match(r"boundaryField\s*\{(.*)\}", content, 1)
    if boundary_match === nothing
        return boundary_field
    end

    boundary_content = boundary_match.captures[1]

    # Parse individual patches (simplified)
    patch_pattern = r"(\w+)\s*\{([^{}]*(?:\{[^{}]*\}[^{}]*)*)\}"

    for patch_match in eachmatch(patch_pattern, boundary_content)
        patch_name = patch_match.captures[1]
        patch_content = patch_match.captures[2]

        patch_data = Dict{String, Any}()

        # Parse type
        type_match = match(r"type\s+(\w+)", patch_content)
        if type_match !== nothing
            patch_data["type"] = type_match.captures[1]
        end

        # Parse value if present
        value_match = match(r"value\s+uniform\s+(.*?);", patch_content)
        if value_match !== nothing
            value_str = strip(value_match.captures[1])
            if occursin(r"\(.*\)", value_str)
                # Vector value
                vector_match = match(r"\(\s*([-+]?[0-9]*\.?[0-9]+(?:[eE][-+]?[0-9]+)?)\s+([-+]?[0-9]*\.?[0-9]+(?:[eE][-+]?[0-9]+)?)\s+([-+]?[0-9]*\.?[0-9]+(?:[eE][-+]?[0-9]+)?)\s*\)", value_str)
                if vector_match !== nothing
                    x = parse(Float64, vector_match.captures[1])
                    y = parse(Float64, vector_match.captures[2])
                    z = parse(Float64, vector_match.captures[3])
                    patch_data["value"] = [x, y, z]
                end
            else
                # Scalar value
                try
                    patch_data["value"] = parse(Float64, value_str)
                catch
                    patch_data["value"] = value_str
                end
            end
        end

        boundary_field[patch_name] = patch_data
    end

    return boundary_field
end

"""
Write internal field to file
"""
function write_internal_field(f::IO, internal_field::Dict{String, Any})
    field_type = get(internal_field, "type", "uniform")
    value = get(internal_field, "value", 0.0)

    if field_type == "uniform"
        if isa(value, AbstractVector) && length(value) == 3
            println(f, "internalField   uniform ($(value[1]) $(value[2]) $(value[3]));")
        else
            println(f, "internalField   uniform $value;")
        end
    else
        println(f, "internalField   $field_type;")
    end
    println(f)
end

"""
Write boundary field section to file
"""
function write_boundary_field_section(f::IO, boundary_field::Dict{String, Any})
    println(f, "boundaryField")
    println(f, "{")

    for (patch_name, patch_data) in boundary_field
        println(f, "    $patch_name")
        println(f, "    {")

        patch_type = get(patch_data, "type", "zeroGradient")
        println(f, "        type            $patch_type;")

        if haskey(patch_data, "value")
            value = patch_data["value"]
            if isa(value, AbstractVector) && length(value) == 3
                println(f, "        value           uniform ($(value[1]) $(value[2]) $(value[3]));")
            else
                println(f, "        value           uniform $value;")
            end
        end

        println(f, "    }")
    end

    println(f, "}")
end

# ============================================================================
# CASE RECONSTRUCTION
# ============================================================================

"""
Reconstruct a decomposed case back to serial format
"""
function reconstruct_case(decomposed_case_path::String, output_path::String="")
    if isempty(output_path)
        output_path = decomposed_case_path * "_reconstructed"
    end

    println("🔄 Reconstructing case from $decomposed_case_path to $output_path")

    # Import decomposed case
    decomposed_case = import_decomposed_case(decomposed_case_path)

    # Create output directory structure
    mkpath(joinpath(output_path, "constant"))
    mkpath(joinpath(output_path, "system"))
    mkpath(joinpath(output_path, "0"))

    # Copy system files
    for file in ["controlDict", "fvSchemes", "fvSolution"]
        src = joinpath(decomposed_case_path, "system", file)
        dst = joinpath(output_path, "system", file)
        if isfile(src)
            cp(src, dst, force=true)
        end
    end

    # Reconstruct mesh (simplified - would need full implementation)
    println("   📐 Reconstructing mesh...")
    # This would require complex mesh reconstruction logic

    # Reconstruct fields (simplified)
    println("   📊 Reconstructing fields...")
    # This would require field reconstruction from processor data

    println("✅ Case reconstruction completed")

    return output_path
end

end # module