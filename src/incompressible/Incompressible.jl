module Incompressible

using LinearAlgebra
using StaticArrays
using SparseArrays
using MPI
using Base.Threads
using ..JuliaFOAM
using ..LinearSolvers

export solve_piso!, solve_simple!, momentum_predictor!, solve_pressure_equation!, correct_velocity!
export solve_piso_parallel!, solve_simple_parallel!, momentum_predictor_parallel!
export solve_pressure_equation_parallel!, correct_velocity_parallel!

"""
    solve_piso!(U::Field{SVector{3,Float64}}, p::Field{Float64}, mesh::Mesh,
               properties::FluidProperties, time_step::Float64,
               n_correctors::Int = 3, n_non_orthogonal_correctors::Int = 1)

PISO algorithm for incompressible flow.

# Arguments
- `U`: Velocity field
- `p`: Pressure field
- `mesh`: The mesh
- `properties`: Fluid properties
- `time_step`: Time step size
- `n_correctors`: Number of PISO corrector steps
- `n_non_orthogonal_correctors`: Number of non-orthogonal corrector steps

# Returns
- `Tuple{Field{SVector{3,<PERSON>loat64}},Field{Float64}}`: Updated velocity and pressure fields
"""
function solve_piso!(
    U::Field{SVector{3,Float64}},
    p::Field{Float64},
    mesh::Mesh,
    properties::FluidProperties,
    time_step::Float64,
    n_correctors::Int = 3,
    n_non_orthogonal_correctors::Int = 1
)
    # Store old velocity
    U_old = copy(U.internal_field)

    # Momentum predictor - semi-implicit Euler for now
    momentum_predictor!(U, p, mesh, properties, time_step)

    # PISO loop
    for i in 1:n_correctors
        # Pressure equation
        solve_pressure_equation!(U, p, mesh, properties, time_step)

        # Non-orthogonal corrections
        for j in 1:n_non_orthogonal_correctors
            solve_pressure_equation!(U, p, mesh, properties, time_step)
        end

        # Velocity correction
        correct_velocity!(U, p, mesh, time_step)
    end

    return U, p
end

"""
    solve_simple!(U::Field{SVector{3,Float64}}, p::Field{Float64}, mesh::Mesh,
                 properties::FluidProperties, under_relaxation_U::Float64 = 0.7,
                 under_relaxation_p::Float64 = 0.3, max_iterations::Int = 1000,
                 tolerance::Float64 = 1e-6)

SIMPLE algorithm for steady-state incompressible flow.

# Arguments
- `U`: Velocity field
- `p`: Pressure field
- `mesh`: The mesh
- `properties`: Fluid properties
- `under_relaxation_U`: Under-relaxation factor for velocity
- `under_relaxation_p`: Under-relaxation factor for pressure
- `max_iterations`: Maximum number of iterations
- `tolerance`: Convergence tolerance

# Returns
- `Tuple{Field{SVector{3,Float64}},Field{Float64}}`: Updated velocity and pressure fields
"""
function solve_simple!(
    U::Field{SVector{3,Float64}},
    p::Field{Float64},
    mesh::Mesh,
    properties::FluidProperties,
    under_relaxation_U::Float64 = 0.7,
    under_relaxation_p::Float64 = 0.3,
    max_iterations::Int = 1000,
    tolerance::Float64 = 1e-6
)
    # Main SIMPLE loop
    for iter in 1:max_iterations
        # Store old fields
        U_old = copy(U.internal_field)
        p_old = copy(p.internal_field)

        # Momentum predictor with under-relaxation
        momentum_predictor!(U, p, mesh, properties, under_relaxation_U, false)

        # Pressure equation with under-relaxation
        solve_pressure_equation!(U, p, mesh, properties, under_relaxation_p, false)

        # Velocity correction
        correct_velocity!(U, p, mesh, under_relaxation_U, false)

        # Check convergence
        U_residual = norm(U.internal_field - U_old) / norm(U.internal_field)
        p_residual = norm(p.internal_field - p_old) / norm(p.internal_field)

        println("Iteration $iter: U residual = $U_residual, p residual = $p_residual")

        if max(U_residual, p_residual) < tolerance
            println("Converged in $iter iterations")
            break
        end
    end

    return U, p
end

"""
    momentum_predictor!(U::Field{SVector{3,Float64}}, p::Field{Float64}, mesh::Mesh,
                       properties::FluidProperties, time_step_or_relax::Float64,
                       is_transient::Bool = true)

Momentum predictor step.

# Arguments
- `U`: Velocity field
- `p`: Pressure field
- `mesh`: The mesh
- `properties`: Fluid properties
- `time_step_or_relax`: Time step or relaxation factor
- `is_transient`: Whether this is a transient simulation
"""
function momentum_predictor!(
    U::Field{SVector{3,Float64}},
    p::Field{Float64},
    mesh::Mesh,
    properties::FluidProperties,
    time_step_or_relax::Float64,
    is_transient::Bool = true
)
    # 1. Build momentum matrix
    A, b = build_momentum_matrix(U, p, mesh, properties, time_step_or_relax, is_transient)

    # 2. Apply boundary conditions
    apply_boundary_conditions!(U, mesh)

    # 3. Solve momentum equation
    solver_settings = SolverSettings(
        :bicgstab,
        :dic,
        1e-6,
        1000
    )

    # Create diagonal preconditioner
    diag_precond = DiagonalPreconditioner(diag(A))

    # Wrap matrix
    A_wrapper = MatrixWrapper(A)

    # Solve for each velocity component
    n = length(mesh.cells)

    for i in 1:3
        # Extract component from vector field
        component = [U.internal_field[j][i] for j in 1:n]

        # Extract RHS for this component
        b_component = [b[(j-1)*3 + i] for j in 1:n]

        # Solve
        solve!(A_wrapper, component, b_component, solver_settings, diag_precond)

        # Update velocity field
        for j in 1:n
            U.internal_field[j] = SVector{3,Float64}(
                i == 1 ? component[j] : U.internal_field[j][1],
                i == 2 ? component[j] : U.internal_field[j][2],
                i == 3 ? component[j] : U.internal_field[j][3]
            )
        end
    end
end

"""
    solve_pressure_equation!(U::Field{SVector{3,Float64}}, p::Field{Float64}, mesh::Mesh,
                           properties::FluidProperties, time_step_or_relax::Float64,
                           is_transient::Bool = true)

Pressure equation solver step.

# Arguments
- `U`: Velocity field
- `p`: Pressure field
- `mesh`: The mesh
- `properties`: Fluid properties
- `time_step_or_relax`: Time step or relaxation factor
- `is_transient`: Whether this is a transient simulation
"""
function solve_pressure_equation!(
    U::Field{SVector{3,Float64}},
    p::Field{Float64},
    mesh::Mesh,
    properties::FluidProperties,
    time_step_or_relax::Float64,
    is_transient::Bool = true
)
    # 1. Build pressure Poisson equation
    A, b = build_pressure_equation(U, p, mesh, properties, time_step_or_relax)

    # 2. Apply boundary conditions
    apply_boundary_conditions!(p, mesh)

    # 3. Solve pressure equation
    solver_settings = SolverSettings(
        :pcg,        # Conjugate Gradient for symmetric matrix
        :dic,        # Diagonal Incomplete Cholesky preconditioner
        1e-6,
        1000
    )

    # Create preconditioner
    precond = DICPreconditioner(incomplete_cholesky(A))

    # Wrap matrix
    A_wrapper = MatrixWrapper(A)

    # Solve
    solve!(A_wrapper, p.internal_field, b, solver_settings, precond)

    # Apply boundary conditions again
    apply_boundary_conditions!(p, mesh)
end

"""
    correct_velocity!(U::Field{SVector{3,Float64}}, p::Field{Float64}, mesh::Mesh,
                     time_step_or_relax::Float64, is_transient::Bool = true)

Velocity correction step.

# Arguments
- `U`: Velocity field
- `p`: Pressure field
- `mesh`: The mesh
- `time_step_or_relax`: Time step or relaxation factor
- `is_transient`: Whether this is a transient simulation
"""
function correct_velocity!(
    U::Field{SVector{3,Float64}},
    p::Field{Float64},
    mesh::Mesh,
    time_step_or_relax::Float64,
    is_transient::Bool = true
)
    # Calculate pressure gradient
    grad_p = zeros(SVector{3,Float64}, length(mesh.cells))
    grad_gauss_linear!(grad_p, p.internal_field, mesh)

    # Correct velocity
    n = length(mesh.cells)

    for i in 1:n
        # H(U)/A_p is the current velocity
        # We subtract the pressure gradient contribution

        # For transient, A_p = rho*V/dt
        # For steady, A_p = rho*V/alpha_U
        A_p = mesh.cells[i].volume / time_step_or_relax

        # U = H(U)/A_p - grad(p)/A_p
        U.internal_field[i] -= grad_p[i] / A_p
    end

    # Apply boundary conditions
    apply_boundary_conditions!(U, mesh)
end

"""
    solve_piso_parallel!(U::Field{SVector{3,Float64}}, p::Field{Float64}, mesh::Any,
                       properties::FluidProperties, time_step::Float64,
                       n_correctors::Int = 3, n_non_orthogonal_correctors::Int = 1)

Parallel PISO algorithm for incompressible flow using hierarchical parallelism (MPI + threading).

# Arguments
- `U`: Velocity field
- `p`: Pressure field
- `mesh`: The optimized mesh
- `properties`: Fluid properties
- `time_step`: Time step size
- `n_correctors`: Number of PISO corrector steps
- `n_non_orthogonal_correctors`: Number of non-orthogonal corrector steps

# Returns
- `Tuple{Field{SVector{3,Float64}},Field{Float64}}`: Updated velocity and pressure fields
"""
function solve_piso_parallel!(
    U::Field{SVector{3,Float64}},
    p::Field{Float64},
    mesh::Any,
    properties::FluidProperties,
    time_step::Float64,
    n_correctors::Int = 3,
    n_non_orthogonal_correctors::Int = 1
)
    # Initialize MPI if not already initialized
    if !MPI.Initialized()
        MPI.Init()
    end

    # Get MPI info
    comm = hasfield(typeof(mesh), :comm) ? mesh.comm : MPI.COMM_WORLD
    rank = MPI.Comm_rank(comm)
    nprocs = MPI.Comm_size(comm)

    # Get thread info
    num_threads = Threads.nthreads()

    # Determine optimal thread/MPI balance based on hardware topology
    threads_per_node = min(num_threads, Sys.CPU_THREADS ÷ nprocs)

    # Create thread partitioning for local cells
    n_local_cells = hasfield(typeof(mesh), :local_indices) ? length(mesh.local_indices) : length(mesh.cells)
    cells_per_thread = cld(n_local_cells, threads_per_node)
    thread_cell_ranges = [((i-1)*cells_per_thread + 1):min(i*cells_per_thread, n_local_cells) for i in 1:threads_per_node]

    # Store old velocity
    U_old = copy(U.internal_field)

    # Momentum predictor - semi-implicit Euler for now
    momentum_predictor_parallel!(U, p, mesh, properties, time_step, thread_cell_ranges)

    # Synchronize velocity field across processes
    if nprocs > 1
        requests = exchange_halo_data_nonblocking_vector!(U.internal_field, mesh)
        wait_for_halo_exchange_vector!(requests, U.internal_field, mesh)
    end

    # PISO loop
    for i in 1:n_correctors
        # Pressure equation
        solve_pressure_equation_parallel!(U, p, mesh, properties, time_step, thread_cell_ranges)

        # Synchronize pressure field across processes
        if nprocs > 1
            requests = exchange_halo_data_nonblocking!(p.internal_field, mesh)
            wait_for_halo_exchange!(requests, p.internal_field, mesh)
        end

        # Non-orthogonal corrections
        for j in 1:n_non_orthogonal_correctors
            solve_pressure_equation_parallel!(U, p, mesh, properties, time_step, thread_cell_ranges)

            # Synchronize pressure field across processes
            if nprocs > 1
                requests = exchange_halo_data_nonblocking!(p.internal_field, mesh)
                wait_for_halo_exchange!(requests, p.internal_field, mesh)
            end
        end

        # Velocity correction
        correct_velocity_parallel!(U, p, mesh, time_step, thread_cell_ranges)

        # Synchronize velocity field across processes
        if nprocs > 1
            requests = exchange_halo_data_nonblocking_vector!(U.internal_field, mesh)
            wait_for_halo_exchange_vector!(requests, U.internal_field, mesh)
        end
    end

    return U, p
end

"""
    solve_simple_parallel!(U::Field{SVector{3,Float64}}, p::Field{Float64}, mesh::Any,
                         properties::FluidProperties, under_relaxation_U::Float64 = 0.7,
                         under_relaxation_p::Float64 = 0.3, max_iterations::Int = 1000,
                         tolerance::Float64 = 1e-6)

Parallel SIMPLE algorithm for steady-state incompressible flow using hierarchical parallelism (MPI + threading).

# Arguments
- `U`: Velocity field
- `p`: Pressure field
- `mesh`: The optimized mesh
- `properties`: Fluid properties
- `under_relaxation_U`: Under-relaxation factor for velocity
- `under_relaxation_p`: Under-relaxation factor for pressure
- `max_iterations`: Maximum number of iterations
- `tolerance`: Convergence tolerance

# Returns
- `Tuple{Field{SVector{3,Float64}},Field{Float64}}`: Updated velocity and pressure fields
"""
function solve_simple_parallel!(
    U::Field{SVector{3,Float64}},
    p::Field{Float64},
    mesh::Any,
    properties::FluidProperties,
    under_relaxation_U::Float64 = 0.7,
    under_relaxation_p::Float64 = 0.3,
    max_iterations::Int = 1000,
    tolerance::Float64 = 1e-6
)
    # Initialize MPI if not already initialized
    if !MPI.Initialized()
        MPI.Init()
    end

    # Get MPI info
    comm = hasfield(typeof(mesh), :comm) ? mesh.comm : MPI.COMM_WORLD
    rank = MPI.Comm_rank(comm)
    nprocs = MPI.Comm_size(comm)

    # Get thread info
    num_threads = Threads.nthreads()

    # Determine optimal thread/MPI balance based on hardware topology
    threads_per_node = min(num_threads, Sys.CPU_THREADS ÷ nprocs)

    # Create thread partitioning for local cells
    n_local_cells = hasfield(typeof(mesh), :local_indices) ? length(mesh.local_indices) : length(mesh.cells)
    cells_per_thread = cld(n_local_cells, threads_per_node)
    thread_cell_ranges = [((i-1)*cells_per_thread + 1):min(i*cells_per_thread, n_local_cells) for i in 1:threads_per_node]

    # Main SIMPLE loop
    for iter in 1:max_iterations
        # Store old fields
        U_old = copy(U.internal_field)
        p_old = copy(p.internal_field)

        # Momentum predictor with under-relaxation
        momentum_predictor_parallel!(U, p, mesh, properties, under_relaxation_U, thread_cell_ranges, false)

        # Synchronize velocity field across processes
        if nprocs > 1
            requests = exchange_halo_data_nonblocking_vector!(U.internal_field, mesh)
            wait_for_halo_exchange_vector!(requests, U.internal_field, mesh)
        end

        # Pressure equation with under-relaxation
        solve_pressure_equation_parallel!(U, p, mesh, properties, under_relaxation_p, thread_cell_ranges, false)

        # Synchronize pressure field across processes
        if nprocs > 1
            requests = exchange_halo_data_nonblocking!(p.internal_field, mesh)
            wait_for_halo_exchange!(requests, p.internal_field, mesh)
        end

        # Velocity correction
        correct_velocity_parallel!(U, p, mesh, under_relaxation_U, thread_cell_ranges, false)

        # Synchronize velocity field across processes
        if nprocs > 1
            requests = exchange_halo_data_nonblocking_vector!(U.internal_field, mesh)
            wait_for_halo_exchange_vector!(requests, U.internal_field, mesh)
        end

        # Check convergence
        local_U_residual = norm(U.internal_field - U_old) / max(norm(U.internal_field), 1e-10)
        local_p_residual = norm(p.internal_field - p_old) / max(norm(p.internal_field), 1e-10)

        # Global reduction for convergence check
        if nprocs > 1
            U_residual = MPI.Allreduce(local_U_residual, MPI.MAX, comm)
            p_residual = MPI.Allreduce(local_p_residual, MPI.MAX, comm)
        else
            U_residual = local_U_residual
            p_residual = local_p_residual
        end

        if rank == 0
            println("Iteration $iter: U residual = $U_residual, p residual = $p_residual")
        end

        if max(U_residual, p_residual) < tolerance
            if rank == 0
                println("Converged in $iter iterations")
            end
            break
        end
    end

    return U, p
end

"""
    momentum_predictor_parallel!(U::Field{SVector{3,Float64}}, p::Field{Float64}, mesh::Any,
                               properties::FluidProperties, time_step_or_relax::Float64,
                               thread_cell_ranges, is_transient::Bool = true)

Parallel momentum predictor step using threading.

# Arguments
- `U`: Velocity field
- `p`: Pressure field
- `mesh`: The optimized mesh
- `properties`: Fluid properties
- `time_step_or_relax`: Time step or relaxation factor
- `thread_cell_ranges`: Cell ranges for each thread
- `is_transient`: Whether this is a transient simulation
"""
function momentum_predictor_parallel!(
    U::Field{SVector{3,Float64}},
    p::Field{Float64},
    mesh::Any,
    properties::FluidProperties,
    time_step_or_relax::Float64,
    thread_cell_ranges,
    is_transient::Bool = true
)
    # 1. Build momentum matrix in parallel
    A, b = build_momentum_matrix_parallel(U, p, mesh, properties, time_step_or_relax, thread_cell_ranges, is_transient)

    # 2. Apply boundary conditions
    apply_boundary_conditions!(U, mesh)

    # 3. Solve momentum equation in parallel
    solver_settings = SolverSettings(
        :bicgstab,
        :dic,
        1e-6,
        1000
    )

    # Create diagonal preconditioner
    diag_precond = DiagonalPreconditioner(diag(A))

    # Wrap matrix
    A_wrapper = MatrixWrapper(A)

    # Solve for each velocity component in parallel
    n = length(mesh.cells)

    Threads.@threads for i in 1:3
        # Extract component from vector field
        component = [U.internal_field[j][i] for j in 1:n]

        # Extract RHS for this component
        b_component = [b[(j-1)*3 + i] for j in 1:n]

        # Solve
        solve!(A_wrapper, component, b_component, solver_settings, diag_precond)

        # Update velocity field
        for j in 1:n
            U.internal_field[j] = SVector{3,Float64}(
                i == 1 ? component[j] : U.internal_field[j][1],
                i == 2 ? component[j] : U.internal_field[j][2],
                i == 3 ? component[j] : U.internal_field[j][3]
            )
        end
    end
end

"""
    solve_pressure_equation_parallel!(U::Field{SVector{3,Float64}}, p::Field{Float64}, mesh::Any,
                                   properties::FluidProperties, time_step_or_relax::Float64,
                                   thread_cell_ranges, is_transient::Bool = true)

Parallel pressure equation solver step using threading.

# Arguments
- `U`: Velocity field
- `p`: Pressure field
- `mesh`: The optimized mesh
- `properties`: Fluid properties
- `time_step_or_relax`: Time step or relaxation factor
- `thread_cell_ranges`: Cell ranges for each thread
- `is_transient`: Whether this is a transient simulation
"""
function solve_pressure_equation_parallel!(
    U::Field{SVector{3,Float64}},
    p::Field{Float64},
    mesh::Any,
    properties::FluidProperties,
    time_step_or_relax::Float64,
    thread_cell_ranges,
    is_transient::Bool = true
)
    # 1. Build pressure Poisson equation in parallel
    A, b = build_pressure_equation_parallel(U, p, mesh, properties, time_step_or_relax, thread_cell_ranges)

    # 2. Apply boundary conditions
    apply_boundary_conditions!(p, mesh)

    # 3. Solve pressure equation in parallel
    solver_settings = SolverSettings(
        :pcg,        # Conjugate Gradient for symmetric matrix
        :dic,        # Diagonal Incomplete Cholesky preconditioner
        1e-6,
        1000
    )

    # Create preconditioner
    precond = DICPreconditioner(incomplete_cholesky(A))

    # Wrap matrix
    A_wrapper = MatrixWrapper(A)

    # Solve
    solve!(A_wrapper, p.internal_field, b, solver_settings, precond)

    # Apply boundary conditions again
    apply_boundary_conditions!(p, mesh)
end

"""
    correct_velocity_parallel!(U::Field{SVector{3,Float64}}, p::Field{Float64}, mesh::Any,
                             time_step_or_relax::Float64, thread_cell_ranges, is_transient::Bool = true)

Parallel velocity correction step using threading.

# Arguments
- `U`: Velocity field
- `p`: Pressure field
- `mesh`: The optimized mesh
- `time_step_or_relax`: Time step or relaxation factor
- `thread_cell_ranges`: Cell ranges for each thread
- `is_transient`: Whether this is a transient simulation
"""
function correct_velocity_parallel!(
    U::Field{SVector{3,Float64}},
    p::Field{Float64},
    mesh::Any,
    time_step_or_relax::Float64,
    thread_cell_ranges,
    is_transient::Bool = true
)
    # Calculate pressure gradient in parallel
    grad_p = zeros(SVector{3,Float64}, length(mesh.cells))
    grad_gauss_linear_parallel!(grad_p, p.internal_field, mesh, thread_cell_ranges)

    # Correct velocity in parallel
    Threads.@threads for thread_id in 1:length(thread_cell_ranges)
        cell_range = thread_cell_ranges[thread_id]

        for i in cell_range
            # H(U)/A_p is the current velocity
            # We subtract the pressure gradient contribution

            # For transient, A_p = rho*V/dt
            # For steady, A_p = rho*V/alpha_U
            A_p = mesh.cells[i].volume / time_step_or_relax

            # U = H(U)/A_p - grad(p)/A_p
            U.internal_field[i] -= grad_p[i] / A_p
        end
    end

    # Apply boundary conditions
    apply_boundary_conditions!(U, mesh)
end

"""
    build_momentum_matrix_parallel(U::Field{SVector{3,Float64}}, p::Field{Float64}, mesh::Any,
                                 properties::FluidProperties, time_step_or_relax::Float64,
                                 thread_cell_ranges, is_transient::Bool = true)

Build the momentum matrix in parallel.

# Arguments
- `U`: Velocity field
- `p`: Pressure field
- `mesh`: The optimized mesh
- `properties`: Fluid properties
- `time_step_or_relax`: Time step or relaxation factor
- `thread_cell_ranges`: Cell ranges for each thread
- `is_transient`: Whether this is a transient simulation

# Returns
- `Tuple{SparseMatrixCSC{Float64,Int},Vector{Float64}}`: Coefficient matrix and right-hand side
"""
function build_momentum_matrix_parallel(
    U::Field{SVector{3,Float64}},
    p::Field{Float64},
    mesh::Any,
    properties::FluidProperties,
    time_step_or_relax::Float64,
    thread_cell_ranges,
    is_transient::Bool = true
)
    # This is a placeholder implementation
    # In a real implementation, we would build the momentum matrix in parallel

    # For now, just call the sequential version
    return build_momentum_matrix(U, p, mesh, properties, time_step_or_relax, is_transient)
end

"""
    build_pressure_equation_parallel(U::Field{SVector{3,Float64}}, p::Field{Float64}, mesh::Any,
                                   properties::FluidProperties, time_step_or_relax::Float64,
                                   thread_cell_ranges)

Build the pressure equation in parallel.

# Arguments
- `U`: Velocity field
- `p`: Pressure field
- `mesh`: The optimized mesh
- `properties`: Fluid properties
- `time_step_or_relax`: Time step or relaxation factor
- `thread_cell_ranges`: Cell ranges for each thread

# Returns
- `Tuple{SparseMatrixCSC{Float64,Int},Vector{Float64}}`: Coefficient matrix and right-hand side
"""
function build_pressure_equation_parallel(
    U::Field{SVector{3,Float64}},
    p::Field{Float64},
    mesh::Any,
    properties::FluidProperties,
    time_step_or_relax::Float64,
    thread_cell_ranges
)
    # This is a placeholder implementation
    # In a real implementation, we would build the pressure equation in parallel

    # For now, just call the sequential version
    return build_pressure_equation(U, p, mesh, properties, time_step_or_relax)
end

"""
    grad_gauss_linear_parallel!(grad::Vector{SVector{3,Float64}}, field::Vector{Float64},
                              mesh::Any, thread_cell_ranges)

Compute the gradient of a scalar field using the Gauss theorem in parallel.

# Arguments
- `grad`: Output gradient field
- `field`: Input scalar field
- `mesh`: The optimized mesh
- `thread_cell_ranges`: Cell ranges for each thread
"""
function grad_gauss_linear_parallel!(
    grad::Vector{SVector{3,Float64}},
    field::Vector{Float64},
    mesh::Any,
    thread_cell_ranges
)
    # Initialize gradient to zero
    fill!(grad, SVector{3,Float64}(0.0, 0.0, 0.0))

    # Compute gradient in parallel using Gauss theorem
    Threads.@threads for thread_id in 1:length(thread_cell_ranges)
        cell_range = thread_cell_ranges[thread_id]

        for i in cell_range
            cell_volume = mesh.cells[i].volume

            for face_idx in mesh.cell_faces[i]
                face = mesh.faces[face_idx]

                # Determine if this cell is the owner or neighbor
                is_owner = (face.owner == i)

                # Get face value
                if face.neighbour > 0
                    # Internal face - interpolate between owner and neighbor
                    face_value = 0.5 * (field[face.owner] + field[face.neighbour])
                else
                    # Boundary face - use owner value
                    face_value = field[face.owner]
                end

                # Add contribution to gradient
                sign = is_owner ? 1.0 : -1.0
                grad[i] += sign * face_value * face.area / cell_volume
            end
        end
    end
end

end # module Incompressible
