module JuliaFOAM

# External Dependencies
using StaticArrays
using SparseArrays
using LinearAlgebra
using Base.Threads
using Statistics
using Dates
using HDF5
using JSON
using Serialization

# Optional packages
const HAVE_LOOPVECTORIZATION = try
    using LoopVectorization
    true
catch
    @warn "LoopVectorization package not available. Some optimizations will be disabled."
    false
end

const HAVE_MPI = try
    using MPI
    true
catch
    @warn "MPI package not available. Parallel features will be limited."
    false
end

const HAVE_WRITEVTK = try
    using WriteVTK
    true
catch
    @warn "WriteVTK package not available. VTK output will be disabled."
    false
end

# Core Types
export Mesh, Cell, Face, Field, VectorField, ScalarField
export BoundaryCondition, FluidProperties
export UnstructuredMesh, MeshConnectivity

# Boundary Conditions
export FixedValueBC, FixedGradientBC, ZeroGradientBC, SymmetryPlaneBC
export NoSlipBC, MovingWallBC, EmptyBC
export apply_boundary_conditions!

# Mesh Operations
export create_box_mesh, create_2d_mesh_as_3d
export read_openfoam_mesh, write_openfoam_mesh

# Finite Volume
export grad_gauss_linear!, div_gauss_linear!, laplacian_gauss_linear!
export TVDScheme, FluxLimiter, VanLeerLimiter, MinmodLimiter
export compute_face_flux

# Linear Solvers
export CG, GMRES, BiCGSTAB, AMG, ILU
export GeometricMultigrid, SolverDiagnostics

# Solvers
export solve_navier_stokes!, solve_navier_stokes_enhanced!
export solve_momentum_step!, solve_pressure_correction!
export solve_pressure_correction_robust!
export SimpleSolverConfig, PressureVelocityCoupling
export SIMPLE, PISO, PIMPLE

# Time Stepping
export AdaptiveTimeStepping, compute_adaptive_timestep

# Validation
export run_analytical_validation, run_robustness_validation
export run_benchmark_validation_suite

# Include core modules
include("core/Types.jl")

# Type aliases
const VectorField = Field{SVector{3,Float64}}
const ScalarField = Field{Float64}

# Mesh modules
include("mesh/UnstructuredMesh.jl")
include("mesh/Mesh2DUtilities.jl")

# Boundary conditions
include("boundaryConditions/BoundaryConditions.jl")

# Finite volume methods
include("finiteVolume/TVDSchemes.jl")
include("finiteVolume/NonOrthogonalCorrections.jl")
include("finiteVolume/MassConservation.jl")
include("finiteVolume/UnstructuredFiniteVolume.jl")

# Numerical methods
include("numerics/VectorizedOperations.jl")

# Linear solvers
include("linear/GeometricMultigrid.jl")
include("linear/SolverDiagnostics.jl")

# Temporal schemes
include("temporal/AdaptiveTimeStepping.jl")

# Solvers
include("solvers/MomentumSolvers.jl")
include("solvers/PressureVelocityCoupling.jl")
include("solvers/RobustPressureCorrection.jl")
include("solvers/NavierStokesSolver.jl")
include("solvers/EnhancedNavierStokesSolver.jl")

# Validation
include("validation/AnalyticalValidation.jl")
include("validation/RobustnessValidation.jl")
include("validation/BenchmarkValidation.jl")

# I/O
include("io/FileIO.jl")
include("IO/OpenFOAMImporter.jl")
using .OpenFOAMImporter

# Parallel computing support
if HAVE_MPI
    include("parallel/TransparentParallel.jl")
    using .TransparentParallel
    export TransparentParallel
    
    # Export main parallel types and functions
    export DistributedField, DistributedMeshData
    export init_parallel, finalize_parallel, with_parallel
    export create_distributed_field, automatic_partitioning!
    export parallel_mesh_stats, to_distributed, to_serial
end

# Domain decomposition module (always available)
include("parallel/Parallel.jl")
using .Parallel
export Parallel

# Export key parallel functionality (limited - see docs)
# Note: Most parallel features are experimental/incomplete
# export PartitionMethod, SimplePartition, MetisPartition, ScotchPartition
# export HierarchicalPartition, ManualPartition
# export decompose_par, reconstruct_par, redistribute_par
# export setup_parallel_case, check_load_balance, decomposition_info

# Use modules
using .UnstructuredMesh
using .Mesh2DUtilities
using .BoundaryConditions
using .TVDSchemes
using .NonOrthogonalCorrections
using .MassConservation
using .UnstructuredFiniteVolume
using .VectorizedOperations
using .GeometricMultigrid
using .SolverDiagnostics
using .AdaptiveTimeStepping
using .MomentumSolvers
using .PressureVelocityCoupling
using .RobustPressureCorrection
using .NavierStokesSolver
using .EnhancedNavierStokesSolver
using .AnalyticalValidation
using .RobustnessValidation
using .BenchmarkValidation

end # module JuliaFOAM