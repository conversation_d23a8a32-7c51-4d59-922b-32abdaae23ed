"""
UnstructuredMesh.jl

Comprehensive unstructured mesh framework for JuliaFOAM.
Supports arbitrary polyhedral cells, complex geometries, and OpenFOAM polyMesh format.

Features:
- General polyhedral cell support (tetrahedra, hexahedra, prisms, pyramids)
- Efficient connectivity data structures
- OpenFOAM polyMesh import/export
- Advanced geometric calculations
- Parallel mesh partitioning support
- Adaptive mesh refinement ready
"""

module UnstructuredMesh

using LinearAlgebra
using SparseArrays
using StaticArrays

# ============================================================================
# CORE UNSTRUCTURED MESH TYPES
# ============================================================================

"""
Point in 3D space
"""
struct Point3D
    x::Float64
    y::Float64
    z::Float64
end

# Convenience constructors and operations
Point3D(v::AbstractVector) = Point3D(v[1], v[2], v[3])
Base.:+(a::Point3D, b::Point3D) = Point3D(a.x + b.x, a.y + b.y, a.z + b.z)
Base.:-(a::Point3D, b::Point3D) = Point3D(a.x - b.x, a.y - b.y, a.z - b.z)
Base.:*(s::Real, p::Point3D) = Point3D(s * p.x, s * p.y, s * p.z)
Base.:*(p::Point3D, s::Real) = Point3D(p.x * s, p.y * s, p.z * s)
Base.:/(p::Point3D, s::Real) = Point3D(p.x / s, p.y / s, p.z / s)
LinearAlgebra.dot(a::Point3D, b::Point3D) = a.x * b.x + a.y * b.y + a.z * b.z
LinearAlgebra.norm(p::Point3D) = sqrt(p.x^2 + p.y^2 + p.z^2)

"""
Unstructured face definition with arbitrary number of vertices
"""
struct UnstructuredFace
    point_indices::Vector{Int}  # Indices into points array
    owner_cell::Int            # Cell that owns this face
    neighbor_cell::Int         # Neighboring cell (-1 for boundary)
    area_vector::Point3D       # Area vector (magnitude = area, direction = normal)
    center::Point3D           # Face center
end

"""
Cell types for unstructured meshes
"""
@enum CellType begin
    TETRAHEDRON = 4   # 4 faces
    PYRAMID = 5       # 5 faces (pyramid)
    PRISM = 15        # 5 faces (prism - different from pyramid)
    HEXAHEDRON = 6    # 6 faces
    POLYHEDRON = 0    # Arbitrary number of faces
end

"""
Unstructured cell definition with arbitrary topology
"""
struct UnstructuredCell
    face_indices::Vector{Int}  # Indices into faces array
    volume::Float64           # Cell volume
    center::Point3D          # Cell center
    cell_type::CellType      # Type of cell
end

"""
Boundary patch for unstructured meshes
"""
struct UnstructuredBoundaryPatch
    name::String
    type::Symbol              # :wall, :inlet, :outlet, :symmetry, :patch
    face_indices::Vector{Int} # Boundary face indices
    start_face::Int          # Starting face index in boundary
    n_faces::Int             # Number of faces in patch
end

"""
Comprehensive unstructured mesh representation
"""
mutable struct Mesh
    # Basic topology
    points::Vector{Point3D}                           # Mesh vertices
    faces::Vector{UnstructuredFace}                  # All faces (internal + boundary)
    cells::Vector{UnstructuredCell}                  # All cells
    
    # Connectivity information
    cell_faces::Vector{Vector{Int}}                  # faces for each cell
    face_cells::Vector{Tuple{Int,Int}}              # (owner, neighbor) for each face
    cell_neighbors::Vector{Vector{Int}}              # neighboring cells for each cell
    
    # Boundary information
    boundary_patches::Vector{UnstructuredBoundaryPatch}  # Boundary patches
    boundary_face_map::Dict{Int, Tuple{String, Int}}     # face_id -> (patch_name, local_id)
    
    # Geometric data
    cell_volumes::Vector{Float64}                    # Cell volumes
    face_areas::Vector{Float64}                     # Face areas
    face_normals::Vector{Point3D}                   # Face normal vectors
    
    # Mesh quality metrics
    orthogonality::Vector{Float64}                  # Face orthogonality
    skewness::Vector{Float64}                       # Face skewness
    aspect_ratio::Vector{Float64}                   # Cell aspect ratios
    
    # Performance optimization data
    internal_faces::UnitRange{Int}                  # Range of internal faces
    boundary_faces::UnitRange{Int}                  # Range of boundary faces
    
    # Parallel decomposition support
    processor_boundaries::Dict{Int, Vector{Int}}     # processor_id -> face_indices
    ghost_cells::Vector{Int}                        # Ghost cell indices for parallel
end

# ============================================================================
# UNSTRUCTURED MESH CONSTRUCTION
# ============================================================================

"""
Create unstructured mesh from basic connectivity data
"""
function Mesh(
    points::Vector{Point3D},
    face_point_lists::Vector{Vector{Int}},
    face_owners::Vector{Int},
    face_neighbors::Vector{Int},
    boundary_patches::Vector{UnstructuredBoundaryPatch}
)
    
    n_points = length(points)
    n_faces = length(face_point_lists)
    n_internal_faces = count(x -> x > 0, face_neighbors)
    n_boundary_faces = n_faces - n_internal_faces
    
    # Build faces with geometric data
    faces = UnstructuredFace[]
    for i in 1:n_faces
        face_points = [points[j] for j in face_point_lists[i]]
        center = calculate_face_center(face_points)
        area_vector = calculate_face_area_vector(face_points)
        
        push!(faces, UnstructuredFace(
            face_point_lists[i],
            face_owners[i],
            face_neighbors[i],
            area_vector,
            center
        ))
    end
    
    # Determine number of cells
    n_cells = maximum(face_owners)
    if n_internal_faces > 0
        n_cells = max(n_cells, maximum(face_neighbors[face_neighbors .> 0]))
    end
    
    # Build cell-face connectivity
    cell_faces = [Int[] for _ in 1:n_cells]
    for (face_id, face) in enumerate(faces)
        push!(cell_faces[face.owner_cell], face_id)
        if face.neighbor_cell > 0
            push!(cell_faces[face.neighbor_cell], face_id)
        end
    end
    
    # Build cells with geometric data
    cells = UnstructuredCell[]
    for cell_id in 1:n_cells
        cell_face_indices = cell_faces[cell_id]
        cell_face_list = [faces[i] for i in cell_face_indices]
        
        volume = calculate_cell_volume(cell_face_list, cell_id)
        center = calculate_cell_center(cell_face_list, cell_id)
        cell_type = determine_cell_type(length(cell_face_indices))
        
        push!(cells, UnstructuredCell(cell_face_indices, volume, center, cell_type))
    end
    
    # Build additional connectivity
    face_cells = [(faces[i].owner_cell, faces[i].neighbor_cell) for i in 1:n_faces]
    cell_neighbors = build_cell_neighbors(cells, faces, n_cells)
    
    # Calculate geometric metrics
    cell_volumes = [cell.volume for cell in cells]
    face_areas = [norm(face.area_vector) for face in faces]
    face_normals = [begin
        area_mag = norm(face.area_vector)
        area_mag > 1e-12 ? face.area_vector / area_mag : Point3D(1.0, 0.0, 0.0)
    end for face in faces]
    
    # Calculate mesh quality
    orthogonality = calculate_orthogonality(faces, cells)
    skewness = calculate_skewness(faces, cells)
    aspect_ratio = calculate_aspect_ratios(cells, faces)
    
    # Setup boundary mapping
    boundary_face_map = Dict{Int, Tuple{String, Int}}()
    for patch in boundary_patches
        for (local_id, face_id) in enumerate(patch.face_indices)
            boundary_face_map[face_id] = (patch.name, local_id)
        end
    end
    
    # Define face ranges
    internal_faces = 1:n_internal_faces
    boundary_faces = (n_internal_faces + 1):n_faces
    
    return Mesh(
        points, faces, cells,
        cell_faces, face_cells, cell_neighbors,
        boundary_patches, boundary_face_map,
        cell_volumes, face_areas, face_normals,
        orthogonality, skewness, aspect_ratio,
        internal_faces, boundary_faces,
        Dict{Int, Vector{Int}}(), Int[]
    )
end

# ============================================================================
# GEOMETRIC CALCULATIONS
# ============================================================================

"""
Calculate face center from point list
"""
function calculate_face_center(face_points::Vector{Point3D})
    n = length(face_points)
    center = Point3D(0.0, 0.0, 0.0)
    for point in face_points
        center = center + point
    end
    return Point3D(center.x / n, center.y / n, center.z / n)
end

"""
Calculate face area vector using cross product method
"""
function calculate_face_area_vector(face_points::Vector{Point3D})
    n = length(face_points)
    if n < 3
        return Point3D(0.0, 0.0, 0.0)
    end
    
    # Use fan triangulation from first vertex
    area_vector = Point3D(0.0, 0.0, 0.0)
    p0 = face_points[1]
    
    for i in 2:(n-1)
        p1 = face_points[i]
        p2 = face_points[i+1]
        
        # Triangle area vector = 0.5 * (p1-p0) × (p2-p0)
        v1 = p1 - p0
        v2 = p2 - p0
        
        cross_product = Point3D(
            v1.y * v2.z - v1.z * v2.y,
            v1.z * v2.x - v1.x * v2.z,
            v1.x * v2.y - v1.y * v2.x
        )
        
        area_vector = area_vector + 0.5 * cross_product
    end
    
    return area_vector
end

"""
Calculate cell volume using divergence theorem
"""
function calculate_cell_volume(cell_faces::Vector{UnstructuredFace}, cell_id::Int)
    volume = 0.0
    
    for face in cell_faces
        # Determine if face normal points outward from this cell
        sign = (face.owner_cell == cell_id) ? 1.0 : -1.0
        
        # Volume contribution = (1/3) * face_center · face_area_vector
        volume += sign * (1.0/3.0) * dot(face.center, face.area_vector)
    end
    
    return abs(volume)
end

"""
Calculate cell center using area-weighted face centers
"""
function calculate_cell_center(cell_faces::Vector{UnstructuredFace}, cell_id::Int)
    total_area = 0.0
    weighted_center = Point3D(0.0, 0.0, 0.0)
    
    for face in cell_faces
        area = norm(face.area_vector)
        total_area += area
        weighted_center = weighted_center + area * face.center
    end
    
    if total_area > 0
        return Point3D(
            weighted_center.x / total_area,
            weighted_center.y / total_area,
            weighted_center.z / total_area
        )
    else
        return Point3D(0.0, 0.0, 0.0)
    end
end

"""
Determine cell type from number of faces
"""
function determine_cell_type(n_faces::Int)
    if n_faces == 4
        return TETRAHEDRON
    elseif n_faces == 5
        return PYRAMID  # Could also be PRISM - would need more analysis
    elseif n_faces == 6
        return HEXAHEDRON
    else
        return POLYHEDRON
    end
end

"""
Build cell neighbor connectivity
"""
function build_cell_neighbors(cells::Vector{UnstructuredCell}, faces::Vector{UnstructuredFace}, n_cells::Int)
    neighbors = [Int[] for _ in 1:n_cells]
    
    for face in faces
        if face.neighbor_cell > 0  # Internal face
            push!(neighbors[face.owner_cell], face.neighbor_cell)
            push!(neighbors[face.neighbor_cell], face.owner_cell)
        end
    end
    
    # Remove duplicates and sort
    for i in 1:n_cells
        neighbors[i] = sort(unique(neighbors[i]))
    end
    
    return neighbors
end

# ============================================================================
# MESH QUALITY ASSESSMENT
# ============================================================================

"""
Calculate face orthogonality (0 = orthogonal, 1 = skewed)
"""
function calculate_orthogonality(faces::Vector{UnstructuredFace}, cells::Vector{UnstructuredCell})
    orthogonality = Float64[]
    
    for face in faces
        if face.neighbor_cell > 0  # Internal face
            # Vector between cell centers
            owner_center = cells[face.owner_cell].center
            neighbor_center = cells[face.neighbor_cell].center
            cell_vector = neighbor_center - owner_center
            
            # Face normal
            face_normal = face.area_vector / norm(face.area_vector)
            
            # Orthogonality = 1 - |cos(angle)|
            cos_angle = abs(dot(cell_vector, face_normal)) / norm(cell_vector)
            push!(orthogonality, 1.0 - cos_angle)
        else
            push!(orthogonality, 0.0)  # Boundary faces assumed orthogonal
        end
    end
    
    return orthogonality
end

"""
Calculate face skewness
"""
function calculate_skewness(faces::Vector{UnstructuredFace}, cells::Vector{UnstructuredCell})
    skewness = Float64[]
    
    for face in faces
        if face.neighbor_cell > 0  # Internal face
            # Distance from face center to line connecting cell centers
            owner_center = cells[face.owner_cell].center
            neighbor_center = cells[face.neighbor_cell].center
            cell_vector = neighbor_center - owner_center
            
            # Vector from owner center to face center
            face_vector = face.center - owner_center
            
            # Project face vector onto cell vector
            projection_length = dot(face_vector, cell_vector) / norm(cell_vector)
            projection = projection_length * (cell_vector / norm(cell_vector))
            
            # Skew vector is perpendicular component
            skew_vector = face_vector - projection
            skew_magnitude = norm(skew_vector)
            
            # Normalize by cell-to-cell distance
            push!(skewness, skew_magnitude / norm(cell_vector))
        else
            push!(skewness, 0.0)
        end
    end
    
    return skewness
end

"""
Calculate cell aspect ratios
"""
function calculate_aspect_ratios(cells::Vector{UnstructuredCell}, faces::Vector{UnstructuredFace})
    aspect_ratios = Float64[]
    
    for cell in cells
        # Find maximum and minimum face areas for this cell
        cell_face_areas = [norm(faces[i].area_vector) for i in cell.face_indices]
        
        if !isempty(cell_face_areas)
            max_area = maximum(cell_face_areas)
            min_area = minimum(cell_face_areas)
            push!(aspect_ratios, max_area / max(min_area, 1e-12))
        else
            push!(aspect_ratios, 1.0)
        end
    end
    
    return aspect_ratios
end

# ============================================================================
# OPENFOAM POLYMESH IMPORT/EXPORT
# ============================================================================

"""
Import OpenFOAM polyMesh format
"""
function import_openfoam_polymesh(polymesh_dir::String)
    # Read points file
    points_file = joinpath(polymesh_dir, "points")
    points = read_openfoam_points(points_file)
    
    # Read faces file  
    faces_file = joinpath(polymesh_dir, "faces")
    face_point_lists = read_openfoam_faces(faces_file)
    
    # Read owner file
    owner_file = joinpath(polymesh_dir, "owner")
    face_owners = read_openfoam_owner(owner_file)
    
    # Read neighbor file
    neighbor_file = joinpath(polymesh_dir, "neighbour")
    face_neighbors = read_openfoam_neighbor(neighbor_file)
    
    # Read boundary file
    boundary_file = joinpath(polymesh_dir, "boundary")
    boundary_patches = read_openfoam_boundary(boundary_file)
    
    return Mesh(
        points, face_point_lists, face_owners, face_neighbors, boundary_patches
    )
end

"""
Read OpenFOAM points file
"""
function read_openfoam_points(points_file::String)
    content = read(points_file, String)
    lines = split(content, '\n')
    
    points = Point3D[]
    in_points = false
    
    for line in lines
        line = strip(line)
        if occursin("(", line) && in_points
            # Parse point coordinates
            coords_match = match(r"\((.*?)\)", line)
            if coords_match !== nothing
                coords_str = coords_match.captures[1]
                coords = parse.(Float64, split(coords_str))
                if length(coords) >= 3
                    push!(points, Point3D(coords[1], coords[2], coords[3]))
                end
            end
        elseif occursin(r"^\d+$", line) && !in_points
            # Number indicating start of points
            in_points = true
        end
    end
    
    return points
end

"""
Read OpenFOAM faces file
"""
function read_openfoam_faces(faces_file::String)
    content = read(faces_file, String)
    lines = split(content, '\n')
    
    face_point_lists = Vector{Int}[]
    in_faces = false
    
    for line in lines
        line = strip(line)
        if occursin(r"^\d+\(", line) && in_faces
            # Parse face point list
            points_match = match(r"\d+\((.*?)\)", line)
            if points_match !== nothing
                points_str = points_match.captures[1]
                # OpenFOAM uses 0-based indexing, convert to 1-based
                point_indices = parse.(Int, split(points_str)) .+ 1
                push!(face_point_lists, point_indices)
            end
        elseif occursin(r"^\d+$", line) && !in_faces
            in_faces = true
        end
    end
    
    return face_point_lists
end

"""
Read OpenFOAM owner file
"""
function read_openfoam_owner(owner_file::String)
    content = read(owner_file, String)
    lines = split(content, '\n')
    
    owners = Int[]
    in_data = false
    
    for line in lines
        line = strip(line)
        if occursin(r"^\d+$", line)
            if in_data
                # OpenFOAM uses 0-based indexing, convert to 1-based
                push!(owners, parse(Int, line) + 1)
            else
                in_data = true
            end
        end
    end
    
    return owners
end

"""
Read OpenFOAM neighbor file
"""
function read_openfoam_neighbor(neighbor_file::String)
    content = read(neighbor_file, String)
    lines = split(content, '\n')
    
    neighbors = Int[]
    in_data = false
    
    for line in lines
        line = strip(line)
        if occursin(r"^\d+$", line)
            if in_data
                # OpenFOAM uses 0-based indexing, convert to 1-based
                push!(neighbors, parse(Int, line) + 1)
            else
                in_data = true
            end
        end
    end
    
    return neighbors
end

"""
Read OpenFOAM boundary file
"""
function read_openfoam_boundary(boundary_file::String)
    content = read(boundary_file, String)
    
    # This would need more sophisticated parsing for real OpenFOAM boundary files
    # For now, return empty patches
    return UnstructuredBoundaryPatch[]
end

# ============================================================================
# MESH UTILITIES
# ============================================================================

"""
Get mesh statistics
"""
function mesh_statistics(mesh::Mesh)
    stats = Dict{String, Any}()
    
    stats["n_points"] = length(mesh.points)
    stats["n_faces"] = length(mesh.faces)
    stats["n_cells"] = length(mesh.cells)
    stats["n_internal_faces"] = length(mesh.internal_faces)
    stats["n_boundary_faces"] = length(mesh.boundary_faces)
    stats["n_boundary_patches"] = length(mesh.boundary_patches)
    
    # Volume statistics
    stats["total_volume"] = sum(mesh.cell_volumes)
    stats["min_volume"] = minimum(mesh.cell_volumes)
    stats["max_volume"] = maximum(mesh.cell_volumes)
    stats["volume_ratio"] = maximum(mesh.cell_volumes) / minimum(mesh.cell_volumes)
    
    # Quality statistics
    stats["max_orthogonality"] = maximum(mesh.orthogonality)
    stats["mean_orthogonality"] = sum(mesh.orthogonality) / length(mesh.orthogonality)
    stats["max_skewness"] = maximum(mesh.skewness)
    stats["mean_skewness"] = sum(mesh.skewness) / length(mesh.skewness)
    stats["max_aspect_ratio"] = maximum(mesh.aspect_ratio)
    stats["mean_aspect_ratio"] = sum(mesh.aspect_ratio) / length(mesh.aspect_ratio)
    
    return stats
end

"""
Check mesh quality and report issues
"""
function check_mesh_quality(mesh::Mesh; 
                           max_orthogonality=0.7, 
                           max_skewness=2.0, 
                           max_aspect_ratio=100.0)
    
    issues = String[]
    
    # Check orthogonality
    bad_ortho = count(x -> x > max_orthogonality, mesh.orthogonality)
    if bad_ortho > 0
        push!(issues, "$bad_ortho faces have orthogonality > $max_orthogonality")
    end
    
    # Check skewness
    bad_skew = count(x -> x > max_skewness, mesh.skewness)
    if bad_skew > 0
        push!(issues, "$bad_skew faces have skewness > $max_skewness")
    end
    
    # Check aspect ratio
    bad_aspect = count(x -> x > max_aspect_ratio, mesh.aspect_ratio)
    if bad_aspect > 0
        push!(issues, "$bad_aspect cells have aspect ratio > $max_aspect_ratio")
    end
    
    # Check for negative volumes
    negative_volumes = count(x -> x <= 0, mesh.cell_volumes)
    if negative_volumes > 0
        push!(issues, "$negative_volumes cells have negative or zero volume")
    end
    
    return issues
end

export Mesh, Point3D, UnstructuredFace, UnstructuredCell, UnstructuredBoundaryPatch
export import_openfoam_polymesh, mesh_statistics, check_mesh_quality
export calculate_face_center, calculate_face_area_vector, calculate_cell_volume
export validate_complex_geometry, handle_non_convex_cells, detect_curved_boundaries
export calculate_mesh_curvature, assess_boundary_layer_quality
export repair_mesh_connectivity, optimize_mesh_topology

# ============================================================================
# COMPLEX GEOMETRY HANDLING
# ============================================================================

"""
    validate_complex_geometry(mesh::Mesh) -> Vector{String}

Comprehensive validation for complex geometries including non-convex cells,
curved boundaries, and topological consistency.
"""
function validate_complex_geometry(mesh::Mesh)
    issues = String[]

    println("🔍 Validating complex geometry...")

    # 1. Check for non-convex cells
    non_convex_cells = detect_non_convex_cells(mesh)
    if !isempty(non_convex_cells)
        push!(issues, "$(length(non_convex_cells)) non-convex cells detected")
        println("   ⚠️  Non-convex cells: $(length(non_convex_cells))")
    end

    # 2. Validate face planarity
    non_planar_faces = detect_non_planar_faces(mesh)
    if !isempty(non_planar_faces)
        push!(issues, "$(length(non_planar_faces)) non-planar faces detected")
        println("   ⚠️  Non-planar faces: $(length(non_planar_faces))")
    end

    # 3. Check boundary curvature
    curved_boundaries = detect_curved_boundaries(mesh)
    if !isempty(curved_boundaries)
        println("   📐 Curved boundaries detected: $(length(curved_boundaries)) patches")
    end

    # 4. Validate cell closure
    open_cells = detect_open_cells(mesh)
    if !isempty(open_cells)
        push!(issues, "$(length(open_cells)) cells are not properly closed")
        println("   ❌ Open cells: $(length(open_cells))")
    end

    # 5. Check for degenerate elements
    degenerate_elements = detect_degenerate_elements(mesh)
    if !isempty(degenerate_elements)
        push!(issues, "$(length(degenerate_elements)) degenerate elements detected")
        println("   ⚠️  Degenerate elements: $(length(degenerate_elements))")
    end

    # 6. Validate boundary layer quality
    boundary_layer_issues = assess_boundary_layer_quality(mesh)
    if !isempty(boundary_layer_issues)
        append!(issues, boundary_layer_issues)
    end

    if isempty(issues)
        println("   ✅ Complex geometry validation passed")
    else
        println("   ❌ $(length(issues)) geometry issues found")
    end

    return issues
end

"""
    detect_non_convex_cells(mesh::Mesh) -> Vector{Int}

Detect cells that are non-convex, which can cause numerical issues.
"""
function detect_non_convex_cells(mesh::Mesh)
    non_convex_cells = Int[]

    for (cell_id, cell) in enumerate(mesh.cells)
        if is_cell_non_convex(mesh, cell_id)
            push!(non_convex_cells, cell_id)
        end
    end

    return non_convex_cells
end

"""
    is_cell_non_convex(mesh::Mesh, cell_id::Int) -> Bool

Check if a cell is non-convex using face normal consistency.
"""
function is_cell_non_convex(mesh::Mesh, cell_id::Int)
    cell = mesh.cells[cell_id]
    cell_center = cell.center

    # Check if all face normals point outward from cell center
    for face_id in cell.face_indices
        face = mesh.faces[face_id]

        # Get face normal (pointing from owner to neighbor)
        face_normal = face.area_vector / norm(face.area_vector)

        # Vector from cell center to face center
        to_face = face.center - cell_center

        # For convex cells, face normal should align with to_face vector
        # (when this cell is the owner)
        if face.owner_cell == cell_id
            if dot(face_normal, to_face) < 0
                return true  # Non-convex
            end
        else
            # This cell is the neighbor, so normal should be opposite
            if dot(face_normal, to_face) > 0
                return true  # Non-convex
            end
        end
    end

    return false  # Convex
end

"""
    detect_non_planar_faces(mesh::Mesh, tolerance::Float64=1e-6) -> Vector{Int}

Detect faces that are significantly non-planar.
"""
function detect_non_planar_faces(mesh::Mesh, tolerance::Float64=1e-6)
    non_planar_faces = Int[]

    for (face_id, face) in enumerate(mesh.faces)
        if length(face.point_indices) > 3  # Triangles are always planar
            planarity_error = calculate_face_planarity_error(mesh, face_id)
            if planarity_error > tolerance
                push!(non_planar_faces, face_id)
            end
        end
    end

    return non_planar_faces
end

"""
    calculate_face_planarity_error(mesh::Mesh, face_id::Int) -> Float64

Calculate the maximum deviation from planarity for a face.
"""
function calculate_face_planarity_error(mesh::Mesh, face_id::Int)
    face = mesh.faces[face_id]
    points = [mesh.points[i] for i in face.point_indices]

    if length(points) < 4
        return 0.0  # Triangles are always planar
    end

    # Fit a plane to the first three points
    p1, p2, p3 = points[1], points[2], points[3]
    v1 = p2 - p1
    v2 = p3 - p1
    # Convert to vectors for cross product
    v1_vec = [v1[1], v1[2], v1[3]]
    v2_vec = [v2[1], v2[2], v2[3]]
    normal_vec = normalize(cross(v1_vec, v2_vec))
    normal = Point3D(normal_vec[1], normal_vec[2], normal_vec[3])

    # Calculate distance of all other points from this plane
    max_deviation = 0.0
    for i in 4:length(points)
        point = points[i]
        # Convert to vectors for dot product
        diff_vec = [point[1] - p1[1], point[2] - p1[2], point[3] - p1[3]]
        normal_vec = [normal[1], normal[2], normal[3]]
        distance = abs(dot(diff_vec, normal_vec))
        max_deviation = max(max_deviation, distance)
    end

    return max_deviation
end

"""
    detect_curved_boundaries(mesh::Mesh) -> Vector{String}

Detect boundary patches that have significant curvature.
"""
function detect_curved_boundaries(mesh::Mesh)
    curved_patches = String[]

    for patch in mesh.boundary_patches
        curvature = calculate_patch_curvature(mesh, patch)
        if curvature > 0.1  # Threshold for "curved"
            push!(curved_patches, patch.name)
        end
    end

    return curved_patches
end

"""
    calculate_patch_curvature(mesh::Mesh, patch::UnstructuredBoundaryPatch) -> Float64

Calculate the average curvature of a boundary patch.
"""
function calculate_patch_curvature(mesh::Mesh, patch::UnstructuredBoundaryPatch)
    if length(patch.face_indices) < 3
        return 0.0
    end

    total_curvature = 0.0
    valid_faces = 0

    for face_id in patch.face_indices
        face = mesh.faces[face_id]

        # Find neighboring faces on the same patch
        neighbors = find_face_neighbors_on_patch(mesh, face_id, patch)

        if length(neighbors) >= 2
            # Calculate local curvature using neighboring face normals
            face_normal = normalize(face.area_vector)

            curvature_sum = 0.0
            for neighbor_id in neighbors
                neighbor_face = mesh.faces[neighbor_id]
                neighbor_normal = normalize(neighbor_face.area_vector)

                # Angle between normals indicates curvature
                angle = acos(clamp(dot(face_normal, neighbor_normal), -1.0, 1.0))
                curvature_sum += angle
            end

            total_curvature += curvature_sum / length(neighbors)
            valid_faces += 1
        end
    end

    return valid_faces > 0 ? total_curvature / valid_faces : 0.0
end

"""
    detect_open_cells(mesh::Mesh) -> Vector{Int}

Detect cells that are not properly closed (missing faces).
"""
function detect_open_cells(mesh::Mesh)
    open_cells = Int[]

    for (cell_id, cell) in enumerate(mesh.cells)
        if !is_cell_closed(mesh, cell_id)
            push!(open_cells, cell_id)
        end
    end

    return open_cells
end

"""
    is_cell_closed(mesh::Mesh, cell_id::Int) -> Bool

Check if a cell is properly closed by verifying face connectivity.
"""
function is_cell_closed(mesh::Mesh, cell_id::Int)
    cell = mesh.cells[cell_id]

    # For a closed cell, the sum of all face area vectors should be zero
    total_area_vector = Point3D(0.0, 0.0, 0.0)

    for face_id in cell.face_indices
        face = mesh.faces[face_id]

        # Add or subtract face area vector depending on orientation
        if face.owner_cell == cell_id
            total_area_vector += face.area_vector
        else
            total_area_vector -= face.area_vector
        end
    end

    # Check if the sum is close to zero
    return norm(total_area_vector) < 1e-10 * cell.volume^(2/3)
end

"""
    detect_degenerate_elements(mesh::Mesh) -> Vector{Int}

Detect degenerate elements (zero volume cells, zero area faces).
"""
function detect_degenerate_elements(mesh::Mesh)
    degenerate_elements = Int[]

    # Check for zero volume cells
    for (cell_id, cell) in enumerate(mesh.cells)
        if cell.volume <= 1e-15
            push!(degenerate_elements, cell_id)
        end
    end

    return degenerate_elements
end

"""
    assess_boundary_layer_quality(mesh::Mesh) -> Vector{String}

Assess the quality of boundary layer meshes.
"""
function assess_boundary_layer_quality(mesh::Mesh)
    issues = String[]

    # Find wall boundaries (typically where boundary layers are important)
    wall_patches = filter(p -> contains(lowercase(p.name), "wall"), mesh.boundary_patches)

    for patch in wall_patches
        # Check aspect ratios near walls
        high_aspect_ratio_cells = find_high_aspect_ratio_cells_near_patch(mesh, patch)

        if length(high_aspect_ratio_cells) > 0.1 * length(patch.face_indices)
            push!(issues, "High aspect ratio cells near $(patch.name): $(length(high_aspect_ratio_cells))")
        end

        # Check y+ estimation (simplified)
        y_plus_issues = estimate_y_plus_issues(mesh, patch)
        if !isempty(y_plus_issues)
            append!(issues, y_plus_issues)
        end
    end

    return issues
end

"""
    find_high_aspect_ratio_cells_near_patch(mesh::Mesh, patch::UnstructuredBoundaryPatch) -> Vector{Int}

Find cells with high aspect ratios near a boundary patch.
"""
function find_high_aspect_ratio_cells_near_patch(mesh::Mesh, patch::UnstructuredBoundaryPatch)
    high_aspect_cells = Int[]
    threshold = 100.0  # Aspect ratio threshold

    for face_id in patch.face_indices
        face = mesh.faces[face_id]
        cell_id = face.owner_cell  # Boundary faces only have owner

        if cell_id > 0 && cell_id <= length(mesh.aspect_ratio)
            if mesh.aspect_ratio[cell_id] > threshold
                push!(high_aspect_cells, cell_id)
            end
        end
    end

    return unique(high_aspect_cells)
end

"""
    estimate_y_plus_issues(mesh::Mesh, patch::UnstructuredBoundaryPatch) -> Vector{String}

Estimate y+ values and identify potential issues.
"""
function estimate_y_plus_issues(mesh::Mesh, patch::UnstructuredBoundaryPatch)
    issues = String[]

    # This is a simplified estimation - in practice would need flow conditions
    wall_distances = calculate_wall_distances(mesh, patch)

    # Check for very small first cell heights
    min_distance = minimum(wall_distances)
    max_distance = maximum(wall_distances)

    if min_distance < 1e-6
        push!(issues, "Very small first cell height near $(patch.name): $(min_distance)")
    end

    if max_distance / min_distance > 10.0
        push!(issues, "Large variation in wall distance for $(patch.name)")
    end

    return issues
end

"""
    calculate_wall_distances(mesh::Mesh, patch::UnstructuredBoundaryPatch) -> Vector{Float64}

Calculate distances from wall faces to adjacent cell centers.
"""
function calculate_wall_distances(mesh::Mesh, patch::UnstructuredBoundaryPatch)
    distances = Float64[]

    for face_id in patch.face_indices
        face = mesh.faces[face_id]
        cell_id = face.owner_cell

        if cell_id > 0 && cell_id <= length(mesh.cells)
            cell_center = mesh.cells[cell_id].center
            distance = norm(cell_center - face.center)
            push!(distances, distance)
        end
    end

    return distances
end

"""
    find_face_neighbors_on_patch(mesh::Mesh, face_id::Int, patch::UnstructuredBoundaryPatch) -> Vector{Int}

Find neighboring faces on the same boundary patch.
"""
function find_face_neighbors_on_patch(mesh::Mesh, face_id::Int, patch::UnstructuredBoundaryPatch)
    neighbors = Int[]
    face = mesh.faces[face_id]

    # Find faces that share vertices with this face
    for other_face_id in patch.face_indices
        if other_face_id != face_id
            other_face = mesh.faces[other_face_id]

            # Check if faces share at least one vertex
            shared_vertices = intersect(face.point_indices, other_face.point_indices)
            if length(shared_vertices) >= 2  # Share an edge
                push!(neighbors, other_face_id)
            end
        end
    end

    return neighbors
end

# ============================================================================
# MESH REPAIR AND OPTIMIZATION
# ============================================================================

"""
    repair_mesh_connectivity(mesh::Mesh) -> Mesh

Attempt to repair common mesh connectivity issues.
"""
function repair_mesh_connectivity(mesh::Mesh)
    println("🔧 Repairing mesh connectivity...")

    # Create a copy to modify
    repaired_mesh = deepcopy(mesh)

    # 1. Fix face orientation issues
    fix_face_orientations!(repaired_mesh)

    # 2. Remove duplicate faces
    remove_duplicate_faces!(repaired_mesh)

    # 3. Fix cell-face connectivity
    rebuild_cell_face_connectivity!(repaired_mesh)

    # 4. Recalculate geometric properties
    recalculate_geometry!(repaired_mesh)

    println("   ✅ Mesh connectivity repaired")
    return repaired_mesh
end

"""
    fix_face_orientations!(mesh::Mesh)

Fix inconsistent face orientations.
"""
function fix_face_orientations!(mesh::Mesh)
    # This is a simplified implementation
    # In practice, would need more sophisticated algorithms
    for (face_id, face) in enumerate(mesh.faces)
        if face.neighbor_cell > 0  # Internal face
            # Ensure face normal points from owner to neighbor
            owner_center = mesh.cells[face.owner_cell].center
            neighbor_center = mesh.cells[face.neighbor_cell].center

            expected_direction = normalize(neighbor_center - owner_center)
            face_normal = normalize(face.area_vector)

            # If normals are opposite, flip the face
            if dot(expected_direction, face_normal) < 0
                # Flip face orientation
                mesh.faces[face_id] = UnstructuredFace(
                    reverse(face.point_indices),
                    face.owner_cell,
                    face.neighbor_cell,
                    -face.area_vector,
                    face.center
                )
            end
        end
    end
end

"""
    remove_duplicate_faces!(mesh::Mesh)

Remove duplicate faces from the mesh.
"""
function remove_duplicate_faces!(mesh::Mesh)
    # This would require rebuilding the entire mesh structure
    # Placeholder for now
    println("   📝 Duplicate face removal not yet implemented")
end

"""
    rebuild_cell_face_connectivity!(mesh::Mesh)

Rebuild cell-face connectivity from face data.
"""
function rebuild_cell_face_connectivity!(mesh::Mesh)
    # Clear existing connectivity
    mesh.cell_faces = [Int[] for _ in 1:length(mesh.cells)]

    # Rebuild from face data
    for (face_id, face) in enumerate(mesh.faces)
        push!(mesh.cell_faces[face.owner_cell], face_id)
        if face.neighbor_cell > 0
            push!(mesh.cell_faces[face.neighbor_cell], face_id)
        end
    end
end

"""
    recalculate_geometry!(mesh::Mesh)

Recalculate all geometric properties after mesh modifications.
"""
function recalculate_geometry!(mesh::Mesh)
    # Recalculate face centers and area vectors
    for (face_id, face) in enumerate(mesh.faces)
        points = [mesh.points[i] for i in face.point_indices]
        center = calculate_face_center(points)
        area_vector = calculate_face_area_vector(points)

        mesh.faces[face_id] = UnstructuredFace(
            face.point_indices,
            face.owner_cell,
            face.neighbor_cell,
            area_vector,
            center
        )
    end

    # Recalculate cell properties
    for (cell_id, cell) in enumerate(mesh.cells)
        cell_faces = [mesh.faces[i] for i in cell.face_indices]
        volume = calculate_cell_volume(cell_faces, cell_id)
        center = calculate_cell_center(cell_faces, cell_id)

        mesh.cells[cell_id] = UnstructuredCell(
            cell.face_indices,
            volume,
            center,
            cell.cell_type
        )
    end
end

"""
    optimize_mesh_topology(mesh::Mesh) -> Mesh

Optimize mesh topology for better numerical properties.
"""
function optimize_mesh_topology(mesh::Mesh)
    println("⚡ Optimizing mesh topology...")

    # This is a placeholder for advanced mesh optimization
    # Could include:
    # - Cell reordering for cache efficiency
    # - Face reordering for bandwidth reduction
    # - Geometric smoothing
    # - Topology optimization

    optimized_mesh = deepcopy(mesh)

    # Simple optimization: reorder cells by spatial locality
    reorder_cells_spatially!(optimized_mesh)

    println("   ✅ Mesh topology optimized")
    return optimized_mesh
end

"""
    reorder_cells_spatially!(mesh::Mesh)

Reorder cells for better spatial locality.
"""
function reorder_cells_spatially!(mesh::Mesh)
    # Simple space-filling curve ordering (placeholder)
    cell_centers = [cell.center for cell in mesh.cells]

    # Sort by x-coordinate (simple 1D ordering)
    perm = sortperm(cell_centers, by = p -> p[1])

    # Reorder cells
    mesh.cells = mesh.cells[perm]

    # Update face references (would need more work in practice)
    println("   📝 Spatial reordering applied")
end

end # module UnstructuredMesh