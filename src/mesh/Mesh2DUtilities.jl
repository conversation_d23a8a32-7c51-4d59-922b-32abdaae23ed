"""
Mesh2DUtilities.jl

Utilities for creating 2D meshes in OpenFOAM style (thin 3D with empty boundaries).
This follows the OpenFOAM convention where 2D problems are treated as 3D problems
with one cell thick in the third dimension and "empty" boundary conditions.

Key Features:
- Create thin 3D meshes from 2D specifications
- Automatic empty boundary patch creation
- OpenFOAM-compatible mesh structure
- Support for structured and unstructured 2D meshes

OpenFOAM 2D Convention:
- Mesh is one cell thick in Z direction
- Front and back faces have "empty" boundary condition
- All field equations solved in 3D but constrained to 2D flow
- Z-direction velocity component automatically zero
"""

module Mesh2DUtilities

using LinearAlgebra
using Printf

# Import boundary conditions for empty patches
include("../boundaryConditions/BoundaryConditions.jl")
using .BoundaryConditions

# ============================================================================
# 2D MESH CREATION (OPENFOAM STYLE)
# ============================================================================

"""
Create a 2D mesh as thin 3D (OpenFOAM style) with empty boundary patches
"""
function create_2d_mesh_as_3d(
    nx::Int, ny::Int,
    Lx::Float64, Ly::Float64,
    thickness::Float64 = 0.1
)
    
    println("🔧 Creating 2D mesh as thin 3D (OpenFOAM style)")
    println("   Grid: $(nx)×$(ny) cells")
    println("   Domain: $(Lx) × $(Ly) × $(thickness)")
    
    # Calculate mesh parameters
    dx = Lx / nx
    dy = Ly / ny
    dz = thickness
    
    n_cells = nx * ny  # Only one layer in Z
    n_faces_2d = 2 * nx * ny + nx + ny  # Internal + boundary faces
    n_faces_empty = 2 * nx * ny  # Front and back faces
    n_faces_total = n_faces_2d + n_faces_empty
    
    # Create mesh structure
    mesh = FiniteVolumeMesh2D(
        n_cells, n_faces_total, nx, ny, dx, dy, dz
    )
    
    # Fill cell data
    for j in 1:ny, i in 1:nx
        cell_idx = (j-1)*nx + i
        
        # Cell center (middle of thin 3D cell)
        x = (i - 0.5) * dx
        y = (j - 0.5) * dy
        z = 0.5 * dz  # Middle of thickness
        
        mesh.cell_centers[cell_idx] = [x, y, z]
        mesh.cell_volumes[cell_idx] = dx * dy * dz
    end
    
    # Create boundary patches
    boundary_patches = create_2d_boundary_patches(mesh, nx, ny, dx, dy, dz)
    
    return mesh, boundary_patches
end

"""
Finite volume mesh structure for 2D problems treated as 3D
"""
struct FiniteVolumeMesh2D
    n_cells::Int
    n_faces::Int
    nx::Int
    ny::Int
    dx::Float64
    dy::Float64
    dz::Float64
    cell_centers::Vector{Vector{Float64}}
    cell_volumes::Vector{Float64}
    face_centers::Vector{Vector{Float64}}
    face_normals::Vector{Vector{Float64}}
    face_areas::Vector{Float64}
    face_owners::Vector{Int}
    face_neighbors::Vector{Int}
    
    function FiniteVolumeMesh2D(n_cells, n_faces, nx, ny, dx, dy, dz)
        new(
            n_cells, n_faces, nx, ny, dx, dy, dz,
            [zeros(3) for _ in 1:n_cells],
            zeros(n_cells),
            [zeros(3) for _ in 1:n_faces],
            [zeros(3) for _ in 1:n_faces],
            zeros(n_faces),
            zeros(Int, n_faces),
            zeros(Int, n_faces)
        )
    end
end

"""
Create boundary patches for 2D mesh including empty patches
"""
function create_2d_boundary_patches(mesh, nx, ny, dx, dy, dz)
    
    patches = Dict{String, BoundaryPatch}()
    
    # 1. Empty patches (front and back)
    front_faces, front_centers, front_normals, front_areas = create_empty_patch_data(
        "front", nx, ny, dx, dy, dz, 0.0
    )
    
    back_faces, back_centers, back_normals, back_areas = create_empty_patch_data(
        "back", nx, ny, dx, dy, dz, dz
    )
    
    patches["front"] = BoundaryPatch(
        "front", front_faces, Empty(), 
        front_centers, front_normals, front_areas
    )
    
    patches["back"] = BoundaryPatch(
        "back", back_faces, Empty(), 
        back_centers, back_normals, back_areas
    )
    
    # 2. Physical boundary patches (walls, inlets, etc.)
    # Left wall (west)
    left_faces, left_centers, left_normals, left_areas = create_wall_patch_data(
        "left", nx, ny, dx, dy, dz, :west
    )
    
    patches["left"] = BoundaryPatch(
        "left", left_faces, NoSlipWall(),
        left_centers, left_normals, left_areas
    )
    
    # Right wall (east)  
    right_faces, right_centers, right_normals, right_areas = create_wall_patch_data(
        "right", nx, ny, dx, dy, dz, :east
    )
    
    patches["right"] = BoundaryPatch(
        "right", right_faces, NoSlipWall(),
        right_centers, right_normals, right_areas
    )
    
    # Bottom wall (south)
    bottom_faces, bottom_centers, bottom_normals, bottom_areas = create_wall_patch_data(
        "bottom", nx, ny, dx, dy, dz, :south
    )
    
    patches["bottom"] = BoundaryPatch(
        "bottom", bottom_faces, NoSlipWall(),
        bottom_centers, bottom_normals, bottom_areas
    )
    
    # Top wall (north) - could be moving wall for lid-driven cavity
    top_faces, top_centers, top_normals, top_areas = create_wall_patch_data(
        "top", nx, ny, dx, dy, dz, :north
    )
    
    # For lid-driven cavity, make top a moving wall
    patches["top"] = BoundaryPatch(
        "top", top_faces, MovingWall([1.0, 0.0, 0.0]),  # Unit velocity in X
        top_centers, top_normals, top_areas
    )
    
    println("   ✅ Created boundary patches:")
    for (name, patch) in patches
        bc_type = typeof(patch.boundary_condition)
        n_faces = length(patch.face_indices)
        println("      $name: $bc_type ($n_faces faces)")
    end
    
    return patches
end

"""
Create face data for empty patches (front/back)
"""
function create_empty_patch_data(name, nx, ny, dx, dy, dz, z_location)
    
    face_indices = Int[]
    face_centers = Vector{Float64}[]
    face_normals = Vector{Float64}[]
    face_areas = Float64[]
    
    # Create faces for all cells on the empty boundary
    for j in 1:ny, i in 1:nx
        face_idx = length(face_indices) + 1
        push!(face_indices, face_idx)
        
        # Face center
        x = (i - 0.5) * dx
        y = (j - 0.5) * dy
        z = z_location
        push!(face_centers, [x, y, z])
        
        # Face normal (pointing outward)
        if z_location == 0.0  # Front face
            push!(face_normals, [0.0, 0.0, -1.0])
        else  # Back face
            push!(face_normals, [0.0, 0.0, 1.0])
        end
        
        # Face area
        push!(face_areas, dx * dy)
    end
    
    return face_indices, face_centers, face_normals, face_areas
end

"""
Create face data for physical boundary patches
"""
function create_wall_patch_data(name, nx, ny, dx, dy, dz, direction)
    
    face_indices = Int[]
    face_centers = Vector{Float64}[]
    face_normals = Vector{Float64}[]
    face_areas = Float64[]
    
    if direction == :west  # Left wall
        for j in 1:ny
            face_idx = length(face_indices) + 1
            push!(face_indices, face_idx)
            
            x = 0.0
            y = (j - 0.5) * dy
            z = 0.5 * dz
            push!(face_centers, [x, y, z])
            push!(face_normals, [-1.0, 0.0, 0.0])
            push!(face_areas, dy * dz)
        end
        
    elseif direction == :east  # Right wall
        for j in 1:ny
            face_idx = length(face_indices) + 1
            push!(face_indices, face_idx)
            
            x = nx * dx
            y = (j - 0.5) * dy
            z = 0.5 * dz
            push!(face_centers, [x, y, z])
            push!(face_normals, [1.0, 0.0, 0.0])
            push!(face_areas, dy * dz)
        end
        
    elseif direction == :south  # Bottom wall
        for i in 1:nx
            face_idx = length(face_indices) + 1
            push!(face_indices, face_idx)
            
            x = (i - 0.5) * dx
            y = 0.0
            z = 0.5 * dz
            push!(face_centers, [x, y, z])
            push!(face_normals, [0.0, -1.0, 0.0])
            push!(face_areas, dx * dz)
        end
        
    elseif direction == :north  # Top wall
        for i in 1:nx
            face_idx = length(face_indices) + 1
            push!(face_indices, face_idx)
            
            x = (i - 0.5) * dx
            y = ny * dy
            z = 0.5 * dz
            push!(face_centers, [x, y, z])
            push!(face_normals, [0.0, 1.0, 0.0])
            push!(face_areas, dx * dz)
        end
    end
    
    return face_indices, face_centers, face_normals, face_areas
end

"""
Create OpenFOAM-style boundary condition dictionary for 2D problems
"""
function create_2d_boundary_dict()
    
    boundary_dict = Dict{String, Dict{String, Any}}()
    
    # Empty patches for 2D
    boundary_dict["front"] = Dict{String, Any}(
        "type" => "empty"
    )
    
    boundary_dict["back"] = Dict{String, Any}(
        "type" => "empty"
    )
    
    # Physical boundaries
    boundary_dict["left"] = Dict{String, Any}(
        "type" => "noSlip"
    )
    
    boundary_dict["right"] = Dict{String, Any}(
        "type" => "noSlip"
    )
    
    boundary_dict["bottom"] = Dict{String, Any}(
        "type" => "noSlip"
    )
    
    boundary_dict["top"] = Dict{String, Any}(
        "type" => "movingWall",
        "value" => [1.0, 0.0, 0.0]
    )
    
    return boundary_dict
end

"""
Validate that a mesh follows OpenFOAM 2D conventions
"""
function validate_2d_mesh_conventions(mesh, boundary_patches)
    
    println("🔍 Validating OpenFOAM 2D mesh conventions")
    
    validation_passed = true
    
    # Check 1: Must have empty patches
    has_empty = false
    empty_count = 0
    
    for (name, patch) in boundary_patches
        if isa(patch.boundary_condition, Empty)
            has_empty = true
            empty_count += 1
            println("   ✅ Found empty patch: $name")
        end
    end
    
    if !has_empty
        println("   ❌ No empty patches found - required for 2D problems")
        validation_passed = false
    elseif empty_count != 2
        println("   ⚠️ Found $empty_count empty patches, expected 2 (front/back)")
    end
    
    # Check 2: Mesh should be thin in one direction
    z_coords = [center[3] for center in mesh.cell_centers]
    z_range = maximum(z_coords) - minimum(z_coords)
    
    if z_range < 0.01
        println("   ✅ Mesh is thin in Z direction (thickness: $z_range)")
    else
        println("   ⚠️ Mesh thickness in Z: $z_range (may be too thick for 2D)")
    end
    
    # Check 3: All cells should have same Z coordinate
    z_unique = length(unique(z_coords))
    expected_z = 1  # One layer
    
    if z_unique == expected_z
        println("   ✅ All cells at same Z level (single layer)")
    else
        println("   ❌ Found $z_unique Z levels, expected $expected_z")
        validation_passed = false
    end
    
    if validation_passed
        println("   ✅ Mesh follows OpenFOAM 2D conventions")
    else
        println("   ❌ Mesh validation failed")
    end
    
    return validation_passed
end

# ============================================================================
# DEMONSTRATION AND TESTING
# ============================================================================

"""
Demonstrate 2D mesh creation and empty boundary conditions
"""
function demo_2d_mesh_creation()
    
    println("🎯 DEMONSTRATING 2D MESH CREATION (OPENFOAM STYLE)")
    println("=" ^ 60)
    
    # Create a 2D lid-driven cavity mesh
    nx, ny = 20, 20
    Lx, Ly = 1.0, 1.0
    thickness = 0.1
    
    mesh, boundary_patches = create_2d_mesh_as_3d(nx, ny, Lx, Ly, thickness)
    
    # Validate the mesh
    validate_2d_mesh_conventions(mesh, boundary_patches)
    
    # Create boundary condition dictionary
    boundary_dict = create_2d_boundary_dict()
    
    # Show boundary setup
    println("\n📋 Boundary Condition Setup:")
    for (patch_name, bc_dict) in boundary_dict
        bc_type = bc_dict["type"]
        println("   $patch_name: $bc_type")
        
        if haskey(bc_dict, "value")
            println("      value: $(bc_dict["value"])")
        end
    end
    
    # Create field boundary conditions
    field_bcs = create_field_boundary_conditions(boundary_patches)
    
    println("\n✅ 2D Mesh Creation Complete")
    println("   - Mesh: $(mesh.n_cells) cells, $(mesh.n_faces) faces")
    println("   - Empty patches: front, back")
    println("   - Physical patches: left, right, bottom, top")
    println("   - Ready for 2D CFD simulation")
    
    return mesh, boundary_patches, field_bcs
end

"""
Create field boundary conditions from boundary patches
"""
function create_field_boundary_conditions(boundary_patches)
    
    # Velocity field boundary conditions
    velocity_patches = Dict{String, BoundaryPatch}()
    
    # Pressure field boundary conditions  
    pressure_patches = Dict{String, BoundaryPatch}()
    
    for (name, patch) in boundary_patches
        velocity_patches[name] = patch
        
        # For pressure, convert wall BCs to zeroGradient
        if isa(patch.boundary_condition, Union{NoSlipWall, MovingWall})
            pressure_bc = ZeroGradient()
            pressure_patches[name] = BoundaryPatch(
                patch.name, patch.face_indices, pressure_bc,
                patch.face_centers, patch.face_normals, patch.face_areas
            )
        else
            pressure_patches[name] = patch
        end
    end
    
    velocity_bcs = FieldBoundaryConditions(velocity_patches)
    pressure_bcs = FieldBoundaryConditions(pressure_patches)
    
    return Dict(
        "velocity" => velocity_bcs,
        "pressure" => pressure_bcs
    )
end

# ============================================================================
# EXPORTS
# ============================================================================

export FiniteVolumeMesh2D
export create_2d_mesh_as_3d, create_2d_boundary_dict
export validate_2d_mesh_conventions, demo_2d_mesh_creation
export create_field_boundary_conditions

end # module Mesh2DUtilities