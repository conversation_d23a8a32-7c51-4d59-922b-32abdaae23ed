"""
TVDSchemes.jl

Total Variation Diminishing (TVD) schemes for convection discretization.
Provides high-resolution, oscillation-free solutions for convection-dominated flows.

Features:
- Multiple limiter functions (minmod, <PERSON>, <PERSON>bee, etc.)
- Second-order accuracy in smooth regions
- Non-oscillatory behavior near discontinuities
- Compatible with structured and unstructured meshes
- Flux-limited formulation for stability
"""

module TVDSchemes

using LinearAlgebra
using Printf

# ============================================================================
# TVD LIMITER FUNCTIONS
# ============================================================================

"""
Abstract type for TVD limiters
"""
abstract type TVDLimiter end

"""
Minmod limiter - most diffusive, most stable
"""
struct MinmodLimiter <: TVDLimiter end

function (::MinmodLimiter)(r::Float64)
    return max(0.0, min(1.0, r))
end

"""
Van Leer limiter - smooth, good compromise
"""
struct VanLeerLimiter <: TVDLimiter end

function (::VanLeerLimiter)(r::Float64)
    return (r + abs(r)) / (1.0 + abs(r))
end

"""
Superbee limiter - least diffusive, may be less stable
"""
struct SuperbeeLimiter <: TVDLimiter end

function (::SuperbeeLimiter)(r::Float64)
    return max(0.0, min(2.0*r, 1.0), min(r, 2.0))
end

"""
MC (Monotonized Central) limiter - good accuracy
"""
struct MCLimiter <: TVDLimiter end

function (::MCLimiter)(r::Float64)
    return max(0.0, min((1.0 + r)/2.0, 2.0, 2.0*r))
end

"""
QUICK limiter - third-order in smooth regions
"""
struct QUICKLimiter <: TVDLimiter end

function (::QUICKLimiter)(r::Float64)
    return max(0.0, min(2.0*r, (3.0 + r)/4.0, 2.0))
end

# ============================================================================
# TVD SCHEME IMPLEMENTATION FOR STRUCTURED MESHES
# ============================================================================

"""
TVD discretization for 1D convection equation: ∂φ/∂t + u∂φ/∂x = 0
"""
function tvd_flux_1d(
    φ::Vector{Float64},      # Cell values
    u::Vector{Float64},      # Velocity at cell centers
    dx::Float64,             # Cell size
    limiter::TVDLimiter      # Limiter function
)
    n = length(φ)
    flux = zeros(n+1)  # Face fluxes
    
    # Add ghost cells for boundary handling
    φ_extended = [φ[1]; φ; φ[end]]
    u_extended = [u[1]; u; u[end]]
    
    for i in 2:n+1  # Loop over faces
        # Upwind face velocity
        u_face = 0.5 * (u_extended[i] + u_extended[i+1])
        
        if u_face >= 0.0
            # Flow from left to right
            φ_upwind = φ_extended[i]
            
            # Calculate gradient ratio for limiter
            if i > 2
                dφ_minus = φ_extended[i] - φ_extended[i-1]
                dφ_plus = φ_extended[i+1] - φ_extended[i]
                
                if abs(dφ_plus) > 1e-14
                    r = dφ_minus / dφ_plus
                else
                    r = 0.0
                end
                
                # Apply limiter
                ψ = limiter(r)
                
                # High-resolution correction
                φ_face = φ_upwind + 0.5 * ψ * dφ_plus
            else
                φ_face = φ_upwind
            end
        else
            # Flow from right to left
            φ_upwind = φ_extended[i+1]
            
            # Calculate gradient ratio
            if i < n+1
                dφ_plus = φ_extended[i+2] - φ_extended[i+1]
                dφ_minus = φ_extended[i+1] - φ_extended[i]
                
                if abs(dφ_minus) > 1e-14
                    r = dφ_plus / dφ_minus
                else
                    r = 0.0
                end
                
                # Apply limiter
                ψ = limiter(r)
                
                # High-resolution correction
                φ_face = φ_upwind - 0.5 * ψ * dφ_minus
            else
                φ_face = φ_upwind
            end
        end
        
        flux[i] = u_face * φ_face
    end
    
    # Remove ghost cell contributions
    return flux[2:end-1]
end

"""
TVD scheme for 2D structured mesh
"""
function tvd_convection_2d(
    φ::Matrix{Float64},      # Scalar field
    u::Matrix{Float64},      # x-velocity
    v::Matrix{Float64},      # y-velocity
    dx::Float64,             # x-spacing
    dy::Float64,             # y-spacing
    limiter::TVDLimiter      # Limiter function
)
    nx, ny = size(φ)
    div_flux = zeros(nx, ny)
    
    # X-direction fluxes
    for j in 1:ny
        φ_slice = φ[:, j]
        u_slice = u[:, j]
        
        # Calculate fluxes at x-faces
        x_fluxes = tvd_flux_1d(φ_slice, u_slice, dx, limiter)
        
        # Update divergence
        for i in 1:nx
            if i == 1
                div_flux[i, j] += x_fluxes[i] / dx
            elseif i == nx
                div_flux[i, j] -= x_fluxes[i-1] / dx
            else
                div_flux[i, j] += (x_fluxes[i] - x_fluxes[i-1]) / dx
            end
        end
    end
    
    # Y-direction fluxes
    for i in 1:nx
        φ_slice = φ[i, :]
        v_slice = v[i, :]
        
        # Calculate fluxes at y-faces
        y_fluxes = tvd_flux_1d(φ_slice, v_slice, dy, limiter)
        
        # Update divergence
        for j in 1:ny
            if j == 1
                div_flux[i, j] += y_fluxes[j] / dy
            elseif j == ny
                div_flux[i, j] -= y_fluxes[j-1] / dy
            else
                div_flux[i, j] += (y_fluxes[j] - y_fluxes[j-1]) / dy
            end
        end
    end
    
    return div_flux
end

# ============================================================================
# TVD SCHEME FOR UNSTRUCTURED MESHES
# ============================================================================

"""
TVD flux calculation for unstructured mesh face
"""
function tvd_face_flux_unstructured(
    φ_owner::Float64,        # Owner cell value
    φ_neighbor::Float64,     # Neighbor cell value
    φ_upwind2::Float64,      # Second upwind cell value
    face_velocity::Float64,   # Normal velocity at face
    face_area::Float64       # Face area
)
    if abs(face_velocity) < 1e-14
        return 0.0
    end
    
    if face_velocity > 0.0
        # Flow from owner to neighbor
        φ_upwind = φ_owner
        dφ_plus = φ_neighbor - φ_owner
        dφ_minus = φ_owner - φ_upwind2
    else
        # Flow from neighbor to owner  
        φ_upwind = φ_neighbor
        dφ_plus = φ_owner - φ_neighbor
        dφ_minus = φ_neighbor - φ_upwind2
    end
    
    # Calculate gradient ratio
    if abs(dφ_plus) > 1e-14
        r = dφ_minus / dφ_plus
    else
        r = 0.0
    end
    
    # Apply Van Leer limiter (default for unstructured)
    ψ = (r + abs(r)) / (1.0 + abs(r))
    
    # TVD flux
    if face_velocity > 0.0
        φ_face = φ_upwind + 0.5 * ψ * dφ_plus
    else
        φ_face = φ_upwind - 0.5 * ψ * dφ_minus
    end
    
    return face_velocity * face_area * φ_face
end

# ============================================================================
# HIGH-LEVEL INTERFACE
# ============================================================================

"""
Configuration for TVD schemes
"""
struct TVDConfig
    limiter::TVDLimiter
    use_gradient_reconstruction::Bool
    gradient_limiter_threshold::Float64
    
    function TVDConfig(;
        limiter::TVDLimiter = VanLeerLimiter(),
        use_gradient_reconstruction::Bool = true,
        gradient_limiter_threshold::Float64 = 0.1
    )
        new(limiter, use_gradient_reconstruction, gradient_limiter_threshold)
    end
end

"""
Apply TVD convection discretization to a general field
"""
function apply_tvd_convection(
    φ::Array{Float64},
    velocity_field::Any,
    mesh_info::Any,
    config::TVDConfig
)
    if ndims(φ) == 1
        # 1D case
        return tvd_flux_1d(φ, velocity_field, mesh_info.dx, config.limiter)
    elseif ndims(φ) == 2
        # 2D structured case
        u, v = velocity_field
        dx, dy = mesh_info.dx, mesh_info.dy
        return tvd_convection_2d(φ, u, v, dx, dy, config.limiter)
    else
        error("TVD scheme not implemented for $(ndims(φ))D arrays")
    end
end

# ============================================================================
# VALIDATION AND TESTING
# ============================================================================

"""
Test TVD schemes with standard test problems
"""
function validate_tvd_schemes()
    println("🔬 Validating TVD Schemes")
    println("=" ^ 50)
    
    # Test 1: Square wave advection (tests non-oscillatory property)
    println("\n📊 Test 1: Square Wave Advection")
    n = 100
    x = range(0, 1, length=n)
    dx = x[2] - x[1]
    
    # Initial square wave
    φ_initial = zeros(n)
    φ_initial[30:50] .= 1.0
    
    # Constant velocity
    u = ones(n)
    dt = 0.5 * dx / maximum(abs.(u))  # CFL = 0.5
    
    # Test different limiters
    limiters = [
        ("Minmod", MinmodLimiter()),
        ("Van Leer", VanLeerLimiter()),
        ("Superbee", SuperbeeLimiter()),
        ("MC", MCLimiter())
    ]
    
    for (name, limiter) in limiters
        φ = copy(φ_initial)
        
        # Advect for several time steps
        for _ in 1:20
            flux = tvd_flux_1d(φ, u, dx, limiter)
            
            # Update using finite volume
            for i in 1:n
                if i == 1
                    φ[i] -= dt * flux[i] / dx
                elseif i == n
                    φ[i] += dt * flux[i-1] / dx
                else
                    φ[i] -= dt * (flux[i] - flux[i-1]) / dx
                end
            end
        end
        
        # Check for oscillations
        oscillations = count(i -> φ[i] < -0.01 || φ[i] > 1.01, 1:n)
        max_val = maximum(φ)
        min_val = minimum(φ)
        
        println("   $name limiter:")
        @printf "      Range: [%.3f, %.3f]\n" min_val max_val
        @printf "      Oscillations: %d points\n" oscillations
        @printf "      TVD property: %s\n" (oscillations == 0 ? "✅ SATISFIED" : "❌ VIOLATED")
    end
    
    # Test 2: Smooth sine wave (tests accuracy)
    println("\n📊 Test 2: Sine Wave Advection")
    φ_smooth = sin.(2π * x)
    φ_initial_smooth = copy(φ_smooth)
    
    # Advect one period
    steps = Int(round(1.0 / (u[1] * dt)))
    φ_tvd = copy(φ_initial_smooth)
    
    for _ in 1:steps
        flux = tvd_flux_1d(φ_tvd, u, dx, VanLeerLimiter())
        for i in 1:n
            if i == 1
                φ_tvd[i] -= dt * flux[i] / dx
            elseif i == n
                φ_tvd[i] += dt * flux[i-1] / dx
            else
                φ_tvd[i] -= dt * (flux[i] - flux[i-1]) / dx
            end
        end
    end
    
    # Compare with first-order upwind
    φ_upwind = copy(φ_initial_smooth)
    for _ in 1:steps
        φ_new = copy(φ_upwind)
        for i in 2:n
            φ_new[i] = φ_upwind[i] - u[i] * dt / dx * (φ_upwind[i] - φ_upwind[i-1])
        end
        φ_upwind = φ_new
    end
    
    error_tvd = norm(φ_tvd - φ_initial_smooth) / norm(φ_initial_smooth)
    error_upwind = norm(φ_upwind - φ_initial_smooth) / norm(φ_initial_smooth)
    
    println("   Error comparison:")
    @printf "      TVD (Van Leer): %.4f\n" error_tvd
    @printf "      First-order upwind: %.4f\n" error_upwind
    @printf "      Improvement factor: %.2fx\n" (error_upwind / error_tvd)
    
    println("\n✅ TVD Validation Complete")
    return true
end

# ============================================================================
# EXPORTS
# ============================================================================

export TVDLimiter, MinmodLimiter, VanLeerLimiter, SuperbeeLimiter, MCLimiter, QUICKLimiter
export tvd_flux_1d, tvd_convection_2d, tvd_face_flux_unstructured
export TVDConfig, apply_tvd_convection, validate_tvd_schemes

end # module TVDSchemes