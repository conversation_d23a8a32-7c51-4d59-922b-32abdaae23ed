"""
    DiscretizationSchemes.jl - Higher-order discretization schemes for JuliaFOAM

This module implements various higher-order discretization schemes for convection, 
diffusion, and gradient terms in the finite volume method.
"""
module DiscretizationSchemes

using LinearAlgebra
using StaticArrays
using SparseArrays
using ..JuliaFOAM
include("FluxLimiters.jl")
using .FluxLimiters: FluxLimiter, VanLeerLimiter, MinmodLimiter, SuperbeeLimiter, limiter_function

export ConvectionScheme, DiffusionScheme, GradientScheme
export UpwindScheme, CentralDifferenceScheme, LinearUpwindScheme, QuickScheme
export TVDScheme
export compute_face_flux, compute_gradient, compute_laplacian, compute_cell_gradient_least_squares

# Abstract types for different scheme categories
abstract type ConvectionScheme end
abstract type DiffusionScheme end
abstract type GradientScheme end
# FluxLimiter is imported from FluxLimiters module

# Upwind scheme (first-order)
struct UpwindScheme <: ConvectionScheme end

# Central difference scheme (second-order)
struct CentralDifferenceScheme <: ConvectionScheme end

# Linear upwind scheme (second-order)
struct LinearUpwindScheme <: ConvectionScheme end

# QUICK scheme (third-order)
struct QuickScheme <: ConvectionScheme end

# TVD scheme with flux limiters
struct TVDScheme <: ConvectionScheme
    limiter::FluxLimiter
end

# Limiter functions are imported from FluxLimiters module

"""
    compute_face_flux(scheme::ConvectionScheme, field::Vector{T}, mesh::Mesh, face_idx::Int) where T

Compute the face value using the specified convection scheme.
"""
function compute_face_flux(scheme::UpwindScheme, field::Vector{T}, mesh::Mesh, face_idx::Int, flux::Float64) where T
    face = mesh.faces[face_idx]
    owner = face.owner
    
    if face.neighbour > 0  # Internal face
        neighbor = face.neighbour
        
        # Upwind based on flux direction
        if flux >= 0.0
            return field[owner]
        else
            return field[neighbor]
        end
    else
        # Boundary face - get boundary value from field boundary conditions
        # Find which boundary patch this face belongs to
        for (patch_name, face_indices) in mesh.boundary_patches
            if face_idx in face_indices
                local_idx = findfirst(x -> x == face_idx, face_indices)
                if local_idx !== nothing && haskey(field.boundary_values, patch_name)
                    return field.boundary_values[patch_name][local_idx]
                end
                break
            end
        end
        # Fallback to owner cell value if boundary value not found
        return field[owner]
    end
end

function compute_face_flux(scheme::CentralDifferenceScheme, field::Vector{T}, mesh::Mesh, face_idx::Int, flux::Float64) where T
    face = mesh.faces[face_idx]
    owner = face.owner
    
    if face.neighbour > 0  # Internal face
        neighbor = face.neighbour
        
        # Linear interpolation (central differencing)
        # Compute interpolation factor based on distance
        owner_center = mesh.cells[owner].center
        neighbor_center = mesh.cells[neighbor].center
        face_center = face.center
        
        total_dist = norm(neighbor_center - owner_center)
        owner_dist = norm(face_center - owner_center)
        
        # Interpolation weight
        weight = owner_dist / total_dist
        
        # Interpolated value
        return (1.0 - weight) * field[owner] + weight * field[neighbor]
    else
        # Boundary face - get boundary value from field boundary conditions
        # Find which boundary patch this face belongs to
        for (patch_name, face_indices) in mesh.boundary_patches
            if face_idx in face_indices
                local_idx = findfirst(x -> x == face_idx, face_indices)
                if local_idx !== nothing && haskey(field.boundary_values, patch_name)
                    return field.boundary_values[patch_name][local_idx]
                end
                break
            end
        end
        # Fallback to owner cell value if boundary value not found
        return field[owner]
    end
end

function compute_face_flux(scheme::LinearUpwindScheme, field::Vector{T}, mesh::Mesh, face_idx::Int, flux::Float64) where T
    face = mesh.faces[face_idx]
    owner = face.owner
    
    if face.neighbour > 0  # Internal face
        neighbor = face.neighbour
        
        # Determine upwind and downwind cells based on flux direction
        upwind_cell, downwind_cell = flux >= 0.0 ? (owner, neighbor) : (neighbor, owner)
        
        # Compute gradient at upwind cell using least squares method
        grad_upwind = compute_cell_gradient_least_squares(mesh, field, upwind_cell)
        
        # Distance vector from upwind cell to face
        upwind_center = mesh.cells[upwind_cell].center
        r = face.center - upwind_center
        
        # Linear upwind value: φ_upwind + grad(φ)_upwind ⋅ r
        return field[upwind_cell] + dot(grad_upwind, r)
    else
        # Boundary face - get boundary value from field boundary conditions
        # Find which boundary patch this face belongs to
        for (patch_name, face_indices) in mesh.boundary_patches
            if face_idx in face_indices
                local_idx = findfirst(x -> x == face_idx, face_indices)
                if local_idx !== nothing && haskey(field.boundary_values, patch_name)
                    return field.boundary_values[patch_name][local_idx]
                end
                break
            end
        end
        # Fallback to owner cell value if boundary value not found
        return field[owner]
    end
end

function compute_face_flux(scheme::QuickScheme, field::Vector{T}, mesh::Mesh, face_idx::Int, flux::Float64) where T
    face = mesh.faces[face_idx]
    owner = face.owner
    
    if face.neighbour > 0  # Internal face
        neighbor = face.neighbour
        
        # For QUICK scheme, we need to find an additional upstream point
        # This is a simplified implementation
        
        # Determine upwind and downwind cells based on flux direction
        upwind_cell, downwind_cell = flux >= 0.0 ? (owner, neighbor) : (neighbor, owner)
        
        # In a full implementation, we would find the upstream neighbor
        # For now, we'll just use a quadratic interpolation with the available points
        
        # Proper QUICK scheme implementation
        # Need points: upwind-upwind (U), upwind (C), downwind (D)
        
        # Find upwind-upwind cell
        upwind_upwind_cell = -1
        upwind_cell_obj = mesh.cells[upwind_cell]
        
        # Search for the face that connects to a cell upstream of upwind_cell
        for face_id in upwind_cell_obj.faces
            test_face = mesh.faces[face_id]
            other_cell = test_face.owner == upwind_cell ? test_face.neighbour : test_face.owner
            
            if other_cell > 0 && other_cell != downwind_cell
                # Check if this cell is upstream
                dr = mesh.cells[other_cell].center - mesh.cells[upwind_cell].center
                face_normal = face.area / norm(face.area)
                if dot(dr, face_normal) * (flux >= 0 ? -1 : 1) > 0
                    upwind_upwind_cell = other_cell
                    break
                end
            end
        end
        
        if upwind_upwind_cell > 0
            # Full QUICK interpolation: φ_f = 6/8*φ_C + 3/8*φ_D - 1/8*φ_U
            φ_U = field[upwind_upwind_cell]
            φ_C = field[upwind_cell]
            φ_D = field[downwind_cell]
            
            return (6.0/8.0) * φ_C + (3.0/8.0) * φ_D - (1.0/8.0) * φ_U
        else
            # Fall back to linear interpolation if upwind-upwind not available
            return 0.5 * (field[owner] + field[neighbor])
        end
    else
        # Boundary face - get boundary value from field boundary conditions
        # Find which boundary patch this face belongs to
        for (patch_name, face_indices) in mesh.boundary_patches
            if face_idx in face_indices
                local_idx = findfirst(x -> x == face_idx, face_indices)
                if local_idx !== nothing && haskey(field.boundary_values, patch_name)
                    return field.boundary_values[patch_name][local_idx]
                end
                break
            end
        end
        # Fallback to owner cell value if boundary value not found
        return field[owner]
    end
end

function compute_face_flux(scheme::TVDScheme, field::Vector{T}, mesh::Mesh, face_idx::Int, flux::Float64) where T
    face = mesh.faces[face_idx]
    owner = face.owner
    
    if face.neighbour > 0  # Internal face
        neighbor = face.neighbour
        
        # Determine upwind, downwind, and upstream cells based on flux direction
        if flux >= 0.0
            upwind_cell = owner
            downwind_cell = neighbor
            # In a full implementation, we would find the upstream neighbor
            upstream_cell = owner  # Placeholder
        else
            upwind_cell = neighbor
            downwind_cell = owner
            # In a full implementation, we would find the upstream neighbor
            upstream_cell = neighbor  # Placeholder
        end
        
        # Compute r for the limiter
        # r = (φ_upwind - φ_upstream) / (φ_downwind - φ_upwind)
        # Avoid division by zero
        denominator = field[downwind_cell] - field[upwind_cell]
        if abs(denominator) < 1e-10
            r = 0.0
        else
            r = (field[upwind_cell] - field[upstream_cell]) / denominator
        end
        
        # Apply limiter
        limiter_value = limiter_function(scheme.limiter, r)
        
        # TVD scheme: φ_upwind + 0.5 * limiter(r) * (φ_downwind - φ_upwind)
        return field[upwind_cell] + 0.5 * limiter_value * (field[downwind_cell] - field[upwind_cell])
    else
        # Boundary face - get boundary value from field boundary conditions
        # Find which boundary patch this face belongs to
        for (patch_name, face_indices) in mesh.boundary_patches
            if face_idx in face_indices
                local_idx = findfirst(x -> x == face_idx, face_indices)
                if local_idx !== nothing && haskey(field.boundary_values, patch_name)
                    return field.boundary_values[patch_name][local_idx]
                end
                break
            end
        end
        # Fallback to owner cell value if boundary value not found
        return field[owner]
    end
end

# =========================================================================
# Gradient Calculation Methods
# =========================================================================

"""
    compute_cell_gradient_least_squares(mesh, field, cell_id)

Compute cell gradient using least squares method.
"""
function compute_cell_gradient_least_squares(mesh, field::Vector{T}, cell_id::Int) where T
    cell = mesh.cells[cell_id]
    cell_center = cell.center
    
    # Build least squares matrix
    ATA = zeros(3, 3)
    ATb = zeros(3)
    
    # Loop over neighbor cells
    for face_id in cell.faces
        face = mesh.faces[face_id]
        
        # Get neighbor cell
        neighbor_id = face.owner == cell_id ? face.neighbour : face.owner
        
        if neighbor_id > 0  # Valid neighbor
            # Distance vector
            dr = mesh.cells[neighbor_id].center - cell_center
            
            # Weight based on distance (inverse distance weighting)
            w = 1.0 / norm(dr)
            
            # Field difference
            df = field[neighbor_id] - field[cell_id]
            
            # Add to least squares system
            for i in 1:3
                for j in 1:3
                    ATA[i,j] += w * dr[i] * dr[j]
                end
                ATb[i] += w * dr[i] * df
            end
        end
    end
    
    # Solve least squares system
    # Add regularization for stability
    for i in 1:3
        ATA[i,i] += 1e-10
    end
    
    # Solve ATA * grad = ATb
    try
        grad = ATA \ ATb
        return SVector{3, Float64}(grad[1], grad[2], grad[3])
    catch
        # If system is singular, return zero gradient
        return SVector{3, Float64}(0.0, 0.0, 0.0)
    end
end

end # module DiscretizationSchemes
