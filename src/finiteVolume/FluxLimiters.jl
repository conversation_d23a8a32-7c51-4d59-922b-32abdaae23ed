# =========================================================================
# FluxLimiters Module - TVD schemes for enhanced stability and accuracy
# =========================================================================

"""
    FluxLimiters

This module implements various flux limiters to enhance stability in numerical simulations,
particularly for convection-dominated problems with sharp gradients.

# Overview
Flux limiters are used in Total Variation Diminishing (TVD) schemes to prevent spurious
oscillations near discontinuities while maintaining high-order accuracy in smooth regions.
This module provides a variety of limiters with different characteristics.

# Available Limiters
- `VanLeerLimiter`: Smooth limiter with good convergence properties
- `MinmodLimiter`: Most dissipative limiter, very stable but diffusive
- `SuperbeeLimiter`: Least dissipative limiter, can lead to compression of gradients
- `KorenLimiter`: Third-order accurate in smooth regions
- `SWEBYLimiter`: Symmetric Weighted Essentially Non-Oscillatory Bounded Yee limiter
- `OsherLimiter`: Provides good accuracy for smooth solutions
- `AlbadaLimiter`: Smooth limiter with good convergence properties
- `BarthJespersenLimiter`: Multidimensional limiter for unstructured meshes

# OpenFOAM Compatibility
These limiters are implemented to match the behavior of equivalent OpenFOAM limiters,
ensuring compatibility when importing/exporting cases.
"""
module FluxLimiters

using LinearAlgebra
using StaticArrays

export FluxLimiter, apply_limiter, limiter_function
export VanLeerLimiter, MinmodLimiter, SuperbeeLimiter, KorenLimiter
export SWEBYLimiter, OsherLimiter, AlbadaLimiter, BarthJespersenLimiter

# Abstract type for all flux limiters
abstract type FluxLimiter end

# Concrete limiter types
struct VanLeerLimiter <: FluxLimiter end
struct MinmodLimiter <: FluxLimiter end
struct SuperbeeLimiter <: FluxLimiter end
struct KorenLimiter <: FluxLimiter end
struct SWEBYLimiter <: FluxLimiter end
struct OsherLimiter <: FluxLimiter end
struct AlbadaLimiter <: FluxLimiter end
struct BarthJespersenLimiter <: FluxLimiter end

"""
    apply_limiter(limiter::FluxLimiter, r::Float64)

Apply the flux limiter function to the ratio of consecutive gradients r.
"""
function apply_limiter(limiter::FluxLimiter, r::Float64)
    # Default implementation
    return limiter_function(limiter, r)
end

"""
    limiter_function(limiter::FluxLimiter, r::Float64)

Compute the limiter function value for a given ratio r.
"""
function limiter_function(limiter::VanLeerLimiter, r::Float64)
    # Van Leer limiter: φ(r) = (r + |r|) / (1 + |r|)
    return (r + abs(r)) / (1.0 + abs(r))
end

function limiter_function(limiter::MinmodLimiter, r::Float64)
    # Minmod limiter: φ(r) = max(0, min(1, r))
    return max(0.0, min(1.0, r))
end

function limiter_function(limiter::SuperbeeLimiter, r::Float64)
    # Superbee limiter: φ(r) = max(0, min(2r, 1), min(r, 2))
    return max(0.0, min(2.0 * r, 1.0), min(r, 2.0))
end

function limiter_function(limiter::KorenLimiter, r::Float64)
    # Koren limiter: φ(r) = max(0, min(2r, (1+2r)/3, 2))
    return max(0.0, min(2.0 * r, (1.0 + 2.0 * r) / 3.0, 2.0))
end

function limiter_function(limiter::SWEBYLimiter, r::Float64)
    # SWEBY limiter: φ(r) = max(0, min(βr, 1), min(r, β)) with β=1.5
    β = 1.5
    return max(0.0, min(β * r, 1.0), min(r, β))
end

function limiter_function(limiter::OsherLimiter, r::Float64)
    # Osher limiter: φ(r) = max(0, min(r, β)) with β=2
    β = 2.0
    return max(0.0, min(r, β))
end

function limiter_function(limiter::AlbadaLimiter, r::Float64)
    # Albada limiter: φ(r) = (r² + r) / (r² + 1)
    return (r^2 + r) / (r^2 + 1.0)
end

"""
    apply_limiter_to_gradient(limiter::FluxLimiter, grad::SVector{3,Float64}, 
                             max_grad::SVector{3,Float64}, min_grad::SVector{3,Float64})

Apply the Barth-Jespersen limiter to a gradient vector.
"""
function apply_limiter_to_gradient(limiter::BarthJespersenLimiter, 
                                  grad::SVector{3,Float64}, 
                                  cell_value::Float64,
                                  max_neighbor_value::Float64,
                                  min_neighbor_value::Float64,
                                  cell_center::SVector{3,Float64},
                                  face_centers::Vector{SVector{3,Float64}})
    # Barth-Jespersen limiter for unstructured meshes
    # This limiter ensures that the reconstructed value at faces doesn't exceed
    # the min/max values of neighboring cells
    
    α = 1.0  # Initial limiter value (no limiting)
    
    # Check each face
    for face_center in face_centers
        # Vector from cell center to face
        r = face_center - cell_center
        
        # Reconstructed value at face
        face_value = cell_value + dot(grad, r)
        
        # Apply limiting based on local min/max
        if face_value > cell_value
            # Limit based on maximum
            if max_neighbor_value > cell_value
                α = min(α, (max_neighbor_value - cell_value) / (face_value - cell_value))
            else
                α = 0.0
            end
        elseif face_value < cell_value
            # Limit based on minimum
            if min_neighbor_value < cell_value
                α = min(α, (min_neighbor_value - cell_value) / (face_value - cell_value))
            else
                α = 0.0
            end
        end
    end
    
    # Apply limiter to gradient
    return α * grad
end

"""
    compute_limited_face_value(limiter::FluxLimiter, upwind_value::Float64, 
                              downwind_value::Float64, upstream_value::Float64)

Compute the limited face value using the specified flux limiter.
"""
function compute_limited_face_value(limiter::FluxLimiter, 
                                   upwind_value::Float64, 
                                   downwind_value::Float64, 
                                   upstream_value::Float64)
    # Compute r for the limiter
    # r = (φ_upwind - φ_upstream) / (φ_downwind - φ_upwind)
    # Avoid division by zero
    denominator = downwind_value - upwind_value
    if abs(denominator) < 1e-10
        r = 0.0
    else
        r = (upwind_value - upstream_value) / denominator
    end
    
    # Apply limiter
    limiter_value = limiter_function(limiter, r)
    
    # Limited face value: φ_upwind + 0.5 * limiter(r) * (φ_downwind - φ_upwind)
    return upwind_value + 0.5 * limiter_value * (downwind_value - upwind_value)
end

end # module FluxLimiters
