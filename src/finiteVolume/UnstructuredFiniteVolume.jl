"""
UnstructuredFiniteVolume.jl

Finite volume discretization for unstructured meshes.
Implements gradient calculation, divergence, Laplacian operators 
for arbitrary polyhedral cells.

Features:
- Green-Gauss and least-squares gradient methods
- Central and upwind differencing schemes
- Non-orthogonal mesh corrections
- Boundary condition treatment
- High-order discretization schemes
"""

module UnstructuredFiniteVolume

using LinearAlgebra
using SparseArrays
using StaticArrays

using ..UnstructuredMesh: Mesh, Point3D, UnstructuredFace, UnstructuredCell

# ============================================================================
# FIELD STORAGE FOR UNSTRUCTURED MESHES
# ============================================================================

"""
Scalar field on unstructured mesh
"""
struct UnstructuredScalarField
    values::Vector{Float64}        # Cell-centered values
    boundary_values::Dict{String, Vector{Float64}}  # Boundary values by patch
    mesh::Mesh
    name::String
    
    function UnstructuredScalarField(mesh::Mesh, name::String, initial_value::Float64=0.0)
        values = fill(initial_value, length(mesh.cells))
        boundary_values = Dict{String, Vector{Float64}}()
        
        # Initialize boundary values
        for patch in mesh.boundary_patches
            boundary_values[patch.name] = fill(initial_value, patch.n_faces)
        end
        
        new(values, boundary_values, mesh, name)
    end
end

"""
Vector field on unstructured mesh
"""
struct UnstructuredVectorField
    x_values::Vector{Float64}      # X-component
    y_values::Vector{Float64}      # Y-component
    z_values::Vector{Float64}      # Z-component
    boundary_values::Dict{String, Vector{Point3D}}  # Boundary values by patch
    mesh::Mesh
    name::String
    
    function UnstructuredVectorField(mesh::Mesh, name::String, initial_value::Point3D=Point3D(0.0, 0.0, 0.0))
        n_cells = length(mesh.cells)
        x_values = fill(initial_value.x, n_cells)
        y_values = fill(initial_value.y, n_cells)
        z_values = fill(initial_value.z, n_cells)
        
        boundary_values = Dict{String, Vector{Point3D}}()
        for patch in mesh.boundary_patches
            boundary_values[patch.name] = fill(initial_value, patch.n_faces)
        end
        
        new(x_values, y_values, z_values, boundary_values, mesh, name)
    end
end

# Utility functions for field access
function get_vector(field::UnstructuredVectorField, cell_id::Int)
    return Point3D(field.x_values[cell_id], field.y_values[cell_id], field.z_values[cell_id])
end

function set_vector!(field::UnstructuredVectorField, cell_id::Int, value::Point3D)
    field.x_values[cell_id] = value.x
    field.y_values[cell_id] = value.y
    field.z_values[cell_id] = value.z
end

# ============================================================================
# GRADIENT CALCULATION
# ============================================================================

"""
Calculate cell-centered gradient using Green-Gauss theorem
"""
function calculate_gradient_green_gauss(field::UnstructuredScalarField)
    mesh = field.mesh
    n_cells = length(mesh.cells)
    
    # Initialize gradient field
    grad_x = zeros(n_cells)
    grad_y = zeros(n_cells)
    grad_z = zeros(n_cells)
    
    # Green-Gauss: ∇φ = (1/V) ∑ φ_f * S_f
    for cell_id in 1:n_cells
        cell = mesh.cells[cell_id]
        volume = cell.volume
        
        for face_id in cell.face_indices
            face = mesh.faces[face_id]
            
            # Determine face value (interpolation for internal faces)
            if face.neighbor_cell > 0  # Internal face
                # Linear interpolation
                owner_val = field.values[face.owner_cell]
                neighbor_val = field.values[face.neighbor_cell]
                
                # Weight by distance to face center
                owner_dist = norm(face.center - mesh.cells[face.owner_cell].center)
                neighbor_dist = norm(face.center - mesh.cells[face.neighbor_cell].center)
                total_dist = owner_dist + neighbor_dist
                
                if total_dist > 1e-12
                    weight = neighbor_dist / total_dist
                    face_value = weight * owner_val + (1.0 - weight) * neighbor_val
                else
                    face_value = 0.5 * (owner_val + neighbor_val)
                end
            else  # Boundary face
                # Use boundary condition
                patch_name, local_face_id = mesh.boundary_face_map[face_id]
                face_value = field.boundary_values[patch_name][local_face_id]
            end
            
            # Determine area vector direction
            area_vector = face.area_vector
            if face.owner_cell != cell_id
                # Face normal points in wrong direction for this cell
                area_vector = Point3D(-area_vector.x, -area_vector.y, -area_vector.z)
            end
            
            # Add contribution: φ_f * S_f / V
            grad_x[cell_id] += face_value * area_vector.x / volume
            grad_y[cell_id] += face_value * area_vector.y / volume
            grad_z[cell_id] += face_value * area_vector.z / volume
        end
    end
    
    return UnstructuredVectorField(mesh, "grad_$(field.name)", Point3D(0.0, 0.0, 0.0)), grad_x, grad_y, grad_z
end

"""
Calculate cell-centered gradient using least-squares method
"""
function calculate_gradient_least_squares(field::UnstructuredScalarField)
    mesh = field.mesh
    n_cells = length(mesh.cells)
    
    grad_x = zeros(n_cells)
    grad_y = zeros(n_cells)
    grad_z = zeros(n_cells)
    
    for cell_id in 1:n_cells
        cell = mesh.cells[cell_id]
        cell_center = cell.center
        cell_value = field.values[cell_id]
        
        # Build least-squares system: A * grad = b
        # Where A is geometric matrix and b is value differences
        neighbors = mesh.cell_neighbors[cell_id]
        n_neighbors = length(neighbors)
        
        if n_neighbors >= 3  # Need at least 3 neighbors for 3D gradient
            A = zeros(n_neighbors, 3)
            b = zeros(n_neighbors)
            
            for (i, neighbor_id) in enumerate(neighbors)
                neighbor_center = mesh.cells[neighbor_id].center
                neighbor_value = field.values[neighbor_id]
                
                # Distance vector
                dx = neighbor_center - cell_center
                A[i, 1] = dx.x
                A[i, 2] = dx.y
                A[i, 3] = dx.z
                
                # Value difference
                b[i] = neighbor_value - cell_value
            end
            
            # Solve least-squares system: grad = (A^T * A)^(-1) * A^T * b
            try
                grad = (A' * A) \ (A' * b)
                grad_x[cell_id] = grad[1]
                grad_y[cell_id] = grad[2]
                grad_z[cell_id] = grad[3]
            catch
                # Fallback to zero gradient if system is singular
                grad_x[cell_id] = 0.0
                grad_y[cell_id] = 0.0
                grad_z[cell_id] = 0.0
            end
        end
    end
    
    return UnstructuredVectorField(mesh, "grad_$(field.name)", Point3D(0.0, 0.0, 0.0)), grad_x, grad_y, grad_z
end

# ============================================================================
# DIVERGENCE CALCULATION
# ============================================================================

"""
Calculate divergence of vector field using Green-Gauss theorem
"""
function calculate_divergence(field::UnstructuredVectorField)
    mesh = field.mesh
    n_cells = length(mesh.cells)
    
    div_values = zeros(n_cells)
    
    # Green-Gauss: ∇·U = (1/V) ∑ U_f · S_f
    for cell_id in 1:n_cells
        cell = mesh.cells[cell_id]
        volume = cell.volume
        
        for face_id in cell.face_indices
            face = mesh.faces[face_id]
            
            # Get face vector value
            if face.neighbor_cell > 0  # Internal face
                # Linear interpolation
                owner_vec = get_vector(field, face.owner_cell)
                neighbor_vec = get_vector(field, face.neighbor_cell)
                
                # Weight by distance
                owner_dist = norm(face.center - mesh.cells[face.owner_cell].center)
                neighbor_dist = norm(face.center - mesh.cells[face.neighbor_cell].center)
                total_dist = owner_dist + neighbor_dist
                
                if total_dist > 1e-12
                    weight = neighbor_dist / total_dist
                    face_vector = Point3D(
                        weight * owner_vec.x + (1.0 - weight) * neighbor_vec.x,
                        weight * owner_vec.y + (1.0 - weight) * neighbor_vec.y,
                        weight * owner_vec.z + (1.0 - weight) * neighbor_vec.z
                    )
                else
                    face_vector = Point3D(
                        0.5 * (owner_vec.x + neighbor_vec.x),
                        0.5 * (owner_vec.y + neighbor_vec.y),
                        0.5 * (owner_vec.z + neighbor_vec.z)
                    )
                end
            else  # Boundary face
                patch_name, local_face_id = mesh.boundary_face_map[face_id]
                face_vector = field.boundary_values[patch_name][local_face_id]
            end
            
            # Determine area vector direction
            area_vector = face.area_vector
            if face.owner_cell != cell_id
                area_vector = Point3D(-area_vector.x, -area_vector.y, -area_vector.z)
            end
            
            # Add contribution: U_f · S_f / V
            face_flux = dot(face_vector, area_vector)
            div_values[cell_id] += face_flux / volume
        end
    end
    
    return UnstructuredScalarField(mesh, "div_$(field.name)", 0.0), div_values
end

# ============================================================================
# LAPLACIAN CALCULATION
# ============================================================================

"""
Calculate Laplacian using finite volume method with non-orthogonal corrections
"""
function calculate_laplacian(field::UnstructuredScalarField, diffusivity::Float64=1.0)
    mesh = field.mesh
    n_cells = length(mesh.cells)
    
    # Build sparse matrix for Laplacian operator
    I = Int[]  # Row indices
    J = Int[]  # Column indices
    V = Float64[]  # Matrix values
    
    rhs = zeros(n_cells)  # Right-hand side vector
    
    for cell_id in 1:n_cells
        cell = mesh.cells[cell_id]
        
        # Diagonal coefficient (will be accumulated)
        diag_coeff = 0.0
        
        for face_id in cell.face_indices
            face = mesh.faces[face_id]
            face_area = norm(face.area_vector)
            face_normal = face.area_vector / face_area
            
            if face.neighbor_cell > 0  # Internal face
                neighbor_id = (face.owner_cell == cell_id) ? face.neighbor_cell : face.owner_cell
                
                # Distance between cell centers
                cell_to_neighbor = mesh.cells[neighbor_id].center - cell.center
                distance = norm(cell_to_neighbor)
                
                if distance > 1e-12
                    # Orthogonal component
                    ortho_distance = abs(dot(cell_to_neighbor, face_normal))
                    
                    # Diffusion coefficient
                    diff_coeff = diffusivity * face_area / ortho_distance
                    
                    # Add off-diagonal coefficient
                    push!(I, cell_id)
                    push!(J, neighbor_id)
                    push!(V, -diff_coeff)
                    
                    # Update diagonal coefficient
                    diag_coeff += diff_coeff
                    
                    # Non-orthogonal correction (simplified)
                    if mesh.orthogonality[face_id] > 0.1  # Significant non-orthogonality
                        # This would require gradient calculation - simplified for now
                        # non_ortho_correction = calculate_non_orthogonal_correction(...)
                        # rhs[cell_id] += non_ortho_correction
                    end
                end
            else  # Boundary face
                # Apply boundary condition
                patch_name, local_face_id = mesh.boundary_face_map[face_id]
                boundary_value = field.boundary_values[patch_name][local_face_id]
                
                # Distance from cell center to face
                cell_to_face = face.center - cell.center
                distance = abs(dot(cell_to_face, face_normal))
                
                if distance > 1e-12
                    diff_coeff = diffusivity * face_area / distance
                    
                    # Dirichlet boundary condition: add to RHS
                    rhs[cell_id] += diff_coeff * boundary_value
                    diag_coeff += diff_coeff
                end
            end
        end
        
        # Add diagonal coefficient
        push!(I, cell_id)
        push!(J, cell_id)
        push!(V, diag_coeff)
    end
    
    # Create sparse matrix
    laplacian_matrix = sparse(I, J, V, n_cells, n_cells)
    
    return laplacian_matrix, rhs
end

# ============================================================================
# CONVECTION SCHEMES
# ============================================================================

"""
Calculate convection term using upwind differencing
"""
function calculate_convection_upwind(field::UnstructuredScalarField, velocity_field::UnstructuredVectorField)
    mesh = field.mesh
    n_cells = length(mesh.cells)
    
    I = Int[]
    J = Int[]
    V = Float64[]
    rhs = zeros(n_cells)
    
    for cell_id in 1:n_cells
        cell = mesh.cells[cell_id]
        diag_coeff = 0.0
        
        for face_id in cell.face_indices
            face = mesh.faces[face_id]
            
            # Calculate mass flux through face
            if face.neighbor_cell > 0  # Internal face
                neighbor_id = (face.owner_cell == cell_id) ? face.neighbor_cell : face.owner_cell
                
                # Face velocity (interpolated)
                owner_vel = get_vector(velocity_field, face.owner_cell)
                neighbor_vel = get_vector(velocity_field, face.neighbor_cell)
                face_velocity = Point3D(
                    0.5 * (owner_vel.x + neighbor_vel.x),
                    0.5 * (owner_vel.y + neighbor_vel.y),
                    0.5 * (owner_vel.z + neighbor_vel.z)
                )
                
                # Area vector (outward from current cell)
                area_vector = face.area_vector
                if face.owner_cell != cell_id
                    area_vector = Point3D(-area_vector.x, -area_vector.y, -area_vector.z)
                end
                
                # Mass flux
                mass_flux = dot(face_velocity, area_vector)
                
                # Upwind scheme
                if mass_flux >= 0.0  # Flow out of cell
                    # Use cell value (implicit)
                    diag_coeff += mass_flux
                else  # Flow into cell
                    # Use neighbor value (explicit in matrix)
                    push!(I, cell_id)
                    push!(J, neighbor_id)
                    push!(V, mass_flux)  # Negative value
                end
            else  # Boundary face
                # Boundary flux treatment
                patch_name, local_face_id = mesh.boundary_face_map[face_id]
                boundary_velocity = velocity_field.boundary_values[patch_name][local_face_id]
                boundary_value = field.boundary_values[patch_name][local_face_id]
                
                area_vector = face.area_vector
                if face.owner_cell != cell_id
                    area_vector = Point3D(-area_vector.x, -area_vector.y, -area_vector.z)
                end
                
                mass_flux = dot(boundary_velocity, area_vector)
                
                if mass_flux >= 0.0  # Outflow
                    diag_coeff += mass_flux
                else  # Inflow
                    rhs[cell_id] -= mass_flux * boundary_value
                end
            end
        end
        
        # Add diagonal coefficient
        if diag_coeff != 0.0
            push!(I, cell_id)
            push!(J, cell_id)
            push!(V, diag_coeff)
        end
    end
    
    convection_matrix = sparse(I, J, V, n_cells, n_cells)
    return convection_matrix, rhs
end

"""
Calculate convection term using central differencing
"""
function calculate_convection_central(field::UnstructuredScalarField, velocity_field::UnstructuredVectorField)
    mesh = field.mesh
    n_cells = length(mesh.cells)
    
    I = Int[]
    J = Int[]
    V = Float64[]
    rhs = zeros(n_cells)
    
    for cell_id in 1:n_cells
        cell = mesh.cells[cell_id]
        
        for face_id in cell.face_indices
            face = mesh.faces[face_id]
            
            if face.neighbor_cell > 0  # Internal face
                neighbor_id = (face.owner_cell == cell_id) ? face.neighbor_cell : face.owner_cell
                
                # Face velocity
                owner_vel = get_vector(velocity_field, face.owner_cell)
                neighbor_vel = get_vector(velocity_field, face.neighbor_cell)
                face_velocity = Point3D(
                    0.5 * (owner_vel.x + neighbor_vel.x),
                    0.5 * (owner_vel.y + neighbor_vel.y),
                    0.5 * (owner_vel.z + neighbor_vel.z)
                )
                
                # Area vector
                area_vector = face.area_vector
                if face.owner_cell != cell_id
                    area_vector = Point3D(-area_vector.x, -area_vector.y, -area_vector.z)
                end
                
                mass_flux = dot(face_velocity, area_vector)
                
                # Central differencing: 0.5 * flux * (phi_neighbor + phi_cell)
                push!(I, cell_id)
                push!(J, cell_id)
                push!(V, 0.5 * mass_flux)
                
                push!(I, cell_id)
                push!(J, neighbor_id)
                push!(V, 0.5 * mass_flux)
            else
                # Boundary treatment (similar to upwind)
                patch_name, local_face_id = mesh.boundary_face_map[face_id]
                boundary_velocity = velocity_field.boundary_values[patch_name][local_face_id]
                boundary_value = field.boundary_values[patch_name][local_face_id]
                
                area_vector = face.area_vector
                if face.owner_cell != cell_id
                    area_vector = Point3D(-area_vector.x, -area_vector.y, -area_vector.z)
                end
                
                mass_flux = dot(boundary_velocity, area_vector)
                rhs[cell_id] += mass_flux * boundary_value
            end
        end
    end
    
    convection_matrix = sparse(I, J, V, n_cells, n_cells)
    return convection_matrix, rhs
end

# ============================================================================
# BOUNDARY CONDITIONS
# ============================================================================

"""
Apply Dirichlet boundary condition
"""
function apply_dirichlet_bc!(field::UnstructuredScalarField, patch_name::String, value::Float64)
    if haskey(field.boundary_values, patch_name)
        field.boundary_values[patch_name] .= value
    end
end

"""
Apply Neumann boundary condition (zero gradient)
"""
function apply_neumann_bc!(field::UnstructuredScalarField, patch_name::String)
    mesh = field.mesh
    
    if haskey(field.boundary_values, patch_name)
        # Find patch
        patch = nothing
        for p in mesh.boundary_patches
            if p.name == patch_name
                patch = p
                break
            end
        end
        
        if patch !== nothing
            # Set boundary values equal to adjacent cell values
            for (local_id, face_id) in enumerate(patch.face_indices)
                face = mesh.faces[face_id]
                owner_cell_id = face.owner_cell
                field.boundary_values[patch_name][local_id] = field.values[owner_cell_id]
            end
        end
    end
end

# ============================================================================
# EXPORTS
# ============================================================================

export UnstructuredScalarField, UnstructuredVectorField
export calculate_gradient_green_gauss, calculate_gradient_least_squares
export calculate_divergence, calculate_laplacian
export calculate_convection_upwind, calculate_convection_central
export apply_dirichlet_bc!, apply_neumann_bc!
export get_vector, set_vector!

end # module UnstructuredFiniteVolume