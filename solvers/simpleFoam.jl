#!/usr/bin/env julia

# simpleFoam.jl - Steady-state incompressible flow solver

using Pkg
# Activate the JuliaFOAM package
if isfile(joinpath(@__DIR__, "..", "Project.toml"))
    Pkg.activate(joinpath(@__DIR__, ".."))
end

using JuliaFOAM
# The Solvers module is not yet defined in JuliaFOAM
# using JuliaFOAM.Solvers

function main()
    # Parse command line arguments
    if length(ARGS) < 1
        println("Usage: simpleFoam.jl <case_directory>")
        exit(1)
    end

    case_dir = ARGS[1]

    if !isdir(case_dir)
        println("Error: Case directory $case_dir does not exist")
        exit(1)
    end

    # Run the solver
    simple_foam(case_dir)
end

# Run the main function
main()
