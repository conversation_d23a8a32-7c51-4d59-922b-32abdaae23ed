#!/usr/bin/env julia

"""
Quick Enhanced Solver Test - focused validation
"""

push!(LOAD_PATH, "./src")

using JuliaFOAM
using LinearAlgebra
using SparseArrays
using Printf
using Statistics

function quick_test()
    println("🚀 Quick Enhanced Linear Solver Validation")
    println("=" ^ 50)
    
    # Test different problem sizes
    sizes = [25, 50, 100]
    
    for n in sizes
        println("\nTesting $n x $n system...")
        
        # Create 2D Poisson matrix
        N = n * n
        A = spzeros(N, N)
        for i in 1:n, j in 1:n
            idx = (i-1)*n + j
            A[idx, idx] = 4.0
            if i > 1; A[idx, idx-n] = -1.0; end
            if i < n; A[idx, idx+n] = -1.0; end
            if j > 1; A[idx, idx-1] = -1.0; end
            if j < n; A[idx, idx+1] = -1.0; end
        end
        
        b = ones(N)
        center = div(n,2)*n + div(n,2)
        b[center] = 10.0
        
        # Test configurations (simplified)
        configs = [
            ("CG+Jacobi", EnhancedSolverConfig(solver_type=:cg, preconditioner=:jacobi, tolerance=1e-6, max_iterations=500)),
            ("BiCGStab+Jacobi", EnhancedSolverConfig(solver_type=:bicgstabl, preconditioner=:jacobi, tolerance=1e-6, max_iterations=500)),
            ("GMRES+Jacobi", EnhancedSolverConfig(solver_type=:gmres, preconditioner=:jacobi, tolerance=1e-6, max_iterations=500))
        ]
        
        for (name, config) in configs
            x = zeros(N)
            
            try
                start_time = time()
                diagnostics = enhanced_solve!(A, b, x, config)
                solve_time = time() - start_time
                
                residual = norm(A * x - b) / norm(b)
                status = residual < 1e-5 ? "✓ PASS" : "✗ FAIL"
                
                @printf "  %-15s: %s (%3d iter, %.1e res, %.3f s, %7.0f DOF/s)\\n" name status diagnostics.iterations residual solve_time (N/solve_time)
                
            catch e
                println("  $name: ✗ ERROR - $e")
            end
        end
        
        # Test CFD interfaces
        println("  CFD interfaces:")
        for problem_type in [:pressure, :momentum, :turbulence, :temperature]
            x = zeros(N)
            try
                start_time = time()
                diagnostics = solve_cfd_system!(A, b, x, tolerance=1e-6, max_iterations=500, problem_type=problem_type)
                solve_time = time() - start_time
                
                residual = norm(A * x - b) / norm(b)
                status = residual < 1e-5 ? "✓" : "✗"
                
                @printf "    %-12s: %s (%.1e res, %.3f s)\\n" problem_type status residual solve_time
                
            catch e
                println("    $problem_type: ✗ ERROR")
            end
        end
    end
    
    println("\n" * "=" ^ 50)
    println("✅ Quick validation completed!")
    println("📝 Summary: Enhanced linear solvers are functional with Jacobi preconditioning")
    println("🔧 Next steps: Integrate proper AMG and ILU preconditioners for better performance")
end

quick_test()