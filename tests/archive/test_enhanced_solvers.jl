#!/usr/bin/env julia

"""
Test script for enhanced linear solvers
"""

# Add current directory to path
push!(LOAD_PATH, "./src")

using JuliaFOAM
using LinearAlgebra
using SparseArrays
using Printf

function test_enhanced_linear_solvers()
    println("Testing Enhanced Linear Solvers...")
    
    # Create a test problem: 2D Poisson equation on a small grid
    n = 50  # Grid size
    N = n * n  # Total unknowns
    
    println("Creating $N x $N test matrix...")
    
    # Create 2D Poisson matrix using finite differences
    function create_2d_poisson_matrix(n)
        N = n * n
        A = spzeros(N, N)
        
        for i in 1:n
            for j in 1:n
                idx = (i-1)*n + j
                
                # Center coefficient
                A[idx, idx] = 4.0
                
                # Neighbors
                if i > 1  # Left
                    A[idx, idx - n] = -1.0
                end
                if i < n  # Right
                    A[idx, idx + n] = -1.0
                end
                if j > 1  # Bottom
                    A[idx, idx - 1] = -1.0
                end
                if j < n  # Top
                    A[idx, idx + 1] = -1.0
                end
            end
        end
        
        return A
    end
    
    A = create_2d_poisson_matrix(n)
    
    # Create right-hand side (unit source in center)
    b = zeros(N)
    center_idx = div(n, 2) * n + div(n, 2)
    b[center_idx] = 1.0
    
    # Initial guess
    x = zeros(N)
    
    println("Matrix properties:")
    println("  Size: $(size(A))")
    println("  Density: $(nnz(A) / (N * N))")
    println("  Symmetric: $(issymmetric(A))")
    
    # Test different solver configurations
    test_configs = [
        ("Auto solver selection", EnhancedSolverConfig(tolerance=1e-8, verbose=false)),
        ("CG with AMG", EnhancedSolverConfig(solver_type=:cg, preconditioner=:amg, tolerance=1e-8, verbose=false)),
        ("BiCGStab with AMG", EnhancedSolverConfig(solver_type=:bicgstabl, preconditioner=:amg, tolerance=1e-8, verbose=false)),
        ("GMRES with ILU", EnhancedSolverConfig(solver_type=:gmres, preconditioner=:ilu, tolerance=1e-8, verbose=false))
    ]
    
    println("\nTesting solver configurations:")
    println("="^70)
    
    for (name, config) in test_configs
        println("\nTesting: $name")
        x .= 0.0  # Reset initial guess
        
        try
            start_time = time()
            diagnostics = enhanced_solve!(A, b, x, config)
            total_time = time() - start_time
            
            # Verify solution
            residual = norm(A * x - b)
            
            @printf "  Solver: %-10s  Precond: %-10s\n" diagnostics.solver_used diagnostics.preconditioner_used
            @printf "  Iterations: %-6d  Final residual: %.2e\n" diagnostics.iterations diagnostics.final_residual
            @printf "  Setup time: %.3f s  Solve time: %.3f s  Total: %.3f s\n" diagnostics.setup_time diagnostics.solve_time total_time
            @printf "  Convergence factor: %.3f\n" diagnostics.convergence_factor
            @printf "  Verification residual: %.2e\n" residual
            
            if !isempty(diagnostics.warnings)
                println("  Warnings: ", join(diagnostics.warnings, "; "))
            end
            
            if !isempty(diagnostics.recommendations)
                println("  Recommendations: ", join(diagnostics.recommendations, "; "))
            end
            
            # Mark as successful if residual is low
            if residual < 1e-6
                println("  ✓ PASSED")
            else
                println("  ✗ FAILED - High residual")
            end
            
        catch e
            println("  ✗ FAILED - Error: $e")
        end
    end
    
    println("\nTesting CFD-specific interface...")
    println("="^50)
    
    # Test CFD-specific solver interface
    problem_types = [:pressure, :momentum, :turbulence, :temperature]
    
    for problem_type in problem_types
        println("\nTesting CFD solver for $problem_type equations:")
        x .= 0.0  # Reset initial guess
        
        try
            start_time = time()
            diagnostics = solve_cfd_system!(A, b, x, 
                                          tolerance=1e-8, 
                                          problem_type=problem_type, 
                                          verbose=false)
            total_time = time() - start_time
            
            # Verify solution
            residual = norm(A * x - b)
            
            @printf "  Problem: %-12s  Solver: %-10s  Precond: %-10s\n" problem_type diagnostics.solver_used diagnostics.preconditioner_used
            @printf "  Iterations: %-6d  Residual: %.2e  Time: %.3f s\n" diagnostics.iterations residual total_time
            
            if residual < 1e-6
                println("  ✓ PASSED")
            else
                println("  ✗ FAILED - High residual")
            end
            
        catch e
            println("  ✗ FAILED - Error: $e")
        end
    end
    
    println("\n" * "="^70)
    println("Enhanced linear solver test completed!")
end

# Run the test
if abspath(PROGRAM_FILE) == @__FILE__
    test_enhanced_linear_solvers()
end