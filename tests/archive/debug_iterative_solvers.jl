#!/usr/bin/env julia

"""
Debug IterativeSolvers.jl integration
"""

push!(LOAD_PATH, "./src")

using LinearAlgebra
using SparseArrays
using IterativeSolvers
using Printf

function test_iterative_solvers_directly()
    println("Testing IterativeSolvers.jl directly")
    println("=" ^ 50)
    
    # Simple test problem
    n = 10
    A = spdiagm(0 => fill(2.0, n), 1 => fill(-1.0, n-1), -1 => fill(-1.0, n-1))
    b = ones(n)
    
    println("Matrix condition number: ", cond(Matrix(A)))
    
    # Test CG directly
    println("\nTesting CG directly:")
    x_cg = zeros(n)
    result_cg = cg!(x_cg, A, b, tol=1e-8, maxiter=100, log=true, verbose=true)
    println("CG result: ", result_cg)
    println("CG solution: ", x_cg)
    println("CG residual: ", norm(A * x_cg - b))
    
    # Test BiCGStab directly
    println("\nTesting BiCGStab directly:")
    x_bicg = zeros(n)
    result_bicg = bicgstabl!(x_bicg, A, b, tol=1e-8, maxiter=100, log=true, verbose=true)
    println("BiCGStab result: ", result_bicg)
    println("BiCGStab solution: ", x_bicg)
    println("BiCGStab residual: ", norm(A * x_bicg - b))
    
    # Test with diagonal preconditioner
    println("\nTesting CG with diagonal preconditioner:")
    D = Diagonal(diag(A))
    x_cg_precond = zeros(n)
    result_cg_precond = cg!(x_cg_precond, A, b, Pl=D, tol=1e-8, maxiter=100, log=true, verbose=true)
    println("CG+Diag result: ", result_cg_precond)
    println("CG+Diag solution: ", x_cg_precond)
    println("CG+Diag residual: ", norm(A * x_cg_precond - b))
end

test_iterative_solvers_directly()