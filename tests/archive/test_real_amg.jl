#!/usr/bin/env julia

"""
Test Real AMG Integration in Enhanced Solvers
=============================================

Test the proper AMG implementation.
"""

push!(LOAD_PATH, "./src")

using JuliaFOAM
using LinearAlgebra
using SparseArrays
using Printf

function test_real_amg_integration()
    println("🎯 Testing Real AMG Integration")
    println("=" ^ 50)
    
    # Create test problems of increasing size
    problems = [
        ("Small 2D Poisson", 20),
        ("Medium 2D Poisson", 40), 
        ("Large 2D Poisson", 60)
    ]
    
    for (name, n) in problems
        println("\n📊 Testing: $name ($(n)×$(n) = $(n^2) DOF)")
        
        # Create 2D Poisson matrix
        N = n * n
        A = spzeros(N, N)
        for i in 1:n, j in 1:n
            idx = (i-1)*n + j
            A[idx, idx] = 4.0
            if i > 1; A[idx, idx-n] = -1.0; end
            if i < n; A[idx, idx+n] = -1.0; end
            if j > 1; A[idx, idx-1] = -1.0; end
            if j < n; A[idx, idx+1] = -1.0; end
        end
        
        b = ones(N)
        center = div(n,2)*n + div(n,2)
        b[center] = 10.0
        
        println("   Condition number: $(round(cond(Matrix(A)), digits=2))")
        
        # Test different solver+preconditioner combinations
        tests = [
            ("CG+Jacobi", :cg, :jacobi),
            ("CG+AMG", :cg, :amg),
            ("BiCGStab+Jacobi", :bicgstabl, :jacobi),
            ("BiCGStab+AMG", :bicgstabl, :amg),
            ("GMRES+AMG", :gmres, :amg),
            ("Auto", :auto, :auto)
        ]
        
        for (test_name, solver, precond) in tests
            config = EnhancedSolverConfig(
                solver_type=solver,
                preconditioner=precond,
                tolerance=1e-8,
                max_iterations=200,
                verbose=false
            )
            
            x = zeros(N)
            print("   $test_name: ")
            
            try
                start_time = time()
                diagnostics = enhanced_solve!(A, b, x, config)
                solve_time = time() - start_time
                
                residual = norm(A * x - b) / norm(b)
                
                if residual < 1e-6
                    throughput = N / solve_time
                    speedup_label = ""
                    if endswith(test_name, "+AMG") && diagnostics.iterations < 50
                        speedup_label = " 🚀"
                    end
                    @printf "✅ %.3fs (%2d iter, %.1e res, %6.0f DOF/s)%s\\n" solve_time diagnostics.iterations residual throughput speedup_label
                else
                    @printf "⚠️  %.3fs (%2d iter, %.1e res, NO CONV)\\n" solve_time diagnostics.iterations residual
                end
                
                # Check for warnings
                if !isempty(diagnostics.warnings)
                    for warning in diagnostics.warnings
                        println("      ⚠️  $warning")
                    end
                end
                
            catch e
                println("❌ ERROR: $e")
            end
        end
    end
    
    return true
end

function test_cfd_interfaces_with_amg()
    println("\n" * "=" ^ 50)
    println("🧪 Testing CFD Interfaces with Real AMG")
    println("=" ^ 50)
    
    # Create a representative CFD problem
    n = 35
    N = n * n
    A = spzeros(N, N)
    
    for i in 1:n, j in 1:n
        idx = (i-1)*n + j
        A[idx, idx] = 4.0
        if i > 1; A[idx, idx-n] = -1.0; end
        if i < n; A[idx, idx+n] = -1.0; end
        if j > 1; A[idx, idx-1] = -1.0; end
        if j < n; A[idx, idx+1] = -1.0; end
    end
    
    b = ones(N)
    
    println("Problem: 2D Poisson $(n)×$(n) = $(N) DOF")
    
    cfd_types = [:pressure, :momentum, :turbulence, :temperature]
    
    for problem_type in cfd_types
        print("  $(problem_type): ")
        
        x = zeros(N)
        try
            start_time = time()
            diagnostics = solve_cfd_system!(A, b, x,
                                          tolerance=1e-8,
                                          max_iterations=100,
                                          problem_type=problem_type,
                                          verbose=false)
            solve_time = time() - start_time
            
            residual = norm(A * x - b) / norm(b)
            
            if residual < 1e-6
                throughput = N / solve_time
                amg_used = any(contains(w, "AMG") for w in diagnostics.warnings) ? "" : " (AMG)"
                @printf "✅ %.3fs (%s%s, %2d iter, %.1e res, %6.0f DOF/s)\\n" solve_time diagnostics.solver_used amg_used diagnostics.iterations residual throughput
            else
                @printf "⚠️  %.3fs (%s, %2d iter, %.1e res)\\n" solve_time diagnostics.solver_used diagnostics.iterations residual
            end
            
        catch e
            println("❌ ERROR: $e")
        end
    end
    
    return true
end

function main()
    success1 = test_real_amg_integration()
    success2 = test_cfd_interfaces_with_amg()
    
    if success1 && success2
        println("\n🎉 SUCCESS: Real AMG integration is working!")
        println("📈 Expected major performance improvements with AMG preconditioning")
        return true
    else
        println("\n❌ Some AMG tests failed")
        return false
    end
end

if abspath(PROGRAM_FILE) == @__FILE__
    main()
end