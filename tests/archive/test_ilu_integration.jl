#!/usr/bin/env julia

"""
Test ILU Integration
====================

Figure out the correct way to use IncompleteLU.jl as a preconditioner.
"""

push!(LOAD_PATH, "./src")

using LinearAlgebra
using SparseArrays
using IncompleteLU
using IterativeSolvers
using Printf

function test_ilu_as_preconditioner()
    println("🔧 Testing ILU Integration")
    println("=" ^ 40)
    
    # Create test problems
    problems = [
        ("2D Poisson 20×20", () -> begin
            n = 20
            N = n * n
            A = spzeros(N, N)
            for i in 1:n, j in 1:n
                idx = (i-1)*n + j
                A[idx, idx] = 4.0
                if i > 1; A[idx, idx-n] = -1.0; end
                if i < n; A[idx, idx+n] = -1.0; end
                if j > 1; A[idx, idx-1] = -1.0; end
                if j < n; A[idx, idx+1] = -1.0; end
            end
            b = ones(N)
            (A, b)
        end),
        ("Nonsymmetric ConvDiff", () -> begin
            n = 20
            A = spzeros(n, n)
            Pe = 20.0
            h = 1.0 / (n + 1)
            for i in 1:n
                A[i, i] = 2.0 + Pe * h
                if i > 1; A[i, i-1] = -1.0 - Pe * h / 2; end
                if i < n; A[i, i+1] = -1.0 + Pe * h / 2; end
            end
            b = ones(n)
            (A, b)
        end)
    ]
    
    for (name, create_problem) in problems
        println("\n📊 Testing: $name")
        A, b = create_problem()
        x_ref = A \ b
        
        println("   Matrix: $(size(A,1))×$(size(A,2)), symmetric: $(issymmetric(A))")
        println("   Condition number: $(round(cond(Matrix(A)), digits=2))")
        
        # Test 1: Basic ILU factorization
        print("   ILU factorization: ")
        try
            LU = ilu(A, τ=0.01)  # τ is drop tolerance
            println("✅ Success (type: $(typeof(LU)))")
            
            # Test applying ILU as preconditioner
            print("   ILU as preconditioner: ")
            
            x_ilu = zeros(size(A, 1))
            result = cg!(x_ilu, A, b, Pl=LU, reltol=1e-8, maxiter=100, log=true)
            
            residual = norm(A * x_ilu - b) / norm(b)
            error = norm(x_ilu - x_ref) / norm(x_ref)
            
            @printf "✅ %d iter, %.2e res, %.2e err\\n" result[2].iters residual error
            
        catch e
            println("❌ $e")
        end
        
        # Test 2: Different drop tolerances
        println("   Testing different drop tolerances:")
        for τ in [0.0, 0.01, 0.1]
            try
                LU = ilu(A, τ=τ)
                x_test = zeros(size(A, 1))
                result = cg!(x_test, A, b, Pl=LU, reltol=1e-6, maxiter=50, log=true)
                residual = norm(A * x_test - b) / norm(b)
                @printf "     τ=%.2f: %2d iter, %.2e res\\n" τ result[2].iters residual
            catch e
                @printf "     τ=%.2f: ❌ %s\\n" τ string(e)
            end
        end
        
        # Test 3: Compare with no preconditioning
        print("   No preconditioner: ")
        try
            x_none = zeros(size(A, 1))
            result = gmres!(x_none, A, b, reltol=1e-6, maxiter=100, log=true)
            residual = norm(A * x_none - b) / norm(b)
            @printf "%d iter, %.2e res\\n" result[2].iters residual
        catch e
            println("❌ $e")
        end
    end
    
    return true
end

function test_working_ilu_interface()
    println("\n" * "=" ^ 50)
    println("🎯 Testing Working ILU Interface")
    println("=" ^ 50)
    
    # Create a moderately sized problem
    n = 30
    N = n * n
    A = spzeros(N, N)
    
    for i in 1:n, j in 1:n
        idx = (i-1)*n + j
        A[idx, idx] = 4.0
        if i > 1; A[idx, idx-n] = -1.0; end
        if i < n; A[idx, idx+n] = -1.0; end
        if j > 1; A[idx, idx-1] = -1.0; end
        if j < n; A[idx, idx+1] = -1.0; end
    end
    
    b = ones(N)
    x_ref = A \ b
    
    println("Problem: 2D Poisson $(n)×$(n) = $(N) DOF")
    println("Condition number: $(round(cond(Matrix(A)), digits=2))")
    
    # Compare different methods
    methods = [
        ("No Preconditioner", () -> I),
        ("Diagonal (Jacobi)", () -> Diagonal(diag(A))),
        ("ILU(τ=0.01)", () -> ilu(A, τ=0.01)),
        ("ILU(τ=0.1)", () -> ilu(A, τ=0.1))
    ]
    
    println("\\nComparison:")
    for (method_name, create_precond) in methods
        try
            P = create_precond()
            
            x = zeros(N)
            start_time = time()
            
            if issymmetric(A)
                result = cg!(x, A, b, Pl=P, reltol=1e-8, maxiter=100, log=true)
            else
                result = gmres!(x, A, b, Pl=P, reltol=1e-8, maxiter=100, log=true)
            end
            
            solve_time = time() - start_time
            residual = norm(A * x - b) / norm(b)
            throughput = N / solve_time
            
            @printf "  %-20s: %.3fs (%2d iter, %.1e res, %6.0f DOF/s)\\n" method_name solve_time result[2].iters residual throughput
            
        catch e
            println("  $method_name: ❌ ERROR - $e")
        end
    end
    
    return true
end

function main()
    success1 = test_ilu_as_preconditioner()
    success2 = test_working_ilu_interface()
    
    if success1 && success2
        println("\\n🎉 ILU integration tests completed successfully!")
        return true
    else
        println("\\n❌ Some ILU tests failed")
        return false
    end
end

if abspath(PROGRAM_FILE) == @__FILE__
    main()
end