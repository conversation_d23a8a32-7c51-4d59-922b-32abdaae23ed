#!/usr/bin/env julia

"""
Debug AMG preconditioner integration
"""

push!(LOAD_PATH, "./src")

using LinearAlgebra
using SparseArrays
using AlgebraicMultigrid
using IterativeSolvers
using Printf

function test_amg_directly()
    println("Testing AlgebraicMultigrid.jl directly")
    println("=" ^ 50)
    
    # Create a simple 2D Poisson problem
    n = 10
    N = n * n
    A = spzeros(N, N)
    
    for i in 1:n, j in 1:n
        idx = (i-1)*n + j
        A[idx, idx] = 4.0
        
        if i > 1; A[idx, idx-n] = -1.0; end
        if i < n; A[idx, idx+n] = -1.0; end
        if j > 1; A[idx, idx-1] = -1.0; end
        if j < n; A[idx, idx+1] = -1.0; end
    end
    
    b = ones(N)
    x = zeros(N)
    
    println("Matrix condition number: ", cond(Matrix(A)))
    
    # Test direct AMG solve
    println("\nTesting AMG direct solve:")
    try
        ml = ruge_stuben(A, max_levels=10, max_coarse=50, θ=0.25)
        x_amg = copy(b)
        # Use the correct interface for AlgebraicMultigrid.jl
        x_amg = ml \ b
        
        println("AMG solution residual: ", norm(A * x_amg - b))
        println("AMG solution: ", x_amg[1:5])
        
        # Test as preconditioner
        println("\nTesting CG with AMG preconditioner:")
        x_cg = zeros(N)
        
        function amg_preconditioner!(y, x)
            # Use AMG as preconditioner
            y .= ml \ x
        end
        
        result = cg!(x_cg, A, b, Pl=amg_preconditioner!, reltol=1e-8, maxiter=100, log=true, verbose=true)
        println("CG+AMG result: ", result)
        println("CG+AMG residual: ", norm(A * x_cg - b))
        
    catch e
        println("AMG test failed: ", e)
        println(stacktrace())
    end
end

test_amg_directly()