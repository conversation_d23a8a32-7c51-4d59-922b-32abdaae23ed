#!/usr/bin/env julia

"""
Test BiCGStab Fix
=================

Verify that the BiCGStab API fix works correctly.
"""

push!(LOAD_PATH, "./src")

using JuliaFOAM
using LinearAlgebra
using SparseArrays
using IterativeSolvers
using Printf

function test_bicgstab_fix()
    println("🔧 Testing BiCGStab Fix")
    println("=" ^ 40)
    
    # Create test problems
    problems = [
        ("Symmetric 20x20", () -> begin
            n = 20
            A = spdiagm(0 => fill(3.0, n), 1 => fill(-1.0, n-1), -1 => fill(-1.0, n-1))
            b = ones(n)
            (A, b)
        end),
        ("Nonsymmetric ConvDiff", () -> begin
            n = 25
            A = spzeros(n, n)
            Pe = 10.0
            h = 1.0 / (n + 1)
            for i in 1:n
                A[i, i] = 2.0 + Pe * h
                if i > 1; A[i, i-1] = -1.0 - Pe * h / 2; end
                if i < n; A[i, i+1] = -1.0 + Pe * h / 2; end
            end
            b = ones(n)
            (A, b)
        end)
    ]
    
    for (name, create_problem) in problems
        println("\n📊 Testing: $name")
        A, b = create_problem()
        x_ref = A \ b
        
        println("   Matrix: $(size(A,1))×$(size(A,2)), symmetric: $(issymmetric(A))")
        
        # Test direct IterativeSolvers.jl call
        print("   Direct bicgstabl!: ")
        x_direct = zeros(size(A, 1))
        try
            result = bicgstabl!(x_direct, A, b, 2, reltol=1e-6, maxiter=100, log=true)
            residual = norm(A * x_direct - b) / norm(b)
            error = norm(x_direct - x_ref) / norm(x_ref)
            @printf "✅ %d iter, %.2e res, %.2e err\\n" result[2].iters residual error
        catch e
            println("❌ $e")
        end
        
        # Test enhanced solver BiCGStab
        print("   Enhanced BiCGStab: ")
        config = EnhancedSolverConfig(
            solver_type=:bicgstabl,
            preconditioner=:jacobi,
            tolerance=1e-6,
            max_iterations=100,
            verbose=false
        )
        
        x_enhanced = zeros(size(A, 1))
        try
            diagnostics = enhanced_solve!(A, b, x_enhanced, config)
            residual = norm(A * x_enhanced - b) / norm(b)
            error = norm(x_enhanced - x_ref) / norm(x_ref)
            
            if diagnostics.iterations < config.max_iterations && residual < 1e-4
                @printf "✅ %d iter, %.2e res, %.2e err\\n" diagnostics.iterations residual error
            else
                @printf "⚠️  %d iter, %.2e res, %.2e err (slow/no conv)\\n" diagnostics.iterations residual error
            end
        catch e
            println("❌ $e")
        end
    end
    
    return true
end

function main()
    success = test_bicgstab_fix()
    
    if success
        println("\n✅ BiCGStab fix test completed")
        println("Now testing with the fixed implementation...")
        
        # Quick rerun of our honest benchmarks to see improvement
        println("\n" * "=" ^ 50)
        println("🚀 Quick Retest with Fixed BiCGStab")
        println("=" ^ 50)
        
        # Test one problem that failed before
        n = 40
        N = n * n  
        A = spzeros(N, N)
        for i in 1:n, j in 1:n
            idx = (i-1)*n + j
            A[idx, idx] = 4.0
            if i > 1; A[idx, idx-n] = -1.0; end
            if i < n; A[idx, idx+n] = -1.0; end
            if j > 1; A[idx, idx-1] = -1.0; end
            if j < n; A[idx, idx+1] = -1.0; end
        end
        b = ones(N)
        
        println("Testing 2D Poisson 40×40 (1600 DOF):")
        
        solvers = [
            ("CG+Jacobi", :cg, :jacobi),
            ("BiCGStab+Jacobi", :bicgstabl, :jacobi), 
            ("GMRES+Jacobi", :gmres, :jacobi),
            ("Auto", :auto, :auto)
        ]
        
        for (name, solver, precond) in solvers
            config = EnhancedSolverConfig(
                solver_type=solver,
                preconditioner=precond,
                tolerance=1e-6,
                max_iterations=200,
                verbose=false
            )
            
            x = zeros(N)
            print("  $name: ")
            
            try
                start_time = time()
                diagnostics = enhanced_solve!(A, b, x, config)
                solve_time = time() - start_time
                
                residual = norm(A * x - b) / norm(b)
                
                if residual < 1e-4
                    throughput = N / solve_time
                    @printf "✅ %.3fs (%d iter, %.1e res, %.0f DOF/s)\\n" solve_time diagnostics.iterations residual throughput
                else
                    @printf "❌ %.3fs (%d iter, %.1e res, NO CONV)\\n" solve_time diagnostics.iterations residual
                end
            catch e
                println("❌ ERROR: $e")
            end
        end
        
        println("\n🎯 BiCGStab should now be working!")
    end
    
    return success
end

if abspath(PROGRAM_FILE) == @__FILE__
    main()
end