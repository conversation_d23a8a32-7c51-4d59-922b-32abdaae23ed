#!/usr/bin/env julia

"""
Test the diagnostic utilities to ensure they work properly
"""

using Printf

# Add parent directories to path for importing JuliaFOAM
push!(LOAD_PATH, joinpath(@__DIR__, "src"))

include("src/core/Types.jl")

println("🔍 Testing JuliaFOAM Diagnostic Utilities")
println("="^60)

# Create a simple test mesh
function create_test_mesh()::Mesh
    println("📐 Creating test mesh...")
    
    # Create a simple 2D mesh (3x3 cells)
    cells = Cell[]
    faces = Face[]
    
    # Create cells
    for i in 1:3
        for j in 1:3
            center = SVector{3,Float64}((i-0.5)*0.33, (j-0.5)*0.33, 0.1)
            volume = 0.33 * 0.33 * 0.2
            faces_list = Int32[]  # Empty for now, will be populated
            push!(cells, Cell(faces_list, center, volume))
        end
    end
    
    # Create some internal faces
    for i in 1:3
        for j in 1:2
            owner = (i-1)*3 + j
            neighbour = (i-1)*3 + j + 1
            center = SVector{3,<PERSON>loat64}((i-0.5)*0.33, j*0.33, 0.1)
            area = SVector{3,Float64}(0.0, 0.33*0.2, 0.0)
            push!(faces, Face(owner, neighbour, area, center))
        end
    end
    
    # Add some boundary faces
    for j in 1:3
        owner = j
        neighbour = -1  # Boundary marker
        center = SVector{3,Float64}(0.0, (j-0.5)*0.33, 0.1)
        area = SVector{3,Float64}(-0.33*0.2, 0.0, 0.0)
        push!(faces, Face(owner, neighbour, area, center))
    end
    
    # Create minimal boundary information
    boundary_faces = Int32[length(faces)]  # List of boundary face indices
    boundary_patches = Dict{String, Vector{Int32}}("walls" => [length(faces)])
    boundary_conditions = Dict{String, BoundaryCondition}(
        "walls" => FixedValueBC(0.0)
    )
    
    println("  Created mesh: $(length(cells)) cells, $(length(faces)) faces")
    return Mesh(cells, faces, boundary_faces, boundary_patches, boundary_conditions)
end

# Test 1: Basic mesh creation
test_mesh = create_test_mesh()
println("✅ Test 1: Mesh creation - PASSED")

# Test 2: Test mesh diagnostics
println("\n🩺 Testing Mesh Diagnostics...")
try
    include("src/tools/MeshDiagnostics.jl")
    using .MeshDiagnostics
    
    doctor = MeshDiagnostics.MeshDoctor(test_mesh, verbose=false, tolerance=1e-12)
    results = MeshDiagnostics.diagnose_mesh(doctor)
    
    println("  Mesh quality: $(results.overall_quality)")
    println("  Critical issues: $(length(results.critical_issues))")
    println("✅ Test 2: Mesh Diagnostics - PASSED")
catch e
    println("❌ Test 2: Mesh Diagnostics - FAILED: $e")
end

# Test 3: Test FVC diagnostics
println("\n🧮 Testing FVC Diagnostics...")
try
    include("src/tools/FVCDiagnostics.jl")
    using .FVCDiagnostics
    
    doctor = FVCDiagnostics.FVCDoctor(test_mesh, verbose=false, tolerance=1e-10)
    results = FVCDiagnostics.diagnose_fvc_operators(doctor)
    
    println("  FVC quality: $(results.overall_fvc_quality)")
    println("  Gradient convergence: $(round(results.gradient_convergence_order, digits=2))")
    println("✅ Test 3: FVC Diagnostics - PASSED")
catch e
    println("❌ Test 3: FVC Diagnostics - FAILED: $e")
end

# Test 4: Test FVM diagnostics
println("\n⚙️  Testing FVM Diagnostics...")
try
    include("src/tools/FVMDiagnostics.jl")
    using .FVMDiagnostics
    
    doctor = FVMDiagnostics.FVMDoctor(test_mesh, verbose=false, tolerance=1e-10)
    results = FVMDiagnostics.diagnose_fvm_system(doctor)
    
    println("  FVM quality: $(results.overall_fvm_quality)")
    println("  Assembly time: $(round(results.assembly_performance_ms, digits=2)) ms")
    println("✅ Test 4: FVM Diagnostics - PASSED")
catch e
    println("❌ Test 4: FVM Diagnostics - FAILED: $e")
end

# Test 5: Test master doctor
println("\n🩺 Testing Master Doctor...")
try
    include("src/tools/JuliaFOAMDoctor.jl")
    using .JuliaFOAMDoctor
    
    health_check = JuliaFOAMDoctor.JuliaFOAMHealthCheck(test_mesh, verbose=false)
    results = JuliaFOAMDoctor.run_full_diagnostics(health_check)
    
    println("  Health score: $(round(results.overall_health_score, digits=1))/100 ($(results.health_grade))")
    println("  System readiness: $(results.system_readiness)")
    println("✅ Test 5: Master Doctor - PASSED")
catch e
    println("❌ Test 5: Master Doctor - FAILED: $e")
end

# Test 6: Test mesh quality checks
println("\n📐 Testing Mesh Quality Checks...")
try
    include("src/tools/MeshQualityChecks.jl")
    using .MeshQualityChecks
    
    analyzer = MeshQualityChecks.MeshQualityAnalyzer(test_mesh, cfd_application="general")
    results = MeshQualityChecks.analyze_mesh_quality(analyzer)
    
    println("  Quality grade: $(results.overall_mesh_grade)")
    println("  CFD readiness: $(round(results.cfd_readiness_score, digits=1))/100")
    println("✅ Test 6: Mesh Quality Checks - PASSED")
catch e
    println("❌ Test 6: Mesh Quality Checks - FAILED: $e")
end

# Test 7: Test validation tests still work
println("\n✅ Testing Core Validation...")
try
    result = read(`julia validation/quick_validation_test.jl`, String)
    if occursin("✅ PASS", result) && occursin("OVERALL STATUS: ✅ PASS", result)
        println("✅ Test 7: Core Validation - PASSED")
    else
        println("⚠️  Test 7: Core Validation - Some issues detected")
    end
catch e
    println("❌ Test 7: Core Validation - FAILED: $e")
end

println("\n📊 Diagnostic Test Summary")
println("="^40)
println("All core diagnostic utilities have been tested.")
println("The diagnostic framework is ready for use!")
println("\n🚀 Try running:")
println("  julia tools/juliafoam_doctor.jl --quick")
println("  julia validation/quick_validation_test.jl")