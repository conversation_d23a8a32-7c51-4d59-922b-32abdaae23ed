#!/usr/bin/env julia

"""
Test Real AMG Integration
=========================

Figure out the correct way to use AlgebraicMultigrid.jl as a preconditioner.
"""

push!(LOAD_PATH, "./src")

using LinearAlgebra
using SparseArrays
using AlgebraicMultigrid
using IterativeSolvers
using Printf

function test_amg_as_preconditioner()
    println("🔧 Testing Real AMG Integration")
    println("=" ^ 40)
    
    # Create a 2D Poisson problem that AMG should handle well
    n = 30
    N = n * n
    A = spzeros(N, N)
    
    for i in 1:n, j in 1:n
        idx = (i-1)*n + j
        A[idx, idx] = 4.0
        
        if i > 1; A[idx, idx-n] = -1.0; end
        if i < n; A[idx, idx+n] = -1.0; end
        if j > 1; A[idx, idx-1] = -1.0; end
        if j < n; A[idx, idx+1] = -1.0; end
    end
    
    b = ones(N)
    x_ref = A \ b
    
    println("Test problem:")
    println("  Size: $(N) ($(n)×$(n) grid)")
    println("  Condition number: $(round(cond(Matrix(A)), digits=2))")
    println("  Reference solution norm: $(round(norm(x_ref), digits=4))")
    
    # Test 1: Basic AMG setup
    println("\n1. Testing basic AMG setup:")
    try
        ml = ruge_stuben(A)
        println("   ✅ AMG hierarchy created successfully")
        println("   Levels: $(length(ml.levels))")
        println("   Coarsest size: $(size(ml.levels[end].A, 1))")
    catch e
        println("   ❌ AMG setup failed: $e")
        return false
    end
    
    # Test 2: AMG as linear operator  
    println("\n2. Testing AMG as linear operator:")
    try
        ml = ruge_stuben(A)
        
        # Try to use as linear operator
        y = similar(b)
        y .= b
        
        # This should apply one AMG cycle  
        y = ml * b  # This might not work - let's see
        
        println("   ✅ AMG multiplication works")
        println("   Output norm: $(norm(y))")
    catch e
        println("   ❌ AMG as operator failed: $e")
    end
    
    # Test 3: Using AMG with IterativeSolvers.jl
    println("\n3. Testing AMG with IterativeSolvers:")
    try
        ml = ruge_stuben(A)
        
        # Create a function that applies AMG as preconditioner
        function amg_preconditioner(y, x)
            # Apply one AMG V-cycle
            y .= x
            # Use the solve! method if it exists
            AlgebraicMultigrid.solve!(y, ml, x)
            return y
        end
        
        x_cg = zeros(N)
        result = cg!(x_cg, A, b, Pl=amg_preconditioner, reltol=1e-8, maxiter=50, log=true)
        
        residual = norm(A * x_cg - b) / norm(b)
        error = norm(x_cg - x_ref) / norm(x_ref)
        
        println("   ✅ CG+AMG result: $result")
        @printf "   Residual: %.2e, Error: %.2e\\n" residual error
        
    catch e
        println("   ❌ CG+AMG failed: $e")
    end
    
    # Test 4: Alternative AMG interface
    println("\n4. Testing alternative AMG interfaces:")
    try
        ml = ruge_stuben(A)
        
        # Try different ways to apply AMG
        methods_to_try = [
            "ml \\ b",
            "aspreconditioner(ml)",
            "solve(ml, b)",
        ]
        
        for method in methods_to_try
            try
                if method == "ml \\ b"
                    result = ml \ b
                    println("   ✅ $method works: norm = $(round(norm(result), digits=4))")
                elseif method == "aspreconditioner(ml)"
                    P = AlgebraicMultigrid.aspreconditioner(ml)
                    println("   ✅ $method works: type = $(typeof(P))")
                else
                    println("   ⏩ Skipping $method")
                end
            catch e
                println("   ❌ $method failed: $e")
            end
        end
        
    catch e
        println("   ❌ Alternative methods failed: $e")
    end
    
    return true
end

function test_successful_amg_integration()
    println("\n" * "=" ^ 50)
    println("🎯 Implementing Working AMG Integration")
    println("=" ^ 50)
    
    # Create test problem
    n = 25
    N = n * n
    A = spzeros(N, N)
    
    for i in 1:n, j in 1:n
        idx = (i-1)*n + j
        A[idx, idx] = 4.0
        if i > 1; A[idx, idx-n] = -1.0; end
        if i < n; A[idx, idx+n] = -1.0; end
        if j > 1; A[idx, idx-1] = -1.0; end
        if j < n; A[idx, idx+1] = -1.0; end
    end
    
    b = ones(N)
    x_ref = A \ b
    
    println("Problem: 2D Poisson $(n)×$(n) = $(N) DOF")
    
    # Try the most promising approach
    try
        ml = ruge_stuben(A)
        
        # Method 1: Use aspreconditioner if available
        try
            P = AlgebraicMultigrid.aspreconditioner(ml)
            
            x_cg = zeros(N)
            start_time = time()
            result = cg!(x_cg, A, b, Pl=P, reltol=1e-8, maxiter=100, log=true)
            solve_time = time() - start_time
            
            residual = norm(A * x_cg - b) / norm(b)
            throughput = N / solve_time
            
            println("✅ CG + AMG Preconditioner:")
            @printf "   Time: %.4fs, Iterations: %d\\n" solve_time result[2].iters
            @printf "   Residual: %.2e, Throughput: %.0f DOF/s\\n" residual throughput
            
            return P  # Return working preconditioner
            
        catch e
            println("❌ aspreconditioner method failed: $e")
        end
        
        # Method 2: Try manual V-cycle
        try
            x_cg = zeros(N)
            
            function manual_amg_precond!(y, x)
                # Simple: just use the AMG hierarchy as approximate inverse
                y .= ml \ x
            end
            
            start_time = time()
            result = cg!(x_cg, A, b, Pl=manual_amg_precond!, reltol=1e-8, maxiter=100, log=true)
            solve_time = time() - start_time
            
            residual = norm(A * x_cg - b) / norm(b)
            throughput = N / solve_time
            
            println("✅ CG + Manual AMG:")
            @printf "   Time: %.4fs, Iterations: %d\\n" solve_time result[2].iters
            @printf "   Residual: %.2e, Throughput: %.0f DOF/s\\n" residual throughput
            
            return manual_amg_precond!
            
        catch e
            println("❌ Manual AMG failed: $e")
        end
        
    catch e
        println("❌ All AMG methods failed: $e")
    end
    
    return nothing
end

function main()
    if test_amg_as_preconditioner()
        result = test_successful_amg_integration()
        
        if result !== nothing
            println("\n🎉 Found working AMG integration method!")
            return true
        else
            println("\n⚠️  Need to find alternative AMG approach")
            return false
        end
    else
        println("\n❌ Basic AMG integration failed")
        return false
    end
end

if abspath(PROGRAM_FILE) == @__FILE__
    main()
end