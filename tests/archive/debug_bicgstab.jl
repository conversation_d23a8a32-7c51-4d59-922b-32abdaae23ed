#!/usr/bin/env julia

"""
Debug BiCGStab Implementation
=============================

Find out exactly why BiCGStab is failing and fix it properly.
"""

push!(LOAD_PATH, "./src")

using LinearAlgebra
using SparseArrays
using IterativeSolvers
using Printf

function test_bicgstab_directly()
    println("🔍 Debugging BiCGStab Implementation")
    println("=" ^ 50)
    
    # Create a simple test problem that should work
    n = 20
    A = spdiagm(0 => fill(3.0, n), 1 => fill(-1.0, n-1), -1 => fill(-1.0, n-1))
    b = ones(n)
    x_ref = A \ b
    
    println("Test problem:")
    println("  Size: $n x $n")
    println("  Condition number: $(round(cond(Matrix(A)), digits=2))")
    println("  Reference solution norm: $(round(norm(x_ref), digits=4))")
    println("  Reference residual: $(norm(A * x_ref - b))")
    
    # Test IterativeSolvers.jl BiCGStab directly
    println("\n1. Testing IterativeSolvers.jl BiCGStab directly:")
    x1 = zeros(n)
    
    try
        result = bicgstabl!(x1, A, b, reltol=1e-8, maxiter=100, log=true, verbose=true)
        println("   Result: $result")
        println("   Solution norm: $(round(norm(x1), digits=4))")
        println("   Residual: $(norm(A * x1 - b))")
        println("   Relative error: $(norm(x1 - x_ref) / norm(x_ref))")
    catch e
        println("   ERROR: $e")
    end
    
    # Test with different parameters
    println("\n2. Testing with looser tolerance:")
    x2 = zeros(n)
    
    try
        result = bicgstabl!(x2, A, b, reltol=1e-6, maxiter=1000, log=true)
        println("   Result: $result")
        println("   Solution norm: $(round(norm(x2), digits=4))")
        println("   Residual: $(norm(A * x2 - b))")
        println("   Relative error: $(norm(x2 - x_ref) / norm(x_ref))")
    catch e
        println("   ERROR: $e")
    end
    
    # Test with diagonal preconditioning
    println("\n3. Testing with diagonal preconditioning:")
    x3 = zeros(n)
    P = Diagonal(diag(A))
    
    try
        result = bicgstabl!(x3, A, b, Pl=P, reltol=1e-6, maxiter=100, log=true)
        println("   Result: $result")
        println("   Solution norm: $(round(norm(x3), digits=4))")
        println("   Residual: $(norm(A * x3 - b))")
        println("   Relative error: $(norm(x3 - x_ref) / norm(x_ref))")
    catch e
        println("   ERROR: $e")
    end
    
    # Test BiCGStab(l) with different l values
    println("\n4. Testing BiCGStab(l) with different l values:")
    for l in [1, 2, 4]
        xl = zeros(n)
        try
            result = bicgstabl!(xl, A, b, l=l, reltol=1e-6, maxiter=100, log=true)
            println("   l=$l: Success, residual=$(norm(A * xl - b))")
        catch e
            println("   l=$l: ERROR - $e")
        end
    end
    
    # Compare with CG for reference
    println("\n5. Reference: CG on same problem:")
    x_cg = zeros(n)
    try
        result_cg = cg!(x_cg, A, b, reltol=1e-8, maxiter=100, log=true)
        println("   CG result: $result_cg")
        println("   CG solution norm: $(round(norm(x_cg), digits=4))")
        println("   CG residual: $(norm(A * x_cg - b))")
        println("   CG relative error: $(norm(x_cg - x_ref) / norm(x_ref))")
    catch e
        println("   CG ERROR: $e")
    end
end

function test_nonsymmetric_problem()
    println("\n" * "=" ^ 50)
    println("🔍 Testing BiCGStab on Nonsymmetric Problem")
    println("=" ^ 50)
    
    # Create convection-diffusion problem
    n = 20
    A = spzeros(n, n)
    Pe = 10.0  # Peclet number
    h = 1.0 / (n + 1)
    
    for i in 1:n
        A[i, i] = 2.0 + Pe * h
        if i > 1; A[i, i-1] = -1.0 - Pe * h / 2; end
        if i < n; A[i, i+1] = -1.0 + Pe * h / 2; end
    end
    
    b = ones(n)
    x_ref = A \ b
    
    println("Convection-diffusion problem:")
    println("  Size: $n")
    println("  Peclet number: $Pe")
    println("  Symmetric: $(issymmetric(A))")
    println("  Reference solution norm: $(round(norm(x_ref), digits=4))")
    
    # Test BiCGStab
    x_bicg = zeros(n)
    try
        result = bicgstabl!(x_bicg, A, b, reltol=1e-6, maxiter=200, log=true, verbose=true)
        println("   BiCGStab result: $result")
        println("   Solution norm: $(round(norm(x_bicg), digits=4))")
        println("   Residual: $(norm(A * x_bicg - b))")
        println("   Relative error: $(norm(x_bicg - x_ref) / norm(x_ref))")
    catch e
        println("   BiCGStab ERROR: $e")
    end
    
    # Test GMRES for comparison
    x_gmres = zeros(n)
    try
        result = gmres!(x_gmres, A, b, reltol=1e-6, maxiter=200, restart=30, log=true)
        println("   GMRES result: $result")
        println("   GMRES solution norm: $(round(norm(x_gmres), digits=4))")
        println("   GMRES residual: $(norm(A * x_gmres - b))")
        println("   GMRES relative error: $(norm(x_gmres - x_ref) / norm(x_ref))")
    catch e
        println("   GMRES ERROR: $e")
    end
end

function main()
    test_bicgstab_directly()
    test_nonsymmetric_problem()
    
    println("\n" * "=" ^ 50)
    println("✅ BiCGStab Debugging Complete")
    println("Check the results above to identify the issue.")
    println("=" ^ 50)
end

if abspath(PROGRAM_FILE) == @__FILE__
    main()
end