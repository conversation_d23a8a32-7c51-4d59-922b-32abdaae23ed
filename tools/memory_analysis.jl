#!/usr/bin/env julia

"""
Memory Analysis Script for JuliaFOAM

This script performs comprehensive memory profiling of JuliaFOAM solvers
to identify allocation hotspots and optimization opportunities.

Usage:
    julia tools/memory_analysis.jl [--solver SOLVER] [--mesh-size SIZE] [--output DIR]

Options:
    --solver <PERSON><PERSON>VER     Solver to analyze (simple, optimized, enhanced) [default: simple]
    --mesh-size SIZE    Mesh size for testing (small, medium, large) [default: medium]
    --output DIR        Output directory for reports [default: memory_analysis_results]
"""

# Add the parent directory to the load path for local development
push!(LOAD_PATH, joinpath(@__DIR__, "..", "src"))

using ArgParse
using JuliaFOAM
using JuliaFOAM.Tools
using Printf
using Dates

function parse_commandline()
    s = ArgParseSettings()
    
    @add_arg_table! s begin
        "--solver"
            help = "Solver to analyze (simple, optimized, enhanced)"
            default = "simple"
        "--mesh-size"
            help = "Mesh size for testing (small, medium, large)"
            default = "medium"
        "--output"
            help = "Output directory for reports"
            default = "memory_analysis_results"
        "--iterations"
            help = "Number of solver iterations"
            arg_type = Int
            default = 10
        "--samples"
            help = "Number of benchmark samples"
            arg_type = Int
            default = 5
    end
    
    return parse_args(s)
end

function create_test_mesh(size_category::String)
    """Create test mesh based on size category."""
    if size_category == "small"
        return create_box_mesh(10, 10, 10, 1.0, 1.0, 1.0)
    elseif size_category == "medium"
        return create_box_mesh(20, 20, 20, 1.0, 1.0, 1.0)
    elseif size_category == "large"
        return create_box_mesh(40, 40, 40, 1.0, 1.0, 1.0)
    else
        error("Unknown mesh size category: $size_category")
    end
end

function create_test_fields(mesh)
    """Create test fields for solver analysis."""
    U = Field("U", mesh, SVector{3,Float64}(1.0, 0.0, 0.0))
    p = Field("p", mesh, 0.0)
    
    # Set some boundary conditions
    for (patch_name, _) in mesh.boundary_patches
        U.boundary_values[patch_name] = fill(SVector{3,Float64}(1.0, 0.0, 0.0), 
                                           length(mesh.boundary_patches[patch_name]))
        p.boundary_values[patch_name] = fill(0.0, length(mesh.boundary_patches[patch_name]))
    end
    
    return Dict("U" => U, "p" => p)
end

function create_solver_config(solver_type::String, iterations::Int)
    """Create solver configuration based on type."""
    if solver_type == "simple"
        return SimpleSolverConfig(
            max_iterations=iterations,
            tolerance=1e-6,
            relaxation_factors=Dict("U"=>0.7, "p"=>0.3),
            track_residuals=false,  # Disable for cleaner profiling
            residual_output_interval=iterations+1
        )
    elseif solver_type == "optimized"
        return OptimizedSimpleSolverConfig(
            max_iterations=iterations,
            tolerance=1e-6,
            relaxation_factors=Dict("U"=>0.7, "p"=>0.3),
            track_residuals=false,
            residual_output_interval=iterations+1
        )
    elseif solver_type == "enhanced"
        return EnhancedSimpleSolverConfig(
            max_iterations=iterations,
            tolerance=1e-6,
            relaxation_factors=Dict("U"=>0.7, "p"=>0.3),
            track_residuals=false,
            residual_output_interval=iterations+1
        )
    else
        error("Unknown solver type: $solver_type")
    end
end

function get_solver_function(solver_type::String)
    """Get solver function based on type."""
    if solver_type == "simple"
        return solve_simple
    elseif solver_type == "optimized"
        return solve_simple_optimized
    elseif solver_type == "enhanced"
        return solve_enhanced_simple!
    else
        error("Unknown solver type: $solver_type")
    end
end

function analyze_solver_memory(solver_type::String, mesh_size::String, 
                             iterations::Int, samples::Int, output_dir::String)
    """Perform comprehensive memory analysis of a JuliaFOAM solver."""
    
    println("="^60)
    println("JuliaFOAM Memory Analysis")
    println("="^60)
    println("Solver: $solver_type")
    println("Mesh size: $mesh_size")
    println("Iterations: $iterations")
    println("Samples: $samples")
    println("Output directory: $output_dir")
    println()
    
    # Create output directory
    mkpath(output_dir)
    
    # Create test case
    println("Creating test mesh and fields...")
    mesh = create_test_mesh(mesh_size)
    fields = create_test_fields(mesh)
    config = create_solver_config(solver_type, iterations)
    solver_func = get_solver_function(solver_type)
    
    n_cells = length(mesh.cells)
    println("Mesh created: $n_cells cells")
    
    # Boundary conditions (simplified)
    boundary_conditions = Dict{String,Any}()
    
    # Single solver run analysis
    println("\nPerforming detailed memory analysis...")
    try
        analysis, profile = profile_juliafoam_solver(
            solver_func, mesh, fields, boundary_conditions, config;
            profile_name="$(solver_type)_$(mesh_size)_detailed"
        )
        
        # Create detailed report
        report_file = joinpath(output_dir, "$(solver_type)_$(mesh_size)_detailed_report.md")
        create_memory_report(analysis, report_file)
        println("Detailed report saved to: $report_file")
        
        # Print summary
        println("\nDetailed Analysis Summary:")
        println("  Peak memory: $(round(analysis.peak_memory_mb, digits=2)) MB")
        println("  Memory per cell: $(round(analysis.peak_memory_mb / n_cells * 1024, digits=2)) KB")
        println("  GC overhead: $(round(analysis.gc_time_percentage, digits=2))%")
        println("  Total allocations: $(analysis.total_allocations)")
        println("  Recommendations: $(length(analysis.recommendations))")
        
    catch e
        println("Error in detailed analysis: $e")
        println("Continuing with benchmark analysis...")
    end
    
    # Benchmark analysis
    println("\nPerforming benchmark analysis ($samples samples)...")
    try
        # Create a wrapper function for benchmarking
        function solver_wrapper()
            return solver_func(mesh, fields, boundary_conditions, config)
        end
        
        benchmark_results = benchmark_memory_performance(
            solver_wrapper; 
            samples=samples, 
            profile_name="$(solver_type)_$(mesh_size)_benchmark"
        )
        
        # Calculate statistics
        peak_memories = [r.peak_memory_mb for r in benchmark_results]
        gc_times = [r.gc_time_percentage for r in benchmark_results]
        total_allocs = [r.total_allocations for r in benchmark_results]
        
        # Save benchmark results
        benchmark_file = joinpath(output_dir, "$(solver_type)_$(mesh_size)_benchmark.txt")
        open(benchmark_file, "w") do io
            println(io, "JuliaFOAM Memory Benchmark Results")
            println(io, "Solver: $solver_type")
            println(io, "Mesh size: $mesh_size ($n_cells cells)")
            println(io, "Iterations: $iterations")
            println(io, "Samples: $samples")
            println(io, "Date: $(now())")
            println(io, "")
            println(io, "Peak Memory Statistics:")
            println(io, "  Mean: $(round(mean(peak_memories), digits=2)) MB")
            println(io, "  Std:  $(round(std(peak_memories), digits=2)) MB")
            println(io, "  Min:  $(round(minimum(peak_memories), digits=2)) MB")
            println(io, "  Max:  $(round(maximum(peak_memories), digits=2)) MB")
            println(io, "")
            println(io, "GC Time Statistics:")
            println(io, "  Mean: $(round(mean(gc_times), digits=2))%")
            println(io, "  Std:  $(round(std(gc_times), digits=2))%")
            println(io, "  Min:  $(round(minimum(gc_times), digits=2))%")
            println(io, "  Max:  $(round(maximum(gc_times), digits=2))%")
            println(io, "")
            println(io, "Allocation Statistics:")
            println(io, "  Mean: $(round(mean(total_allocs), digits=0))")
            println(io, "  Std:  $(round(std(total_allocs), digits=0))")
            println(io, "  Min:  $(round(minimum(total_allocs), digits=0))")
            println(io, "  Max:  $(round(maximum(total_allocs), digits=0))")
        end
        
        println("Benchmark results saved to: $benchmark_file")
        
    catch e
        println("Error in benchmark analysis: $e")
    end
    
    println("\nMemory analysis completed!")
    println("Results saved to: $output_dir")
end

function main()
    args = parse_commandline()
    
    try
        analyze_solver_memory(
            args["solver"],
            args["mesh-size"],
            args["iterations"],
            args["samples"],
            args["output"]
        )
    catch e
        println("Error during analysis: $e")
        println("This might be due to missing solver implementations or dependencies.")
        println("Please ensure JuliaFOAM is properly set up and the requested solver is available.")
        exit(1)
    end
end

if abspath(PROGRAM_FILE) == @__FILE__
    main()
end
