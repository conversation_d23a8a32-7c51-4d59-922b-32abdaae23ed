#!/usr/bin/env julia

"""
Communication Pattern Benchmarking Script for JuliaFOAM

This script benchmarks different communication patterns and strategies
to identify optimal configurations for various mesh sizes and process counts.

Usage:
    mpirun -np N julia tools/communication_benchmark.jl [OPTIONS]

Options:
    --mesh-sizes SIZES     Comma-separated mesh sizes to test [default: small,medium]
    --comm-patterns PATTERNS  Communication patterns to test [default: blocking,nonblocking,overlap]
    --output DIR           Output directory for results [default: communication_benchmarks]
    --samples N            Number of benchmark samples [default: 10]
    --warmup N             Number of warmup iterations [default: 3]
"""

using MPI
using ArgParse
using Printf
using Statistics
using Dates

# Add the parent directory to the load path for local development
push!(LOAD_PATH, joinpath(@__DIR__, "..", "src"))

# Initialize MPI first
if !MPI.Initialized()
    MPI.Init()
end

using JuliaFOAM
using JuliaFOAM.Tools

function parse_commandline()
    s = ArgParseSettings()
    
    @add_arg_table! s begin
        "--mesh-sizes"
            help = "Comma-separated mesh sizes to test"
            default = "small,medium"
        "--comm-patterns"
            help = "Communication patterns to test"
            default = "blocking,nonblocking,overlap"
        "--output"
            help = "Output directory for results"
            default = "communication_benchmarks"
        "--samples"
            help = "Number of benchmark samples"
            arg_type = Int
            default = 10
        "--warmup"
            help = "Number of warmup iterations"
            arg_type = Int
            default = 3
        "--message-sizes"
            help = "Comma-separated message sizes to test (in KB)"
            default = "1,10,100,1000"
    end
    
    return parse_args(s)
end

function create_benchmark_mesh(size_category::String, comm::MPI.Comm)
    """Create benchmark mesh with known communication patterns."""
    rank = MPI.Comm_rank(comm)
    nprocs = MPI.Comm_size(comm)
    
    # Define mesh parameters
    mesh_params = Dict(
        "small" => (20, 20, 20),
        "medium" => (40, 40, 40),
        "large" => (80, 80, 80)
    )
    
    if !haskey(mesh_params, size_category)
        error("Unknown mesh size: $size_category")
    end
    
    nx, ny, nz = mesh_params[size_category]
    
    # Create mock mesh structure for benchmarking
    n_cells_total = nx * ny * nz
    cells_per_proc = div(n_cells_total, nprocs)
    
    # Local cells for this process
    start_cell = rank * cells_per_proc + 1
    end_cell = min((rank + 1) * cells_per_proc, n_cells_total)
    n_local_cells = end_cell - start_cell + 1
    
    # Create mock mesh
    cells = [nothing for _ in 1:n_local_cells]
    
    # Create communication maps
    send_maps = Dict{Int, Vector{Int}}()
    recv_maps = Dict{Int, Vector{Int}}()
    
    # Each process communicates with its neighbors in a ring topology
    left_neighbor = (rank - 1 + nprocs) % nprocs
    right_neighbor = (rank + 1) % nprocs
    
    if nprocs > 1
        # Halo size based on mesh size
        halo_size = max(1, div(n_local_cells, 20))
        
        # Send to right neighbor
        if right_neighbor != rank
            send_maps[right_neighbor] = collect((n_local_cells - halo_size + 1):n_local_cells)
        end
        
        # Receive from left neighbor
        if left_neighbor != rank
            recv_maps[left_neighbor] = collect(1:halo_size)
        end
        
        # For larger process counts, add more neighbors
        if nprocs > 4
            up_neighbor = (rank + nprocs ÷ 2) % nprocs
            down_neighbor = (rank - nprocs ÷ 2 + nprocs) % nprocs
            
            if up_neighbor != rank
                send_maps[up_neighbor] = collect(1:div(halo_size, 2))
            end
            if down_neighbor != rank
                recv_maps[down_neighbor] = collect((n_local_cells - div(halo_size, 2) + 1):n_local_cells)
            end
        end
    end
    
    # Create mock mesh structure
    mesh = (
        cells = cells,
        send_maps = send_maps,
        recv_maps = recv_maps,
        n_local_cells = n_local_cells
    )
    
    return mesh
end

function benchmark_blocking_communication(field::Vector{T}, mesh, comm::MPI.Comm) where T
    """Benchmark blocking communication pattern."""
    rank = MPI.Comm_rank(comm)
    
    start_time = time()
    
    # Blocking send/receive
    for (neighbor_rank, send_indices) in mesh.send_maps
        if neighbor_rank == rank
            continue
        end
        
        send_buffer = field[send_indices]
        MPI.Send(send_buffer, neighbor_rank, 0, comm)
    end
    
    for (neighbor_rank, recv_indices) in mesh.recv_maps
        if neighbor_rank == rank
            continue
        end
        
        recv_buffer = Vector{T}(undef, length(recv_indices))
        MPI.Recv!(recv_buffer, neighbor_rank, 0, comm)
        field[recv_indices] .= recv_buffer
    end
    
    return time() - start_time
end

function benchmark_nonblocking_communication(field::Vector{T}, mesh, comm::MPI.Comm) where T
    """Benchmark non-blocking communication pattern."""
    rank = MPI.Comm_rank(comm)
    
    start_time = time()
    
    send_requests = MPI.Request[]
    recv_requests = MPI.Request[]
    recv_buffers = Dict{Int, Vector{T}}()
    
    # Post receives first
    for (neighbor_rank, recv_indices) in mesh.recv_maps
        if neighbor_rank == rank
            continue
        end
        
        recv_buffers[neighbor_rank] = Vector{T}(undef, length(recv_indices))
        request = MPI.Irecv!(recv_buffers[neighbor_rank], neighbor_rank, 0, comm)
        push!(recv_requests, request)
    end
    
    # Post sends
    for (neighbor_rank, send_indices) in mesh.send_maps
        if neighbor_rank == rank
            continue
        end
        
        send_buffer = field[send_indices]
        request = MPI.Isend(send_buffer, neighbor_rank, 0, comm)
        push!(send_requests, request)
    end
    
    # Wait for completion
    if !isempty(recv_requests)
        MPI.Waitall!(recv_requests)
    end
    if !isempty(send_requests)
        MPI.Waitall!(send_requests)
    end
    
    # Update field
    for (neighbor_rank, recv_indices) in mesh.recv_maps
        if neighbor_rank == rank || !haskey(recv_buffers, neighbor_rank)
            continue
        end
        field[recv_indices] .= recv_buffers[neighbor_rank]
    end
    
    return time() - start_time
end

function benchmark_overlap_communication(field::Vector{T}, mesh, comm::MPI.Comm) where T
    """Benchmark communication with computation overlap."""
    rank = MPI.Comm_rank(comm)
    
    start_time = time()
    
    send_requests = MPI.Request[]
    recv_requests = MPI.Request[]
    recv_buffers = Dict{Int, Vector{T}}()
    
    # Post receives
    for (neighbor_rank, recv_indices) in mesh.recv_maps
        if neighbor_rank == rank
            continue
        end
        
        recv_buffers[neighbor_rank] = Vector{T}(undef, length(recv_indices))
        request = MPI.Irecv!(recv_buffers[neighbor_rank], neighbor_rank, 0, comm)
        push!(recv_requests, request)
    end
    
    # Post sends
    for (neighbor_rank, send_indices) in mesh.send_maps
        if neighbor_rank == rank
            continue
        end
        
        send_buffer = field[send_indices]
        request = MPI.Isend(send_buffer, neighbor_rank, 0, comm)
        push!(send_requests, request)
    end
    
    # Perform computation while communication is in progress
    # Simulate gradient computation
    for i in 1:length(field)
        field[i] = field[i] * 1.01 + 0.001 * sin(i)
    end
    
    # Additional computational work
    for _ in 1:100
        sum(field)
    end
    
    # Wait for communication completion
    if !isempty(recv_requests)
        MPI.Waitall!(recv_requests)
    end
    if !isempty(send_requests)
        MPI.Waitall!(send_requests)
    end
    
    # Update field
    for (neighbor_rank, recv_indices) in mesh.recv_maps
        if neighbor_rank == rank || !haskey(recv_buffers, neighbor_rank)
            continue
        end
        field[recv_indices] .= recv_buffers[neighbor_rank]
    end
    
    return time() - start_time
end

function run_communication_benchmark(mesh_size::String, pattern::String, args, comm::MPI.Comm)
    """Run benchmark for specific mesh size and communication pattern."""
    rank = MPI.Comm_rank(comm)
    nprocs = MPI.Comm_size(comm)
    
    # Create benchmark mesh
    mesh = create_benchmark_mesh(mesh_size, comm)
    
    # Create test field
    field = rand(mesh.n_local_cells)
    
    # Select benchmark function
    if pattern == "blocking"
        benchmark_func = benchmark_blocking_communication
    elseif pattern == "nonblocking"
        benchmark_func = benchmark_nonblocking_communication
    elseif pattern == "overlap"
        benchmark_func = benchmark_overlap_communication
    else
        error("Unknown communication pattern: $pattern")
    end
    
    # Warmup runs
    for _ in 1:args["warmup"]
        benchmark_func(copy(field), mesh, comm)
    end
    
    # Benchmark runs
    times = Float64[]
    for _ in 1:args["samples"]
        test_field = copy(field)
        elapsed_time = benchmark_func(test_field, mesh, comm)
        push!(times, elapsed_time)
    end
    
    # Calculate statistics
    mean_time = mean(times)
    std_time = std(times)
    min_time = minimum(times)
    max_time = maximum(times)
    
    # Calculate communication volume
    total_send_volume = 0
    for (_, send_indices) in mesh.send_maps
        total_send_volume += length(send_indices) * sizeof(Float64)
    end
    
    # Calculate bandwidth
    bandwidth = total_send_volume / mean_time / (1024^2)  # MB/s
    
    return Dict(
        "mesh_size" => mesh_size,
        "pattern" => pattern,
        "processes" => nprocs,
        "mean_time" => mean_time,
        "std_time" => std_time,
        "min_time" => min_time,
        "max_time" => max_time,
        "bandwidth" => bandwidth,
        "volume_mb" => total_send_volume / (1024^2),
        "samples" => args["samples"]
    )
end

function generate_benchmark_report(results, args, comm)
    """Generate comprehensive benchmark report."""
    rank = MPI.Comm_rank(comm)
    
    if rank != 0
        return
    end
    
    output_dir = args["output"]
    mkpath(output_dir)
    
    timestamp = Dates.format(now(), "yyyy-mm-dd_HH-MM-SS")
    report_file = joinpath(output_dir, "communication_benchmark_$(timestamp).md")
    
    open(report_file, "w") do io
        write(io, "# JuliaFOAM Communication Pattern Benchmark Report\n\n")
        write(io, "Generated: $(now())\n")
        write(io, "Processes: $(MPI.Comm_size(comm))\n")
        write(io, "Samples per test: $(args["samples"])\n")
        write(io, "Warmup iterations: $(args["warmup"])\n\n")
        
        # Summary table
        write(io, "## Benchmark Results Summary\n\n")
        write(io, "| Mesh Size | Pattern | Mean Time (ms) | Bandwidth (MB/s) | Volume (MB) |\n")
        write(io, "|-----------|---------|----------------|------------------|-------------|\n")
        
        for result in results
            write(io, "| $(result["mesh_size"]) | $(result["pattern"]) | ")
            write(io, "$(round(result["mean_time"] * 1000, digits=2)) | ")
            write(io, "$(round(result["bandwidth"], digits=2)) | ")
            write(io, "$(round(result["volume_mb"], digits=2)) |\n")
        end
        write(io, "\n")
        
        # Detailed results
        write(io, "## Detailed Results\n\n")
        for result in results
            write(io, "### $(result["mesh_size"]) mesh - $(result["pattern"]) pattern\n\n")
            write(io, "- **Mean time**: $(round(result["mean_time"] * 1000, digits=2)) ± $(round(result["std_time"] * 1000, digits=2)) ms\n")
            write(io, "- **Min time**: $(round(result["min_time"] * 1000, digits=2)) ms\n")
            write(io, "- **Max time**: $(round(result["max_time"] * 1000, digits=2)) ms\n")
            write(io, "- **Bandwidth**: $(round(result["bandwidth"], digits=2)) MB/s\n")
            write(io, "- **Communication volume**: $(round(result["volume_mb"], digits=2)) MB\n\n")
        end
        
        # Performance analysis
        write(io, "## Performance Analysis\n\n")
        
        # Group results by mesh size
        mesh_sizes = unique([r["mesh_size"] for r in results])
        patterns = unique([r["pattern"] for r in results])
        
        for mesh_size in mesh_sizes
            write(io, "### $(mesh_size) mesh comparison\n\n")
            mesh_results = filter(r -> r["mesh_size"] == mesh_size, results)
            
            if length(mesh_results) > 1
                best_pattern = mesh_results[argmin([r["mean_time"] for r in mesh_results])]
                worst_pattern = mesh_results[argmax([r["mean_time"] for r in mesh_results])]
                
                speedup = worst_pattern["mean_time"] / best_pattern["mean_time"]
                write(io, "- **Best pattern**: $(best_pattern["pattern"]) ($(round(best_pattern["mean_time"] * 1000, digits=2)) ms)\n")
                write(io, "- **Worst pattern**: $(worst_pattern["pattern"]) ($(round(worst_pattern["mean_time"] * 1000, digits=2)) ms)\n")
                write(io, "- **Speedup**: $(round(speedup, digits=2))x\n\n")
            end
        end
        
        # Recommendations
        write(io, "## Recommendations\n\n")
        
        # Find overall best pattern
        if length(results) > 0
            best_overall = results[argmin([r["mean_time"] for r in results])]
            write(io, "1. **Best overall pattern**: $(best_overall["pattern"]) on $(best_overall["mesh_size"]) mesh\n")
            
            # Bandwidth analysis
            high_bandwidth_results = filter(r -> r["bandwidth"] > 100, results)
            if !isempty(high_bandwidth_results)
                write(io, "2. **High bandwidth patterns**: $(join(unique([r["pattern"] for r in high_bandwidth_results]), ", "))\n")
            end
            
            # Scalability analysis
            if length(patterns) > 1
                write(io, "3. **Pattern selection**: Use non-blocking or overlap patterns for better scalability\n")
            end
        end
    end
    
    println("Benchmark report saved to: $report_file")
end

function main()
    comm = MPI.COMM_WORLD
    rank = MPI.Comm_rank(comm)
    nprocs = MPI.Comm_size(comm)
    
    args = parse_commandline()
    
    if rank == 0
        println("JuliaFOAM Communication Pattern Benchmarking")
        println("Running with $nprocs processes...")
    end
    
    # Parse input parameters
    mesh_sizes = split(args["mesh-sizes"], ",")
    patterns = split(args["comm-patterns"], ",")
    
    results = []
    
    try
        for mesh_size in mesh_sizes
            for pattern in patterns
                if rank == 0
                    println("Benchmarking: $mesh_size mesh, $pattern pattern...")
                end
                
                result = run_communication_benchmark(strip(mesh_size), strip(pattern), args, comm)
                push!(results, result)
                
                if rank == 0
                    println("  Time: $(round(result["mean_time"] * 1000, digits=2)) ms, Bandwidth: $(round(result["bandwidth"], digits=2)) MB/s")
                end
            end
        end
        
        # Generate report
        generate_benchmark_report(results, args, comm)
        
        if rank == 0
            println("\nBenchmarking completed successfully!")
            println("Results saved to: $(args["output"])")
        end
        
    catch e
        if rank == 0
            println("Error during benchmarking: $e")
        end
        MPI.Abort(comm, 1)
    end
    
    MPI.Finalize()
end

if abspath(PROGRAM_FILE) == @__FILE__
    main()
end
