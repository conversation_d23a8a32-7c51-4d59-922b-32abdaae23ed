#!/usr/bin/env julia

"""
Communication Pattern Analysis Script for JuliaFOAM

This script performs comprehensive analysis of MPI communication patterns,
load balancing efficiency, and computation-communication overlap opportunities
in JuliaFOAM parallel solvers.

Usage:
    mpirun -np N julia tools/communication_analysis.jl [OPTIONS]

Options:
    --mesh-size SIZE        Mesh size for testing (small, medium, large) [default: medium]
    --solver SOLVER         Solver to analyze (simple, optimized, enhanced) [default: simple]
    --output DIR           Output directory for reports [default: communication_analysis_results]
    --iterations N         Number of solver iterations [default: 10]
    --analysis-type TYPE   Type of analysis (patterns, load_balance, overlap, all) [default: all]
"""

using MPI
using ArgParse
using Printf
using Dates

# Add the parent directory to the load path for local development
push!(LOAD_PATH, joinpath(@__DIR__, "..", "src"))

# Initialize MPI first
if !MPI.Initialized()
    MPI.Init()
end

using JuliaFOAM
using JuliaFOAM.Tools

function parse_commandline()
    s = ArgParseSettings()
    
    @add_arg_table! s begin
        "--mesh-size"
            help = "Mesh size for testing (small, medium, large)"
            default = "medium"
        "--solver"
            help = "Solver to analyze (simple, optimized, enhanced)"
            default = "simple"
        "--output"
            help = "Output directory for reports"
            default = "communication_analysis_results"
        "--iterations"
            help = "Number of solver iterations"
            arg_type = Int
            default = 10
        "--analysis-type"
            help = "Type of analysis (patterns, load_balance, overlap, all)"
            default = "all"
        "--halo-iterations"
            help = "Number of halo exchange iterations for profiling"
            arg_type = Int
            default = 20
    end
    
    return parse_args(s)
end

function create_test_mesh_parallel(size_category::String, comm::MPI.Comm)
    """Create test mesh based on size category for parallel analysis."""
    rank = MPI.Comm_rank(comm)
    nprocs = MPI.Comm_size(comm)
    
    if size_category == "small"
        nx, ny, nz = 20, 20, 20
    elseif size_category == "medium"
        nx, ny, nz = 40, 40, 40
    elseif size_category == "large"
        nx, ny, nz = 80, 80, 80
    else
        error("Unknown mesh size category: $size_category")
    end
    
    # Create sequential mesh on rank 0, then distribute
    if rank == 0
        println("Creating $(nx)x$(ny)x$(nz) mesh...")
        mesh = create_box_mesh(nx, ny, nz, 1.0, 1.0, 1.0)
        opt_mesh = convert_to_optimized_mesh(mesh)
    else
        opt_mesh = nothing
    end
    
    # Broadcast mesh to all processes (simplified for testing)
    # In practice, this would use proper mesh decomposition
    if rank == 0
        # Create mock parallel mesh structure for testing
        n_cells = length(opt_mesh.cells)
        cells_per_proc = div(n_cells, nprocs)
        
        # Create send/recv maps for communication analysis
        send_maps = Dict{Int, Vector{Int}}()
        recv_maps = Dict{Int, Vector{Int}}()
        
        # Each process communicates with its neighbors
        for p in 0:(nprocs-1)
            if p != rank
                # Mock halo exchange pattern
                start_idx = p * cells_per_proc + 1
                end_idx = min((p + 1) * cells_per_proc, n_cells)
                halo_size = min(10, div(end_idx - start_idx + 1, 4))
                
                send_maps[p] = collect(start_idx:(start_idx + halo_size - 1))
                recv_maps[p] = collect((end_idx - halo_size + 1):end_idx)
            end
        end
        
        opt_mesh.send_maps = send_maps
        opt_mesh.recv_maps = recv_maps
    end
    
    # Broadcast mesh structure (simplified)
    # In a real implementation, this would use proper mesh partitioning
    return opt_mesh
end

function create_test_fields_parallel(mesh, comm::MPI.Comm)
    """Create test fields for parallel analysis."""
    rank = MPI.Comm_rank(comm)
    
    if mesh === nothing
        return nothing
    end
    
    n_cells = length(mesh.cells)
    
    # Create velocity and pressure fields
    U_values = [SVector{3,Float64}(1.0 + 0.1*sin(i), 0.0, 0.0) for i in 1:n_cells]
    p_values = [0.1 * cos(i) for i in 1:n_cells]
    
    U = Field("U", mesh, U_values)
    p = Field("p", mesh, p_values)
    
    return Dict("U" => U, "p" => p)
end

function analyze_communication_patterns_comprehensive(mesh, comm, args)
    """Perform comprehensive communication pattern analysis."""
    rank = MPI.Comm_rank(comm)
    nprocs = MPI.Comm_size(comm)
    
    if rank == 0
        println("\n" * "="^60)
        println("Communication Pattern Analysis")
        println("="^60)
        println("Processes: $nprocs")
        println("Mesh size: $(args["mesh-size"])")
        println("Analysis type: $(args["analysis-type"])")
    end
    
    results = Dict{String, Any}()
    
    # 1. Basic Communication Pattern Analysis
    if args["analysis-type"] in ["patterns", "all"]
        if rank == 0
            println("\n1. Analyzing communication patterns...")
        end
        
        comm_profile = analyze_communication_patterns(mesh, comm; 
                                                    profile_name="$(args["mesh-size"])_patterns")
        results["communication_patterns"] = comm_profile
        
        if rank == 0
            println("   Communication volume: $(round(comm_profile.communication_volume / 1024^2, digits=2)) MB")
            println("   Load imbalance: $(round(comm_profile.load_imbalance * 100, digits=2))%")
            println("   Communication imbalance: $(round(comm_profile.communication_imbalance * 100, digits=2))%")
        end
    end
    
    # 2. Load Balance Analysis
    if args["analysis-type"] in ["load_balance", "all"]
        if rank == 0
            println("\n2. Analyzing load balance...")
        end
        
        # Create mock work weights based on mesh
        n_cells = mesh === nothing ? 0 : length(mesh.cells)
        work_weights = ones(n_cells) .+ 0.1 * rand(n_cells)  # Slight variation
        
        load_metrics = analyze_load_balance(mesh, comm, work_weights; 
                                          profile_name="$(args["mesh-size"])_load_balance")
        results["load_balance"] = load_metrics
        
        if rank == 0
            println("   Work imbalance: $(round(load_metrics.work_imbalance * 100, digits=2))%")
            println("   Max/Avg work ratio: $(round(load_metrics.max_work_ratio, digits=2))")
            println("   Parallel efficiency: $(round(load_metrics.parallel_efficiency * 100, digits=2))%")
            println("   Recommendations: $(length(load_metrics.recommendations))")
        end
    end
    
    # 3. Halo Exchange Profiling
    if args["analysis-type"] in ["overlap", "all"] && mesh !== nothing
        if rank == 0
            println("\n3. Profiling halo exchange...")
        end
        
        # Create test field for halo exchange
        n_cells = length(mesh.cells)
        test_field = rand(n_cells)
        
        halo_profile = profile_halo_exchange(test_field, mesh, comm; 
                                           n_iterations=args["halo-iterations"],
                                           profile_name="$(args["mesh-size"])_halo")
        results["halo_exchange"] = halo_profile
        
        if rank == 0
            println("   Communication time: $(round(halo_profile.communication_time * 1000, digits=2)) ms")
            println("   Peak bandwidth: $(round(halo_profile.peak_bandwidth, digits=2)) MB/s")
        end
    end
    
    # 4. Overlap Analysis
    if args["analysis-type"] in ["overlap", "all"] && mesh !== nothing
        if rank == 0
            println("\n4. Analyzing computation-communication overlap...")
        end
        
        # Create test field and computation function
        n_cells = length(mesh.cells)
        test_field = rand(n_cells)
        
        function mock_computation(field, mesh)
            # Simulate gradient computation
            for i in 1:length(field)
                field[i] = field[i] * 1.01 + 0.001 * sin(i)
            end
            # Add some computational work
            for _ in 1:100
                sum(field)
            end
        end
        
        overlap_analysis = identify_overlap_opportunities(mesh, comm, mock_computation, test_field;
                                                        profile_name="$(args["mesh-size"])_overlap")
        results["overlap_analysis"] = overlap_analysis
        
        if rank == 0
            println("   Computation time: $(round(overlap_analysis.computation_time * 1000, digits=2)) ms")
            println("   Communication time: $(round(overlap_analysis.communication_time * 1000, digits=2)) ms")
            println("   Overlap ratio: $(round(overlap_analysis.overlap_ratio * 100, digits=2))%")
            println("   Efficiency gain: $(round(overlap_analysis.efficiency_gain * 100, digits=2))%")
            println("   Opportunities: $(length(overlap_analysis.overlap_opportunities))")
        end
    end
    
    return results
end

function generate_reports(results, args, comm)
    """Generate comprehensive analysis reports."""
    rank = MPI.Comm_rank(comm)
    
    if rank != 0
        return  # Only rank 0 generates reports
    end
    
    output_dir = args["output"]
    mkpath(output_dir)
    
    println("\n" * "="^60)
    println("Generating Reports")
    println("="^60)
    
    # Generate comprehensive report if all analyses were performed
    if haskey(results, "communication_patterns") && 
       haskey(results, "load_balance") && 
       haskey(results, "overlap_analysis")
        
        report_file = joinpath(output_dir, "comprehensive_communication_report.md")
        create_communication_report(
            results["communication_patterns"],
            results["load_balance"], 
            results["overlap_analysis"],
            report_file
        )
        println("Comprehensive report: $report_file")
    end
    
    # Generate individual reports
    timestamp = Dates.format(now(), "yyyy-mm-dd_HH-MM-SS")
    
    for (analysis_type, data) in results
        individual_file = joinpath(output_dir, "$(analysis_type)_$(timestamp).txt")
        
        open(individual_file, "w") do io
            println(io, "JuliaFOAM Communication Analysis: $analysis_type")
            println(io, "Generated: $(now())")
            println(io, "Mesh size: $(args["mesh-size"])")
            println(io, "Solver: $(args["solver"])")
            println(io, "")
            
            if analysis_type == "communication_patterns"
                profile = data
                println(io, "Communication Volume: $(round(profile.communication_volume / 1024^2, digits=2)) MB")
                println(io, "Load Imbalance: $(round(profile.load_imbalance * 100, digits=2))%")
                println(io, "Communication Imbalance: $(round(profile.communication_imbalance * 100, digits=2))%")
            elseif analysis_type == "load_balance"
                metrics = data
                println(io, "Work Imbalance: $(round(metrics.work_imbalance * 100, digits=2))%")
                println(io, "Max/Avg Work Ratio: $(round(metrics.max_work_ratio, digits=2))")
                println(io, "Parallel Efficiency: $(round(metrics.parallel_efficiency * 100, digits=2))%")
                println(io, "Recommendations:")
                for (i, rec) in enumerate(metrics.recommendations)
                    println(io, "  $i. $rec")
                end
            elseif analysis_type == "overlap_analysis"
                analysis = data
                println(io, "Overlap Ratio: $(round(analysis.overlap_ratio * 100, digits=2))%")
                println(io, "Efficiency Gain: $(round(analysis.efficiency_gain * 100, digits=2))%")
                println(io, "Opportunities:")
                for (i, opp) in enumerate(analysis.overlap_opportunities)
                    println(io, "  $i. $opp")
                end
            end
        end
        
        println("Individual report: $individual_file")
    end
end

function main()
    comm = MPI.COMM_WORLD
    rank = MPI.Comm_rank(comm)
    nprocs = MPI.Comm_size(comm)
    
    args = parse_commandline()
    
    if rank == 0
        println("JuliaFOAM Communication Pattern Analysis")
        println("Starting analysis with $nprocs processes...")
    end
    
    try
        # Create test mesh and fields
        mesh = create_test_mesh_parallel(args["mesh-size"], comm)
        fields = create_test_fields_parallel(mesh, comm)
        
        # Perform analysis
        results = analyze_communication_patterns_comprehensive(mesh, comm, args)
        
        # Generate reports
        generate_reports(results, args, comm)
        
        if rank == 0
            println("\nCommunication analysis completed successfully!")
            println("Results saved to: $(args["output"])")
        end
        
    catch e
        if rank == 0
            println("Error during analysis: $e")
            println("This might be due to missing dependencies or MPI configuration issues.")
        end
        MPI.Abort(comm, 1)
    end
    
    MPI.Finalize()
end

if abspath(PROGRAM_FILE) == @__FILE__
    main()
end
