#!/usr/bin/env julia

# setup.jl - Setup script for JuliaFOAM

using Pkg

# Add required packages
println("Installing required packages...")
Pkg.add([
    "StaticArrays",
    "SparseArrays",
    "LinearAlgebra",
    "LoopVectorization",
    "MPI",
    "PETSc",
    "PartitionedArrays",
    "Metis",  # Changed from METIS to Metis
    "METIS_jll",  # Added METIS_jll
    "WriteVTK"
])

# Make solver scripts executable
println("Making solver scripts executable...")
chmod(joinpath(@__DIR__, "solvers", "simpleFoam.jl"), 0o755)
chmod(joinpath(@__DIR__, "solvers", "pisoFoam.jl"), 0o755)
chmod(joinpath(@__DIR__, "utilities", "blockMesh.jl"), 0o755)
chmod(joinpath(@__DIR__, "utilities", "benchmark.jl"), 0o755)

# Create symbolic links in a bin directory
println("Creating bin directory with executable links...")
bin_dir = joinpath(@__DIR__, "bin")
mkpath(bin_dir)

# Create symbolic links
symlink(joinpath(@__DIR__, "solvers", "simpleFoam.jl"), joinpath(bin_dir, "simpleFoam"))
symlink(joinpath(@__DIR__, "solvers", "pisoFoam.jl"), joinpath(bin_dir, "pisoFoam"))
symlink(joinpath(@__DIR__, "utilities", "blockMesh.jl"), joinpath(bin_dir, "blockMesh"))
symlink(joinpath(@__DIR__, "utilities", "benchmark.jl"), joinpath(bin_dir, "benchmark"))

println("Setup completed successfully!")
println("Add the following to your .bashrc or .zshrc file to use JuliaFOAM from anywhere:")
println("export PATH=\"\$PATH:$(joinpath(abspath(bin_dir)))\"")
