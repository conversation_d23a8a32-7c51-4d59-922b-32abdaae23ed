#!/usr/bin/env julia

# checkMesh.jl - Mesh checking utility for JuliaFOAM

using Pkg
# Activate the JuliaFOAM package
if isfile(joinpath(@__DIR__, "..", "Project.toml"))
    Pkg.activate(joinpath(@__DIR__, ".."))
end

using JuliaFOAM
using StaticArrays
using Printf
using LinearAlgebra

"""
    check_mesh(mesh::Mesh)

Check a mesh for quality issues.

# Arguments
- `mesh`: The mesh to check

# Returns
- `Dict`: Dictionary of mesh quality metrics
"""
function check_mesh(mesh::Mesh)
    println("Checking mesh quality...")
    
    # Initialize metrics
    metrics = Dict{String,Any}(
        "cells" => length(mesh.cells),
        "faces" => length(mesh.faces),
        "boundary_faces" => length(mesh.boundary_faces),
        "boundary_patches" => Dict{String,Int}(),
        "non_orthogonality" => Dict{String,Any}(),
        "skewness" => Dict{String,Any}(),
        "aspect_ratio" => Dict{String,Any}(),
        "volume_ratio" => Dict{String,Any}(),
        "min_volume" => Inf,
        "max_volume" => -Inf,
        "total_volume" => 0.0,
        "issues" => Dict{String,Int}()
    )
    
    # Count boundary patches
    for (patch_name, face_indices) in mesh.boundary_patches
        metrics["boundary_patches"][patch_name] = length(face_indices)
    end
    
    # Check non-orthogonality
    non_ortho_angles = Float64[]
    
    for face in mesh.faces
        if face.neighbour > 0
            # Get cell centers
            owner_center = mesh.cells[face.owner].center
            neighbour_center = mesh.cells[face.neighbour].center
            
            # Get face normal
            face_normal = normalize(face.area)
            
            # Get cell-to-cell vector
            cell_vector = normalize(neighbour_center - owner_center)
            
            # Calculate angle
            angle = acosd(abs(dot(face_normal, cell_vector)))
            push!(non_ortho_angles, angle)
        end
    end
    
    if !isempty(non_ortho_angles)
        metrics["non_orthogonality"]["max"] = maximum(non_ortho_angles)
        metrics["non_orthogonality"]["avg"] = mean(non_ortho_angles)
        metrics["non_orthogonality"]["min"] = minimum(non_ortho_angles)
        
        # Count severe non-orthogonality
        severe_count = count(a -> a > 70.0, non_ortho_angles)
        if severe_count > 0
            metrics["issues"]["severe_non_orthogonality"] = severe_count
        end
    end
    
    # Check skewness
    skewness_values = Float64[]
    
    for face in mesh.faces
        if face.neighbour > 0
            # Get cell centers
            owner_center = mesh.cells[face.owner].center
            neighbour_center = mesh.cells[face.neighbour].center
            
            # Get face center
            face_center = face.center
            
            # Get cell-to-cell vector
            cell_vector = neighbour_center - owner_center
            
            # Calculate intersection point
            mid_point = owner_center + 0.5 * cell_vector
            
            # Calculate skewness
            skewness = norm(face_center - mid_point) / norm(cell_vector)
            push!(skewness_values, skewness)
        end
    end
    
    if !isempty(skewness_values)
        metrics["skewness"]["max"] = maximum(skewness_values)
        metrics["skewness"]["avg"] = mean(skewness_values)
        metrics["skewness"]["min"] = minimum(skewness_values)
        
        # Count severe skewness
        severe_count = count(s -> s > 0.8, skewness_values)
        if severe_count > 0
            metrics["issues"]["severe_skewness"] = severe_count
        end
    end
    
    # Check cell volumes
    volumes = Float64[]
    
    for cell in mesh.cells
        volume = cell.volume
        push!(volumes, volume)
        
        metrics["min_volume"] = min(metrics["min_volume"], volume)
        metrics["max_volume"] = max(metrics["max_volume"], volume)
        metrics["total_volume"] += volume
    end
    
    # Check volume ratio
    if !isempty(volumes)
        metrics["volume_ratio"]["max"] = metrics["max_volume"] / metrics["min_volume"]
        
        # Count negative volumes
        negative_count = count(v -> v <= 0.0, volumes)
        if negative_count > 0
            metrics["issues"]["negative_volumes"] = negative_count
        end
    end
    
    # Check aspect ratio
    aspect_ratios = Float64[]
    
    for cell in mesh.cells
        # Calculate principal axes and dimensions
        # This is a simplified approach - in a real implementation,
        # we would compute the eigenvalues of the inertia tensor
        
        # For now, use a simple approximation
        aspect_ratio = 1.0
        push!(aspect_ratios, aspect_ratio)
    end
    
    if !isempty(aspect_ratios)
        metrics["aspect_ratio"]["max"] = maximum(aspect_ratios)
        metrics["aspect_ratio"]["avg"] = mean(aspect_ratios)
        metrics["aspect_ratio"]["min"] = minimum(aspect_ratios)
        
        # Count high aspect ratio cells
        high_count = count(a -> a > 1000.0, aspect_ratios)
        if high_count > 0
            metrics["issues"]["high_aspect_ratio"] = high_count
        end
    end
    
    return metrics
end

"""
    check_mesh_case(case_dir::String)

Check the mesh of a case.

# Arguments
- `case_dir`: Path to the case directory
"""
function check_mesh_case(case_dir::String)
    # Read mesh
    mesh_dir = joinpath(case_dir, "constant", "polyMesh")
    
    if !isdir(mesh_dir)
        error("Mesh directory not found: $mesh_dir")
    end
    
    println("Reading mesh from $mesh_dir...")
    mesh = read_mesh(mesh_dir)
    
    # Check mesh
    metrics = check_mesh(mesh)
    
    # Print summary
    println("\nMesh statistics:")
    println("  Cells:          $(metrics["cells"])")
    println("  Faces:          $(metrics["faces"])")
    println("  Boundary faces: $(metrics["boundary_faces"])")
    println("  Total volume:   $(metrics["total_volume"])")
    
    println("\nBoundary patches:")
    for (patch_name, count) in metrics["boundary_patches"]
        println("  $patch_name: $count faces")
    end
    
    println("\nMesh quality metrics:")
    
    if haskey(metrics["non_orthogonality"], "max")
        println("  Non-orthogonality:")
        println("    Max: $(metrics["non_orthogonality"]["max"]) degrees")
        println("    Avg: $(metrics["non_orthogonality"]["avg"]) degrees")
    end
    
    if haskey(metrics["skewness"], "max")
        println("  Skewness:")
        println("    Max: $(metrics["skewness"]["max"])")
        println("    Avg: $(metrics["skewness"]["avg"])")
    end
    
    if haskey(metrics["aspect_ratio"], "max")
        println("  Aspect ratio:")
        println("    Max: $(metrics["aspect_ratio"]["max"])")
        println("    Avg: $(metrics["aspect_ratio"]["avg"])")
    end
    
    if haskey(metrics, "volume_ratio") && haskey(metrics["volume_ratio"], "max")
        println("  Volume ratio: $(metrics["volume_ratio"]["max"])")
    end
    
    println("\nCell volume bounds:")
    println("  Min: $(metrics["min_volume"])")
    println("  Max: $(metrics["max_volume"])")
    
    # Print issues
    if !isempty(metrics["issues"])
        println("\nMesh issues found:")
        
        for (issue, count) in metrics["issues"]
            println("  $issue: $count")
        end
        
        println("\nMesh check failed!")
    else
        println("\nMesh check passed!")
    end
end

function main()
    # Parse command line arguments
    if length(ARGS) < 1
        println("Usage: checkMesh.jl <case_directory>")
        exit(1)
    end
    
    case_dir = ARGS[1]
    
    if !isdir(case_dir)
        println("Error: Case directory $case_dir does not exist")
        exit(1)
    end
    
    # Check mesh
    check_mesh_case(case_dir)
end

# Run the main function
main()
