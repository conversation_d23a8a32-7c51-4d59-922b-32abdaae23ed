#!/usr/bin/env julia

# decomposePar.jl - Domain decomposition utility for JuliaFOAM

using Pkg
# Activate the JuliaFOAM package
if isfile(joinpath(@__DIR__, "..", "Project.toml"))
    Pkg.activate(joinpath(@__DIR__, ".."))
end

using JuliaFOAM
using JuliaFOAM.Parallel
using StaticArrays
using MPI

"""
    parse_decomposition_dict(filename::String)

Parse a decomposeParDict file and extract decomposition parameters.

# Arguments
- `filename`: Path to the decomposeParDict file

# Returns
- `Dict`: Dictionary of decomposition parameters
"""
function parse_decomposition_dict(filename::String)
    if !isfile(filename)
        error("File not found: $filename")
    end
    
    # Initialize parameters
    params = Dict{String,Any}(
        "numberOfSubdomains" => 4,  # Default
        "method" => "scotch",       # Default
        "n" => (2, 2, 1),           # Default for simple
        "delta" => 0.001,           # Default for scotch
        "order" => "xyz"            # Default for simple
    )
    
    # Read file
    open(filename, "r") do file
        for line in eachline(file)
            # Remove comments
            line = replace(line, r"//.*$" => "")
            line = replace(line, r"/\*.*\*/" => "")
            
            # Skip empty lines
            if isempty(strip(line))
                continue
            end
            
            # Parse parameters
            if occursin("numberOfSubdomains", line)
                m = match(r"numberOfSubdomains\s+(\d+);", line)
                if m !== nothing
                    params["numberOfSubdomains"] = parse(Int, m.captures[1])
                end
            elseif occursin("method", line)
                m = match(r"method\s+(\w+);", line)
                if m !== nothing
                    params["method"] = m.captures[1]
                end
            elseif occursin("n", line)
                m = match(r"n\s+\(\s*(\d+)\s+(\d+)\s+(\d+)\s*\);", line)
                if m !== nothing
                    params["n"] = (parse(Int, m.captures[1]), 
                                  parse(Int, m.captures[2]), 
                                  parse(Int, m.captures[3]))
                end
            elseif occursin("delta", line)
                m = match(r"delta\s+(\d+(?:\.\d+)?);", line)
                if m !== nothing
                    params["delta"] = parse(Float64, m.captures[1])
                end
            elseif occursin("order", line)
                m = match(r"order\s+(\w+);", line)
                if m !== nothing
                    params["order"] = m.captures[1]
                end
            end
        end
    end
    
    return params
end

"""
    decompose_case(case_dir::String, params::Dict)

Decompose a case for parallel execution.

# Arguments
- `case_dir`: Path to the case directory
- `params`: Dictionary of decomposition parameters
"""
function decompose_case(case_dir::String, params::Dict)
    # Import case
    println("Importing case from $case_dir...")
    mesh, fields, properties, config = import_openfoam_case(case_dir)
    
    # Decompose mesh
    println("Decomposing mesh...")
    n_procs = params["numberOfSubdomains"]
    decomposed_mesh = decompose_mesh(mesh, n_procs)
    
    # Create processor directories
    for proc in 0:(n_procs-1)
        proc_dir = joinpath(case_dir, "processor$proc")
        mkpath(joinpath(proc_dir, "constant", "polyMesh"))
        
        # Find latest time directory
        time_dirs = filter(isdir, readdir(case_dir, join=true))
        time_dirs = filter(d -> occursin(r"^\d+(\.\d+)?$", basename(d)), time_dirs)
        
        if isempty(time_dirs)
            time_dir = joinpath(case_dir, "0")
            if !isdir(time_dir)
                error("No time directories found in $case_dir")
            end
        else
            # Sort by time value
            sort!(time_dirs, by=d -> parse(Float64, basename(d)))
            time_dir = time_dirs[end]
        end
        
        # Create time directory in processor directory
        mkpath(joinpath(proc_dir, basename(time_dir)))
    end
    
    # Write decomposed mesh
    for proc in 0:(n_procs-1)
        proc_dir = joinpath(case_dir, "processor$proc")
        mesh_dir = joinpath(proc_dir, "constant", "polyMesh")
        
        # Extract processor mesh
        proc_mesh = extract_processor_mesh(decomposed_mesh, proc)
        
        # Write mesh files
        write_openfoam_mesh(mesh_dir, proc_mesh)
    end
    
    # Write decomposed fields
    for proc in 0:(n_procs-1)
        proc_dir = joinpath(case_dir, "processor$proc")
        
        # Find latest time directory
        time_dirs = filter(isdir, readdir(case_dir, join=true))
        time_dirs = filter(d -> occursin(r"^\d+(\.\d+)?$", basename(d)), time_dirs)
        
        if isempty(time_dirs)
            time_dir = joinpath(case_dir, "0")
            if !isdir(time_dir)
                error("No time directories found in $case_dir")
            end
        else
            # Sort by time value
            sort!(time_dirs, by=d -> parse(Float64, basename(d)))
            time_dir = time_dirs[end]
        end
        
        proc_time_dir = joinpath(proc_dir, basename(time_dir))
        
        # Extract processor mesh
        proc_mesh = extract_processor_mesh(decomposed_mesh, proc)
        
        # Extract processor fields
        proc_fields = Dict{String,Field}()
        
        for (field_name, field) in fields
            proc_field = extract_processor_field(field, decomposed_mesh, proc)
            proc_fields[field_name] = proc_field
        end
        
        # Write fields
        for (field_name, field) in proc_fields
            write_field(proc_time_dir, field, proc_mesh, field_name)
        end
    end
    
    println("Case decomposed successfully into $n_procs subdomains")
end

"""
    extract_processor_mesh(mesh::Mesh, proc::Int)

Extract the portion of the mesh for a specific processor.

# Arguments
- `mesh`: The full mesh
- `proc`: Processor index

# Returns
- `Mesh`: The processor mesh
"""
function extract_processor_mesh(mesh::Mesh, proc::Int)
    # Find cells for this processor
    proc_cells = findall(i -> mesh.cell_partition[i] == proc, 1:length(mesh.cells))
    
    # Find faces for this processor
    proc_faces = Int[]
    
    for cell_idx in proc_cells
        append!(proc_faces, mesh.cells[cell_idx].faces)
    end
    
    unique!(proc_faces)
    
    # Find boundary faces
    proc_boundary_faces = intersect(proc_faces, mesh.boundary_faces)
    
    # Find processor boundary faces
    proc_processor_faces = Int[]
    
    for face_idx in proc_faces
        face = mesh.faces[face_idx]
        
        if face.neighbour > 0 && mesh.cell_partition[face.owner] != mesh.cell_partition[face.neighbour]
            push!(proc_processor_faces, face_idx)
        end
    end
    
    # Create processor boundary patches
    proc_boundary_patches = Dict{String,Vector{Int32}}()
    
    for (patch_name, face_indices) in mesh.boundary_patches
        proc_patch_faces = intersect(face_indices, proc_faces)
        
        if !isempty(proc_patch_faces)
            proc_boundary_patches[patch_name] = proc_patch_faces
        end
    end
    
    # Create processor patches
    for neighbor_proc in 0:(length(unique(mesh.cell_partition))-1)
        if neighbor_proc == proc
            continue
        end
        
        patch_name = "processor$(neighbor_proc)"
        proc_boundary_patches[patch_name] = Int32[]
        
        for face_idx in proc_processor_faces
            face = mesh.faces[face_idx]
            
            if face.neighbour > 0
                if mesh.cell_partition[face.owner] == proc && mesh.cell_partition[face.neighbour] == neighbor_proc
                    push!(proc_boundary_patches[patch_name], face_idx)
                elseif mesh.cell_partition[face.neighbour] == proc && mesh.cell_partition[face.owner] == neighbor_proc
                    push!(proc_boundary_patches[patch_name], face_idx)
                end
            end
        end
    end
    
    # Create processor boundary conditions
    proc_boundary_conditions = Dict{String,BoundaryCondition}()
    
    for (patch_name, face_indices) in proc_boundary_patches
        if startswith(patch_name, "processor")
            # Processor boundary
            proc_boundary_conditions[patch_name] = ZeroGradientBC()
        else
            # Regular boundary
            proc_boundary_conditions[patch_name] = mesh.boundary_conditions[patch_name]
        end
    end
    
    # Create processor mesh
    return Mesh(
        mesh.cells[proc_cells],
        mesh.faces[proc_faces],
        Int32[],  # Boundary faces will be recalculated
        proc_boundary_patches,
        proc_boundary_conditions,
        fill(Int32(0), length(proc_cells)),  # No partitioning for processor mesh
        Int32[]  # No halo cells for processor mesh
    )
end

"""
    extract_processor_field(field::Field, mesh::Mesh, proc::Int)

Extract the portion of a field for a specific processor.

# Arguments
- `field`: The full field
- `mesh`: The full mesh
- `proc`: Processor index

# Returns
- `Field`: The processor field
"""
function extract_processor_field(field::Field{T}, mesh::Mesh, proc::Int) where T
    # Find cells for this processor
    proc_cells = findall(i -> mesh.cell_partition[i] == proc, 1:length(mesh.cells))
    
    # Extract internal field
    proc_internal_field = field.internal_field[proc_cells]
    
    # Extract boundary field
    proc_boundary_field = Dict{String,Vector{T}}()
    
    for (patch_name, face_indices) in mesh.boundary_patches
        proc_patch_faces = Int[]
        
        for face_idx in face_indices
            face = mesh.faces[face_idx]
            
            if mesh.cell_partition[face.owner] == proc
                push!(proc_patch_faces, face_idx)
            end
        end
        
        if !isempty(proc_patch_faces)
            # Find local indices in the patch
            local_indices = [findfirst(x -> x == face_idx, face_indices) for face_idx in proc_patch_faces]
            
            # Extract boundary field values
            proc_boundary_field[patch_name] = field.boundary_field[patch_name][local_indices]
        end
    end
    
    # Add processor patches
    for neighbor_proc in 0:(length(unique(mesh.cell_partition))-1)
        if neighbor_proc == proc
            continue
        end
        
        patch_name = "processor$(neighbor_proc)"
        proc_boundary_field[patch_name] = Vector{T}()
        
        # Find processor boundary faces
        proc_processor_faces = Int[]
        
        for face_idx in 1:length(mesh.faces)
            face = mesh.faces[face_idx]
            
            if face.neighbour > 0
                if mesh.cell_partition[face.owner] == proc && mesh.cell_partition[face.neighbour] == neighbor_proc
                    push!(proc_processor_faces, face_idx)
                elseif mesh.cell_partition[face.neighbour] == proc && mesh.cell_partition[face.owner] == neighbor_proc
                    push!(proc_processor_faces, face_idx)
                end
            end
        end
        
        # Extract field values for processor boundary
        for face_idx in proc_processor_faces
            face = mesh.faces[face_idx]
            
            if mesh.cell_partition[face.owner] == proc
                push!(proc_boundary_field[patch_name], field.internal_field[face.owner])
            else
                push!(proc_boundary_field[patch_name], field.internal_field[face.neighbour])
            end
        end
    end
    
    # Create processor field
    return Field{T}(proc_internal_field, proc_boundary_field, copy(proc_internal_field))
end

function main()
    # Parse command line arguments
    if length(ARGS) < 1
        println("Usage: decomposePar.jl <case_directory>")
        exit(1)
    end
    
    case_dir = ARGS[1]
    
    if !isdir(case_dir)
        println("Error: Case directory $case_dir does not exist")
        exit(1)
    end
    
    # Read decomposeParDict
    decompose_dict = joinpath(case_dir, "system", "decomposeParDict")
    
    if !isfile(decompose_dict)
        println("Error: decomposeParDict not found at $decompose_dict")
        exit(1)
    end
    
    println("Reading decomposeParDict from $decompose_dict")
    params = parse_decomposition_dict(decompose_dict)
    
    # Decompose case
    decompose_case(case_dir, params)
end

# Run the main function
main()
