#!/usr/bin/env julia

# reconstructPar.jl - Reconstruction utility for JuliaFOAM

using Pkg
# Activate the JuliaFOAM package
if isfile(joinpath(@__DIR__, "..", "Project.toml"))
    Pkg.activate(joinpath(@__DIR__, ".."))
end

using JuliaFOAM
using JuliaFOAM.Parallel
using StaticArrays
using Printf

"""
    reconstruct_case(case_dir::String)

Reconstruct a decomposed case into a single case.

# Arguments
- `case_dir`: Path to the case directory
"""
function reconstruct_case(case_dir::String)
    # Find processor directories
    proc_dirs = filter(d -> startswith(basename(d), "processor"), readdir(case_dir, join=true))
    
    if isempty(proc_dirs)
        error("No processor directories found in $case_dir")
    end
    
    n_procs = length(proc_dirs)
    println("Found $n_procs processor directories")
    
    # Find time directories
    time_dirs = Dict{String,Vector{String}}()
    
    for proc_dir in proc_dirs
        proc_time_dirs = filter(isdir, readdir(proc_dir, join=true))
        proc_time_dirs = filter(d -> occursin(r"^\d+(\.\d+)?$", basename(d)), proc_time_dirs)
        
        for time_dir in proc_time_dirs
            time = basename(time_dir)
            
            if !haskey(time_dirs, time)
                time_dirs[time] = String[]
            end
            
            push!(time_dirs[time], time_dir)
        end
    end
    
    # Sort time directories
    times = sort(collect(keys(time_dirs)), by=t -> parse(Float64, t))
    
    if isempty(times)
        error("No time directories found in processor directories")
    end
    
    println("Found $(length(times)) time directories: $(join(times, ", "))")
    
    # Load processor meshes
    proc_meshes = Dict{Int,Mesh}()
    
    for proc in 0:(n_procs-1)
        proc_dir = joinpath(case_dir, "processor$proc")
        mesh_dir = joinpath(proc_dir, "constant", "polyMesh")
        
        println("Reading mesh from processor $proc")
        proc_meshes[proc] = read_mesh(mesh_dir)
    end
    
    # Reconstruct mesh
    println("Reconstructing mesh...")
    mesh = reconstruct_mesh(proc_meshes)
    
    # Write reconstructed mesh
    mesh_dir = joinpath(case_dir, "constant", "polyMesh")
    mkpath(mesh_dir)
    write_openfoam_mesh(mesh_dir, mesh)
    
    # Reconstruct fields for each time
    for time in times
        println("Reconstructing fields for time $time...")
        
        # Create time directory
        time_dir = joinpath(case_dir, time)
        mkpath(time_dir)
        
        # Load processor fields
        proc_fields = Dict{Int,Dict{String,Field}}()
        
        for proc in 0:(n_procs-1)
            proc_dir = joinpath(case_dir, "processor$proc")
            proc_time_dir = joinpath(proc_dir, time)
            
            if !isdir(proc_time_dir)
                continue
            end
            
            # Find field files
            field_files = filter(f -> isfile(joinpath(proc_time_dir, f)), readdir(proc_time_dir))
            
            proc_fields[proc] = Dict{String,Field}()
            
            for field_file in field_files
                field_name = splitext(field_file)[1]
                field_path = joinpath(proc_time_dir, field_file)
                
                # Read field
                proc_fields[proc][field_name] = read_field(field_path, proc_meshes[proc])
            end
        end
        
        # Reconstruct fields
        fields = Dict{String,Field}()
        
        # Get all field names
        field_names = Set{String}()
        
        for (proc, proc_field_dict) in proc_fields
            for field_name in keys(proc_field_dict)
                push!(field_names, field_name)
            end
        end
        
        for field_name in field_names
            println("  Reconstructing field $field_name")
            
            # Check field type
            field_type = nothing
            
            for (proc, proc_field_dict) in proc_fields
                if haskey(proc_field_dict, field_name)
                    field_type = eltype(proc_field_dict[field_name].internal_field)
                    break
                end
            end
            
            if field_type === nothing
                error("Could not determine field type for $field_name")
            end
            
            # Reconstruct field
            fields[field_name] = reconstruct_field(proc_fields, field_name, mesh, field_type)
            
            # Write field
            write_field(time_dir, fields[field_name], mesh, field_name)
        end
    end
    
    println("Case reconstructed successfully")
end

"""
    reconstruct_mesh(proc_meshes::Dict{Int,Mesh})

Reconstruct a mesh from processor meshes.

# Arguments
- `proc_meshes`: Dictionary of processor meshes

# Returns
- `Mesh`: The reconstructed mesh
"""
function reconstruct_mesh(proc_meshes::Dict{Int,Mesh})
    # Get the number of processors
    n_procs = length(proc_meshes)
    
    # Initialize arrays for the reconstructed mesh
    cells = Vector{Cell}()
    faces = Vector{Face}()
    boundary_faces = Int32[]
    boundary_patches = Dict{String,Vector{Int32}}()
    boundary_conditions = Dict{String,BoundaryCondition}()
    
    # Cell and face offsets for each processor
    cell_offsets = Dict{Int,Int}()
    face_offsets = Dict{Int,Int}()
    
    # Calculate offsets
    cell_offset = 0
    face_offset = 0
    
    for proc in 0:(n_procs-1)
        cell_offsets[proc] = cell_offset
        face_offsets[proc] = face_offset
        
        cell_offset += length(proc_meshes[proc].cells)
        face_offset += length(proc_meshes[proc].faces)
    end
    
    # Reconstruct cells and faces
    for proc in 0:(n_procs-1)
        proc_mesh = proc_meshes[proc]
        
        # Add cells
        for cell in proc_mesh.cells
            # Adjust face indices
            new_faces = Int32[face_idx + face_offsets[proc] for face_idx in cell.faces]
            
            push!(cells, Cell(new_faces))
        end
        
        # Add faces
        for face in proc_mesh.faces
            # Adjust owner and neighbour indices
            new_owner = face.owner + cell_offsets[proc]
            new_neighbour = face.neighbour > 0 ? face.neighbour + cell_offsets[proc] : -1
            
            # Check if this is a processor boundary face
            is_proc_boundary = false
            
            for (patch_name, face_indices) in proc_mesh.boundary_patches
                if startswith(patch_name, "processor") && face.index in face_indices
                    is_proc_boundary = true
                    break
                end
            end
            
            # Skip processor boundary faces
            if is_proc_boundary
                continue
            end
            
            push!(faces, Face(face.points, new_owner, new_neighbour, length(faces) + 1))
        end
    end
    
    # Reconstruct boundary patches
    for proc in 0:(n_procs-1)
        proc_mesh = proc_meshes[proc]
        
        for (patch_name, face_indices) in proc_mesh.boundary_patches
            # Skip processor patches
            if startswith(patch_name, "processor")
                continue
            end
            
            if !haskey(boundary_patches, patch_name)
                boundary_patches[patch_name] = Int32[]
                boundary_conditions[patch_name] = proc_mesh.boundary_conditions[patch_name]
            end
            
            # Add face indices with offset
            for face_idx in face_indices
                # Find the corresponding face in the reconstructed mesh
                new_face_idx = face_idx + face_offsets[proc]
                push!(boundary_patches[patch_name], new_face_idx)
                push!(boundary_faces, new_face_idx)
            end
        end
    end
    
    # Create reconstructed mesh
    return Mesh(
        cells,
        faces,
        boundary_faces,
        boundary_patches,
        boundary_conditions,
        fill(Int32(0), length(cells)),  # No partitioning for reconstructed mesh
        Int32[]  # No halo cells for reconstructed mesh
    )
end

"""
    reconstruct_field(proc_fields::Dict{Int,Dict{String,Field}}, field_name::String, mesh::Mesh, field_type::Type)

Reconstruct a field from processor fields.

# Arguments
- `proc_fields`: Dictionary of processor fields
- `field_name`: Name of the field to reconstruct
- `mesh`: The reconstructed mesh
- `field_type`: Type of the field values

# Returns
- `Field`: The reconstructed field
"""
function reconstruct_field(proc_fields::Dict{Int,Dict{String,Field}}, field_name::String, mesh::Mesh, field_type::Type)
    # Get the number of processors
    n_procs = length(proc_fields)
    
    # Initialize arrays for the reconstructed field
    internal_field = Vector{field_type}(undef, length(mesh.cells))
    boundary_field = Dict{String,Vector{field_type}}()
    
    # Cell offsets for each processor
    cell_offsets = Dict{Int,Int}()
    
    # Calculate offsets
    cell_offset = 0
    
    for proc in 0:(n_procs-1)
        cell_offsets[proc] = cell_offset
        
        if haskey(proc_fields, proc) && haskey(proc_fields[proc], field_name)
            cell_offset += length(proc_fields[proc][field_name].internal_field)
        end
    end
    
    # Reconstruct internal field
    for proc in 0:(n_procs-1)
        if !haskey(proc_fields, proc) || !haskey(proc_fields[proc], field_name)
            continue
        end
        
        proc_field = proc_fields[proc][field_name]
        
        # Add internal field values
        for (i, value) in enumerate(proc_field.internal_field)
            internal_field[i + cell_offsets[proc]] = value
        end
    end
    
    # Reconstruct boundary field
    for (patch_name, face_indices) in mesh.boundary_patches
        boundary_field[patch_name] = Vector{field_type}(undef, length(face_indices))
        
        # Find processor that has this patch
        for proc in 0:(n_procs-1)
            if !haskey(proc_fields, proc) || !haskey(proc_fields[proc], field_name)
                continue
            end
            
            proc_field = proc_fields[proc][field_name]
            
            if haskey(proc_field.boundary_field, patch_name)
                # Copy boundary field values
                for (i, value) in enumerate(proc_field.boundary_field[patch_name])
                    boundary_field[patch_name][i] = value
                end
                
                break
            end
        end
    end
    
    # Create reconstructed field
    return Field{field_type}(internal_field, boundary_field, copy(internal_field))
end

function main()
    # Parse command line arguments
    if length(ARGS) < 1
        println("Usage: reconstructPar.jl <case_directory>")
        exit(1)
    end
    
    case_dir = ARGS[1]
    
    if !isdir(case_dir)
        println("Error: Case directory $case_dir does not exist")
        exit(1)
    end
    
    # Reconstruct case
    reconstruct_case(case_dir)
end

# Run the main function
main()
