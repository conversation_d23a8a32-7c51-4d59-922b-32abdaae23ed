#!/usr/bin/env julia

# benchmark.jl - Benchmark JuliaFOAM against OpenFOAM

using Pkg
# Activate the JuliaFOAM package
if isfile(joinpath(@__DIR__, "..", "Project.toml"))
    Pkg.activate(joinpath(@__DIR__, ".."))
end

using JuliaFOAM
using JuliaFOAM.Solvers
using StaticArrays
using Printf
using Dates

"""
    run_openfoam_case(case_dir::String, solver::String)

Run an OpenFOAM case using the specified solver.

# Arguments
- `case_dir`: Path to the case directory
- `solver`: OpenFOAM solver to use (e.g., simpleFoam, pisoFoam)

# Returns
- `Float64`: Execution time in seconds
"""
function run_openfoam_case(case_dir::String, solver::String)
    # Source OpenFOAM environment
    source_cmd = "source /opt/openfoam12/etc/bashrc"
    
    # Change to case directory
    cd_cmd = "cd $case_dir"
    
    # Run the solver
    run_cmd = "$solver"
    
    # Combine commands
    cmd = `bash -c "$source_cmd && $cd_cmd && $run_cmd"`
    
    # Measure execution time
    start_time = time()
    run(cmd)
    end_time = time()
    
    return end_time - start_time
end

"""
    run_juliafoam_case(case_dir::String, solver::String)

Run a JuliaFOAM case using the specified solver.

# Arguments
- `case_dir`: Path to the case directory
- `solver`: JuliaFOAM solver to use (e.g., "simple_foam", "piso_foam")

# Returns
- `Float64`: Execution time in seconds
"""
function run_juliafoam_case(case_dir::String, solver::String)
    # Measure execution time
    start_time = time()
    
    if solver == "simple_foam"
        simple_foam(case_dir)
    elseif solver == "piso_foam"
        piso_foam(case_dir)
    else
        error("Unknown solver: $solver")
    end
    
    end_time = time()
    
    return end_time - start_time
end

"""
    compare_results(julia_case_dir::String, openfoam_case_dir::String)

Compare the results of JuliaFOAM and OpenFOAM simulations.

# Arguments
- `julia_case_dir`: Path to the JuliaFOAM case directory
- `openfoam_case_dir`: Path to the OpenFOAM case directory

# Returns
- `Dict`: Dictionary of error metrics
"""
function compare_results(julia_case_dir::String, openfoam_case_dir::String)
    # Import both cases
    julia_mesh, julia_fields, _, _ = import_openfoam_case(julia_case_dir)
    openfoam_mesh, openfoam_fields, _, _ = import_openfoam_case(openfoam_case_dir)
    
    # Compare fields
    errors = Dict{String,Dict{String,Float64}}()
    
    for field_name in intersect(keys(julia_fields), keys(openfoam_fields))
        julia_field = julia_fields[field_name]
        openfoam_field = openfoam_fields[field_name]
        
        # Calculate error metrics
        if eltype(julia_field.internal_field) <: SVector{3,Float64}
            # Vector field
            diff = [norm(julia_field.internal_field[i] - openfoam_field.internal_field[i]) 
                   for i in 1:length(julia_field.internal_field)]
            
            max_diff = maximum(diff)
            mean_diff = mean(diff)
            rms_diff = sqrt(mean(diff.^2))
            
            errors[field_name] = Dict{String,Float64}(
                "max_error" => max_diff,
                "mean_error" => mean_diff,
                "rms_error" => rms_diff
            )
        else
            # Scalar field
            diff = abs.(julia_field.internal_field - openfoam_field.internal_field)
            
            max_diff = maximum(diff)
            mean_diff = mean(diff)
            rms_diff = sqrt(mean(diff.^2))
            
            errors[field_name] = Dict{String,Float64}(
                "max_error" => max_diff,
                "mean_error" => mean_diff,
                "rms_error" => rms_diff
            )
        end
    end
    
    return errors
end

"""
    benchmark_case(case_name::String, julia_solver::String, openfoam_solver::String)

Benchmark JuliaFOAM against OpenFOAM for a specific case.

# Arguments
- `case_name`: Name of the case to benchmark
- `julia_solver`: JuliaFOAM solver to use
- `openfoam_solver`: OpenFOAM solver to use

# Returns
- `Dict`: Dictionary of benchmark results
"""
function benchmark_case(case_name::String, julia_solver::String, openfoam_solver::String)
    # Prepare case directories
    julia_case_dir = joinpath(tempdir(), "juliafoam_$case_name")
    openfoam_case_dir = joinpath(tempdir(), "openfoam_$case_name")
    
    # Copy case files
    mkpath(julia_case_dir)
    mkpath(openfoam_case_dir)
    
    # Copy case files from original case
    original_case_dir = joinpath(@__DIR__, "..", "cases", case_name)
    
    if !isdir(original_case_dir)
        error("Case directory not found: $original_case_dir")
    end
    
    # Copy case files to both directories
    run(`cp -r $original_case_dir/* $julia_case_dir/`)
    run(`cp -r $original_case_dir/* $openfoam_case_dir/`)
    
    # Run OpenFOAM case
    println("Running OpenFOAM case...")
    openfoam_time = run_openfoam_case(openfoam_case_dir, openfoam_solver)
    println("OpenFOAM completed in $openfoam_time seconds")
    
    # Run JuliaFOAM case
    println("Running JuliaFOAM case...")
    julia_time = run_juliafoam_case(julia_case_dir, julia_solver)
    println("JuliaFOAM completed in $julia_time seconds")
    
    # Compare results
    println("Comparing results...")
    errors = compare_results(julia_case_dir, openfoam_case_dir)
    
    # Print comparison
    println("Comparison of results:")
    for (field_name, metrics) in errors
        println("Field: $field_name")
        for (metric_name, value) in metrics
            println("  $metric_name: $value")
        end
    end
    
    # Calculate speedup
    speedup = openfoam_time / julia_time
    println("Speedup: $(speedup)x")
    
    # Return benchmark results
    return Dict{String,Any}(
        "case_name" => case_name,
        "julia_solver" => julia_solver,
        "openfoam_solver" => openfoam_solver,
        "julia_time" => julia_time,
        "openfoam_time" => openfoam_time,
        "speedup" => speedup,
        "errors" => errors
    )
end

"""
    run_all_benchmarks()

Run all available benchmarks.

# Returns
- `Vector{Dict}`: List of benchmark results
"""
function run_all_benchmarks()
    benchmarks = [
        ("cavity", "simple_foam", "simpleFoam"),
        # Add more benchmarks here
    ]
    
    results = Vector{Dict{String,Any}}()
    
    for (case_name, julia_solver, openfoam_solver) in benchmarks
        println("Benchmarking case: $case_name")
        result = benchmark_case(case_name, julia_solver, openfoam_solver)
        push!(results, result)
    end
    
    return results
end

"""
    write_benchmark_report(results::Vector{Dict}, filename::String)

Write benchmark results to a report file.

# Arguments
- `results`: List of benchmark results
- `filename`: Output file name
"""
function write_benchmark_report(results::Vector{Dict}, filename::String)
    open(filename, "w") do file
        write(file, "# JuliaFOAM Benchmark Report\n\n")
        write(file, "Date: $(Dates.format(now(), "yyyy-mm-dd HH:MM:SS"))\n\n")
        
        for result in results
            case_name = result["case_name"]
            julia_solver = result["julia_solver"]
            openfoam_solver = result["openfoam_solver"]
            julia_time = result["julia_time"]
            openfoam_time = result["openfoam_time"]
            speedup = result["speedup"]
            errors = result["errors"]
            
            write(file, "## Case: $case_name\n\n")
            write(file, "- JuliaFOAM solver: $julia_solver\n")
            write(file, "- OpenFOAM solver: $openfoam_solver\n")
            write(file, "- JuliaFOAM time: $(@sprintf("%.2f", julia_time)) seconds\n")
            write(file, "- OpenFOAM time: $(@sprintf("%.2f", openfoam_time)) seconds\n")
            write(file, "- Speedup: $(@sprintf("%.2f", speedup))x\n\n")
            
            write(file, "### Error Metrics\n\n")
            
            for (field_name, metrics) in errors
                write(file, "#### Field: $field_name\n\n")
                
                for (metric_name, value) in metrics
                    write(file, "- $metric_name: $(@sprintf("%.6e", value))\n")
                end
                
                write(file, "\n")
            end
            
            write(file, "---\n\n")
        end
    end
    
    println("Benchmark report written to $filename")
end

function main()
    # Parse command line arguments
    if length(ARGS) < 1
        println("Usage: benchmark.jl <case_name> [output_file]")
        println("       benchmark.jl --all [output_file]")
        exit(1)
    end
    
    case_name = ARGS[1]
    output_file = length(ARGS) >= 2 ? ARGS[2] : "benchmark_report.md"
    
    if case_name == "--all"
        # Run all benchmarks
        results = run_all_benchmarks()
        write_benchmark_report(results, output_file)
    else
        # Run specific benchmark
        if case_name == "cavity"
            result = benchmark_case(case_name, "simple_foam", "simpleFoam")
            write_benchmark_report([result], output_file)
        else
            println("Unknown case: $case_name")
            exit(1)
        end
    end
end

# Run the main function
main()
