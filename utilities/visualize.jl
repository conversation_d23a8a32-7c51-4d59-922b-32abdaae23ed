#!/usr/bin/env julia

# visualize.jl - Visualization utility for JuliaFOAM

using Pkg
# Activate the JuliaFOAM package
if isfile(joinpath(@__DIR__, "..", "Project.toml"))
    Pkg.activate(joinpath(@__DIR__, ".."))
end

using JuliaFOAM
using Printf
using WriteVTK

"""
    convert_case_to_vtk(case_dir::String, time::String="latest")

Convert a case to VTK format for visualization.

# Arguments
- `case_dir`: Path to the case directory
- `time`: Time to convert (default: "latest")
"""
function convert_case_to_vtk(case_dir::String, time::String="latest")
    println("Converting case to VTK format...")
    
    # Find time directories
    time_dirs = filter(isdir, readdir(case_dir, join=true))
    time_dirs = filter(d -> occursin(r"^\d+(\.\d+)?$", basename(d)), time_dirs)
    
    if isempty(time_dirs)
        error("No time directories found in $case_dir")
    end
    
    # Sort time directories
    sort!(time_dirs, by=d -> parse(Float64, basename(d)))
    
    # Select time directory
    time_dir = ""
    
    if time == "latest"
        time_dir = time_dirs[end]
    elseif time == "all"
        # Convert all time directories
        for dir in time_dirs
            convert_time_to_vtk(case_dir, dir)
        end
        return
    else
        # Find specific time
        for dir in time_dirs
            if basename(dir) == time
                time_dir = dir
                break
            end
        end
        
        if time_dir == ""
            error("Time directory $time not found")
        end
    end
    
    # Convert single time directory
    convert_time_to_vtk(case_dir, time_dir)
end

"""
    convert_time_to_vtk(case_dir::String, time_dir::String)

Convert a time directory to VTK format.

# Arguments
- `case_dir`: Path to the case directory
- `time_dir`: Path to the time directory
"""
function convert_time_to_vtk(case_dir::String, time_dir::String)
    time = basename(time_dir)
    println("Converting time $time to VTK format...")
    
    # Read mesh
    mesh_dir = joinpath(case_dir, "constant", "polyMesh")
    
    if !isdir(mesh_dir)
        error("Mesh directory not found: $mesh_dir")
    end
    
    println("Reading mesh from $mesh_dir...")
    mesh = read_mesh(mesh_dir)
    
    # Read fields
    println("Reading fields from $time_dir...")
    fields = Dict{String,Field}()
    
    field_files = filter(f -> isfile(joinpath(time_dir, f)), readdir(time_dir))
    
    for field_file in field_files
        field_name = splitext(field_file)[1]
        field_path = joinpath(time_dir, field_file)
        
        println("  Reading field $field_name...")
        fields[field_name] = read_field(field_path, mesh)
    end
    
    # Create VTK directory
    vtk_dir = joinpath(case_dir, "VTK")
    mkpath(vtk_dir)
    
    # Write VTK file
    vtk_file = joinpath(vtk_dir, "$(basename(case_dir))_$time")
    println("Writing VTK file to $vtk_file.vtu...")
    
    # Extract points and cells
    points = Vector{Vector{Float64}}()
    
    for cell in mesh.cells
        push!(points, [cell.center[1], cell.center[2], cell.center[3]])
    end
    
    # Create VTK grid
    vtkfile = vtk_grid(vtk_file, points)
    
    # Add field data
    for (field_name, field) in fields
        if eltype(field.internal_field) <: SVector{3,Float64}
            # Vector field
            vectors = Vector{Vector{Float64}}()
            
            for value in field.internal_field
                push!(vectors, [value[1], value[2], value[3]])
            end
            
            vtk_point_data(vtkfile, vectors, field_name)
        else
            # Scalar field
            vtk_point_data(vtkfile, field.internal_field, field_name)
        end
    end
    
    # Save VTK file
    vtk_save(vtkfile)
    
    println("VTK conversion completed for time $time")
end

"""
    launch_paraview(case_dir::String)

Launch ParaView with the VTK files.

# Arguments
- `case_dir`: Path to the case directory
"""
function launch_paraview(case_dir::String)
    # Check if ParaView is installed
    paraview_cmd = "paraview"
    
    try
        run(`which $paraview_cmd`)
    catch
        println("ParaView not found. Please install ParaView to visualize results.")
        return
    end
    
    # Check if VTK directory exists
    vtk_dir = joinpath(case_dir, "VTK")
    
    if !isdir(vtk_dir)
        println("VTK directory not found. Converting case to VTK format...")
        convert_case_to_vtk(case_dir)
    end
    
    # Find VTK files
    vtk_files = filter(f -> endswith(f, ".vtu"), readdir(vtk_dir, join=true))
    
    if isempty(vtk_files)
        println("No VTK files found. Converting case to VTK format...")
        convert_case_to_vtk(case_dir)
        
        # Check again
        vtk_files = filter(f -> endswith(f, ".vtu"), readdir(vtk_dir, join=true))
        
        if isempty(vtk_files)
            error("Failed to create VTK files")
        end
    end
    
    # Launch ParaView
    println("Launching ParaView...")
    run(`$paraview_cmd $vtk_files`)
end

function main()
    # Parse command line arguments
    if length(ARGS) < 1
        println("Usage: visualize.jl <case_directory> [time] [--paraview]")
        println("  time: Time to convert (default: latest)")
        println("  --paraview: Launch ParaView after conversion")
        exit(1)
    end
    
    case_dir = ARGS[1]
    
    if !isdir(case_dir)
        println("Error: Case directory $case_dir does not exist")
        exit(1)
    end
    
    # Parse options
    time = "latest"
    launch_pv = false
    
    for arg in ARGS[2:end]
        if arg == "--paraview"
            launch_pv = true
        else
            time = arg
        end
    end
    
    # Convert case to VTK
    convert_case_to_vtk(case_dir, time)
    
    # Launch ParaView if requested
    if launch_pv
        launch_paraview(case_dir)
    end
end

# Run the main function
main()
