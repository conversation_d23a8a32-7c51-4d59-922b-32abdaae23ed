#!/usr/bin/env bash

# juliaFoamRun - Parallel execution utility for JuliaFOAM

# Function to display usage
usage() {
    echo "Usage: juliaFoamRun [options] <solver> <case>"
    echo "Options:"
    echo "  -np <n>       Number of processors (default: 1)"
    echo "  -hostfile <f> Host file for MPI"
    echo "  -parallel     Run in parallel mode"
    echo "  -validate     Validate case before running"
    echo "  -decompose    Decompose case before running"
    echo "  -reconstruct  Reconstruct case after running"
    echo "  -help         Display this help message"
    echo ""
    echo "Example:"
    echo "  juliaFoamRun -parallel -np 4 simpleFoam cavity"
    exit 1
}

# Parse command line arguments
np=1
hostfile=""
parallel=false
validate=false
decompose=false
reconstruct=false
solver=""
case_dir=""

while [ $# -gt 0 ]; do
    case "$1" in
        -np)
            shift
            np=$1
            ;;
        -hostfile)
            shift
            hostfile=$1
            ;;
        -parallel)
            parallel=true
            ;;
        -validate)
            validate=true
            ;;
        -decompose)
            decompose=true
            ;;
        -reconstruct)
            reconstruct=true
            ;;
        -help)
            usage
            ;;
        -*)
            echo "Error: Unknown option: $1"
            usage
            ;;
        *)
            if [ -z "$solver" ]; then
                solver=$1
            elif [ -z "$case_dir" ]; then
                case_dir=$1
            else
                echo "Error: Too many arguments"
                usage
            fi
            ;;
    esac
    shift
done

# Check required arguments
if [ -z "$solver" ] || [ -z "$case_dir" ]; then
    echo "Error: Solver and case directory must be specified"
    usage
fi

# Get absolute path to case directory
if [ ! -d "$case_dir" ]; then
    echo "Error: Case directory not found: $case_dir"
    exit 1
fi
case_dir=$(cd "$case_dir" && pwd)

# Get path to JuliaFOAM
juliafoam_dir=$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)

# Check if solver exists
solver_path="$juliafoam_dir/solvers/${solver}.jl"
if [ ! -f "$solver_path" ]; then
    echo "Error: Solver not found: $solver_path"
    exit 1
fi

# Validate case if requested
if [ "$validate" = true ]; then
    echo "Validating case..."
    "$juliafoam_dir/utilities/validateCase.jl" "$case_dir"
    if [ $? -ne 0 ]; then
        echo "Error: Case validation failed"
        exit 1
    fi
fi

# Decompose case if requested
if [ "$parallel" = true ] && [ "$decompose" = true ]; then
    echo "Decomposing case..."
    "$juliafoam_dir/utilities/decomposePar.jl" "$case_dir"
    if [ $? -ne 0 ]; then
        echo "Error: Case decomposition failed"
        exit 1
    fi
fi

# Run solver
if [ "$parallel" = true ]; then
    echo "Running $solver in parallel on $np processors..."
    
    # Prepare MPI command
    mpi_cmd="mpirun -np $np"
    if [ ! -z "$hostfile" ]; then
        mpi_cmd="$mpi_cmd -hostfile $hostfile"
    fi
    
    # Run with MPI
    $mpi_cmd julia --project="$juliafoam_dir" "$solver_path" "$case_dir" -parallel
else
    echo "Running $solver in serial mode..."
    julia --project="$juliafoam_dir" "$solver_path" "$case_dir"
fi

# Reconstruct case if requested
if [ "$parallel" = true ] && [ "$reconstruct" = true ]; then
    echo "Reconstructing case..."
    "$juliafoam_dir/utilities/reconstructPar.jl" "$case_dir"
    if [ $? -ne 0 ]; then
        echo "Error: Case reconstruction failed"
        exit 1
    fi
fi

echo "Done!"
exit 0
