#!/usr/bin/env julia

# validateCase.jl - Case validation utility for JuliaFOAM

using Pkg
# Activate the JuliaFOAM package
if isfile(joinpath(@__DIR__, "..", "Project.toml"))
    Pkg.activate(joinpath(@__DIR__, ".."))
end

using JuliaFOAM
using Printf

"""
    validate_case_structure(case_dir::String)

Validate the structure of a case directory.

# Arguments
- `case_dir`: Path to the case directory

# Returns
- `Bool`: True if the case structure is valid
"""
function validate_case_structure(case_dir::String)
    println("Validating case structure in $case_dir...")
    
    # Check required directories
    required_dirs = [
        joinpath(case_dir, "0"),
        joinpath(case_dir, "constant"),
        joinpath(case_dir, "system")
    ]
    
    for dir in required_dirs
        if !isdir(dir)
            println("Error: Required directory not found: $dir")
            return false
        end
    end
    
    # Check required system files
    required_system_files = [
        joinpath(case_dir, "system", "controlDict"),
        joinpath(case_dir, "system", "fvSchemes"),
        joinpath(case_dir, "system", "fvSolution")
    ]
    
    for file in required_system_files
        if !isfile(file)
            println("Error: Required system file not found: $file")
            return false
        end
    end
    
    # Check mesh
    mesh_dir = joinpath(case_dir, "constant", "polyMesh")
    
    if !isdir(mesh_dir)
        println("Warning: Mesh directory not found: $mesh_dir")
        println("         This is OK if you plan to generate the mesh.")
    else
        required_mesh_files = [
            joinpath(mesh_dir, "points"),
            joinpath(mesh_dir, "faces"),
            joinpath(mesh_dir, "owner"),
            joinpath(mesh_dir, "neighbour"),
            joinpath(mesh_dir, "boundary")
        ]
        
        for file in required_mesh_files
            if !isfile(file)
                println("Error: Required mesh file not found: $file")
                return false
            end
        end
    end
    
    # Check initial fields
    time_dir = joinpath(case_dir, "0")
    
    if !isdir(time_dir)
        println("Error: Initial time directory not found: $time_dir")
        return false
    end
    
    # Find field files
    field_files = filter(f -> isfile(joinpath(time_dir, f)), readdir(time_dir))
    
    if isempty(field_files)
        println("Error: No field files found in initial time directory")
        return false
    end
    
    println("Found initial fields: $(join(field_files, ", "))")
    
    # Check transport properties
    transport_file = joinpath(case_dir, "constant", "transportProperties")
    
    if !isfile(transport_file)
        println("Error: Transport properties file not found: $transport_file")
        return false
    end
    
    println("Case structure validation passed!")
    return true
end

"""
    validate_field_file(file_path::String)

Validate a field file.

# Arguments
- `file_path`: Path to the field file

# Returns
- `Bool`: True if the field file is valid
"""
function validate_field_file(file_path::String)
    println("Validating field file: $file_path")
    
    if !isfile(file_path)
        println("Error: File not found: $file_path")
        return false
    end
    
    # Read file
    lines = readlines(file_path)
    
    # Check FoamFile header
    foam_file_found = false
    
    for i in 1:min(20, length(lines))
        if occursin("FoamFile", lines[i])
            foam_file_found = true
            break
        end
    end
    
    if !foam_file_found
        println("Error: FoamFile header not found in $file_path")
        return false
    end
    
    # Check dimensions
    dimensions_found = false
    
    for line in lines
        if occursin("dimensions", line)
            dimensions_found = true
            break
        end
    end
    
    if !dimensions_found
        println("Error: dimensions not found in $file_path")
        return false
    end
    
    # Check internalField
    internal_field_found = false
    
    for line in lines
        if occursin("internalField", line)
            internal_field_found = true
            break
        end
    end
    
    if !internal_field_found
        println("Error: internalField not found in $file_path")
        return false
    end
    
    # Check boundaryField
    boundary_field_found = false
    
    for line in lines
        if occursin("boundaryField", line)
            boundary_field_found = true
            break
        end
    end
    
    if !boundary_field_found
        println("Error: boundaryField not found in $file_path")
        return false
    end
    
    println("Field file validation passed!")
    return true
end

"""
    validate_case_files(case_dir::String)

Validate all files in a case.

# Arguments
- `case_dir`: Path to the case directory

# Returns
- `Bool`: True if all files are valid
"""
function validate_case_files(case_dir::String)
    println("Validating case files in $case_dir...")
    
    # Validate case structure
    if !validate_case_structure(case_dir)
        return false
    end
    
    # Validate initial fields
    time_dir = joinpath(case_dir, "0")
    
    field_files = filter(f -> isfile(joinpath(time_dir, f)), readdir(time_dir))
    
    for field_file in field_files
        if !validate_field_file(joinpath(time_dir, field_file))
            return false
        end
    end
    
    # Validate controlDict
    control_dict = joinpath(case_dir, "system", "controlDict")
    
    if !isfile(control_dict)
        println("Error: controlDict not found: $control_dict")
        return false
    end
    
    # Read controlDict
    lines = readlines(control_dict)
    
    # Check application
    application_found = false
    application = ""
    
    for line in lines
        m = match(r"application\s+(\w+);", line)
        if m !== nothing
            application_found = true
            application = m.captures[1]
            break
        end
    end
    
    if !application_found
        println("Error: application not found in controlDict")
        return false
    end
    
    println("Application: $application")
    
    # Check solver exists
    solver_path = joinpath(@__DIR__, "..", "solvers", "$(lowercase(application)).jl")
    
    if !isfile(solver_path)
        println("Warning: Solver not found: $solver_path")
        println("         This is OK if you're using a custom solver.")
    end
    
    # Check time control
    start_time_found = false
    end_time_found = false
    delta_t_found = false
    
    for line in lines
        if occursin("startTime", line)
            start_time_found = true
        elseif occursin("endTime", line)
            end_time_found = true
        elseif occursin("deltaT", line)
            delta_t_found = true
        end
    end
    
    if !start_time_found
        println("Error: startTime not found in controlDict")
        return false
    end
    
    if !end_time_found
        println("Error: endTime not found in controlDict")
        return false
    end
    
    if !delta_t_found
        println("Error: deltaT not found in controlDict")
        return false
    end
    
    # Validate fvSchemes
    fv_schemes = joinpath(case_dir, "system", "fvSchemes")
    
    if !isfile(fv_schemes)
        println("Error: fvSchemes not found: $fv_schemes")
        return false
    end
    
    # Read fvSchemes
    lines = readlines(fv_schemes)
    
    # Check required schemes
    required_schemes = ["ddtSchemes", "gradSchemes", "divSchemes", "laplacianSchemes"]
    found_schemes = Set{String}()
    
    for line in lines
        for scheme in required_schemes
            if occursin(scheme, line)
                push!(found_schemes, scheme)
            end
        end
    end
    
    for scheme in required_schemes
        if !(scheme in found_schemes)
            println("Error: Required scheme not found in fvSchemes: $scheme")
            return false
        end
    end
    
    # Validate fvSolution
    fv_solution = joinpath(case_dir, "system", "fvSolution")
    
    if !isfile(fv_solution)
        println("Error: fvSolution not found: $fv_solution")
        return false
    end
    
    # Read fvSolution
    lines = readlines(fv_solution)
    
    # Check solvers section
    solvers_found = false
    
    for line in lines
        if occursin("solvers", line)
            solvers_found = true
            break
        end
    end
    
    if !solvers_found
        println("Error: solvers section not found in fvSolution")
        return false
    end
    
    # Check algorithm section
    algorithm_found = false
    
    for line in lines
        if occursin("SIMPLE", line) || occursin("PISO", line) || occursin("PIMPLE", line)
            algorithm_found = true
            break
        end
    end
    
    if !algorithm_found
        println("Error: Algorithm section (SIMPLE/PISO/PIMPLE) not found in fvSolution")
        return false
    end
    
    println("Case files validation passed!")
    return true
end

"""
    validate_case_compatibility(case_dir::String)

Validate compatibility of a case with JuliaFOAM.

# Arguments
- `case_dir`: Path to the case directory

# Returns
- `Bool`: True if the case is compatible
"""
function validate_case_compatibility(case_dir::String)
    println("Validating case compatibility with JuliaFOAM...")
    
    # Read controlDict
    control_dict = joinpath(case_dir, "system", "controlDict")
    
    if !isfile(control_dict)
        println("Error: controlDict not found: $control_dict")
        return false
    end
    
    lines = readlines(control_dict)
    
    # Check application
    application = ""
    
    for line in lines
        m = match(r"application\s+(\w+);", line)
        if m !== nothing
            application = m.captures[1]
            break
        end
    end
    
    # Check if application is supported
    supported_applications = ["simpleFoam", "pisoFoam"]
    
    if !(lowercase(application) in lowercase.(supported_applications))
        println("Warning: Application $application may not be fully supported by JuliaFOAM")
        println("         Supported applications: $(join(supported_applications, ", "))")
    end
    
    # Check boundary conditions
    time_dir = joinpath(case_dir, "0")
    
    field_files = filter(f -> isfile(joinpath(time_dir, f)), readdir(time_dir))
    
    unsupported_bcs = Set{String}()
    
    for field_file in field_files
        file_path = joinpath(time_dir, field_file)
        lines = readlines(file_path)
        
        in_boundary_field = false
        
        for line in lines
            if occursin("boundaryField", line)
                in_boundary_field = true
            elseif in_boundary_field && occursin("type", line)
                m = match(r"type\s+(\w+);", line)
                if m !== nothing
                    bc_type = m.captures[1]
                    
                    # Check if BC type is supported
                    supported_bcs = [
                        "fixedValue", "zeroGradient", "empty", "symmetry",
                        "cyclic", "wedge", "inletOutlet", "outletInlet",
                        "totalPressure", "pressureInletOutletVelocity",
                        "slip", "noSlip", "movingWall", "atmospheric"
                    ]
                    
                    if !(bc_type in supported_bcs)
                        push!(unsupported_bcs, bc_type)
                    end
                end
            end
        end
    end
    
    if !isempty(unsupported_bcs)
        println("Warning: The following boundary condition types may not be fully supported:")
        for bc in unsupported_bcs
            println("         - $bc")
        end
    end
    
    # Check numerical schemes
    fv_schemes = joinpath(case_dir, "system", "fvSchemes")
    
    if !isfile(fv_schemes)
        println("Error: fvSchemes not found: $fv_schemes")
        return false
    end
    
    lines = readlines(fv_schemes)
    
    unsupported_schemes = Set{String}()
    
    for line in lines
        # Check for scheme specifications
        for scheme_type in ["grad", "div", "laplacian", "interpolation", "snGrad"]
            pattern = Regex("$(scheme_type)\\s+\\w+\\s+(\\w+)")
            m = match(pattern, line)
            if m !== nothing
                scheme = m.captures[1]
                
                # Check if scheme is supported
                supported_schemes = [
                    "Gauss", "linear", "upwind", "linearUpwind",
                    "QUICK", "limitedLinear", "corrected"
                ]
                
                if !(scheme in supported_schemes)
                    push!(unsupported_schemes, scheme)
                end
            end
        end
    end
    
    if !isempty(unsupported_schemes)
        println("Warning: The following numerical schemes may not be fully supported:")
        for scheme in unsupported_schemes
            println("         - $scheme")
        end
    end
    
    println("Case compatibility validation completed!")
    return true
end

function main()
    # Parse command line arguments
    if length(ARGS) < 1
        println("Usage: validateCase.jl <case_directory>")
        exit(1)
    end
    
    case_dir = ARGS[1]
    
    if !isdir(case_dir)
        println("Error: Case directory $case_dir does not exist")
        exit(1)
    end
    
    # Validate case
    if validate_case_files(case_dir) && validate_case_compatibility(case_dir)
        println("\nCase validation passed! The case is ready to run with JuliaFOAM.")
    else
        println("\nCase validation failed! Please fix the issues before running with JuliaFOAM.")
        exit(1)
    end
end

# Run the main function
main()
