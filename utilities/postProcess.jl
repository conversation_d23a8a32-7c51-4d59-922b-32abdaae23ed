#!/usr/bin/env julia

# postProcess.jl - Post-processing utility for JuliaFOAM

using Pkg
# Activate the JuliaFOAM package
if isfile(joinpath(@__DIR__, "..", "Project.toml"))
    Pkg.activate(joinpath(@__DIR__, ".."))
end

using JuliaFOAM
using JuliaFOAM.PostProcessing
using StaticArrays
using Printf

"""
    run_post_processing(case_dir::String, function_name::String, args::Vector{String})

Run a post-processing function on a case.

# Arguments
- `case_dir`: Path to the case directory
- `function_name`: Name of the post-processing function to run
- `args`: Additional arguments for the function
"""
function run_post_processing(case_dir::String, function_name::String, args::Vector{String})
    # Import case
    println("Importing case from $case_dir...")
    mesh, fields, properties, config = import_openfoam_case(case_dir)
    
    # Run the requested function
    println("Running $function_name...")
    
    if function_name == "vorticity"
        # Calculate vorticity
        if !haskey(fields, "U")
            error("Velocity field (U) not found in case")
        end
        
        vorticity = calculate_vorticity(fields["U"], mesh)
        
        # Write vorticity field
        write_fields(case_dir, "postProcessing", Dict("vorticity" => vorticity), mesh)
        
    elseif function_name == "Q"
        # Calculate Q-criterion
        if !haskey(fields, "U")
            error("Velocity field (U) not found in case")
        end
        
        Q = calculate_q_criterion(fields["U"], mesh)
        
        # Write Q field
        write_fields(case_dir, "postProcessing", Dict("Q" => Q), mesh)
        
    elseif function_name == "wallShearStress"
        # Calculate wall shear stress
        if !haskey(fields, "U")
            error("Velocity field (U) not found in case")
        end
        
        # Get wall patch names
        wall_patches = String[]
        if length(args) > 0
            wall_patches = args
        else
            # Use all wall patches
            for (patch_name, bc) in mesh.boundary_conditions
                if bc isa FixedValueBC || bc isa MovingWallBC
                    push!(wall_patches, patch_name)
                end
            end
        end
        
        wall_shear = calculate_wall_shear_stress(fields["U"], mesh, properties, wall_patches)
        
        # Print wall shear stress
        for (patch_name, tau_w) in wall_patches
            println("Wall shear stress on $patch_name:")
            for (i, tau) in enumerate(tau_w)
                println("  Face $i: $tau")
            end
        end
        
    elseif function_name == "forces"
        # Calculate forces
        if !haskey(fields, "U") || !haskey(fields, "p")
            error("Velocity (U) or pressure (p) field not found in case")
        end
        
        # Get patch names
        patches = String[]
        if length(args) > 0
            patches = args
        else
            # Use all patches
            patches = collect(keys(mesh.boundary_patches))
        end
        
        forces = calculate_forces(fields["p"], fields["U"], mesh, properties, patches)
        
        # Print forces
        for (patch_name, (pressure_force, viscous_force)) in forces
            println("Forces on $patch_name:")
            println("  Pressure force: $pressure_force")
            println("  Viscous force: $viscous_force")
            println("  Total force: $(pressure_force + viscous_force)")
        end
        
    elseif function_name == "Cp"
        # Calculate pressure coefficient
        if !haskey(fields, "p")
            error("Pressure field (p) not found in case")
        end
        
        # Get reference values
        U_ref = 1.0  # Default
        p_ref = 0.0  # Default
        
        if length(args) >= 1
            U_ref = parse(Float64, args[1])
        end
        
        if length(args) >= 2
            p_ref = parse(Float64, args[2])
        end
        
        Cp = calculate_pressure_coefficient(fields["p"], U_ref, properties.density, p_ref, mesh)
        
        # Write Cp field
        write_fields(case_dir, "postProcessing", Dict("Cp" => Cp), mesh)
        
    elseif function_name == "Co"
        # Calculate Courant number
        if !haskey(fields, "U")
            error("Velocity field (U) not found in case")
        end
        
        # Get time step
        dt = config.delta_t
        
        if length(args) >= 1
            dt = parse(Float64, args[1])
        end
        
        Co = calculate_courant_number(fields["U"], mesh, dt)
        
        # Write Co field
        write_fields(case_dir, "postProcessing", Dict("Co" => Co), mesh)
        
    elseif function_name == "sample"
        # Extract data along a line or on a plane
        if length(args) < 1
            error("No field specified for sampling")
        end
        
        field_name = args[1]
        
        if !haskey(fields, field_name)
            error("Field $field_name not found in case")
        end
        
        field = fields[field_name]
        
        if length(args) >= 7 && args[2] == "line"
            # Extract data along a line
            start_x = parse(Float64, args[3])
            start_y = parse(Float64, args[4])
            start_z = parse(Float64, args[5])
            end_x = parse(Float64, args[6])
            end_y = parse(Float64, args[7])
            end_z = parse(Float64, args[8])
            
            n_points = 100  # Default
            
            if length(args) >= 9
                n_points = parse(Int, args[9])
            end
            
            start = SVector{3,Float64}(start_x, start_y, start_z)
            end_ = SVector{3,Float64}(end_x, end_y, end_z)
            
            distances, values = extract_line_data(field, mesh, start, end_, n_points)
            
            # Write data to file
            output_file = joinpath(case_dir, "postProcessing", "sampleLine_$field_name.csv")
            mkpath(dirname(output_file))
            
            open(output_file, "w") do file
                write(file, "distance,$field_name\n")
                
                for (d, v) in zip(distances, values)
                    if eltype(field.internal_field) <: SVector{3,Float64}
                        write(file, "$d,$(v[1]),$(v[2]),$(v[3])\n")
                    else
                        write(file, "$d,$v\n")
                    end
                end
            end
            
            println("Line data written to $output_file")
            
        elseif length(args) >= 8 && args[2] == "plane"
            # Extract data on a plane
            origin_x = parse(Float64, args[3])
            origin_y = parse(Float64, args[4])
            origin_z = parse(Float64, args[5])
            normal_x = parse(Float64, args[6])
            normal_y = parse(Float64, args[7])
            normal_z = parse(Float64, args[8])
            
            n_points_x = 50  # Default
            n_points_y = 50  # Default
            
            if length(args) >= 9
                n_points_x = parse(Int, args[9])
            end
            
            if length(args) >= 10
                n_points_y = parse(Int, args[10])
            end
            
            origin = SVector{3,Float64}(origin_x, origin_y, origin_z)
            normal = SVector{3,Float64}(normal_x, normal_y, normal_z)
            
            points, values = extract_plane_data(field, mesh, origin, normal, n_points_x, n_points_y)
            
            # Write data to VTK file
            output_file = joinpath(case_dir, "postProcessing", "samplePlane_$field_name")
            mkpath(dirname(output_file))
            
            # Create VTK grid
            vtkfile = vtk_grid(output_file, 
                              reshape([p[1] for p in points], (n_points_x, n_points_y)),
                              reshape([p[2] for p in points], (n_points_x, n_points_y)),
                              reshape([p[3] for p in points], (n_points_x, n_points_y)))
            
            # Add field data
            if eltype(field.internal_field) <: SVector{3,Float64}
                vtk_point_data(vtkfile, 
                              (reshape([v[1] for v in values], (n_points_x, n_points_y)),
                               reshape([v[2] for v in values], (n_points_x, n_points_y)),
                               reshape([v[3] for v in values], (n_points_x, n_points_y))),
                              field_name)
            else
                vtk_point_data(vtkfile, reshape(values, (n_points_x, n_points_y)), field_name)
            end
            
            # Save VTK file
            vtk_save(vtkfile)
            
            println("Plane data written to $output_file.vtr")
        else
            error("Invalid sampling type. Use 'line' or 'plane'.")
        end
        
    elseif function_name == "residuals"
        # Plot residuals
        # This is a placeholder - in a full implementation, we would read residuals from log files
        println("Residual plotting not implemented yet")
        
    elseif function_name == "vtk"
        # Write all fields to VTK format
        write_vtk_visualization(case_dir, mesh, fields, "postProcessing")
        
        println("VTK files written to $(joinpath(case_dir, "VTK"))")
        
    else
        error("Unknown post-processing function: $function_name")
    end
    
    println("Post-processing completed successfully")
end

function main()
    # Parse command line arguments
    if length(ARGS) < 2
        println("Usage: postProcess.jl <case_directory> <function> [args...]")
        println("Available functions:")
        println("  vorticity")
        println("  Q")
        println("  wallShearStress [patch_names...]")
        println("  forces [patch_names...]")
        println("  Cp [U_ref] [p_ref]")
        println("  Co [dt]")
        println("  sample <field> line <start_x> <start_y> <start_z> <end_x> <end_y> <end_z> [n_points]")
        println("  sample <field> plane <origin_x> <origin_y> <origin_z> <normal_x> <normal_y> <normal_z> [n_points_x] [n_points_y]")
        println("  residuals")
        println("  vtk")
        exit(1)
    end
    
    case_dir = ARGS[1]
    function_name = ARGS[2]
    args = ARGS[3:end]
    
    if !isdir(case_dir)
        println("Error: Case directory $case_dir does not exist")
        exit(1)
    end
    
    # Run post-processing
    run_post_processing(case_dir, function_name, args)
end

# Run the main function
main()
